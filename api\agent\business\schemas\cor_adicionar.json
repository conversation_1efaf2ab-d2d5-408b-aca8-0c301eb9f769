{"generated_at": "2025-08-24T01:16:01-03:00", "database": "neo4j", "node_types": [{"label": "Cor", "properties": [{"name": "nome", "type": "STRING"}, {"name": "codigo", "type": "STRING"}, {"name": "hexadecimal", "type": "STRING"}]}, {"label": "Negocio", "properties": [{"name": "areaId", "type": "INTEGER"}, {"name": "bairro", "type": "STRING"}, {"name": "cep", "type": "STRING"}, {"name": "cidade", "type": "STRING"}, {"name": "cnpjCpf", "type": "STRING"}, {"name": "complemento", "type": "STRING"}, {"name": "email", "type": "STRING"}, {"name": "excluido", "type": "INTEGER"}, {"name": "idx", "type": "STRING"}, {"name": "logradouro", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "numero", "type": "STRING"}, {"name": "razaoSocial", "type": "STRING"}, {"name": "responsavel", "type": "STRING"}, {"name": "telefone", "type": "STRING"}, {"name": "tipoId", "type": "INTEGER"}, {"name": "uf", "type": "STRING"}, {"name": "usuarioIdx", "type": "STRING"}]}], "relationship_types": [{"type": "DISPONIBILIZA_COR", "properties": []}], "patterns": [["Negocio", "DISPONIBILIZA_COR", "Cor"]]}