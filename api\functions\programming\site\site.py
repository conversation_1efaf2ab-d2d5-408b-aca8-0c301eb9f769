from ...llm.openai import openai_chat
from ...llm.gemini import gemini_generate
from ...llm.llm_hub import llm_model_execute
from .cpanel import cpanel_subdomain_create, cpanel_subdomain_file_save, cpanel_file_list, cpanel_get_file_content, cpanel_subdomain_exists
from ...secrets import fetch_api_key_env
import asyncio
from ...programming.database.my_sql import mysql_add_data, mysql_query,  mysql_update_data
import requests
import time 

model_openai = ["gpt-4-0125-preview","gpt-3.5-turbo"]
model_deepseek = ["deepseek-coder"]
model_gemini = ["gemini.pro"]
  


async def site_requirements(data):


#==============================
async  def site_verify_domain(data):

  data["DOMAIN"] = "site.gptalk.com.br"
  data["USER"] = "gptalk"
  data["TOKEN"] = "0ZIAKOBQD195AU35UQZXNUSC00CNHKW1"

  
  result = await cpanel_subdomain_exists(data)
  return result


#========================
async def site_files_contents(data):
    if "DOMINIO_PADRAO" in data:
    # Separa o DOMINIO_PADRAO no primeiro ponto encontrado
        partes = data["DOMINIO_PADRAO"].split('.', 1)
        nome = partes[0]
        subdominio = partes[1] if len(partes) > 1 else ""

        # Adiciona as novas chaves ao dicionário data
        data["NOME"] = nome
        data["DOMAIN"] = subdominio
    else:
        print("DOMINIO_PADRAO não definido em data.")
    
    data["SUBDIRECTORY"] = "site/sites/" + data["NOME"]
    data["TOKEN"] =fetch_api_key_env("GPTALK_CPANEL_TOKEN")
    data['USER'] = fetch_api_key_env("GPTALK_CPANEL_USER")
    
    pages = {}
    files = await cpanel_file_list (data)
    for file in files:
        data["FILENAME"] = file
        content = await cpanel_get_file_content(data)  # Aqui você precisa de uma função assíncrona real
       
        pages[file] =  content
 
    return pages



#========================
async def site_fetch(data):
    #print("site_fetch()")
    data['bd'] = "GPTALK_01"
    sql = "SELECT * FROM SITE WHERE DOMINIO_PADRAO = '" + data["DOMINIO_PADRAO"] + "' AND EXCLUIDO = 0"
    data['sql'] = sql
    #print("data",data)
    site = await mysql_query(data)
    #print("site retornado", site)
    return site  

#========================
async def sites_fetch(data):
    print("sites_fetch()")
    data['bd'] = "GPTALK_01"
    sql = "SELECT * FROM SITE WHERE USUARIO_ID = '" + data["USUARIO_ID"] + "' AND EXCLUIDO = 0"
    data['sql'] = sql
    #print("data",data)
    sites = await mysql_query(data)
    #print("site retornado", site)
    return sites  
  
  #========================
async def site_delete(data):
    print("site_excluir()")
    url = 'https://www.gptalk.com.br/site_file_manager.php?excluirarquivos=site/sites/'+ data["NOME"]
    response = requests.get(url)
    
    data["bd"] = "gptalk_01"
    data["tb"] = "SITE"
    sql = f"UPDATE SITE SET EXCLUIDO = 1 WHERE ID = {data['ID']}" 
    data['sql'] = sql
    result = await mysql_query(data)
    print("result", result)


    return "ok"  
  
#========================
async def site_create(data):
    #print("***** site_create *****")
    #print("data")
    #print(data)
    nome = data["NOME"]
    area = data["AREA"]
    objetivo = data["OBJETIVO"]
    publico_alvo = data["PUBLICO_ALVO"]
    design_estilo = data["DESIGN_ESTILO"]
    funcionalidades = data["FUNCIONALIDADES"]
    seo_marketing = data.get("TATICA", "")
    observacao = data.get("OBSERVACAO", "")
    llm = data.get("LLM","gemini-1.5-pro-latest")
   
    requirements = f""" 
    {area}
    {objetivo}
    {publico_alvo}
    {design_estilo}
    {funcionalidades}
    {seo_marketing}
    {observacao}
"""

    system = """
    Você é especialista em criação de sites, landing pages e páginas de vendas. Você é uma  webdesigner com amplo conhecimento em design web, desenvolvimento front-end e back-end, e especialização em integrações via API com servidores de serviços e dados. Você tem um profundo entendimento de HTML, CSS, JavaScript, e uma habilidade excepcional para criar sites dinâmicos que maximizam as taxas de conversão, sejam eles voltados para e-commerce , geração de leads, institucionais ou uma combinação de uma ou mais função. Seu trabalho é reconhecido por ser otimizado para SEO, altamente responsivo e acessível em todos os dispositivos, além de seguro contra as mais diversas vulnerabilidades web. 

    Você também domina copywriting, possui um profundo conhecimento de argumentos persuasivos para vendas online, e usa este conhecimento para criar textos para sites, landing pages  e paginas de vendas de alta conversão.
    Você utiliza toda esta capcidade para criar sites que atendam a todas as especificações e desejos dos seus clientes.
    
    """
    
    instruction  = """
Objetivo:
Criar o site solicitado, se possivel realizando todas as especificações solicitadas.

Regras a serem seguidas:
1 - O código deverá ser retornado em forma de objeto, onde a chave será o nome da página.
 Exemplo (site com 1 página):
    {
        'index.html':
        '''
        <!DOCTYPE html>
        <html lang="pt">
        <head>
            <meta charset="UTF-8">
            <title>Página Básica</title>
        </head>
        <body>
            <h1>Olá, Mundo!</h1>
            <p>Esta é uma página HTML básica.</p>
        </body>
        </html>
    }
    É de extrema importância que o codigo seja retornado desta forma, sem exceção.

    2 - Sempre que for  necessário adiconar imagem , qualquer imagem, seja logo, produtos, serviços, ou até de icones,
    crie um placholder  usando o site makeplaceholder.com .
    Exemplo: https://makeplaceholder.com/?size=400x400 .

    3 - Preecha o alt da imagem de forma a descrever a imagem que será exibida, com o máximo de detalhes. Exemplos:
    <img src="https://makeplaceholder.com/?size=200x200" alt="familia almoçando juntos a mesa">
    <img src="https://makeplaceholder.com/?size=300x300" alt="ração para gatos">
    <img src="https://makeplaceholder.com/?size=300x300" alt="homem pedalando sob  o sol em volta de uma lagoa">
    Isto irá facilitar ao gestor de conteúdo visual buscar estas imagens e fazer a substituição futuramente.
    
    4 - Caso haja icones , utilizar icones do iconify(https://icon-sets.iconify.design/)

    5 = Criar apenas o layout. usar somente html e css. Não é necessário que o site execute funções neste momento.
     O objetivo é apresentar o layout para  o cliente.
    6 - Os links entre as páginas deverão funcionar normalmente, caso haja mais de uma.
    
    7 - Todos os elementos semalhantes devem ter a mesma classe. Exemplo todos os h1 terão a classe 'h1_class'. Todos os buttons terao a classe 'button_class'. 
    
    8 - Verifque se os codigos estão completos antes de considerar a tarefa finalizada, e retorne o codigo completo.

    9 - O site deverá ter pelo menos 3 seções principais: cabeçalho, corpo e rodapé.
    
    10 - Verifique  2x se as paginas geradas estão completas (começam com <html> e terminam com </html>. Isto é crucial para um site. Caso a página naõ encerre com </hmtl> complete o código ou recrie a página.)

    11 - Não faça nenhum comentário antes ou depois do código. Não explique nada. Apenas gere e retorne o código.
"""


    
    message = [
     {
    "role": "system",
    "content": system
    },
    {
    "role": "user",
    "content": f"Crie um site que atenda aos seguintes requisitos:\n {requirements} \n {instruction} \n gere o código do site agora"},
    ]

    attempts = 0
    while attempts < 5:
        site = await llm_model_execute(llm=llm, message=message)
        siteOk = True  # Assume inicialmente que o site está ok
        print(site)
        # Verifica se o HTML está presente e se as tags estão balanceadas
        if "<html" not in site or site.count("<html") != site.count("</html"):
         
            siteOk = False
            print("nao é um código html válido")
        # Se o site estiver ok, retorna o site
        if siteOk:
            return site
        
        # Incrementa o contador de tentativas
        #print("site",site)
        attempts += 1
        time.sleep(5)  # Aguarda 5 segundos antes da próxima tentativa

    return None
        
    

#==============================
async def extract_object(data):
    # Encontra a primeira ocorrência de '{'
    start = data.find('{')
    if start == -1:
        return None  # Retorna None se não encontrar '{'

    # Contador para acompanhar os pares de chaves
    brace_count = 0
    for i in range(start, len(data)):
        if data[i] == '{':
            brace_count += 1
        elif data[i] == '}':
            brace_count -= 1

        # Quando brace_count volta a zero, encontramos o par correspondente
        if brace_count == 0:

             return  data[start:i+1]

    return None  # Retorna None se não encontrar um par de chaves correspondente

#====================================
async def site_save(data):
 
    site = {}
    site["USUARIO_ID"] = data["USUARIO_ID"]
    site["NOME"] = data["NOME"]
    site["AREA"] = data["AREA"]
    site["OBJETIVO"] = data["OBJETIVO"]   
    site["PUBLICO_ALVO"] = data["PUBLICO_ALVO"]
    site["DESIGN_ESTILO"] = data["DESIGN_ESTILO"]
    site["FUNCIONALIDADES"] = data["FUNCIONALIDADES"]
    site["DOMINIO_PADRAO"] = data["NOME"] + "." + data["DOMAIN"]   
    site["ID"] = data.get("ID","")
    
    if not site["ID"]:
         result = await mysql_add_data("GPTALK_01","SITE",site)
    else:
        result = await mysql_update_data("GPTALK_01","SITE",site)
   
    return result




#====================================
def extract_code_pages(html_completo):
    """
    Esta função é uma versão atualizada que recebe uma string contendo páginas HTML envoltas em tags personalizadas
    <paginaXX>, onde XX pode ser qualquer número identificando a página. Ela extrai o código HTML de cada página, 
    removendo as tags personalizadas, e retorna um array onde cada elemento é o código de uma das páginas, independentemente
    da quantidade de páginas.
    """
    import re
    
    # Atualizando o padrão para capturar o conteúdo interno de qualquer tag <paginaXX>
    padrao = r'<pagina\d+>((.|\n)*?)<\/pagina\d+>'
    
    # Encontrando todos os matches e retornando apenas o conteúdo interno das tags capturadas
    codigos = re.findall(padrao, html_completo)
    
    # Extraindo apenas o conteúdo necessário da lista de tuplas retornada pela função findall
    codigos = [codigo[0] for codigo in codigos]
    
    return codigos

# Para testar a função com flexibilidade no número de páginas, vamos criar uma string de teste com páginas adicionais
html_paginas_variadas = """
<pagina01>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Página 1</h1>
</body>
</html>
</pagina01>
<pagina02>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Página 2</h1>
</body>
</html>
</pagina02>
<pagina03>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Página 3</h1>
</body>
</html>
</pagina03>
"""

# Testando a função atualizada
code_pages = extract_code_pages(html_paginas_variadas)


#===================
site_create_map = {
    "name":"site_create",
    "description":
        "Use esta função para criar o site solicitado pelo usuário. Passe todas as informações informadas pelo usuário. Será retornado o link de acesso ao site.",
    "parameters" : {
        "type": "object",
        "properties": {
            "NOME": {
                "type": "string",
                "description" : "nome do site"
                 },
            "AREA": {
                "type": "string",
                "description" : "área de atuação da empresa ou profissional"
                 },
           "OBJETIVO": {
                "type": "string",
                "description" : "objetivo do site"
                 },
           "PUBLICO_ALVO": {
                "type": "string",
                "description" : "Público alvo do site"
                 },
            "DESIGN_ESTILO": {
                "type": "string",
                "description" : """preferências em termos de design, incluindo exemplos de sites que você gosta, paletas de cores desejadas, 
                e qualquer elemento visual específico que deseja incluir"""
                 },
            "FUNCIONALIDADES": {
                "type": "string",
                "description" : """
                Detalhes sobre qualquer funcionalidade especial que o site deve ter, como formulários de contato, galerias de imagens, integração com redes sociais, funcionalidades de e-commerce, chat ao vivo, entre outros.
                """
            },
            "SEO_MARKETING": {
                "type": "string",
                "description" : """
                Estratégias específicas de SEO ou marketing digital que gostaria de implementar, como palavras-chave alvo, integração com Google Analytics, campanhas de PPC, etc. Opcional
                """
            },    
            "OBSERVACAO": {
                "type": "string",
                "description" : "Observação adicional que o usuário queira acrescentar para o desenvolvimento do site. Opcional."
                 },
            },
          "required": ["NOME","AREA","OBJETIVO","PUBLICO_ALVO","DESIGN_ESTILO","FUNCIONALIDADES"],
       }

}   



         
#===================
async def site_update(data):
    system = """
  Haja como um programador web senior, que ja atua hÃ¡ 20 anos como programador  html, css e javascript , 
  e que consegue criar sites ou fazer atualziações , conforme as solicitações dos seus clientes.
  """

    requirements = data["requirements"]

    instruction = """

    FAça a alteração no código informado, fazendo estritamente e somente o que foi solicitado, e me retorne o codigo alterado dentro da tag
    com o nome do arquivo de origem e que ja vem no codigo forencido. Exemplo:
    
    REGRAS IMPORTANTES PARA SE FAZER ALTERAÇÕES:
    Se for informado o ID de elmento, a modificação deve ser feita única e exclusivamente no elemento que possui exatamente este id.
    Exemplo:bt1
    Elemento id: bt1
    mudar a cor de fundo para verde
    será feita a alteraçao no elmento que possui o id bt1
    <button id='bt1' style="background:green">clique aqui </button>
    IMPORTANTE: o id precisa ser EXATAMENTE IGUAL. (case insensitive)
    Exemplos:
    botao é diferente de botao1 .  botao é igual a botao. 

    
    REGRAS IMPORTANTES AO SE CRIAR NOVOS ELMENTOS SOLICITADOS
    1 - Sempre de um ID para este novo elemento.  Este id deve ser único e exclusivo, ou seja, nenhum outro elemento , mesmo em outras págians do site, pode ter o mesmo id.
    2 - Todo elemento deve ter uma classe cujo nome é o prórpio tipo de elemento. 
    
    O exemplo abaixo mostra a aplicação de todas estas regras acima citadas:
    <button id="bt123446" class= "button"> Saiba |Mais </button>    


    
    o codigo esta dentro de um dicionario, deve ser alterado e retornado dentro da mesma estrutura. Nao colocar marcação json (```json) ou html (```html).
    
    """

    messages = [
     {
    "role": "system",
    "content": system
    },
    {
    "role": "user",
    "content": requirements + instruction
    },
     ]
    config = {}
    config["model"] = data["llm_model"]
    result = await llm_model_execute(messages, config)
    return result                

#======================
async def main():
 
  data = {}
  data["ID"] = 37
  data["USUARIO_ID"] = "ei5Vm2BLTLPcgzK6tGnf7aP2a1I3"
  data["NOME"]=  "Teste16"
  #data["SUBDOMAIN"]=  "advogadovirtual"
  data["OBJETIVO"] = "Venda de serviço de advocacia"
  data["AREA"] = "advocacia"
  data["PUBLICO_ALVO"] = "Pessoas que precisam de advogados"
  data["DESIGN_ESTILO"]= "Fundo nas cores vermelho e verde. Texto branco"
  data["FUNCIONALIDADES"] = "Formulário de cadastro para contato. Formulario de news Letter. Links para redes sociais Twitter, Linkedin e Facebook , com icones."
  data["TATICA"] = ""
  data["LLM"] = "gemini-1.5-pro-latest"
  
  site1 = await site_create(data)
  if site1:
      print("1 - criaçaõ do site, ok (site1)")
     
      site2 = await extract_object(site1)
    
      if site2:
            print("2 - Trasformaçao em objeto ok - (site2)")
            
            site3 = add_unique_id_to_all_elements(site2)
            
            if site3:
                print("3 - Adicionado IDs únicos para todos os objetos- (site2)")
          
      print("site2",site2)


    

asyncio.run(main())



