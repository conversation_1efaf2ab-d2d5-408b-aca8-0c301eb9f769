#!/usr/bin/env python3
"""
TESTE 3: Verificar se o webhook está recebendo mensagens
"""
import requests
import time
import json
from datetime import datetime

print('🔍 TESTE 3: Verificação do webhook de recebimento')
print('=' * 60)
print('📱 INSTRUÇÕES:')
print('1. Envie uma mensagem de teste para: +55 31 8419-8720')
print('2. Mensagem sugerida: "TESTE WEBHOOK MEGAAPI"')
print('3. O webhook deve capturar a mensagem em: /api/agent/megaapi/webhook')
print('=' * 60)

# Verificar se o servidor está rodando
print('🔍 Verificando se o servidor está ativo...')

try:
    # Testa se o servidor local está rodando
    response = requests.get('http://localhost:8000/api/agent/megaapi/webhook', timeout=5)
    print(f'✅ Servidor local ativo - Status: {response.status_code}')
    print(f'Response: {response.text}')
except requests.exceptions.ConnectionError:
    print('❌ Servidor local não está rodando')
    print('💡 Execute: uvicorn api.main:app --reload --port 8000')
    exit(1)
except Exception as e:
    print(f'❌ Erro ao conectar: {e}')
    exit(1)

print('-' * 60)
print('🌐 Verificando webhook via ngrok...')

try:
    # Testa o webhook via ngrok
    webhook_url = 'https://41b2-177-212-26-162.ngrok-free.app/api/agent/megaapi/webhook'
    response = requests.get(webhook_url, timeout=10)
    print(f'✅ Webhook ngrok ativo - Status: {response.status_code}')
    print(f'Response: {response.text}')
except Exception as e:
    print(f'❌ Erro no webhook ngrok: {e}')

print('-' * 60)
print('📨 AGUARDANDO MENSAGEM DE TESTE...')
print('Envie agora uma mensagem para +55 31 8419-8720')
print('Pressione Ctrl+C para parar o monitoramento')
print('-' * 60)

# Simular monitoramento (na prática, as mensagens aparecerão nos logs do servidor)
try:
    for i in range(60):  # Monitora por 60 segundos
        print(f'⏰ Aguardando... {i+1}/60 segundos', end='\r')
        time.sleep(1)
        
        # Aqui normalmente verificaríamos logs ou banco de dados
        # Por enquanto, apenas mostra que está monitorando
        
except KeyboardInterrupt:
    print('\n🛑 Monitoramento interrompido pelo usuário')

print('\n' + '=' * 60)
print('📋 RESULTADO DO TESTE 3:')
print('✅ SUCESSO se você viu logs no terminal do servidor')
print('❌ FALHA se não houve resposta do webhook')
print('=' * 60)
print('💡 Para ver os logs em tempo real, execute em outro terminal:')
print('   uvicorn api.main:app --reload --port 8000')
print('   E observe as mensagens chegando!') 