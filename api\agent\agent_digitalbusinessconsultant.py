from .agent_llm import LLM
from .agent_converter import Converter
from .agent_task import Task
import  json



class DigitalBusinessConsultant:
    def __init__(self,**data):
        self.name = 'Paulo'
        self.llm = LLM()
        self.converter = Converter()
        self.model = data.get("model",None)
        self.user = data.get("user",None)
        self.system =  """
"""

    async def  consultation_site_requirements(self,NOME=None,AREA=None,OBJETIVO=None,PUBLICO_ALVO=None,DESIGN_ESTILO=None,FUNCIONALIDADES=None,SEO_MARKETING=None,INFORMACOES_CADASTRAIS=None,OBSERVACAO=None,model=None):
        print("========== consultation_site_equerimentos() ==========")
        #print("request",request)
        #print(request['NOME'])
        #print(request['AREA'])
        system = """
           Você é um experiente consultor de negócios digitais, com profundo conhecimento de marketing digital e vendas online.
    É capaz de ajudar seus clientes a colocarem seus negócios online, através de sites. Você atua como um intermediário entre seus clientes e um webdesigner.
    Você ajuda seus clientes a especificar de forma clara, o que precisa ter em seus sites para que possam atender ao objetivo do site, seja institucional, vendas, marketing ou outro.
    Seu objetivo é eteminar recursos, funcionalidades e qualidade de um site. somente elementos e recursos on page (dentro ddo site).               '
    """
        tarefa = f"""
      Especificar requisitos a serem informados ao webdesigner para o site do cliente
      Area de negócio do cliente: {AREA}
      Objetivo do site: {OBJETIVO}
      Idioma do site: Português
      publico alvo: {PUBLICO_ALVO}

      As informações abaixo são pedidos do cliente .Leve elas em consideração quando elaborar  próprias observações e requisitos, caso ele tenha feito solicitações.
      design e estilo: {DESIGN_ESTILO}
      funcionalidades: {FUNCIONALIDADES}

      Informações Cadastrais:
      {INFORMACOES_CADASTRAIS}

      O retorno deverá ser em forma de um projeto com tarefas, onde cada página é uma tarefa,, e o que fazer na página para atender ao objetivo do site será incluido com checklist.
      As tarefas e os itens do checklist deverão ser numerados sequencialmente. Os itens do checklist podem ter subitens, que tb deverão ser numerados usando como prefixo o numero do item superior ao qual pertencem.
      As tarefas com seus respectivos subitens serão formatadas num array de objetos javascript. Cada objeto corresponde a uma tarefa, e deverá ter as seguintes chaves:
      DESCRICAO: nome da pagina seguido de .html. exemplos: index.html (home ou primeira pagina), contato.html (pagina de contato), sobrenos.html , produtos.html (pagina dos produtos)
      CHECKLIST: lista do que fazer em cada página.
  
      
      Segue um exemplo:

[
    {{
        'DESCRICAO': 'index.html',
        'CHECKLIST': '''
1 Banner principal com imagem atrativa e texto de oferta ou destaque.
2 Seção de categorias de produtos (ex: cães, gatos, pássaros, etc.).
3 Produtos em destaque com imagem, nome, breve descrição e preço.
4 Botão de 'Comprar' ou 'Saiba Mais' para cada produto.
5 Seção 'Sobre Nós' com breve descrição da loja e sua missão.
6 Depoimentos de clientes satisfeitos. 
7 Logo da empresa no cabeçalho. 
8 - CNPJJ e endereço no rodapé.
''',
    }},
    {{
        'DESCRICAO': '2 - Página de Produtos',
        'CHECKLIST': '''
1 Listagem de produtos por categoria.
2 Filtros de busca por preço, marca, etc.
3 Página individual de produto com:
  3.1 Imagens do produto em diferentes ângulos.
  3.2 Descrição detalhada do produto.
  3.3 Preço e opções de pagamento/parcelamento.
  3.4 Botão de 'Adicionar ao Carrinho'.
  3.5 Produtos relacionados.''',
    }}
]

Inclua também tarefas , se necessário, que afetem o site globalmente, ou todas as páginas, como cores, fontes e outras que serão definidas num arquivo global de css e estilo.

Inclua os elementos minimos necessários e comuns  sites do mesmo tipo de empresa e objetivo o site. Por exemplo, praticamente todo site comercial tem a logo no cabeçalho.

Retorne somente o array de objetos, sem comentários adicionais antes ou depois.
      
      O objetivo é que o webdesigner tenha um guia claro e objetivo que irá seguir, passo a passso, até a conclusão do site perfeito.
   """
        message = [{"role":"user","content":system + tarefa}]
        result = await self.llm.run_model(model=model,message=message)
        #print("result", result)
        tarefas = await self.converter.extract_array(result)
        print('tarefas',tarefas)
        return {"STATUS":"R","RESULT":json.dumps(tarefas)}
