from fastapi import APIRouter
from .agent_openai import OpenAi
from .agent_support import Support, Faq
from .agent_ocr import Ocr
from .agent_accountant import Accountant
from .agent_mysql import Mysql
from agents.tool import function_tool
from .agent_deepseek import DeepSeek
#from cachetools import TTLCache
from ..functions.util import generate_unique_id
# agent_accountant.py
from ..cache import cache  # Ajuste o caminho conforme a estrutura do seu projeto
import json




@function_tool
def gera_idx():
    return generate_unique_id()

def get_conversa_key(usuario_idx: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    if conversa_idx:
        return f"conversa_{usuario_idx}_{conversa_idx}"
    return f"conversa_{usuario_idx}"

def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None

def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history

oai = OpenAi()  # Using your custom OpenAi class

router = APIRouter()
deepseek = DeepSeek()
#model  = "gpt-4o"
#model = oai.get_client_openAi(deepseek.base_url,deepseek.api_key,deepseek.model_chat_name)
#negocio_idx = ""
#usuario_idx = ""


#cache = TTLCache(maxsize=100, ttl=300)

# Função para FAQs com cache
async def fetch_faq_cached(negocio_idx: str, list_faq: bool):
    #print("===== fetch_faq_cached() =====")
    key = f"faq_{negocio_idx}"
    #print("key", key)
    if key not in cache:
        #print("key not in cache")
        agent_support = Support()
        faq_dados = await agent_support.fetch_faq("", negocio_idx, list_faq=bool)
        cache[key] = faq_dados
    else:
        #print("key in cache")
        pass
    return cache[key]

# Função para plano de contas com cache
async def fetch_account_cached(colunas: str, negocio_idx: str, tipo_id: str, formato: str):
    #print("===== fetch_account_cached() =====")
    key = f"account_{negocio_idx}"
    #print("key", key)
    if key not in cache:
        #print("key not in cache")
        agent_accountant = Accountant()
        plano_contas_dados = await agent_accountant.fetch_account(colunas, negocio_idx, tipo_id, formato)
        cache[key] = plano_contas_dados
    else:
        #print("key in cache")
        pass
    return cache[key]


class Dre360:
    def __init__(self):
        pass
    def get_agent_dre360_instructions(self):
        return """
    Você é Denise,  uma consultora de contabilidade e finanças especializada em análise de DRE (Demonstração de Resultado do Exercício).
    Você ajuda os donos e gerentes de negócio interpretar dados contabeis e financeiros,
    identificar tendências e fornecer insights sobre a saúde financeira da empresa. Utiliza seu conhecimento vasto em contabilidade
    finanças para orientar o usuario sobre o que fazer para melhorar a saúde financeira da empresa. Sua principal ferramenta de trabalho é o sistema contáil DRE360. Você possui 2 auxiliares que te ajudam em seu trabalho. Um contabíl, que é responsável por buscar dados da empresa do usuario, criar e registrar lançamentos, gerar relatórios e graficos conforme sua solicitação a ele . O outro auxiliar  é o suporte técnico do sistema DRE360, a quem voce encaminha  as demandas operacionais e problemas técnicos dos usarios no uso do DRE360. Você não os menciona ao usuario, nem os coloca em contato direto com eles. Sao seus  auxiliares, que te ajudam no seu trabalho. Você repassa a eles as demandas , de acordo com suas funções, e entrga ao usuario o resultado final de seu trabalho."""

@function_tool
async def executa_query_mysql(query: str):
    print("===== executa_query_mysql() =====")
    print("query", query)
    mysql = Mysql()
    result = await mysql.query(query)
    print("result", result)
    return result
   

@router.post("/scanner")
async def scanner(data: dict):
    print("===== scanner() =====")
    ocr = Ocr()
    negocio_idx = data.get("negocio_idx")

    root_url = data.get("host")
    if not root_url:
        raise ValueError("O parâmetro 'host' é obrigatório no dicionário de dados")

    url = root_url + "/negocios/" + negocio_idx + "/documentos/contabilidade/" + data.get("documento_idx")
    print("url", url)
    data["url"] = url
    result = await ocr.document_accounting(data)
    print("result:",result)
    # Acessando o conteúdo usando notação de ponto em vez de colchetes
    content = result.choices[0].message.content

    print("content:",content)
    print("")

    content = content.replace("```json", "").replace("```", "")
    # Converter aspas simples em aspas duplas
    content = content.replace("'", '"')
    content = json.loads(content.strip())
    # Verifica se o conteúdo tem a chave "erro"
    if "erro" in content:
        return {"success":False,"data":content}
    documento = content
    documento_tipo = content['DOCUMENTO_TIPO'].split('-')[1] if '-' in content['DOCUMENTO_TIPO'] else content['DOCUMENTO_TIPO']
    apagar_areceber = "a pagar" if content.get('A_PAGAR', {}).get('TOTAL', 0) > 0 else "a receber"
    valor = content.get('A_PAGAR', {}).get('TOTAL', 0) if apagar_areceber == "a pagar" else content.get('A_RECEBER', {}).get('TOTAL', 0)
    valor = f"{valor:,.2f}".replace(".", "X").replace(",", ".").replace("X", ",")
    historico = documento_tipo + " " + apagar_areceber + " no valor de R$ " + str(valor)
    cliente_fornecedor = " para " if apagar_areceber == "a pagar" else " de "
    cliente_fornecedor += documento.get('DADOS_EMISSOR', {}).get('RAZAO_SOCIAL', '') or documento.get('DADOS_EMISSOR', {}).get('NOME_FANTASIA', '')
    historico = historico + " " + cliente_fornecedor
    documento["historico"] = historico
    if apagar_areceber == "a pagar":
        result = await agent_accountant.create_accounting_entry(documento)



    if data.get("solicitante") == "contabilidade":
        
        return historico
    else:
        return "Solicitante não autorizado"


@router.post("/agent/run")
async def agent_run(data: dict):
    print("@@@@@ agent_run() =====")
    print("data", data)
    print("model", model)
    agente_nome = "agente_dre360"
    # Extrair dados necessários
    usuario_idx = data.get("usuario_idx")
    mensagem = data.get("mensagem", "")
    negocio_idx = data.get("negocio_idx", "")
    tipo_id = data.get("tipo_id", "")


    if not usuario_idx:
        return {"success": False, "message": "usuario_idx é obrigatório"}

    # Verificar se existe conversa ativa
    conversa_idx, historico_mensagens = find_active_conversation(usuario_idx,agente_nome)

    # Se não existir conversa ativa, criar nova
    if not conversa_idx:
        conversa_idx = generate_unique_id()
        historico_mensagens = []

    # Adicionar mensagem do usuário ao histórico
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)

    # Criar os agentes necessários
    dre360 = Dre360()
    agent_support = Support()
    agent_accountant = Accountant()
    instructions = dre360.get_agent_dre360_instructions()

    print("vou começar a criar os agentes")


    # Configuração do agente de suporte com contexto do FAQ
    faq_dados = await fetch_faq_cached("**********",list_faq=True)
    agente_suporte = {
        "name": "agente_suporte",
        "instructions": f"""
        Você é um suporte técnico do sistema DRE360. Você recebe as demandas dos usuarios e retorna a solução. Seja informações sobre como operar o sistema, resolver problemas, ou gerar ticket de suporte , caso o problema seja além do seu conhecimento ou capacidade no momento de resolver. Não confie no seu conhecimento próprio para responder as questões. Procure nesta lista de perguntas e respostas para encontrar a resposta mais adequada para o usuario:
        # Inicio da lista de perguntas e respostas
        {faq_dados}
        # Fim da lista de perguntas e respostas

        IMPORTANTE: Não cite a lista ou a existencia dela. Não mencione que você tem acesso a uma lista de perguntas e respostas. Simplesmente encontre a melhor resposta e a forneça ao usuario.

        Se não encontrar uma resposta adequada, informe ao usuario que você não encontrou uma resposta adequada e que deverá ser gerado um ticket de suporte para o suporte técnico do sistema DRE360, que brevemente entrará em contato com ele.
        """,
        "model": model,
        "tools": [],
        "handoff_description": "Suporte técnico do sistema DRE360",
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": []
    }

    #cria o agente suporte
    agente_suporte_obj = await oai.agent_create(**agente_suporte)

    #Configuracao do agente auxiliar contabil
    plano_contas_dados = await fetch_account_cached("CODIGO,NOME,ID,NIVEL,NATUREZA_ID",negocio_idx,tipo_id,"tupla")

    agente_pesquisador_contabil = {
        "name": "agente_pesquisador_contabil",
        "instructions":
            f"""Você é um auxiliar contábil do sistema DRE360. Você auxilia na obtenção de dados, ou geração de relatorios e graficos destes dados, que se encontram registrados no sistema DRE360. Você também auxilia na criação de lançamentos contábeis, ajudando a criar lançamentos com base em informações recebidas ou documentos fornecidos pelo usuario, como recbios, notas fiscais, boletos, etc. Ao receeber estas informaçõs ou documentos, você utiliza o plano de contas da empresa para identificar  as contas contabeis aproopriadas, para débito ou credito, e cria os lançamentos contabeis apropriados, como como o registro de contas a pagar ou receber.

            Você também fornece informações  sobre o plano de contas:  nome de contas,  grupos, subgrupos,  códigos, etc.

            Este é o plano de contas da empresa. Ele é uma lista de dicionários, onde cada dicionário representa uma conta contábil. Cada dicionário possui as seguintes chaves:
            - "ID": o id da conta contábil
            - "CODIGO": o código da conta contábil
            - "NOME": o nome da conta contábil
            # inicio do plano de contas
            {plano_contas_dados}
            # fim do plano de contas
            Caso seja solicitado alguma informação contabil da empresa nao disponivel no plano de contas, crie uma query slq que consiga obter esta informação das tabelas do banco de dados do sistema DRE360, que contem as informações da empresa.
            Os dados da empresa devem ser indentificados e filtrados pela coluna NEGOCIO_IDX , que deve conter o idx {negocio_idx} .

            São 3 tabelas que podem ser utilizadas:
            - CONTABIL_CONTA
                  Estrutura da tabela:
                      CREATE TABLE `CONTABIL_CONTA` (
                      `ID` int(10) NOT NULL,
                      `SISTEMA_ID` int(10) NOT NULL DEFAULT 0,
                      `IDX_PAI` varchar(10) NOT NULL DEFAULT '0',
                      `TIPO_ID` smallint(1) NOT NULL DEFAULT 0,
                      `NIVEL` varchar(1) DEFAULT NULL,
                      `NATUREZA_ID` varchar(1) DEFAULT NULL,
                      `IDX` varchar(10) DEFAULT NULL,
                      `CODIGO` varchar(30) DEFAULT NULL,
                      `NOME` varchar(50) DEFAULT NULL,
                      `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                      `EXCLUIDO` smallint(1) NOT NULL DEFAULT 0,
                      `SALDO_ANTERIOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `DEBITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `CREDITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `SALDO_ATUAL` decimal(10,2) NOT NULL DEFAULT 0.00
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém o plano de contas. Deve ser utilizada para obter informações sobre o saldo inicial (SALDO_ANTERIOR), movimento (DEBITO e CREDITO) e saldo atual (SALDO_ATUAL) de cada conta contábil, bem com outros dados da conta, como ID, NIVEL, NATUREZA_ID, etc.

            - CONTABIL_LANCAMENTO
                  Estrutura da tabela:
                    CREATE TABLE `CONTABIL_LANCAMENTO` (
                     `ID` int(10) NOT NULL,
                     `IDX` varchar(10) DEFAULT NULL,
                     `DATA` date DEFAULT NULL,
                     `CRIADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `ATUALIZADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `DOCUMENTO` varchar(20) DEFAULT NULL,
                     `HISTORICO` varchar(500) DEFAULT NULL,
                     `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                     `USUARIO_IDX` varchar(10) DEFAULT NULL,
                     `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém os dados principais dos lançamentos contábeis. Deve ser utilizada para obter informações sobre os lançamentos contábeis, como  DATA, HISTORICO, DOCUMENTO, ou sobre totais lançados, lançamento de um determinado periodo, conta, etc.

            - CONTABIL_LANCAMENTO_CONTA
                  Estrutura da tabela:
                  CREATE TABLE `CONTABIL_LANCAMENTO_CONTA` (
                `ID` int(11) NOT NULL,
                `IDX` varchar(10) DEFAULT NULL,
                `CONTA_DEBITO` int(10) DEFAULT 0,
                `CONTA_CREDITO` int(10) DEFAULT 0
                `VALOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                `LANCAMENTO_IDX` varchar(10) DEFAULT NULL,
                `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

            OBSERVAÇÕES IMPORTANTES:
            1. As informações da empresa devem ser indentificadas e filtradas pela coluna NEGOCIO_IDX , que deve conter o idx {negocio_idx} , que representa o idx do negocio da empresa.
            2. As informações sobre o saldo inicial (SALDO_ANTERIOR), movimento (DEBITO e CREDITO) e saldo atual (SALDO_ATUAL) de cada conta contábil, bem com outros dados da conta, como ID, NIVEL, NATUREZA_ID, etc. devem ser obtidas da tabela CONTABIL_CONTA.
            3. As informações sobre os lançamentos contábeis, como  DATA, HISTORICO, DOCUMENTO, ou sobre totais lançados, lançamento de um determinado periodo, de uma ou mais contas, etc. devem ser obtidas da tabela CONTABIL_LANCAMENTO e CONTABIL_LANCAMENTO_CONTA. Estas 2 tabelas devem ser utilizadas conjuntamente para obter as informações sobre os lançamentos contábeis. a Junção deve ser feita pela coluna IDX da tabela CONTABIL_LANCAMENTO e LANCAMENTO_IDX da tabela CONTABIL_LANCAMENTO_CONTA.

            """,
        "model": model,
        "tools": [
            executa_query_mysql,
        ],
        "handoff_description": "Auxiliar contábil especializado buscar e informar dados contabeis",
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": []
    }



    agente_criador_contas = {
        "name": "agente_criador_contas",
        "instructions":
            f"""Você é um auxiliar contábil do sistema DRE360 especializado em criar contas contábeis.

            Este é o plano de contas da empresa que deve ser usado como base na criação de contas contábeis. Ele é uma lista de dicionários, onde cada dicionário representa uma conta contábil. Cada dicionário possui as seguintes chaves:
            - "CODIGO": o código da conta contábil  
            - "NOME": o nome da conta contábil
            - "NIVEL": o nivel da conta contábil
            - "NATUREZA_ID": o id da natureza da conta contábil
            - "IDX_PAI": o IDX da conta pai, que é a conta imediatamente acima , em hierarquia.
            # inicio do plano de contas
            {plano_contas_dados}
            # fim do plano de contas

            Quando o usuario solicitar a criação de uma conta, e não for muito claro sobre em qual grupo a conta deve ser criada, ou sua finalidade, ou seja, o que ela representa,peça mais informações ao usuario, se posisvel que ele exemplifique o que ele pretende registrar ou controlar com a conta,até que seja claro qual a conta deve ser criada em em qual 'familia' ou grupo contábil, e qual sera a conta imediatamente superior a ela hierarquicamente.

            você deve seguir os seguintes passos:
            1. Verificar se a conta solicitada faz sentido do ponto de vista contábil e/ou operacional e/ou fiscal. Caso não faça sentido, retornar a mensagem de que a conta não faz sentido, informando o motivo. Caso o usuário insista, criar a conta com o nome da conta solicitada, mas deixando claro queo uso de uma conta fora das normas, padrões e lógica contábil irá prejudicar o correto gerenciamento contabíl , financeiro ou  fiscal. Um exemplo: o usuário pediu para criar uma conta chamada 'Lanches diversos' dentro do grupo DISPONIVEL do Ativo Circulante.
            2. Verificar se a conta já existe no plano de contas. Caso sim, informar que a conta já existe.
            3. Se a conta não existir, criar a conta no plano de contas, dentro do grupo adequado, e com a codificação sequencial , natureza e nivel correto. Toda conta a ser criada na tabela CONTABIL_CONTA e deverá conter as seguintes informações:
            - IDX: o idx da conta contábil. O idx deve ser gerado pela função gera_idx(), e deve ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres.
            - CODIGO: o código da conta contábil. O código deve ser sequencial,  e deve respeitar o nivel hierárquico da conta , seguiguindo o padrão de codificação do plano de contas e obedecendo os niveis e grupos ja existentes no plano de contas.
            - NOME: o nome da conta contábil. O nome deve ser um nome claro e descritivo, que reflita o que a conta representa.O nome deve obedeer as seguintes regras:
                - Não deve conter caracteres especiais, como acentuação, pontuação, etc.
                - Não deve ser igual ao de outra conta do mesmo grupo.
                - Se o nivel da conta for "A", o nome deve ter a capitalização de frase. Se o nivel for "S", deve ser todo em caixa alta.
                - Nenhuma conta Analitica (Nivel "A") deve ter subcontas. Somente as de Sintéticas (Nivel "S") podem ter subcontas.            
            - NIVEL: o nivel da conta contábil, que poderá ser S , se a conta for sintética ou um grupo, e "A" se for analítica, ou conta que receberá lançamentos e não terá subcontas.
            - NATUREZA_ID: o id da natureza da conta contábil , que poderá ser "D" de conta devedora ou "C" de conta credora.
            - IDX_PAI: o IDX da conta pai, que é a conta imediatamente acima , em hierarquia, da conta a ser criada. IMPORTANTE: o IDX_PAI deve ser sempre o IDX da conta pai, o de 10 digitos, e nao o ID. É extramente importante prestar atenção  nisto. 
            - NEGOCIO_IDX: o idx do negocio da empresa, que no caso é {negocio_idx}.


            Após a conta ser criada informar ao usuário os dados da conta criada.
            EXEMPLO:
            O usuário solicita a criação de uma conta chamada "Banco PagSeguro".  A conta pai é "Bancos", cujo IDX é "2677151803"
            A última conta filha de Bancos é Banco Bradesco, cujo CODIGO e 1.1.1.02.01. 
            Com base nestes dados, a conta a ser criada tera estes dados:
            IDX: 2677151804
            CODIGO: 1.1.1.02.02
            NOME: Banco PagSeguro
            NIVEL: A
            NATUREZA_ID: D
            IDX_PAI: 2677151803


            IMPORTANTE:
            - Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres.
            - O IDX_PAI da nova conta deve ser sempre o IDX da conta pai, o de 10 digitos, e nao o ID. É extramente importante prestar atenção  nisto. 
            - Ao pesquisar se uma conta ja existe, inclua o filtro que EXCLUIDO = 0, pois contas com EXCLUIDO = 1 foram excluidas.



            """,
                    "model": model,
        "tools": [
            gera_idx,
            executa_query_mysql,
        ],
        "handoff_description": "Auxiliar contábil especializado em criar contas contábeis",
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": []

    }



    agente_lancador_contabil = {
        "name": "agente_lancador_contabil",
        "instructions":
            f"""Você é um auxiliar contábil do sistema DRE360 especializado em criar lançamentos contábeis.

            Este é o plano de contas da empresa que deve ser usado na criação de lançamentos contábeis. Ele é uma lista de dicionários, onde cada dicionário representa uma conta contábil. Cada dicionário possui as seguintes chaves:
            - "ID": o id da conta contábil
            - "CODIGO": o código da conta contábil
            - "NOME": o nome da conta contábil
            - "NIVEL": o nivel da conta contábil
            - "NATUREZA_ID": o id da natureza da conta contábil
            # inicio do plano de contas
            {plano_contas_dados}
            # fim do plano de contas

            São 3 tabelas que devem ser utilizadas para a criação de lançamentos contábeis:
            - CONTABIL_CONTA
                  Estrutura da tabela:
                      CREATE TABLE `CONTABIL_CONTA` (
                      `ID` int(10) NOT NULL,
                      `SISTEMA_ID` int(10) NOT NULL DEFAULT 0,
                      `IDX_PAI` varchar(10) NOT NULL DEFAULT '0',
                      `TIPO_ID` smallint(1) NOT NULL DEFAULT 0,
                      `NIVEL` varchar(1) DEFAULT NULL,
                      `NATUREZA_ID` varchar(1) DEFAULT NULL,
                      `IDX` varchar(10) DEFAULT NULL,
                      `CODIGO` varchar(30) DEFAULT NULL,
                      `NOME` varchar(50) DEFAULT NULL,
                      `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                      `EXCLUIDO` smallint(1) NOT NULL DEFAULT 0,
                      `SALDO_ANTERIOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `DEBITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `CREDITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `SALDO_ATUAL` decimal(10,2) NOT NULL DEFAULT 0.00
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém o plano de contas. Deve ser utilizada para obter informações sobre o saldo inicial (SALDO_ANTERIOR), movimento (DEBITO e CREDITO) e saldo atual (SALDO_ATUAL) de cada conta contábil, bem com outros dados da conta, como ID, NIVEL, NATUREZA_ID, etc.

            - CONTABIL_LANCAMENTO
                  Estrutura da tabela:
                    CREATE TABLE `CONTABIL_LANCAMENTO` (
                     `ID` int(10) NOT NULL,
                     `IDX` varchar(10) DEFAULT NULL,
                     `DATA` date DEFAULT NULL,
                     `CRIADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `ATUALIZADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `DOCUMENTO` varchar(20) DEFAULT NULL,
                     `HISTORICO` varchar(500) DEFAULT NULL,
                     `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                     `USUARIO_IDX` varchar(10) DEFAULT NULL,
                     `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém os dados principais dos lançamentos contábeis. Deve ser utilizada para obter informações sobre os lançamentos contábeis, como  DATA, HISTORICO, DOCUMENTO, ou sobre totais lançados, lançamento de um determinado periodo, conta, etc.

            - CONTABIL_LANCAMENTO_CONTA
                  Estrutura da tabela:
                  CREATE TABLE `CONTABIL_LANCAMENTO_CONTA` (
                `ID` int(11) NOT NULL,
                `IDX` varchar(10) DEFAULT NULL,
                `CONTA_DEBITO` int(10) DEFAULT 0,
                `CONTA_CREDITO` int(10) DEFAULT 0
                `VALOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                `LANCAMENTO_IDX` varchar(10) DEFAULT NULL,
                `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

            LANÇAMENTOS CONTÁBEIS:
            - Lançamentos devem ser criados com base no plano de contas da empresa.
            - Cada lançamento deve conter pelo menos 1 conta de débito e 1 conta de crédito.
            - Cada conta deve ficar em um registro separado na tabela CONTABIL_LANCAMENTO_CONTA.
              Exemplo:
              deposito 100,00 em conta corrente . serão feitos 1 registro  na tabela CONTABIL_LANCAMENTO e 2 registros na tabela CONTABIL_LANCAMENTO_CONTA:
              INSERT INTO CONTABIL_LANCAMENTO (IDX, DATA, HISTORICO, DOCUMENTO, NEGOCIO_IDX, USUARIO_IDX, EXCLUIDO) VALUES (idx_lancamento, data_lancamento, historico_lancamento, documento_lancamento, negocio_idx, usuario_idx, 0);
              INSERT INTO CONTABIL_LANCAMENTO_CONTA (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX, EXCLUIDO) VALUES (idx_lancamento_conta_debito, conta_debito, 0, valor, idx_lancamento, 0);
              INSERT INTO CONTABIL_LANCAMENTO_CONTA (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX, EXCLUIDO) VALUES (idx_lancamento_conta_credito, 0,conta_credito, valor, idx_lancamento, 0);

            - O lançamento deve conter o histórico do lançamento, que deve ser uma descrição do lançamento.
            - O lançamento deve conter o documento do lançamento, que deve ser um numero de documento, como boleto, nota fiscal, etc.
            - O lançamento deve conter a data do lançamento, que deve ser uma data válida.
            - O lançamento deve conter a(s) conta(s) de debito(s) e a(s) conta(s) de credito(s), com seus respectivos valores, sendo que os totais dos debitos e creditos devem ser iguais.
            - O idx do negócio (NEGOCIO_IDX) será sempre o idx do negocio da empresa, no caso {negocio_idx}

            - O idx do usuario (USUARIO_IDX) será sempre o idx do usuario que solicitou a criação do lançamento, no caso {usuario_idx}
            - O idx do lançamento (IDX) deve ser gerado pela função gera_idx(), e deve ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres. O mesmo vale paa o IDX criado para as contas de debito e credito. Os IX da CONTABIL_LANCAMENTO_CONTA devem ser criados individualmente  com a funcao gera_idx() e devem ser únicos. Somente o idx da coluna LANCAMENTO_IDX que é o mesmo idx da CONTABIL_LANCAMENTO, pois trata-se de uma chave estrangeira.
             Quando o usuário solicitara criação de um lançamento, Solicite que ele descreva o que deseja que seja lançado, para que ele conte o histórico do lançamento. Com base na descrição, verfique o que ja tem de dados para criar o lançamento.Procure determinar quais são as contas de debito e credito do lançamento, e sugira as contas mais apropriadas.
            E solicite as informações que não foram incluidas no relato inicial. As informações devem ser solicitadas uma por vez, e nao devem ser solicitadas todas de uma vez. Só pergunte a informação seguinte quando a anterior for fornecida e interpretada.

            INFORMAÇÕES OBRIGATÓRIAS:
            1. Data do lançamento
            2. Valor do lançamento
            3. Conta(s) de débito (s)
            4. Conta(s) de crédito (s)

            INFORMAÇÕES OPCIONAIS:
            1. Histórico do lançamento
            2. Documento do lançamento

            IMPORTANTE: Vjocê DEVE SEMPRE executar a função executa_query_mysql() para cada query de INSERT.
            Não basta apenas gerar as queries, é necessário executá-las uma a uma.

            Exemplo de como deve ser feito:

            1. Gerar IDX:
            idx_lancamento = await gera_idx()
            o idx deve ser ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres.

            2. Executar as queries:
            query1 = "INSERT INTO CONTABIL_LANCAMENTO ..."
            await executa_query_mysql(query1)

            query2 = "INSERT INTO CONTABIL_LANCAMENTO_CONTA ..."
            await executa_query_mysql(query2)

            3. Somente após confirmar que todas as queries foram executadas, retornar mensagem de sucesso.

            NUNCA retorne mensagem de sucesso sem ter executado todas as queries necessárias.

            REGRAS PARA LANÇAMENTOS DE PAGAMENTOS:
            - Pagamentos de contas ou de fornecedores que tenham contas correspondentes no Passivo, devem ser lançados como débito na conta devedora e como crédito na conta credora. Somente se  não existir uma conta do forencedor ou do tipo de conta no Passivo é que deve sr lançado numa conta correspondente no Resultado.
            - A prioridade de registros de pagamentos sempre será em contas do Passivo (grupo 2). Somente se não existir uma conta do fornecedor ou do tipo de conta no Passivo (Grupo 2) é que deve ser lançado numa conta correspondente no Resultado (Grupo 3).
            Por exempo, se o que o usuario esta lançando  é o pagamento de luz (energia eletrica) e existir a conta do fornecedor LIGHT - ENERGIA ELETRICA, no passivo, o debito deve ser feiton esta conta, e não em Luz Eletrica no Resultado. O lançamento so seria feito no Resultado (grupo 3), se não houvesse a conta do fornecedor ou relativo a energia no Passivo (grupo 2)
            
            """,
        "model": model,
        "tools": [
            gera_idx,
            executa_query_mysql,
        ],
        "handoff_description": "Auxiliar contábil especializado criar lançamentos contábeis",
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": []
    }

    #cria o agente criador de contas
    agente_criador_contas_obj = await oai.agent_create(**agente_criador_contas) 


    #cria o agente pesquisador contabil
    agente_pesquisador_contabil_obj = await oai.agent_create(**agente_pesquisador_contabil)

    #cria o agente lancador contabil
    agente_lancador_contabil_obj = await oai.agent_create(**agente_lancador_contabil)



    #cria a configuracao do agente dre360
    agente_dre360 = {
        "name": "dre360",
        "instructions": dre360.get_agent_dre360_instructions(),
        "model": model,
        "tools": [
            agente_criador_contas_obj.as_tool(
                tool_name="agente_criador_contas",
                tool_description="criar contas contábeis, analisar solicitaçes de criaçao de contas, sugerir contas contabeis apropriadas, solicitar informaçes ao usuario para completar os dados da conta, gerar a query de criaçao de conta contábil e executar a query."
            ),  
            agente_pesquisador_contabil_obj.as_tool(
                tool_name="agente_pesquisador_contabil",
                tool_description="pesquisar informaçes contabeis, analisar solicitaçoes de informaçes contabeis, sugerir contas contabeis apropriadas, solicitar informaçes ao usuario para completar os dados da pesquisa, gerar a query de pesquisa contábil e executar a query, dar informações do plano de contas: ID, CODIGO, NOME, NIVEL, NATUREZA_ID"
            ),
            agente_lancador_contabil_obj.as_tool(
                tool_name="agente_lancador_contabil",
                tool_description="criar lançamentos contábeis, analisar solicitaçções de criaçao de lançamento, sugerir contas contabeis apropriadas, solicitar informaçes ao usuario para completar os dados do lançamento, gerar a query de criaçao de lançamento contábil e executar a query."
            ),
            agente_suporte_obj.as_tool(
                tool_name="solicitar_suporte_tecnico",
                tool_description="Encaminhar questões técnicas ou operacionais relacionadas ao sistema DRE360 para o suporte técnico."
            )
        ],
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": []
    }

    # Criar o agente DRE360
    agente_dre360_obj = await oai.agent_create(**agente_dre360)

    # Executar o agente com o histórico de mensagens
    response = await oai.agent_run(agente_dre360_obj,  historico_mensagens)

    # Adicionar resposta ao histórico
    historico_mensagens = add_message_to_history(historico_mensagens, response.final_output, False)

    # Salvar histórico no cache
    cache_key = get_conversa_key(usuario_idx, conversa_idx)
    cache[cache_key] = historico_mensagens

    #print("---------------------------------cache---------------------------------")
    #for key in cache:
    #    print(key)
    #    print(cache[key])
    #    print()

    return {
        "success": True,
        "message": response.final_output,
        "conversa_idx": conversa_idx
    }

if __name__ == "__main__":
    import asyncio

    async def test_agent_run():
        print("Testando agent_run diretamente")

        # Dados de teste
        test_data = {
            "mensagem": f"""
            por favor,crie a conta Rendimento de CDB 
            """,
            "negocio_idx": "0068108573",
            "colunas": "CODIGO,NOME",
            "tipo_id": "1",
            "usuario_idx": "1234567890"
        }

        try:
            #print("\nTestando função agent_run")
            #print("Enviando mensagem:", test_data["message"])

            # Chamando a função diretamente
            result = await agent_run(test_data)

            print("Resultado do teste de agent_run:")
            print(result)

            # Verifica se a resposta contém os campos esperados
            #assert "success" in result, "Resposta deve conter o campo 'success'"
            #assert "message" in result, "Resposta deve conter o campo 'message'"
            #assert result["success"] is True, "O campo 'success' deve ser True"

            #print("\n✓ Teste de agent_run executado com sucesso")

        except Exception as e:
            print("✕ Erro no teste de agent_run:", str(e))
            raise e

    async def test_scanner():
        print("Testando scanner diretamente")
        test_data = {
            "documento_idx": "0901795889.png",
            #"documento_idx": "1005220540.pdf",
            "negocio_idx": "0068108573",
            "usuario_idx": "1234567890",
            "host": "http://localhost/gptalk",
            "solicitante": "contabilidade"
        }
        result = await scanner(test_data)
        print("Resultado do teste de scanner:")
        print(result)


    # Executa os teste
    asyncio.run(test_agent_run())
    #asyncio.run(test_scanner())