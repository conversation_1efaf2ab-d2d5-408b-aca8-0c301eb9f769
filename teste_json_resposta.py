#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

# Importar as funções necessárias
from api.agent.agent_assistenciamk import process_agent_stream

def testar_deteccao_json():
    """Testa a detecção de JSON vs HTML"""
    
    # Teste 1: Resposta JSON estruturada
    json_response = """{
    "tipo": "individual",
    "mensagem": "Aqui estão nossos batons disponíveis:",
    "produtos_individuais": [
        {
            "nome": "Batom Mate Ruby Red",
            "preco": 29.90,
            "categoria": "Batom",
            "descricao": "Batom mate de longa duração na cor vermelho intenso"
        }
    ]
}"""
    
    # Teste 2: Resposta HTML
    html_response = """<div style="border: 1px solid #ccc; border-radius: 10px; padding: 15px; margin: 10px 0; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <div style="font-size: 20px; margin-right: 10px;">💄</div>
        <div>
            <div style="font-weight: bold; color: #2c3e50; font-size: 16px;">Batom Mate Ruby Red</div>
            <div style="color: #e74c3c; font-weight: bold; font-size: 18px;">R$ 29,90</div>
        </div>
    </div>
</div>"""

    print("=== TESTE DE DETECÇÃO JSON vs HTML ===\n")
    
    print("🧪 Teste 1: Resposta JSON estruturada")
    print(f"Conteúdo: {json_response[:100]}...")
    try:
        # A função process_agent_stream tem parâmetros específicos, vou testar diretamente a lógica
        import json
        json_buffer_clean = json_response.strip()
        
        # Testar se JSON é válido
        if json_buffer_clean.startswith('{') and json_buffer_clean.endswith('}'):
            try:
                resposta_json = json.loads(json_buffer_clean)
                print(f"✅ JSON válido: {list(resposta_json.keys())}")
                tem_tipo = '"tipo":' in json_buffer_clean
                print(f"✅ Tem campo 'tipo': {tem_tipo}")
                resultado1 = f"JSON detectado corretamente: {list(resposta_json.keys())}"
            except json.JSONDecodeError as e:
                resultado1 = f"Erro ao parsear JSON: {e}"
        else:
            resultado1 = "Não detectado como JSON"
        print(f"✅ Resultado 1: {type(resultado1)}")
        print(f"📋 Conteúdo resultado 1: {str(resultado1)[:200]}...\n")
    except Exception as e:
        print(f"❌ Erro no teste 1: {e}\n")
    
    print("🧪 Teste 2: Resposta HTML")
    print(f"Conteúdo: {html_response[:100]}...")
    try:
        # Testar detecção de HTML
        html_buffer_clean = html_response.strip()
        
        # Testar se é detectado como HTML de produtos
        if '<div style="border: 1px solid #ccc' in html_buffer_clean:
            resultado2 = "HTML de produtos detectado corretamente"
        elif html_buffer_clean.startswith('{') and html_buffer_clean.endswith('}'):
            resultado2 = "Incorretamente detectado como JSON"
        else:
            resultado2 = "Detectado como texto simples"
        print(f"✅ Resultado 2: {type(resultado2)}")
        print(f"📋 Conteúdo resultado 2: {str(resultado2)[:200]}...\n")
    except Exception as e:
        print(f"❌ Erro no teste 2: {e}\n")

if __name__ == "__main__":
    testar_deteccao_json() 