from agents.tool import function_tool  # ✅ CORRETO
from ...agent_neo4j import AgentNeo4j
from ...agent_logger import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional
from ...util.functions.gera_id_unico import gera_id_unico

neo4j = AgentNeo4j()
logger = AgentLogger()  # Adicionar os parênteses para instanciar a classe


@function_tool
async def cliente_adicionar(
    negocio_idx: str,
    usuario_idx: str,
    nome: str, 
    telefone: str, 
    email: Optional[str] = None,      # ✅ Opcional
    cep: Optional[str] = None,        # ✅ Opcional
    numero: Optional[str] = None,     # ✅ Opcional
    complemento: Optional[str] = None, # ✅ Opcional
    logradouro: Optional[str] = None,
    bairro: Optional[str] = None,
    cidade: Optional[str] = None,
    uf: Optional[str] = None,
    cpf_cnpj: Optional[str] = None,
    ):
    """
    Adiciona um novo cliente no banco de dados.

    nome: 
    -Nome completo do cliente.
    -Exemplo: <PERSON>
    -<PERSON>ório. A função não deve ser executada sem esta informação.

    telefone: 
    -Telefone do cliente com ddd.
    -Exemplo: 31984784825
    -Obrigatório. A função não deve ser executada sem esta informação.

    email: 
    -Email do cliente.
    -Exemplo: <EMAIL>
    -Opcional

    cep: 
    -CEP do endereço do cliente.
    -Exemplo: 31910720
    -Opcional
    🚨 Após o cliente informar o cep, carregue os dados do endereço usando a função busca_endereco_pelo_cep(), que fornecerá os dados para as variaveis logradouro, bairro, cidade e uf automaticamente.

    numero: 
    -Número do endereço do cliente.
    -Exemplo: 123
    -Opcional se o cep não for informado.

    complemento: 
    -Complemento do endereço do cliente.
    -Exemplo: apto 101
    -Opcional

    logradouro: 
    -Logradouro do endereço. Normalmente um nome de rua ou avenida.
    -Exemplo: Rua das Flores
    -Opcional se o cep não for informado.

    bairro: 
    -Bairro do endereço.
    -Exemplo: Centro
    -Opcional se o cep não for informado.

    cidade: 
    -Cidade do endereço.
    -Exemplo: São Paulo
    🚨 Os únicos dados obrigatórios são nome e telefone 

    uf: 
    -Unidade Federativa do endereço.
    -Exemplo: MG
    -Opcional se o cep não for informado.

    cpf_cnpj: 
    -CPF ou CNPJ do cliente.
    -Exemplo: 12345678900
    -Opcional.
    -Mensagem para o usuario: informe somente números

    """
    print("===== cliente_adicionar() =====")

    idx = gera_id_unico()

    # Validações obrigatórias
    if not nome or nome.strip() == "":
        return {"success": False, "message": "Nome é obrigatório."}
    
    if not telefone or telefone.strip() == "":
        return {"success": False, "message": "Telefone é obrigatório."}  # ✅ Sem espaços extras
    
    # Normalização de campos opcionais
    email = email.strip() if email and email.strip() else None
    cep = cep.strip() if cep and cep.strip() else None
    numero = numero.strip() if numero and numero.strip() else None
    complemento = complemento.strip() if complemento and complemento.strip() else None

    # Adicionar após as validações e antes da query
    if cep and not logradouro:  # Se CEP foi informado mas endereço não
        from ...util.functions.busca_endereco_pelo_cep import busca_endereco_pelo_cep
        endereco_result = await busca_endereco_pelo_cep(cep)
        
        if endereco_result.get("success"):
            endereco_data = endereco_result.get("data", {})
            logradouro = logradouro or endereco_data.get("logradouro")
            bairro = bairro or endereco_data.get("bairro")
            cidade = cidade or endereco_data.get("localidade")
            uf = uf or endereco_data.get("uf")
    
    query = """
    CREATE (p:Pessoa {
        idx: $idx,
        nome: $nome,
        telefone: $telefone,
        email: $email,
        cep: $cep,
        numero: $numero,
        complemento: $complemento,
        logradouro: $logradouro,
        bairro: $bairro,
        cidade: $cidade,
        uf: $uf,
        cpf_cnpj: $cpf_cnpj
    })
    WITH p
    MATCH (n:Negocio {idx: $negocio_idx})
    MATCH (u:Pessoa {idx: $usuario_idx})
    CREATE (p)-[:CLIENTE_DE]->(n)
    CREATE (p)-[:ATENDIDO_POR]->(u)
    RETURN p.idx as pessoa_idx, p.nome as nome
    """
    
    parameters = {
        "idx": idx,
        "nome": nome,
        "telefone": telefone,
        "email": email,
        "cep": cep,
        "numero": numero,
        "complemento": complemento,
        "logradouro": logradouro,
        "bairro": bairro,
        "cidade": cidade,
        "uf": uf,
        "cpf_cnpj": cpf_cnpj,
        "negocio_idx": negocio_idx,
        "usuario_idx": usuario_idx
    }

    try:
        print("@ query",query)
        print("@ parameters",parameters)  
        # Executar a query de inserção no Neo4j
        result = await neo4j.execute_write_query(query, parameters)
        print("@@@@@ result",result)
        
        # Verificar se houve erro na execução
        if isinstance(result, dict) and "erro" in result:
            logger.error(f"❌ ERRO na inserção do cliente: {result['erro']}")
            return {"success": False, "message": f"Erro ao inserir cliente: {result['erro']}"}
        
        # Para queries CREATE, verificar se retornou dados
        if isinstance(result, list) and len(result) > 0:
            # Sucesso - cliente criado
            cliente_data = result[0]
            logger.info("===== cliente_adicionar SUCESSO =====")
            logger.info(f"👤 Cliente: {nome} | 📞 Telefone: {telefone}")
            
            return {
                "success": True, 
                "message": f"Cliente '{nome}' adicionado com sucesso!",
                "data": cliente_data
            }
        else:
            # Falha - nenhum dado retornado
            logger.error(f"❌ ERRO: Nenhum dado retornado do Neo4j")
            return {"success": False, "message": "Falha ao criar cliente no banco de dados"}
        
        # Caso não seja nem erro nem sucesso esperado
        logger.error(f"❌ ERRO: Resposta inesperada do Neo4j: {result}")
        return {"success": False, "message": "Resposta inesperada do banco de dados"}
        
    except Exception as e:
        logger.error(f"===== ERRO EM cliente_adicionar() =====")
        logger.error(f"🚨 ERRO TIPO: {type(e).__name__}")
        logger.error(f"💬 ERRO MENSAGEM: {str(e)}")
        logger.error(f"📋 DADOS DO CLIENTE:")
        logger.error(f"  👤 NOME: {nome}")
        logger.error(f"  📞 TELEFONE: {telefone}")
        logger.error(f"  📧 EMAIL: {email}")
        logger.error(f"  📮 CEP: {cep}")
        logger.error(f"  🏠 NUMERO: {numero}")
        logger.error(f"  🏢 NEGOCIO_IDX: {negocio_idx}")
        logger.error(f"  👥 USUARIO_IDX: {usuario_idx}")
        logger.error("===== FIM ERRO cliente_adicionar() =====")
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    import asyncio
    import os

    async def testa_cliente_adicionar():
        # Clear terminal screen
        os.system('cls' if os.name == 'nt' else 'clear')
        await cliente_adicionar( "5544332211", "1122334455","Teste Cliente", "31984198720", "<EMAIL>", "31910720", "123", "Apto 101", "Rua das Flores", "Centro", "São Paulo", "MG", "50518151549")
    
    
    asyncio.run(testa_cliente_adicionar())    
    #execuacao
    #python -m api.agent.assistenciamk.functions.cliente_adicionar
   
