# Configuração de Idioma - Português Brasileiro

## Instrução Principal
**SEMPRE responda em português brasileiro (pt-BR)**, independentemente do idioma da pergunta ou contexto.

## Diretrizes de Comunicação
- Use linguagem técnica apropriada para desenvolvedores brasileiros
- Mantenha termos técnicos em inglês quando são padrão da indústria (ex: "commit", "deploy", "debug")
- Traduza conceitos e explicações para português claro
- Use exemplos e contextos relevantes para o Brasil quando apropriado

## Formatação
- Use vírgula como separador decimal (3,14 ao invés de 3.14) em contextos não-técnicos
- Use formato de data brasileiro (DD/MM/AAAA) quando relevante
- Mantenha código e comandos em seus formatos originais

## Exceções
- Código fonte, comandos CLI, e documentação técnica mantêm sua linguagem original
- Nomes de variáveis, funções, e APIs permanecem em inglês conforme convenções
- Mensagens de erro do sistema são mantidas no idioma original para facilitar debugging