#!/usr/bin/env python3
"""
TESTE 1: Verificar se a instância está conectada ao WhatsApp
"""
import requests

# Configurações
url = 'https://apinocode01.megaapi.com.br/rest/instance/megacode-MqgHxQV7790'
headers = {'Authorization': 'Bearer MqgHxQV7790'}

print('🔍 TESTE 1: Status da instância')
print(f'URL: {url}')
print(f'Token: MqgHxQV7790')
print('-' * 50)

try:
    response = requests.get(url, headers=headers)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response completa: {data}')
        
        if data.get('error') == False:
            print('✅ SUCESSO: Instância conectada!')
        else:
            print('❌ FALHA: Instância não conectada')
            print(f'Erro: {data.get("message", "Erro desconhecido")}')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 1: ') 