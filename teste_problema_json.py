#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

def testar_problema_json_html():
    """
    Testa o problema específico: modelo retornando HTML quando deveria retornar JSON
    """
    print("=== TESTE DO PROBLEMA JSON vs HTML ===\n")
    
    # Simular o que acontece quando o modelo retorna HTML em vez de JSON
    
    # Caso 1: Resposta que deveria ser JSON mas veio como HTML (problema atual)
    html_incorreto = """<div style="border: 1px solid #ccc; border-radius: 10px; padding: 15px; margin: 10px 0;">
    <h3>Batom Mate Ruby Red</h3>
    <p>Preço: R$ 29,90</p>
    <p>Código: 10022953</p>
</div>"""
    
    # Caso 2: Resposta JSON correta (como deveria ser)
    json_correto = """{
    "tipo": "individual",
    "mensagem": "Aqui estão nossos batons disponíveis:",
    "produtos_individuais": [
        {
            "nome": "Batom Mate Ruby Red",
            "preco": 29.90,
            "codigo": "10022953"
        }
    ],
    "grupos_produtos": null
}"""
    
    print("🧪 Teste 1: Detectar HTML incorreto (problema atual)")
    print(f"Conteúdo: {html_incorreto[:100]}...")
    
    # Simular a validação que foi adicionada
    tem_html = '<div' in html_incorreto or '<p' in html_incorreto or '<h' in html_incorreto
    print(f"✅ Detectou HTML quando deveria ser JSON: {tem_html}")
    
    if tem_html:
        print("🔧 Ação: HTML rejeitado, forçando resposta estruturada")
        # Extrair texto limpo
        import re
        texto_limpo = re.sub(r'<[^>]+>', '', html_incorreto).strip()
        print(f"📝 Texto extraído: {texto_limpo}")
    
    print("\n" + "="*50 + "\n")
    
    print("🧪 Teste 2: JSON correto (como deveria ser)")
    print(f"Conteúdo: {json_correto[:100]}...")
    
    # Testar se JSON é válido
    import json
    json_clean = json_correto.strip()
    eh_json_valido = json_clean.startswith('{') and json_clean.endswith('}')
    print(f"✅ É JSON válido: {eh_json_valido}")
    
    if eh_json_valido:
        try:
            resposta_json = json.loads(json_clean)
            print(f"✅ JSON parseado com sucesso: {list(resposta_json.keys())}")
            print("🔧 Ação: JSON aceito e processado normalmente")
        except json.JSONDecodeError as e:
            print(f"❌ Erro ao parsear JSON: {e}")
    
    print("\n" + "="*50 + "\n")
    print("📋 RESUMO:")
    print("- HTML será rejeitado e convertido em resposta estruturada básica")
    print("- JSON será aceito e processado normalmente")
    print("- Logs detalhados mostrarão quando o modelo está retornando HTML incorretamente")

if __name__ == "__main__":
    testar_problema_json_html() 