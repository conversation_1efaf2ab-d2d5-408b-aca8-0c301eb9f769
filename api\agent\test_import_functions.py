import importlib.util
import sys
import inspect
import os

def test_carrega_ferramentas():
    """Testa a detecção de funções com @function_tool"""
    print("=== Testando detecção de funções @function_tool ===")
    
    # Adicionar o diretório atual ao path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    try:
        from test_functions import test_function_1, test_function_2, regular_function
        
        # Lista de funções para testar
        funcoes = [test_function_1, test_function_2, regular_function]
        
        print(f"Funções encontradas: {len(funcoes)}")
        
        for func in funcoes:
            print(f"\nFunção: {getattr(func, '__name__', str(func))}")
            print(f"Tipo: {type(func)}")
            
            # Verificar se é uma função decorada
            # Funções decoradas com @function_tool geralmente têm __wrapped__
            is_decorated = hasattr(func, '__wrapped__') or 'FunctionTool' in str(type(func))
            print(f"É função decorada? {is_decorated}")
            
            # Verificar se é callable
            print(f"É callable? {callable(func)}")
            
            # Verificar atributos específicos
            if hasattr(func, '__wrapped__'):
                print(f"Função original: {func.__wrapped__.__name__}")
            
    except Exception as e:
        print(f"Erro ao importar: {e}")
        import traceback
        traceback.print_exc()

def test_import_direto():
    """Testa importação direta usando importlib"""
    print("\n=== Testando importação direta ===")
    
    try:
        # Caminho para o arquivo
        module_path = r'c:\Users\<USER>\Documents\GitHub\gptalk\server\api\agent\test_functions.py'
        
        # Carregar o módulo dinamicamente
        spec = importlib.util.spec_from_file_location("test_module", module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Inspecionar membros
        ferramentas = []
        for name, obj in inspect.getmembers(module):
            if callable(obj) and not name.startswith('_') and name != 'function_tool':
                print(f"\nFunção: {name}")
                print(f"Tipo: {type(obj)}")
                
                # Detectar funções decoradas com @function_tool
                is_tool = (
                    hasattr(obj, '__wrapped__') or
                    'FunctionTool' in str(type(obj)) or
                    str(type(obj)).find('function_tool') > 0
                )
                
                if is_tool:
                    ferramentas.append(obj)
                    print(f"✅ Função @function_tool detectada: {name}")
                else:
                    print(f"❌ Função regular: {name}")
        
        print(f"\nTotal de ferramentas detectadas: {len(ferramentas)}")
        
        # Listar as ferramentas encontradas
        if ferramentas:
            print("\nFerramentas encontradas:")
            for tool in ferramentas:
                print(f"  - {getattr(tool, '__name__', str(tool))}")
        
    except Exception as e:
        print(f"Erro ao importar diretamente: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_carrega_ferramentas()
    test_import_direto()