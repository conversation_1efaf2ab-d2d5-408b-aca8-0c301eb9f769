import pytest
from fastapi.testclient import TestClient
from ...main import app
from ..agent_accountant import Accountant

client = TestClient(app)

def test_fetch_accounts_dre():
    """
    Testa o endpoint /accounts_dre/fetch/{negocio_idx}
    """
    # Dados de teste
    negocio_idx = "1"  # ID de teste
    data = {
        "colunas_nome": [
            "CODIGO",
            "NOME",
            "NATUREZA_ID",
            "SALDO_ATUAL"
        ]
    }

    # Faz a requisição POST
    response = client.post(f"/agent/accountant/accounts_dre/fetch/{negocio_idx}", json=data)

    # Verifica se a resposta foi bem sucedida
    assert response.status_code == 200

    # Verifica se o resultado é uma lista
    result = response.json()
    assert isinstance(result, list)

    # Se houver resultados, verifica a estrutura dos dados
    if result:
        conta = result[0]
        # Verifica se todas as colunas solicitadas estão presentes
        for coluna in data["colunas_nome"]:
            assert coluna in conta

        # Verifica se o código começa com '3' (contas DRE)
        assert conta["CODIGO"].startswith("3")

def test_fetch_accounts_dre_invalid_negocio():
    """
    Testa o endpoint com um negócio inválido
    """
    negocio_idx = "999999"  # ID inválido
    data = {
        "colunas_nome": ["CODIGO", "NOME"]
    }

    response = client.post(f"/agent/accountant/accounts_dre/fetch/{negocio_idx}", json=data)
    
    # Deve retornar 200 mesmo sem resultados (lista vazia)
    assert response.status_code == 200
    result = response.json()
    assert isinstance(result, list)
    assert len(result) == 0

def test_fetch_accounts_dre_missing_columns():
    """
    Testa o endpoint sem especificar colunas
    """
    negocio_idx = "1"
    data = {
        "colunas_nome": []
    }

    response = client.post(f"/agent/accountant/accounts_dre/fetch/{negocio_idx}", json=data)
    
    # Deve retornar erro 422 (Unprocessable Entity) se não houver colunas
    assert response.status_code == 422

def test_fetch_account():
    """
    Testa o endpoint /account/fetch/{negocio_idx}/{area_id}
    """
    # Dados de teste
    negocio_idx = "1"  # ID de teste
    area_id = "1"  # ID da área (ex: 1 para Ativo)
    data = {
        "colunas_nome": [
            "IDX",
            "CODIGO",
            "NOME",
            "SALDO_ATUAL"
        ]
    }

    # Faz a requisição POST
    response = client.post(f"/agent/accountant/account/fetch/{negocio_idx}/{area_id}", json=data)

    # Verifica se a resposta foi bem sucedida
    assert response.status_code == 200

    # Verifica se o resultado é uma lista
    result = response.json()
    assert isinstance(result, list)

    # Se houver resultados, verifica a estrutura dos dados
    if result:
        conta = result[0]
        # Verifica se todas as colunas solicitadas estão presentes
        for coluna in data["colunas_nome"]:
            assert coluna in conta

        # Verifica se o código corresponde à área solicitada
        # Para área 1 (Ativo), o código deve começar com '1'
        assert conta["CODIGO"].startswith(str(area_id))




