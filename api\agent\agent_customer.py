from .agent_logger import <PERSON><PERSON>ogger
from fastapi import APIRouter
from .agent_neo4j import AgentNeo4j


logger = AgentLogger()
router = APIRouter()


class AgentCustomer:
    def __init__(self):
        self.nome = "Customer"
        self.descricao = "Agente de Clientes"
        self.tipo = "customer"
        self.neo4j = AgentNeo4j()

    # Não é mais necessário _format_query_with_params para Neo4j

    async def fetch_clientes(self, data: dict):
        """Busca clientes no Neo4j (Pessoa com relacionamento CLIENTE_DE para Negocio)"""
        logger.info(f"AgentCustomer.fetch_clientes chamado com: {data}")
        try:
            colunas = ["IDX", "EMAIL", "TELEFONE", "NOME", "URL_IMAGEM"]
            query = data.get('query', '')
            offset = data.get('filters', {}).get('offset', 0)
            limit = data.get('filters', {}).get('limit', 10)
            negocio_idx = data.get('filters', {}).get('negocio_idx', '')
            texto_busca = data.get('filters', {}).get('text', query)
            logger.info(f"Parâmetros de busca: query='{texto_busca}', offset={offset}, limit={limit}, negocio_idx='{negocio_idx}'")
            if not negocio_idx:
                logger.error("NEGOCIO_IDX não fornecido")
                return {
                    "success": False,
                    "message": "NEGOCIO_IDX é obrigatório",
                    "data": [],
                    "total": 0
                }
            cypher = """
            MATCH (p:Pessoa)-[:CLIENTE_DE]->(n:Negocio {idx: $negocio_idx})
            WHERE (p.excluido = 0 OR p.excluido IS NULL)
            """
            params = {"negocio_idx": negocio_idx}
            if texto_busca and texto_busca.strip():
                cypher += " AND (toLower(p.nome) CONTAINS toLower($texto_busca) OR toLower(p.email) CONTAINS toLower($texto_busca) OR toLower(p.telefone) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
            cypher += " RETURN p ORDER BY p.nome"
            if limit > 0:
                cypher += " SKIP $offset LIMIT $limit"
                params["offset"] = offset
                params["limit"] = limit
            result = await self.neo4j.execute_read_query(cypher, params)
            clientes = [{k.upper(): v for k, v in r["p"].items()} for r in result if "p" in r]
            resposta = {
                "success": True,
                "data": clientes,
                "total": len(clientes),
                "message": f"Encontrados {len(clientes)} clientes" + (f" para '{texto_busca}'" if texto_busca else "")
            }
            logger.info(f"Resposta construída com sucesso: {len(clientes)} clientes")
            return resposta
        except Exception as e:
            logger.error(f"❌ ERRO em fetch_clientes: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }

    # Não é mais necessário _construir_busca_inteligente para Neo4j

    async def search_all_customers(self, filters: dict):
        """Busca clientes de forma inteligente e flexível por NOME, EMAIL, TELEFONE no Neo4j"""
        logger.info(f"===== search_all_customers() (Neo4j) ===== {filters}")
        try:
            colunas = filters.get("colunas_nome", ["IDX", "NOME", "EMAIL", "TELEFONE", "URL_IMAGEM"])
            texto_busca = filters.get("text", "")
            offset = filters.get('offset', 0)
            limit = filters.get('limit', 10)
            negocio_idx = filters.get('negocio_idx', '')
            logger.info(f"Parâmetros de busca: texto='{texto_busca}', offset={offset}, limit={limit}, negocio_idx={negocio_idx}")
            if not negocio_idx:
                logger.error("NEGOCIO_IDX não fornecido")
                return {
                    "success": False,
                    "message": "NEGOCIO_IDX é obrigatório",
                    "data": [],
                    "total": 0
                }
            cypher = """
            MATCH (p:Pessoa)-[:CLIENTE_DE]->(n:Negocio {idx: $negocio_idx})
            WHERE (p.excluido = 0 OR p.excluido IS NULL)
            """
            params = {"negocio_idx": negocio_idx}
            if texto_busca and texto_busca.strip():
                cypher += " AND (toLower(p.nome) CONTAINS toLower($texto_busca) OR toLower(p.email) CONTAINS toLower($texto_busca) OR toLower(p.telefone) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
            cypher += " RETURN p ORDER BY p.nome"
            if limit > 0:
                cypher += " SKIP $offset LIMIT $limit"
                params["offset"] = offset
                params["limit"] = limit
            result = await self.neo4j.execute_read_query(cypher, params)
            clientes = [{k.upper(): v for k, v in r["p"].items()} for r in result if "p" in r]
            resposta = {
                "success": True,
                "data": clientes,
                "total": len(clientes),
                "message": f"Encontrados {len(clientes)} clientes" + (f" para '{texto_busca}'" if texto_busca else "")
            }
            logger.info(f"Resposta construída com sucesso: {len(clientes)} clientes")
            return resposta
        except Exception as e:
            logger.error(f"❌ ERRO em search_all_customers: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }
                # logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }


@router.post("/fetch")
async def fetch_customers(data: dict):
    """
    Endpoint para buscar clientes com base nos filtros fornecidos.
    """
    logger.info(f"Endpoint /fetch chamado com: {data}")
    agent = AgentCustomer()
    resultado = await agent.fetch_clientes(data)
    logger.info(f"Retornando: {resultado}")
    return resultado

@router.post("/search/all")
async def search_all_customers(data: dict):
    """
    Endpoint para buscar clientes com busca inteligente baseada nos filtros fornecidos.
    """
    logger.info(f"Endpoint /search/all chamado com: {data}")
    agent = AgentCustomer()
    resultado = await agent.search_all_customers(data)
    logger.info(f"Retornando: {resultado}")
    return resultado



if __name__ == "__main__":
    import asyncio

    async def test_products_search_all():
        logger.info("===== test_products_search_all() - INÍCIO =====")
        customer = AgentCustomer()
        negocio_idx = "4015743441"
        data = {
            "negocio_idx": negocio_idx,
            "text": "cruz",
            "limit": 0,
            "offset": 0
        }
        logger.info(f"data: {data}")
        result = await customer.search_all_customers(data)
        logger.info(f"result: {result}")

    asyncio.run(test_products_search_all())




