from .agent_secret import Secret
import requests 
from io import BytesIO
class Cpanel:
    def __init__(self,user_name=None,password=None, token=None, domain=None ,subdomain=None):
        self.secret = Secret()

        self.USER_NAME = user_name if user_name else self.secret.GPTALK_CPANEL_USER
        self.PASSWORD = password if password else self.secret.GPTALK_CPANEL_PASSWORD
        self.TOKEN = token if token else self.secret.GPTALK_CPANEL_TOKEN
        self.DOMAIN =domain if domain else  self.secret.DOMAIN
        self.SUBDOMAIN = subdomain if subdomain else self.secret.GPTALK_SITE_SUBDOMAIN
        
    
    async def delete_subdomain_files(self,subdomain):
                url =f"https://www.gptalk.com.br/site_file_manager.php?excluirdiretorio=site/sites/{subdomain}"
                requests.get(url)


    async def list_files(self,directory=None):
        #print("========== list_files ==========",directory)       
      
        # Cabeçalhos necessários para a autenticação
        headers = {
            "Authorization": f"cpanel {self.USER_NAME}:{self.TOKEN}"
        }
    
        # A URL para a API de Listar Arquivos
        #self.DOMAIN = "www.gptalk.com.br"
        url = f"https://{self.DOMAIN}:2083/execute/Fileman/list_files"
        #print("url",url)
        # Parâmetros para a requisição
        params = {
            'dir': directory
        }
    
        #print("Faça a solicitação GET para listar os arquivos")
        response = requests.get(url, headers=headers, params=params, verify=True)

        # Tentar decodificar a resposta JSON
        try:
            response_data = response.json()
            #print("response_data",response_data)
            if 'errors' in response_data and response_data['errors']:

                error = response_data['errors'][0]       
                if all(phrase in error for phrase in ["The directory", "does not exist"]):
                    return {"STATUS":"F","RESULT":"O diretório não existe"}

            response_files = await Cpanel.extract_files_names(response_data)
            return {"STATUS":"R","RESULT":response_files}
        except ValueError:
            response_data = {'error': 'Failed to decode JSON response'}
    
        return response_data
    

    @staticmethod
    async def extract_files_names(resposta_api):
        """
        Extrai os nomes dos arquivos a partir da resposta da API do cPanel.
    
        Args:
            resposta_api (dict): Resposta da API de listagem de arquivos do cPanel.
    
        Returns:
            list: Uma lista contendo apenas os nomes dos arquivos.
        """
        nomes_arquivos = []
        dados = resposta_api.get('data', [])
    
        for item in dados:
            if item.get('type') == 'file':  # Verifica se o item é um arquivo
                nome_arquivo = item.get('file')  # Obtém o nome do arquivo
                if nome_arquivo:
                    nomes_arquivos.append(nome_arquivo)
    
        return nomes_arquivos



    async def subdomain_exists(self,subdomain):
        print("==== subdomain_exists() =====",subdomain)
    
        headers = {
            "Authorization": f"cpanel {self.USER_NAME}:{self.TOKEN}"
        }
    
        url = f"https://{self.DOMAIN}:2083/execute/DomainInfo/list_domains"
        #print("url",url)
        response = requests.get(url, headers=headers, verify=True)
        data = response.json()
        #print("lista",data)
        #print("")
        if 'sub_domains' in data['data']:
            #print ("data subdmonais",data['data']['sub_domains'])
            for item in data['data']['sub_domains']:
                #print("")
                #print("item",item)
                if item.startswith(subdomain + '.'):
                    return True
        return False


    async def subdomain_create(self,subdomain):
        print("cpanel_subomain_create()")
        domain = self.SUBDOMAIN
        subdomain =  subdomain
        document_root = "site/sites/" + subdomain
        user = self.USER_NAME
        token  = self.TOKEN
   
   
    
        # Cabeçalhos necessários para a autenticação
        headers = {
        "Authorization": f"cpanel {user}:{token}"
        }
    
    
      # Codificar o document_root para uso na URL
      
    
        # A URL foi ajustada para incluir o parâmetro 'dir' para o document_root
        url = f"https://{domain}:2083/execute/SubDomain/addsubdomain?domain={subdomain}&rootdomain={domain}&dir={document_root}"
        print("url",url)
        # Faça a solicitação GET
        response = requests.get(url, headers=headers, verify=True)
        print("response", response)
        #exclui diretorio temporario

        #exclui subpasta cgi-bin
        url =f"https://www.gptalk.com.br/site_file_manager.php?excluirarquivos=site/sites/{subdomain}"
        requests.get(url)

        return response.json()

    async def get_file_content(self,dir,file):

         # Cabeçalhos necessários para a autenticação
         headers = {
             "Authorization": f"cpanel {self.USER_NAME}:{self.TOKEN}"
         }
     
         # A URL para a API de Obter Conteúdo do Arquivo
         url = f"https://{self.DOMAIN}:2083/execute/Fileman/get_file_content"
     
         # Parâmetros para a requisição
         params = {
             'dir': dir,
             'file': file
         }
     
         # Faça a solicitação GET para obter o conteúdo do arquivo
         response = requests.get(url, headers=headers, params=params, verify=True)
     
         # Tentar decodificar a resposta JSON
         try:
             response_data = response.json()
         except ValueError:
             response_data = {'error': 'Failed to decode JSON response'}
     
         return response_data["data"]["content"]

    async def subdomain_file_save(self,content,file_name,upload_directory):
         #print("cpanel_subdomain_file_save()")
         # A URL para a API Fileman de upload de arquivo
         url = f"https://{self.DOMAIN}:2083/execute/Fileman/upload_files"
     
         # Criar um arquivo em memória com o conteúdo a ser salvo
         file_like_object = BytesIO(content.encode('utf-8'))
     
         # Preparar os dados do arquivo para upload
         files = {'file-1': (file_name, file_like_object)}
     
         # Parâmetros para definir o diretório de destino do upload
         data = {
             'dir': upload_directory,
             'overwrite': 1  # Adiciona esta linha para sobrescrever o arquivo existente
         }

         headers = {
        "Authorization": f"cpanel {self.USER_NAME}:{self.TOKEN}"
        }

     
         # Faça a solicitação POST para upload do arquivo
         response = requests.post(url, headers=headers, files=files, data=data, verify=True)
         #print("response",response)
         # Fechar o arquivo em memória para liberar recursos
         file_like_object.close()
     
         return response.status_code
       
import asyncio
async def main():
    cp = Cpanel()
    name="petdocarlos"
    await cp.subdomain_create(subdomain=name)
#asyncio.run(main())