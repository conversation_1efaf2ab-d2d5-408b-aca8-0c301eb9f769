
from .agent_pagseguro import <PERSON>gs<PERSON><PERSON>
from .agent_business import Business
from .agent_entity import Entity
from .agent_user import User
class Token:
    def __init__(self,negocio:Business=None):
        self.pagseguro = Pagseguro(negocio=negocio,modo="sandbox")




    async def recharge(self,value:float,paymentProvider:str="pagseguro",payment_method: str="pix",pessoa:Entity=None,negocio:Business=None):
        if paymentProvider == "pagseguro":
            if payment_method == "pix":
                return await self.pagseguro.gerar_pix_qrcode_pix(valor=value,descricao="Recarga de créditos",pessoa=pessoa)
            

if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        usuario = User()
        pessoa = await usuario.fetch("*",["ID=2"])
        negocio = Business()
        await negocio.fetch("*",["ID=1"])
        token = Token(negocio)
        result =await token.recharge(100,pessoa=pessoa)
        print(result)   
        print(result["qrcode"]["url"])
    asyncio.run(main())