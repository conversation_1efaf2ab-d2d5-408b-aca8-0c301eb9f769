import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
from api.agent.agent_mysql import Mysql
from api.agent.agent_secret import Secret
from app_sl.geral.util import generate_unique_id

async def testar_conexao_loja247():
    secret = Secret()
    
    if not all([secret.LOJA247_MYSQL_HOST, secret.LOJA247_MYSQL_USER, 
                secret.LOJA247_MYSQL_PASSWORD, secret.LOJA247_MYSQL_DATABASE]):
        print("Erro: As variáveis de ambiente para LOJA247 não estão completamente definidas.")
        return None
    
    loja247_db = Mysql(
        host=secret.LOJA247_MYSQL_HOST,
        user=secret.LOJA247_MYSQL_USER,
        password=secret.LOJA247_MYSQL_PASSWORD,
        database=secret.LOJA247_MYSQL_DATABASE
    )
    
    try:
        conn = await loja247_db.conecta()
        print("Conexão com o banco LOJA247 estabelecida com sucesso!")
        return loja247_db
    except Exception as e:
        print(f"Erro ao conectar com o banco LOJA247: {e}")
        return None

async def testar_conexao_gptalk():
    gptalk_db = Mysql()  # Usa as configurações padrão do GPTALK_01
    
    try:
        conn = await gptalk_db.conecta()
        print("Conexão com o banco GPTALK_01 estabelecida com sucesso!")
        return gptalk_db
    except Exception as e:
        print(f"Erro ao conectar com o banco GPTALK_01: {e}")
        return None

async def importar_formas_pagamento(loja247_db, gptalk_db):
    LOJA_ID = 39
    negocio_idx = "9876543210"

    # Delete registros existentes
    delete_query = f"DELETE FROM VENDA_FORMA_PAGAMENTO WHERE NEGOCIO_ID = '{negocio_idx}'"
    await gptalk_db.query(delete_query)

    # 1. Carregar formas de pagamento da LOJA247
    query = f"SELECT * FROM FORMA_PAGTO WHERE LOJA_ID = {LOJA_ID}"
    loja247_formas_pagto = await loja247_db.query(query)

    # 2. Criar array gptalk_formas_pagto
    gptalk_formas_pagto = []

    # 3. Processar formas de pagamento
    for registro in loja247_formas_pagto:
        formaspg = {
            "IDX": generate_unique_id(),
            "NOME": registro['FORMA'],
            "NEGOCIO_ID": negocio_idx,
            "BALCAO": registro['BALCAO'],
            "MESA": registro['MESA'],
            "SITE": registro['SITE'],
            "SERVIDOR_ID": registro['SERVIDOR_ID']
        }

        # 4. Adicionar o objeto formaspag no array gptalk_formas_pagto
        gptalk_formas_pagto.append(formaspg)

    # 5. Gerar e executar query de inserção
    if gptalk_formas_pagto:
        insert_query = "INSERT INTO VENDA_FORMA_PAGAMENTO (IDX, NOME, NEGOCIO_ID, BALCAO, MESA, SITE, SERVIDOR_ID) VALUES "
        values = []
        for forma in gptalk_formas_pagto:
            values.append(f"('{forma['IDX']}', '{forma['NOME']}', '{forma['NEGOCIO_ID']}', {forma['BALCAO']}, {forma['MESA']}, {forma['SITE']}, {forma['SERVIDOR_ID']})")
        
        insert_query += ", ".join(values)
        await gptalk_db.query(insert_query)

    print(f"Importação de {len(gptalk_formas_pagto)} formas de pagamento concluída com sucesso!")

async def main():
    loja247_db = await testar_conexao_loja247()
    gptalk_db = await testar_conexao_gptalk()

    if loja247_db and gptalk_db:
        print("\nIniciando importação de formas de pagamento...")
        await importar_formas_pagamento(loja247_db, gptalk_db)
    else:
        print("Não foi possível estabelecer conexão com um ou ambos os bancos de dados.")

if __name__ == "__main__":
    asyncio.run(main())
