from .agent_mysql import Mysql
mysql = Mysql()

class Process:
    def __init__(self): 
        self.ID = 0
        self.DESCRICAO = ""
        self.EXECUCAO_UNICA = 1

    #==========    
    def save_process(processo):
        
        if 'ID' in processo:
            mysql.mysql_update_data('PROCESSO',processo)
            return True 
        else: 
            id = mysql.mysql_add_data("PROCESSO",processo)
            return id

    #==========
    def fetch_tasks(id:int):
        processo_tarefas = mysql.mysql_query(f'SELECT * FROM PROCESSO_TAREFA WHERE PROCESSO_ID = {id} ORDER BY SEQUENCIA')
        return processo_tarefas

#Teste
#==========
import asyncio
async def main():
    
    processo = {}
    processo['DESCRICAO']  = 'Criação de site'
    processo['EXECUCAO_UNICA'] = 1

    resultado = await mysql.mysql_add_data('PROCESSO', processo)

#asyncio.run(main())

