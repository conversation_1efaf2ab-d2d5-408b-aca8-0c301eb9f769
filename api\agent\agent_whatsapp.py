import requests,json
from fastapi import <PERSON><PERSON>out<PERSON>, Request, HTTPException
router = APIRouter()
from .agent_logger import <PERSON><PERSON><PERSON><PERSON>
from .agent_secret import Secret


router = APIRouter()
secret = Secret()
logger = AgentLogger()
WHATSAPP_API_BASE_URL = secret.get_secret("WHATSAPP_API_BASE_URL")
PHONE_NUMBER_ID = secret.get_secret("WHATSAPP_PHONE_NUMBER_ID")
ACCESS_TOKEN = secret.get_secret("WHATSAPP_ACCESS_TOKEN")
#logger.info(f"WHATSAPP_API_BASE_URL: {WHATSAPP_API_BASE_URL}")
#logger.info(f"PHONE_NUMBER_ID: {PHONE_NUMBER_ID}")
#logger.info(f"ACCESS_TOKEN: {ACCESS_TOKEN}")    

class WhatsApp:
    def __init__(self):
        pass

    async def process_message(self, body: dict):
        # Check if the object is a 'whatsapp_business_account' event
        if "object" in body and body["object"] == "whatsapp_business_account":
            if "entry" in body and body["entry"]:
                for entry in body["entry"]:
                    if "changes" in entry and entry["changes"]:
                        for change in entry["changes"]:
                            # Look for 'messages' field in the change
                            if "field" in change and change["field"] == "messages":
                                value = change.get("value")
                                if value and "messages" in value:
                                    for message in value["messages"]:
                                        # Process only text messages for now
                                        if message.get("type") == "text":
                                            from_number = message["from"]      # Sender's phone number
                                            text_body = message["text"]["body"] # Message content

                                            logger.info(f"Mensagem de {from_number}: {text_body}")

                                            # Example: Echo the message back to the sender
                                            # This calls the async function to send a message.
                                            await self.send_whatsapp_message(from_number, f"Você disse isto pra mim: \"{text_body}\"")
                                        else:
                                            logger.error(f"Tipo de mensagem recebida não é texto: {message.get('type')}")
                                else:
                                    logger.error("Não há 'messages' no valor do change.")
                            else:
                                logger.error(f"Campo de mudança inesperado: {change.get('field')}")
                    else:
                        logger.error("Não há 'changes' na entrada.")
            else:
                logger.error("Não há 'entry' no body.")
        else:
            logger.error("Objeto do webhook não é 'whatsapp_business_account'.")


    async def send_whatsapp_message(self, to_number: str, message_text: str):
        
        """
        Sends a text message to a WhatsApp number using the WhatsApp Cloud API.
        """
        # Construct the API URL for sending messages
        url = f"{WHATSAPP_API_BASE_URL}/{PHONE_NUMBER_ID}/messages"
        headers = {
            "Authorization": f"Bearer {ACCESS_TOKEN}", # Your access token for authentication
            "Content-Type": "application/json"
        }
        payload = {
            "messaging_product": "whatsapp",
            "to": to_number,
            "type": "text",
            "text": {"body": message_text}
        }
        try:
            # Make the POST request to the WhatsApp Cloud API
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status() # Raise an exception for HTTP errors (4xx or 5xx)
            print(f"Mensagem enviada com sucesso para {to_number}: {response.json()}")
        except requests.exceptions.HTTPError as e:
            # Log HTTP errors with status code and response text
            print(f"Erro HTTP ao enviar mensagem: {e.response.status_code} - {e.response.text}")
        except requests.exceptions.RequestException as e:
            # Log other request-related errors
            print(f"Erro ao enviar mensagem: {e}")





@router.get("/webhook")
async def webhook(request: Request):
    logger.info("agent_whatsapp.webhook()")
    logger.info("request",request)
    token = request.query_params.get('hub.verify_token')
    challenge = request.query_params.get('hub.challenge')
    
    logger.info(f"token: {token}")
    logger.info(f"challenge: {challenge}")
    
    if token == "2312532379":
        print("token ok.")
        return PlainTextResponse(challenge)
    
    logger.error("Token inválido")
    raise HTTPException(status_code=403, detail="Invalid verification token")

@router.post("/webhook")
async def handle_webhook(request: Request):
    body = await request.json() # Parse the incoming JSON payload

    logger.info("--- Webhook Body Recebido ---")
    logger.info(json.dumps(body, indent=2)) # Print the full incoming payload for debugging
    logger.info("-----------------------------")

    wz = WhatsApp()
    result = await wz.process_message(body)
    logger.info(f"result: {result}")
    
    return {"status": "ok"} # Always respond with 200 OK to Meta to acknowledge receipt
    


if __name__ == "__main__":
    import asyncio

    async def main():
        message = "Qual o seu nome?"
        wz = WhatsApp()
        await wz.send_whatsapp_message("553191149571", message)

    asyncio.run(main())