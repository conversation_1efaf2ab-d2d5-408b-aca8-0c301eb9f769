from agents import Agent, Runner
from openai.types.responses import ResponseTextDeltaEvent
from fastapi import APIRouter
from .agent_llm import LLM
from ..cache import cache
from ..functions.util import generate_unique_id
from ..functions.util import cep_data
from .agent_openai import OpenAi
from agents import function_tool
from .agent_mysql import Mysql
from ..functions.validation.cpf_cnpj import cpf_cnpj_valido
import json
import asyncio
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator
from ..logging_config import logger
import re
from agents.run import RunConfig
from pydantic import BaseModel, Field 
from typing import Optional, List, Literal 
import pytz
from datetime import datetime



router = APIRouter()
oai = OpenAi()  # Using your custom OpenAi class
mysql = Mysql()

# Modelo para um item de serviço individual
class Produto(BaseModel):
    id: int
    nome: str
    valor: float
    quantidade: int


# Modelo para um item de forma de pagamento individual
class FormaPagamento(BaseModel):
    nr: int = Field(alias="nr") # Use Field(alias) se o nome do campo Python for diferente do JSON
    id: int
    valor: float
    vencimento: str # Ou datetime.date, se preferir lidar com objetos de data
    pago: Literal[0, 1] # 0 ou 1 

    
# Modelos para cliente
class ClienteData(BaseModel):
    ID: int # Ou o tipo correto do ID do seu cliente
    IDX: Optional[str] = None
    NOME: Optional[str] = None
    CPF_CNPJ: Optional[str] = None
    TELEFONE: Optional[str] = None
    EMAIL: Optional[str] = None
    CEP: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None
    # Adicione todos os campos relevantes do seu dicionário 'cliente'



class AgentAssistenciaMK:
    def __init__(
        self,
        name="oficinatech",
        usuario_nome=None, 
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        plataforma_url=None,
        produtos_mary_kay=None,
        marykay_idx="5544332211"):
        self.name = name
        self.instructions = f"""
        Seu nome é Maria Karla. Você é uma consultora de beleza e cuidado pessoal da Mary Kay. Estas são as suas atribuições:
        -responder as perguntas do usuário sobre o uso do aplicativo de forma clara e objetiva.
        -responder perguntas sobre produtos e serviços da empresa Mary Kay.
        -Indicar os produtos da Mary Kay que o usuário pode comprar.
        -Indicar os produtoos ideais para cada tipo de cliente da consultora. Quando ela descrever as caracteristicas e situação do cliente, como pele, tipo de cabelo, idade,etc. Indicar os produtos que são ideais para esta pessoa com base na necessidade dela.
        -Realizar operações cadastrais quando for sollicitada, como cadastrar clientes da consultora, bem como produtos da Mary Kay que ela vende.
        -Controlar o estoque de produtos, registrando entrdas e saídas.
        -Sempre que receber alguma imagem, sem que esteja claro o contexto ou o que fazer com ela, ou o usuário não tenha ainda informado, pergunte a ele o que  deseja fazer com a imagem. 
        - Dar informações sobre a Mary Kay e de todos os seus produtos.
        - Gerar relatórios e gráficos sobre as vendas e desempenho das consultoras.
        - Dar informações sobre o estoque de produtos, volume de vendas, produtos a serem entregues ou qualuquer outra informação que o usuário precise e que esteja disponível no aplicativo ou no site da Mary Kay.
        - Ao apresentar listas ou dados, considere que a maioria dos usuários está em dispositivos móveis com telas pequenas. Portanto, formate a saída para ser responsiva e, se não for possível, priorize uma abordagem "mobile first" para garantir a legibilidade e usabilidade.

        qualquer informação sobre produtos da Mary Kay não deve ser dado de seu proprio conhecimento, mas sim buscado através da função consulta_mary_kay.
        -sempre que for solicitada a buscar dados de produtos para exibir , se possivel use a VISAO_PRODUTO_02, que é uma view que une PRODUTO e COR por PRODUTO.COR_CODIGO = COR.CODIGO. Desta forma sera possivel informar o nome da cor do produto.
        -Se o produto  possuir cor, ao ser listado ou informado ao usuario, coloque a cor de fundo do nome do produto com a cor do produto  , e o texto da cor do nome od produto uma cor contrastante, tornado facil a leitura. Desta forma o usuario pode identificar o produto e suas cores com facilidade. ISTO É MUITO IMPORTANTE. Tem cor? Nome da cor com fundo colorido com a cor do produto, determinada pelo hexadecimal da cor.
        -Uilize tambem a VISAO_PRODUTO_02 para enviar dados do produto para a funcao produto_adicionar, pois ela precisa de todos os dados do produto, inclusive o codigo da cor.

        Alguns dados adicionais que você precisará para algumas tarefas e funções:
        -Nome do usuária: {usuario_nome}
        -URL da plataforma: {plataforma_url}  
        -IDX da consultora: {negocio_idx}. Este IDX dever ser usado para todas as consultas ao banco de dados da consultora, como sendo o NEGOCIO_IDX a ser filtrado e infromado nas queries de busca ou pesquisa na funcao consulta_atualiza_dados.
        -IDX da empresa Mary Kay: {marykay_idx}. Este IDX dever ser usado para todas as consultas ao catálogo da Mary Kay, como sendo o NEGOCIO_IDX a ser filtrado e infromado nas queries de busca ou pesquisa na funcao consulta_mary_kay.
        -DESCONTO deve ser calculado sempre que a informação for solicitada ou necessária. É a diferença entre o preço maior e o preço.
        -Sempre que der alguma informação sobre a cor do produto, na listagem de produtos, siga o formato especificado nas seções de listagem de produto das funções `produto_adicionar` e `consulta_atualiza_dados`.
        

        E estes são os produtos da Mary Kay:
        {produtos_mary_kay}


        FUNÇÕES:
        Você tem a sua disposição uma série de funções (tools) que você pode utilizar para realizar as tarefas que lhe foram atribuídas. São elas:
        - cliente_adicionar - Adiciona um novo cliente da consultora no banco de dados.
        - produto_adicionar - Adiciona um novo produto da Mary Kay ao estoque da consultora.
            Siga estes passos ao usar esta função:
            1.  **Listagem de Produtos (se necessário):**
                *   Se o usuário pedir para adicionar um produto com um termo genérico (ex: "adicionar pincel") e você precisar mostrar opções:
                    *   Use a função `consulta_mary_kay` para buscar na view `VISAO_PRODUTO_02` os produtos correspondentes. Sua consulta DEVE solicitar os campos `ID` (identificador único do produto base no catálogo), `CODIGO` (código da variação específica), `NOME` (nome do produto base), `PRECO`, `PRECO_MAIOR`, `COR_NOME`, `COR_HEXADECIMAL`, `URL_IMAGEM` (do produto base). **Ordene os resultados pelo NOME do produto e depois pelo COR_CODIGO para facilitar o agrupamento e a ordenação interna das cores.**
                    *   Apresente os produtos. Para cada produto ou grupo de produto com o mesmo NOME e PRECO:
                        NOME:
                        [NOME do produto base]
                        PREÇO:
                        R$ [PRECO do produto base]
                        PROMOÇÃO: (exibir esta seção apenas se PRECO_MAIOR > PRECO)
                        Desconto de R$ [PRECO_MAIOR - PRECO]

                        // Lógica para Cor/Código:
                        // Se houver múltiplas cores/variações para este NOME/PRECO (grupo):
                        CORES DISPONÍVEIS:
                            [COR_NOME com fundo colorido pela COR_HEXADECIMAL e texto contrastante] - CÓDIGO: [CODIGO da variação]
                            (repita para cada cor/variação disponível para este produto base)
                        // Else (produto individual ou única cor):
                        //   Se COR_NOME existir:
                        COR: [COR_NOME com fundo colorido pela COR_HEXADECIMAL e texto contrastante] - CÓDIGO: [CODIGO da variação]
                        //   Else (COR_NOME NÃO existe, mas é um produto individual que tem um CODIGO):
                        CÓDIGO: [CODIGO da variação] // Este é o CODIGO do produto individual

                        // Imagem:
                        Se `URL_IMAGEM` estiver disponível, exiba **apenas** a imagem do produto (sem nenhum texto ou rótulo introdutório antes dela) de forma centralizada e responsiva usando HTML. Por exemplo:
                        `<img src="[URL_IMAGEM]" alt="Imagem de [NOME do produto base]" style="display: block; margin-left: auto; margin-right: auto; max-width: 100%; height: auto;">`
                    *   **Importante:** Ao listar as cores, para cada `CODIGO` de variação exibido, mantenha o `ID` do produto base (que você recuperou na consulta) associado a ele. Você precisará desse `ID` do produto base e do `CODIGO` da variação escolhida pelo usuário para os próximos passos.
            2.  **Seleção do Usuário e Coleta de Dados:**
                *   Quando o usuário escolher um produto da lista informando o `CODIGO` (ex: "quero o código 10134442"):
                    *   **Recupere o `ID` que você armazenou/associou ao `CODIGO` escolhido no passo 1.** Este `ID` é o identificador único do produto no catálogo Mary Kay.
                    *   Se o usuário ainda não informou a quantidade em estoque, pergunte agora.
            3.  **Chamada da Função `produto_adicionar`:**
                *   Chame a função `produto_adicionar` com os seguintes parâmetros OBRIGATÓRIOS:
                    *   `id`: O `ID` do produto Mary Kay que você recuperou no passo 2 (que veio da sua consulta no passo 1). **Este campo é crucial e NUNCA deve ser 0 se um produto do catálogo está sendo adicionado.**
                    *   `codigo`: O `CODIGO` do produto.
                    *   `estoque`: A quantidade a ser adicionada.
                    *   `negocio_idx`: O IDX da consultora (você já tem essa informação).
                    *   `marykay_idx`: O IDX da Mary Kay (você já tem essa informação).
            4.  **Caso o usuário forneça o código diretamente (sem listagem prévia):**
                *   Se o usuário informar diretamente um `CODIGO` de produto para adicionar, e você não tiver o `ID` correspondente de uma listagem recente:
                    *   Use `consulta_mary_kay` na `VISAO_PRODUTO_02` para buscar o `ID` do produto usando o `CODIGO` fornecido e o `marykay_idx`.
                    *   Então, prossiga para chamar `produto_adicionar` conforme o passo 3, usando o `ID` que você acabou de buscar.ID é o identificador único do produto no catálogo Mary Kay.

            5. ATENÇÃO: ID não é CODIGO. ID é o identificador no banco de dados. CODIGO é o numero no catalogo da Mary Kay. São números distintos e com finalidades diferentes. Na função produto_adicionar, os 2 devem ser informados em suas respectivas posições.

        Lembre-se: O parâmetro `id` da função `produto_adicionar` refere-se ao `ID` único do produto no catálogo geral da Mary Kay e é usado para vincular o item de estoque da consultora ao produto principal. Ele é essencial.
        - consulta_mary_kay - Consulta o catálogo da Mary Kay para obter informações sobre produtos, preços, promoções, etc.
        - consulta_atualiza_dados - Consulta e atualiza dados da consultora no banco de dados.


    RESPOSTAS:
    A maioria dos usuários irao acessar o aplicativo por celular. Então as respostas tem que ser 'mobile first' , devem ser responsivas , e sempre com este fator em mente: o usuário usa uma tela pequena. Para isto deve ser observado os seguintes principios:
    - Todas as imagens , quando exibidas, devem ficar abaixo dos textos e sozinhas em sua linha, ou seja, nenhum texto do lados.
    - Todas as imagens devem ocupar , no máximo , 90% do viewport;
    - Todas as imagens devem ser responsivas, ou seja, devem se ajustar ao tamanho da tela do usuário;
    - Todas as imagens devem ser centralizadas na tela;
    - Todas as consultas feitas a funcao cnsulta_mary_kay() deve ter o filtro NEGOCIO_IDX = '{marykay_idx}'
    

        """

    def get_router(self):
        return self.router

    def get_instructions(self):
        return self.instructions

    def get_agent(self):
        return self.agent

def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{ conversa_idx}"

def get_produtos_cache_key(negocio_idx: str) -> str:
    """Retorna a chave do cache de produtos Mary Kay para um negócio específico."""
    return f"produtos_mary_kay_{negocio_idx}"
    

def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None
# (Linhas 1-4)
import asyncio

async def get_produtos_mary_kay(negocio_idx: str) -> str:
    query = f"""
    SELECT
        ID,
        NOME,
        CODIGO,
        DESCR,
        PRECO AS PRECO_VENDA,
        PRECO_MAIOR AS PRECO,
        ESTOQUE,
        URL,
        URL_IMAGEM,
        VARIAVEL,
        CASE
            WHEN PRECO_MAIOR > 0 AND PRECO > 0 THEN (PRECO_MAIOR - PRECO)
            ELSE 0
        END AS DESCONTO_VALOR,
        CASE
            WHEN PRECO_MAIOR > 0 AND PRECO > 0 THEN ROUND(((PRECO_MAIOR - PRECO) / PRECO_MAIOR) * 100, 2)
            ELSE 0
        END AS DESCONTO_PERCENTUAL
    FROM PRODUTO
    WHERE NEGOCIO_IDX = '{negocio_idx}'
    """
    try:
        produtos_mary_kay = await mysql.query(query)
        return produtos_mary_kay
    except Exception as e:
        print(f"Erro ao executar a consulta: {e}")
        return [] # Retorna lista vazia em caso de erro

def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    #print("===== add_message_to_history() =====")
    #print("history", history)
    #print("message", message)
    #print("is_user", is_user)   
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


def limitar_tamanho_mensagem(mensagem: str, imagem: str = None, max_tokens: int = 60000) -> str:
    """
    Limita o tamanho total da mensagem para evitar exceder o limite de tokens.
    
    Args:
        mensagem (str): Mensagem original do usuário
        imagem (str): Imagem em base64 (opcional)
        max_tokens (int): Número máximo de tokens permitidos
        
    Returns:
        str: Mensagem formatada dentro do limite de tokens
    """
    # Estima-se que 1 token ~ 4 caracteres em média
    max_chars = max_tokens * 4
    
    if not imagem:
        return mensagem[:max_chars] if len(mensagem) > max_chars else mensagem
    
    # Reserva 20% do espaço para a mensagem do usuário
    max_msg_chars = int(max_chars * 0.2)
    max_img_chars = int(max_chars * 0.8)
    
    # Trunca a mensagem se necessário
    if len(mensagem) > max_msg_chars:
        mensagem = mensagem[:max_msg_chars] + "..."
    
    # Trunca a imagem se necessário
    if len(imagem) > max_img_chars:
        imagem = imagem[:max_img_chars] + "..."
    
    return f"Imagem em base64 (truncada se necessário):\n{imagem}\n\nMensagem do usuário:\n{mensagem}"


@router.post("/agent/run")
async def agent_run(data: dict):
    
    logger.info("===== agent_assistenciamk_run() =====")
    logger.info("data %s", data)

  
    # Extrair dados necessários
    mensagem = data.get("mensagem", "")
    #logger.info(f"***** mensagem1 %s:", mensagem)
    logger.info(f"***** mensagem: {mensagem}")
    negocio_idx = data.get("negocio_idx", "")

    modelo = data.get("modelo", "")
    #modelo = "0987654321"  #deepseek
    #modelo = "0987654321"  #gemini
    #logger.info("modelo: %s", modelo)

    imagem = data.get("imagem", "")
    plataforma_url = data.get("plataforma_url", ""),
    usuario_nome = data.get("usuario_nome", "Carlos"),
    marykay_idx = "5544332211"
    produtos_mary_kay = []

    agentAssistenciaMK = AgentAssistenciaMK(
        negocio_idx=negocio_idx,
        usuario_nome=usuario_nome,
        plataforma_url=plataforma_url,
        produtos_mary_kay=produtos_mary_kay,
        marykay_idx=marykay_idx
    )

    if imagem:
        mensagem = "imagem_link :" + imagem + ";mensagem:" + mensagem
  


    if not modelo:
        # Se o streaming for o único tipo de resposta, este erro precisaria ser streamado também
        # ou tratado de forma diferente. Por enquanto, manteremos o retorno de erro como antes,
        # assumindo que o frontend pode lidar com uma resposta não-stream em caso de erro prematuro.
        return {"success": False, "message": "modelo é obrigatório"}


    # Verificar se existe conversa ativa
    conversa_idx, historico_mensagens = find_active_conversation(negocio_idx,agentAssistenciaMK.name)


    # Se não existir conversa ativa, criar nova
    #logger.info(f"@@@@@@@@ conversa_idx: {conversa_idx}")

    if not conversa_idx:
        #print("Não existe conversa ativa, criando nova conversa")
        conversa_idx = generate_unique_id()
        #print("conversa_idx", conversa_idx)
        historico_mensagens = []

    #logger.info(f"historico_mensagens (1) \n {historico_mensagens}")

    # Adicionar mensagem do usuário ao histórico
    #if imagem:
    #    mensagem_sistema = {
    #        "role": "system",
    #        "content": f"Uma nova imagem foi fornecida em base64: {imagem[:50]}..."
    #    }
    #historico_mensagens.append(mensagem_sistema)
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True) # Usar mensagem_formatada_com_imagem
    #logger.info(f"historico_mensagens(2) \n {historico_mensagens}")



    llm = LLM()
    #print("modelo", modelo) 
    model = llm.get_model_idx(modelo)
    #logger.info("model carregado %s", model)

    instructions = agentAssistenciaMK.get_instructions()  
    #print("instructions", instructions)
    
    agenteAMK = {
        "name": "MCP+",
        "instructions": instructions,
        "model": model,
        "tools": [
                  consulta_atualiza_dados,
                  cliente_adicionar,
                  produto_adicionar,
                  consulta_mary_kay,
                  venda_nova,
                  
          
                  ],
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": [],
    }

    #print("agenteTech", agenteTech)
     # Criar o agente DRE360
    agenteAMK_obj = await oai.agent_create(**agenteAMK)
    #logger.info("agente objeto criado")
    #logger.info("agenteTech_obj %s", agenteAMK_obj)

    #logger.info("Vou executar o agente")


    try:
        async def event_stream():
            nonlocal historico_mensagens
            
            resposta_completa = ""
            async for response in oai.agent_run(agenteAMK_obj, historico_mensagens):
                #logger.info("response: %s", response)
                resposta_completa += response
                yield response  # envia parcial ao navegador
            logger.info("resposta_completa: %s", resposta_completa)
            texto_puro = extrair_texto_puro(resposta_completa)
            historico_mensagens = add_message_to_history(historico_mensagens, texto_puro, False)
            # Salvar histórico no cache
            logger.info("historico_mensagens: %s", historico_mensagens)
            cache_key = get_conversa_key(negocio_idx, agentAssistenciaMK.name, conversa_idx)
            cache[cache_key] = historico_mensagens
            
        return StreamingResponse(event_stream(), media_type="text/plain")

    except Exception as e:
        logger.info(f"Erro durante execução do agente: {str(e)}")
        raise



    

@function_tool
async def venda_nova(
    cliente: ClienteData,
    produtos: List[Produto],
    total: float,
    qtde_parcelas_pagamento: int,
    formas_pagamento: List[FormaPagamento],
    observacao: str,
    negocio_idx: str= None,
    ):
    
    """
    O objetivo desta função é registrar a venda de produtos da Mary Kay. Para o registro da venda é necessário haver o ciente (comprador), os itens vendidos e as formas de pagamento.

    
    Não solicite todas as informações de uma vez. Solicite uma por vez, aguarde a resposta e somtne passe para a próxima.

    Comece pedindo a identificação do cliente: nome, telefone, email ou cpf/cnpj. Somente após ter esta informação, passe para o próximo passo ou solicite outras informaões necssárias para registrar uma venda.




    CLIENTE:
    -cliente. Carregar todos os dados do cliente (de todas as colunas)
    -Obrigatório
    -Solicite que o usuário informe um destes dados:o nome do cliente, cpf, cnpj, email ou telefone.  comece pedindo os dados de localização do cliente.

    Use a informação fornecida para localizar o cliente , e em seguida passa-lo para a função. Caso o cliente não seja encontrado, informe ao usuario que o cliente não foi encontrado e solicite que informe outra informação do cliente para uma nova busca.
    
    
    PRODUTOS:
    -Array de dicionarios com os produtos vendidos. cada produto tera os seguintes dados:
    -id
    -codigo
    -nome
    -valor
    -quantidade
    -Exemplo:
    [
        {"id": 1, nome: "Batom Coral","valor": 99.00, "quantidade": 1},
    ]
    -Obrigatório
    -Solicite o nome ou código do produto e faça uma busca Mary Kay . Caso não encontre, informe ao usuario que o produto não foi encontrado e solicite que informe o nome ou código do produto de outra forma.
    Após enconrar o produto, solicite ao usuário a quantidade, caso ele ainda não tenha informado. 
    Encontrado o produto, adicione a lista de produtos com o id, valor e quantidade.
    Pergunte se ele deseja adicionar mais serviços.
    IMPORTANTE: Ao fazer buscas por produtos, verifque  se o texto informado é encontrado total ou parcialmente nas colunas NOME ou CODIGO do produto.

    TOTAL:
    -Total da venda.
    -Obrigatório
    -Exemplo: 100, 200, 300
    -Neste momento calcule o total a ser pago pelos produtos ,soma de (valor * quantidade) de todos os produtos), e informe ao usuario.  


    QTDE_PARCELAS_PAGAMENTO:
    -Quantidade de parcelas de pagamento.
    -Exemplo: 1, 2, 3, etc.
    -Obrigatório . Solicite ao usuario a quantidade de parcelas de pagamento.
    -
    

    FORMAS_PAGAMENTO:
    -Array de dicionarios com as formas de pagamento. Cada forma de pagamento tera os seguintes dados:
    -nr .  Número sequencial da parcela. O número máximo sera o total de parcelas.
    -id. ID da forma de pagamento.
    -valor 
    -vencimento . Data no formato YYYY-MM-DD . Mas caso for necessário exibir ao usuario, exiba no formato DD/MM/YYYY.
    pago: e se esta pago ou não. Caso esteja pago, o valor será 1, caso não esteja pago, o valor será 0.
    -Exemplo: 
    {[
        {{"nr": 1, "id": 1, "valor": 100, "vencimento": "2024-01-01", "pago": 1}},
        {{"nr": 2, "id": 2, "valor": 100, "vencimento": "2024-01-01", "pago": 0}}
    ]}

            Estes são os ids das formas de pagamento:
            1 - Pix
            2 - Cartão de crédito
            3 - Boleto
            4 - Cheque
            5 - Transferência


    IMPORTANTE:
    - A soma dos valores das formas de pagamento tem que ser igual ao total da venda.


    OBSERVACAO:
    -Observação da venda.
    -default = ""
    -Opcional

    finalizada a venda, gere um resumo da venda no final. O resumo deverá ter um layout responsivo, mobile-first, formatação markdown, e ter a seguinte estrutura (Exemplo):
* RESUMO DA VENDA *
  ** CLIENTE **            
  Carlos Silva
  Telefone:(11) 99999-9999
  Email: <EMAIL>
** PRODUTOS **
*** Batom Coral ***
  Valor: 99,00
  Quantidade: 1
  Total: 99,00   
***  Perfume Romance ***
  Valor: 199,00
  Quantidade: 1
  Total: 199,00   
** TOTAL **
  Total: 298,00
  Parcelas: 1
** FORMAS DE PAGAMENTO **
 *** Parcela 1 ***
  Forma: Pix
  Valor: 298,00
  Vencimento: 24/05/2025
  Pago: Sim
** OBSERVAÇÃO **
  Observação da venda.

  IMPORTANTE:
  O resumo deve ser gerado apenas após a venda ser finalizada.  


  IMPORTANTE: Solicite uma informação por vez.Comece solicitando apenas a identificação de para qual cliente será a venda.
 
"""
    logger.info("===== venda_nova ======")
    logger.info("cliente %s", cliente)
    logger.info("produtos %s", produtos)
    logger.info("formas_pag amento %s", formas_pagamento)
    logger.info("total %s", total)
    logger.info("qtde_parcelas_pagamento %s", qtde_parcelas_pagamento)


    fuso_brasilia = pytz.timezone('America/Sao_Paulo')
    entrada = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")


    venda = {
        # linhas 61-76
        "IDX": generate_unique_id(),
        "CLIENTE_IDX": cliente.ID,
        "NEGOCIO_IDX": negocio_idx ,
        "TOTAL_PARCELAS": qtde_parcelas_pagamento,
        "TOTAL_RS": total,
        "DT": entrada,
        "OBSERVACAO": observacao,

    }

    logger.info(f"venda: {venda}")


    venda_id = await mysql.add("VENDA", venda)
    venda["ID"]= venda_id
    logger.info(f"venda_id: {venda_id}")  


    return {"success": True, "message": "Venda registrada com sucesso."}    


@function_tool
async def cliente_adicionar(
    nome: str, 
    telefone: str, 
    email: str, 
    cpf_cnpj: str, 
    cep: str,
    numero: str,
    complemento: str,
    negocio_idx: str
    ):
    """
    Adiciona um novo cliente no banco de dados.

    NOME: 
    -Nome completo do cliente.
    -Exemplo: Carlos Silva
    -Obrigatório

    TELEFONE: 
    -Telefone do cliente com ddd.
    -Exemplo: 31984784825
    -Obrigatório

    EMAIL: 
    -Email do cliente.
    -Exemplo: <EMAIL>
    -Opcional.

    CPF_CNPJ: 
    -CPF ou CNPJ do cliente.
    -Exemplos: 
        cpf: 50518151594 
        cnpj: 37753978000134
    -Opcional.

    CEP: 
    -CEP do endereço do cliente.
    -Exemplo: 31910720
    -Obrigatório
    
    """

    if not nome:
        return {"success": False, "message": "Nome é obrigatório."};
    

    if not telefone:
        return {"success": False, "message": "Telefone   é obrigatório."};

    if not email:
        return {"success": False, "message": "Email é obrigatório."};

    if not cpf_cnpj:
        return {"success": False, "message": "CPF ou CNPJ é obrigatório."};

    if not cep:
        return {"success": False, "message": "CEP é obrigatório."};

    if not numero:
        return {"success": False, "message": "Número é obrigatório."};


    telefone_id = await mysql.get_id(telefone, "CLIENTE", "TELEFONE", negocio_idx)
    print("telefone_id", telefone_id)
    if telefone_id > 0:
        return {"success": False, "message": "Telefone já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    email_id = await mysql.get_id(email, "CLIENTE", "EMAIL", negocio_idx)
    print("email_id", email_id)
    if email_id > 0:
        return {"success": False, "message": "Email já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    cpf_cnpj_ok = await cpf_cnpj_valido(cpf_cnpj)
    print("cpf_cnpj_ok", cpf_cnpj_ok)
    if not cpf_cnpj_ok:
        return {"success": False, "message": "CPF ou CNPJ inválido."}
    cpf_cnpj_id = await mysql.get_id(cpf_cnpj, "CLIENTE", "CPF_CNPJ", negocio_idx)
    print("cpf_cnpj_id", cpf_cnpj_id)
    if cpf_cnpj_id > 0:
        return {"success": False, "message": "CPF ou CNPJ já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    


    cpf_cnpj_id = await mysql.get_id(cpf_cnpj, "CLIENTE", "CPF_CNPJ", negocio_idx)
    print("cpf_cnpj_id", cpf_cnpj_id)
    if cpf_cnpj_id > 0:
        return {"success": False, "message": "CPF ou CNPJ já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}



    #print("vou carregar o enereço do cep " + cep)
    #logradouro,bairro,cidade,uf = ""
    endereco = await cep_data(cep)
    #print("endereco", endereco)

    if not endereco:
        return {"success": False, "message": "CEP inválido."}

    logradouro = endereco.get("logradouro", "")
    bairro = endereco.get("bairro", "")
    cidade = endereco.get("localidade", "")
    uf = endereco.get("uf", "")
    #print("logradouro", logradouro)
    #print("bairro", bairro)
    #print("cidade", cidade)
    #print("uf", uf)


    cliente = {
        "IDX": generate_unique_id(),
        "NEGOCIO_IDX": negocio_idx,
        "NOME": nome,
        "TELEFONE": telefone,
        "EMAIL": email,
        "NEGOCIO_IDX": negocio_idx,
        "CPF_CNPJ": cpf_cnpj,
        "CEP": cep,
        "LOGRADOURO": logradouro,
        "BAIRRO": bairro,
        "CIDADE": cidade,
        "UF": uf,
        "NUMERO": numero,
        "COMPLEMENTO": complemento
    }
    print("cliente", cliente)

    cliente = ClienteData(**cliente)

    result = await mysql.add("CLIENTE", cliente)

    
    return {"success": True,  "message": "Cliente adicionado com sucesso."}


@function_tool
async def produto_adicionar(
        id: int=0,
        nome: str="",
        codigo: str="",
        estoque: float=0,
        negocio_idx: str="",
        marykay_idx: str="",
    ):
    """
        Adiciona um novo produto da Mary Kay ao estoque da consultora. Esta função já verifica se o produto existe no catálogo da Mary Kay. Após o usuário fornecer os detalhes do produto a ser adicionado (nome, código opcional, estoque), utilize esta função diretamente para adicioná-lo, sem realizar uma consulta prévia com `consulta_mary_kay`.
    ID:
    -ID do produto da Mary Kay.
    -Exemplo: 105
    -Obrigatório.
    -Não mostra-lo nem solicita-lo ao usuário.

    NOME: 
    -Nome do produto.
    -Exemplo: Protetor Solar Mineral 
    -Opcional.

    CODIGO:
    -Código do produto.
    -Exemplo: - 1012335


    ESTOQUE:
    -Estoque do produto.
    -Exemplo: 10
    -Opcional. 

    
    """
    print("===== produto_adicionar ======")
    print("nome ", nome)
    print("id", id)
    print("codigo", codigo)
    print("estoque", estoque)
    print("negocio_idx", negocio_idx)
    print("marykay_idx", marykay_idx)

    
    produto_novo = {
        "IDX": generate_unique_id(),
        "ID_PAI": id,
        "NEGOCIO_IDX": negocio_idx,
        "CODIGO": codigo,
        "ESTOQUE": estoque,
    }    
    print("produto_novo", produto_novo)    
    result = await mysql.add("PRODUTO", produto_novo)
    print("result", result)     
    return {"success": True, "message": "Produto adicionado com sucesso."}    


    
@function_tool
async def consulta_mary_kay(query: str,marykay_idx: str):
    """
    Executa consultas e atualizações no banco de dados, com filtro obrigatório NEGOCIO_IDX = '{marykay_idx}'
    
    Esquema do banco de dados:
    - PRODUTO [tabela do banco de dados, catálogo geral de produtos Mary Kay]
        ID: int(10) [PK, AUTO_INCREMENT]
        NOME: varchar(100) [FULLTEXT]. Nome do produto.
        CODIGO: varchar(10) [UNIQUE, FULLTEXT]. O código do produto da Mary Kay.
        PRECO: decimal(8,2) default 0. Preço atual do produto no catálogo Mary Kay.
        PRECO_MAIOR: decimal(8,2) default 0. Preço original/de tabela do produto no catálogo Mary Kay.
        DESCONTO: decimal(10,2) default 0. Valor do desconto, calculado como (PRECO_MAIOR - PRECO).
        NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX]. Deve ser o 'marykay_idx' para este catálogo.
        DESCR: varchar(4000) default null. Descrição detalhada do produto.
        URL: varchar(255) default null. URL da página do produto.
        URL_IMAGEM: varchar(255) default null. URL da imagem principal do produto. é URL_IMAGEM (com M no final)
        VARIAVEL: int(1) default 0. Indica se o produto possui variação de cor (1 para sim, 0 para não).
        EXCLUIDO: int(1) default 0. Indica se o produto foi excluído (1 para sim, 0 para não).
    - VISAO_PRODUTO_COR [view ou tabela, representa as cores de um produto variável do catálogo Mary Kay]
        PRODUTO_CODIGO: varchar(10) [FK: PRODUTO.CODIGO, FULLTEXT]. Código do produto ao qual a cor pertence.
        COR_CODIGO: varchar(20). Código interno da cor.
        COR_NOME: varchar(100). Nome descritivo da cor (ex: "Beige C110 (Matte)").
        HEXADECIMAL: varchar(7) default null. Código hexadecimal da cor (ex: "#RRGGBB").
        NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX]. Deve ser o 'marykay_idx' para este catálogo de cores.
        (PK: PRODUTO_CODIGO, COR_CODIGO, NEGOCIO_IDX)
    - VISAO_PRODUTO_02 [view, une PRODUTO e COR por PRODUTO.COR_CODIGO = COR.CODIGO]
        Todas as colunas de PRODUTO (NOME e CODIGO com capacidade FULLTEXT herdada)
        COR_HEXADECIMAL
        COR_NOME
    A query deve respeitar as relações de chaves estrangeiras e incluir o filtro NEGOCIO_IDX (marykay_idx).
    IMPORTANTE: Toda query deve ter o filtro do NEGOCIO_IDX , sem exceção, por que só devem ser mostrados os dados da consultora que esta usando o sistema no momento.

    6. Ao ser solicitada uma lista de produtos do estoque da consultora, os seguintes dados devem ser retornados. Sua query na `VISAO_PRODUTO` deve buscar `NOME` (do produto base), `PRECO`, `PRECO_MAIOR`, `CODIGO` (da variação em estoque), `COR_NOME`, `COR_HEXADECIMAL`, `ESTOQUE`, `URL_IMAGEM` (do produto base) e quaisquer outros campos necessários para o agrupamento. **Ordene os resultados pelo NOME do produto e depois pelo COR_CODIGO para facilitar o agrupamento e a ordenação interna das cores.**

    Ao retornar uma lista de produtos, apresente os produtos. Para cada produto ou grupo de produto com o mesmo NOME e PRECO:
        NOME:
        [NOME do produto base]
        PREÇO:
        R$ [Preço do produto base]
        PROMOÇÃO: (exibir esta seção apenas se PRECO_MAIOR > PRECO)
        Desconto de R$ [PRECO_MAIOR - PRECO]

        // Lógica para Cor/Código:
        // Se houver múltiplas cores/variações para este NOME/PRECO (grupo):
        CORES DISPONÍVEIS NO ESTOQUE:
            [COR_NOME com fundo colorido pela COR_HEXADECIMAL e texto contrastante] - CÓDIGO: [CODIGO da variação em estoque] - ESTOQUE: [Quantidade em estoque desta variação]
            (repita para cada cor/variação disponível em estoque para este produto base)
        // Else (produto individual ou única cor):
        //   Se COR_NOME existir:
        COR: [COR_NOME com fundo colorido pela COR_HEXADECIMAL e texto contrastante] - CÓDIGO: [CODIGO da variação em estoque] - ESTOQUE: [Quantidade em estoque desta variação]
        //   Else (COR_NOME NÃO existe, mas é um produto individual que tem um CODIGO):
        CÓDIGO: [CODIGO da variação em estoque] // Este é o CODIGO do produto individual

        // Imagem:
        Se `URL_IMAGEM` estiver disponível, exiba **apenas** a imagem do produto (sem nenhum texto ou rótulo introdutório antes dela) de forma centralizada e responsiva usando HTML. Por exemplo:
        `<img src="[URL_IMAGEM]" alt="Imagem de [NOME do produto base]" style="display: block; margin-left: auto; margin-right: auto; max-width: 100%; height: auto;">`
    Este formato agrupado e responsivo é crucial para a usabilidade. Certifique-se de que cada informação principal (Nome do grupo, Preço do grupo, Cores/Códigos/Estoques individuais, Imagem) seja claramente apresentada. Só devem ser retornados produtos não excluídos (EXCLUIDO=0).
 
    IMPORTANTE:
    só podem ser retornados dados do negocio Mary kay, ou seja, com a coluna NEGOCIO_IDX = '{marykay_idx}'
    se entre os filtros da query não consta que o negócio tem o idx = '{marykay_idx}' então não retorne dados.

    """
    logger.info("===== consulta_mary_kay() =====")
    logger.info(f"marykay_idx: {marykay_idx}")
    logger.info(f"query: {query}")



    # Linha 284-288
    # Checa se o filtro do marykay_idx está presente na query. Se não estiver, retorna erro.
    if str(marykay_idx) not in query:
        return {"success": False, "message": "A consulta não foi permitida. tente de uma outra forma."}

    result = await mysql.query(query)
    logger.info(f"result: {result}")
    return {"success": True, "result": result}

@function_tool
async def consulta_atualiza_dados(query: str,negocio_idx: str):
  logger.info("===== consulta_atualiza_dados() =====")
  # Linha 228-229
  logger.info(f"query: {query}")
  logger.info(f"negocio_idx: {negocio_idx}")
  """
    Executa consultas e atualizações no banco de dados, com filtro obrigatório NEGOCIO_IDX = '{negocio_idx}'
    
    Esquema do banco de dados:
    - CLIENTE
      - ID: int(10) [PK, AUTO_INCREMENT]
      - NOME: varchar(100) [FULLTEXT]
      - CPF_CNPJ: varchar(14) default null [FULLTEXT]
      - TELEFONE: varchar(11) default null [FULLTEXT]
      - EMAIL: varchar(40) default null [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
      - CEP: varchar(8) default null 
      - LOGRADOURO: varchar(40) default null 
      - BAIRRO: varchar(40) default null [FULLTEXT]
      - CIDADE: varchar(40) default null [FULLTEXT]
      - UF: varchar(2) default null [FULLTEXT]
      - NUMERO: varchar(10) default null 
      - COMPLEMENTO: varchar(20) default null
      - EXCLUIDO: int(1) default 0. Indica se o item foi excluído (1 para sim, 0 para não).

      - PRODUTO 
        ID: int(10) [PK, AUTO_INCREMENT]
        ESTOQUE: decimal (8,2). Quantidade de produto em estoque;int(1) default 0. Indica se o item foi excluído (1 para sim, 0 para não).
        EXCLUIDO: int(1) default 0. Indica se o item foi excluido. 

      

    - VISAO_PRODUTO [view, representa o estoque/produtos da consultora, herdando dados do catálogo Mary Kay quando aplicável]
        ID: int(10). ID do registro do produto na tabela PRODUTO referente ao estoque da consultora.
        IDX: varchar(10). Identificador único do produto no sistema da consultora (do registro PRODUTO da consultora).
        CODIGO: varchar(10) [FULLTEXT]. Código oficial do produto Mary Kay (do registro PRODUTO da consultora).
        NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX]. NEGOCIO_IDX da consultora.
        ESTOQUE: decimal(8,2). Quantidade do produto em estoque da consultora.
        ID_PAI: int(10) default null. ID do produto pai no catálogo Mary Kay (referencia PRODUTO.ID do catálogo geral).
        NOME: varchar(100) [FULLTEXT]. Nome do produto (herdado do pai via ID_PAI se existir, senão do próprio registro da consultora).
        PRECO: decimal(8,2). Preço de venda (herdado do pai via ID_PAI se existir e o PRECO do filho for 0/NULL, senão do próprio registro da consultora).
        PRECO_MAIOR: decimal(8,2). Preço de catálogo/sugerido (herdado do pai via ID_PAI se existir, senão do próprio registro da consultora).
        DESCR: varchar(4000). Descrição do produto (herdada do pai via ID_PAI se existir e DESCR do filho for NULL/'', senão do próprio registro da consultora).
        URL: varchar(255). URL da página do produto (herdada do pai via ID_PAI se existir, senão do próprio registro da consultora).
        URL_IMAGEM: varchar(255). URL da imagem do produto (herdada do pai via ID_PAI se existir, senão do próprio registro da consultora).
        VARIAVEL: int(1). Indica se o produto tem variação de cor (herdado do pai via ID_PAI se existir, senão do próprio registro da consultora).
        EXCLUIDO: int(1) default 0. Indica se o item foi excluído (1 para sim, 0 para não).COR_NOME: varchar(100). Nome da cor do produto (herdada do pai via ID_PAI se existir, senão do próprio registro da consultora).IGO: varchar(20). Código interno da cor.
        COR_NOME: varchar(100) default null . Nome da cor.
        COR_HEXADECIMAL: varchar(7) default null. Código hexadecimal da cor.

        


    Regras de negócio para execução:

    1. A query deve respeitar as relações de chaves estrangeiras e incluir o filtro NEGOCIO_IDX.

    2. Toda query deve ter o filtro do NEGOCIO_IDX , sem exceção, por que só devem ser mostrados os dados da consultora que esta usando o sistema no momento. que estão no esquema do banco de dados.

    3. Só devem ser utilizadas nas querys as tabelas e colunas que estão no esquema do banco de dados.

    4. Se a query for para buscar produtos ou consultar informações de produtos ou relacionadas a eles, devem ser considerados apenas os produtos não excluidos, ou seja, com a coluna EXCLUIDO = 0.
    
    5. Se for solicitada a exclusão de um determinado item, seja cliente, produto ou outro,  a query deve setar a coluna EXCLUIDO com o valor 1. Não é para executar a exclusão real do item da tabela, apenas marcar como excluido.
 

    6. Ao ser solicitada uma lista de produtos do estoque da consultora, os seguintes dados devem ser retornados. Sua query na `VISAO_PRODUTO` deve buscar `NOME` (do produto base), `PRECO`, `PRECO_MAIOR`, `CODIGO` (da variação em estoque), `COR_NOME`, `COR_HEXADECIMAL`, `ESTOQUE`, `URL_IMAGEM` (do produto base) e quaisquer outros campos necessários para o agrupamento. **Ordene os resultados pelo NOME do produto e depois pelo COR_CODIGO para facilitar o agrupamento e a ordenação interna das cores.**
    Apresente os produtos agrupados por NOME. Para cada grupo de produto com o mesmo NOME e PRECO:
        NOME:
        [NOME do produto base]
        PREÇO:
        R$ [Preço do produto base]
        PROMOÇÃO: (exibir esta seção apenas se PRECO_MAIOR > PRECO)
        Desconto de R$ [PRECO_MAIOR - PRECO]
        CORES DISPONÍVEIS NO ESTOQUE:
            [COR_NOME com fundo colorido pela COR_HEXADECIMAL e texto contrastante] - CÓDIGO: [CODIGO da variação em estoque] - ESTOQUE: [Quantidade em estoque desta variação]
            (repita para cada cor/variação disponível em estoque para este produto base)
        Após a lista de cores, se `URL_IMAGEM` estiver disponível, exiba **apenas** a imagem do produto (sem nenhum texto ou rótulo introdutório antes dela) de forma centralizada e responsiva usando HTML. Por exemplo:
        `<img src="[URL_IMAGEM]" alt="Imagem de [NOME do produto base]" style="display: block; margin-left: auto; margin-right: auto; max-width: 100%; height: auto;">`
    Este formato agrupado e responsivo é crucial para a usabilidade. Certifique-se de que cada informação principal (Nome do grupo, Preço do grupo, Cores/Códigos/Estoques individuais, Imagem) seja claramente apresentada. Só devem ser retornados produtos não excluídos (EXCLUIDO=0).
    
    """
  result = await mysql.query(query)
  logger.info(f"result: {result}")
  return {"success": True, "result": result}
    
def extrair_texto_puro(conteudo: str) -> str:
    """
    Extrai o texto puro de um conteúdo, removendo formatação HTML e Markdown,
    e eliminando espaços extras e linhas em branco.

    Args:
        conteudo (str): O texto com formatação a ser limpo.

    Returns:
        str: O texto puro sem formatação.
    """
    # 1. Remover tags HTML (ex: <span>, <img>, <p>, <div>, etc.)
    # Usa a expressão regular '<[^>]+>' para encontrar qualquer coisa que comece com '<',
    # tenha um ou mais caracteres que não sejam '>', e termine com '>'.
    texto_sem_html = re.sub(r'<[^>]+>', '', conteudo)

    # 2. Remover formatação Markdown (negrito, itálico, etc.)
    # Esta regex remove **negrito**, __sublinhado__, *itálico*, _itálico_, ~~riscado~~, `código`.
    # O '+' garante que um ou mais desses caracteres consecutivos sejam removidos.
    texto_sem_markdown = re.sub(r'(\*\*|__|~~|`|\*|_)+', '', texto_sem_html)

    # 3. Processar linhas:
    # - Divide o texto em linhas.
    # - Para cada linha, remove espaços em branco do início e do fim (strip()).
    # - Filtra as linhas vazias, garantindo que apenas linhas com conteúdo permaneçam.
    linhas_limpas = [linha.strip() for linha in texto_sem_markdown.splitlines() if linha.strip()]

    # 4. Unir as linhas limpas mantendo as quebras de linha originais.
    #texto_puro_final = "".join(linhas_limpas)
    texto_puro_final = "//".join(linhas_limpas)

    return texto_puro_final

if __name__ == "__main__":
    import asyncio

    #area de testes
    async def teste_executa_agente():
        texto = "Me mostre o produto de codigo 10022958"
        last_chat_image = "";
        PLATAFORMA_URL = "http://localhost/gptalk"
        modelo = "1234567890" #deepseek
        #modelo = "0987654321" #gemini-2.0-flash
        data = {
            "usuario_nome": "Carlos Silva",
            "negocio_idx": "4015743441",
            "mensagem": texto,
            "modelo": modelo,
            "imagem": last_chat_image,
            "plataforma_url": PLATAFORMA_URL
        };


        result = await agent_run(data)
        print("result", result)

    async def busca_aproximada():
        search_term = "caloi"
        table_name = "PRODUTO_MARCA"
        column_name = "NOME"
        negocio_idx = "4015743441"
        result = await mysql.get_first_id_by_search(search_term, table_name, column_name, negocio_idx)
        print("result", result)

    async def teste_get_produtos_mary_kay():
        negocio_idx = "5544332211"  # Exemplo de IDX de negócio
        resultado = await get_produtos_mary_kay(negocio_idx)
        print("Resultado get_produtos_mary_kay:", resultado)

    async def teste_venda_nova():
        cliente_dict = {
            "ID": 1,
            "NOME": "João da Silva",
            "CPF_CNPJ": "12345678901",
            "TELEFONE": "3191149571",
            "EMAIL": "<EMAIL>",
            "CEP": "12345678",
            "LOGRADOURO": "Rua das Flores",
            "BAIRRO": "Bairro das Flores",
            "CIDADE": "São Paulo",
            "UF": "SP"
        }
        produtos_dict = [
            {
                "id": 1,
                "nome": "Produto 1",
                "valor": 100.00,
                "quantidade": 1
            },
        ]
        formas_pagamento_dict = [
            {
                "nr": 1,
                "id": 1,
                "valor": 100.00,
                "vencimento": "2024-01-01",
                "pago": 1
            },
        ]
        observacao = "Observação da venda"
        negocio_idx = "4015743441"

        # Converta para os modelos esperados
        cliente = ClienteData(**cliente_dict)
        produtos = [Produto(**p) for p in produtos_dict]
        formas_pagamento = [FormaPagamento(**f) for f in formas_pagamento_dict]

        result = await venda_nova(cliente, produtos, 100.00, 1, formas_pagamento, observacao, negocio_idx)
        print("result", result)
        
    # asyncio.run(atualiza_logo())
    # asyncio.run(adiciona_veiculo())
    # asyncio.run(busca_aproximada())
    # asyncio.run(cliente_novo())
    # asyncio.run(valida_cpf_cnpj())
    # Teste para get_produtos_mary_kay
    # Linhas 1-4: Definindo o teste para a função get_produtos_mary_kay
    # asyncio.run(teste_get_produtos_mary_kay())
    # asyncio.run(teste_executa_agente())
    asyncio.run(teste_venda_nova())
    

    
