# Arquivo: agent_whisper.py
from pywhispercpp.model import Model
import tempfile
import os
import numpy as np
import subprocess
import json
from datetime import datetime
from fastapi import APIRouter, UploadFile, File, HTTPException
from .agent_logger import AgentLogger
import logging

router = APIRouter()
logger = AgentLogger()

# Configurar logging para silenciar logs do pywhispercpp
logging.getLogger('pywhispercpp').setLevel(logging.WARNING)
logging.getLogger('_pywhispercpp').setLevel(logging.WARNING)

class AgentWhisper:
    _instance = None
    _model_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AgentWhisper, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._model_loaded:
            self.model = None
            self._load_model()
            AgentWhisper._model_loaded = True
    
    def _load_model(self):
        """Carrega o modelo Whisper usando pywhispercpp"""
        try:
            import os
            
            # Método mais eficaz para silenciar logs C++ do whisper.cpp
            # Usando o parâmetro específico da biblioteca
            devnull = open(os.devnull, 'w')
            
            # Modelo 'base' tem melhor capacidade para português que o tiny
            self.model = Model(
                'base',  # Melhor equilíbrio entre velocidade e precisão para português
                # Forçar português para evitar tradução
                language="pt",
                translate=False,
                # Silenciar logs do whisper.cpp redirecionando para devnull
                redirect_whispercpp_logs_to=devnull,  # Redireciona logs C++ para devnull
                print_progress=False,
                print_realtime=False,
                print_timestamps=False
            )
            
        except Exception as e:
            logger.error(f"Erro ao carregar modelo Whisper: {str(e)}")
            raise e
    
    def transcribe(self, file_path: str):
        """Transcreve um arquivo de áudio usando pywhispercpp"""
        try:
            if not self.model:
                raise HTTPException(status_code=500, detail="Modelo não carregado")
            
            # 📊 LOG 1: Início do processo com timestamp preciso
            inicio = datetime.now()
            timestamp_inicio = inicio.strftime("%H:%M:%S:%f")[:-3]  # Remove últimos 3 dígitos para ter milissegundos
            
            # Tentar transcrição direta primeiro
            segments = self.model.transcribe(file_path)
            
            # Se não retornou segmentos, pode ser áudio muito curto - tentar padding simples
            if not segments:
                logger.info("Áudio muito curto, tentando com padding...")
                try:
                    # Carregar áudio e adicionar silêncio simples
                    audio_data = self.model._load_audio(file_path)
                    sample_rate = 16000
                    
                    # Adicionar 0.3 segundos de silêncio 
                    padding_samples = int(0.3 * sample_rate)
                    silence = np.zeros(padding_samples, dtype=np.float32)
                    padded_audio = np.concatenate([audio_data, silence])
                    
                    # Salvar temporariamente
                    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                        temp_path = temp_file.name
                    
                    # Simular salvamento (pywhispercpp não tem save direto)
                    # Vamos tentar transcrever direto com array
                    segments = self.model.transcribe(padded_audio)
                    
                    
                except Exception as e:
                    logger.warning(f"Falha no padding: {e}. Retornando vazio.")
                    segments = []
            
            # 📊 LOG 2: Fim do processo com timestamp preciso
            fim = datetime.now()
            timestamp_fim = fim.strftime("%H:%M:%S:%f")[:-3]  # Remove últimos 3 dígitos para ter milissegundos
            logger.info(f"✅ FIM TRANSCRIÇÃO - Processo concluído em: {timestamp_fim}")
            
            # 📊 LOG 3: Tempo total decorrido
            tempo_decorrido = fim - inicio
            total_segundos = tempo_decorrido.total_seconds()
            milissegundos = int(tempo_decorrido.microseconds / 1000)
            segundos = int(total_segundos)
            logger.info(f"⏱️ TEMPO DECORRIDO - Total: {segundos}.{milissegundos:03d}s ({total_segundos:.3f}s)")
            
            # Combinar todos os segmentos em um texto único
            transcription = ""
            detected_language = "pt"  # Fixo português para acelerar
            
            if segments:
                transcription = " ".join([segment.text.strip() for segment in segments])
                logger.info(f"Transcrição extraída com sucesso: {len(transcription)} caracteres")
                
                # Idioma já fixado como português - sem detecção automática para acelerar
                logger.info(f"Idioma configurado: {detected_language}")
            else:
                logger.warning("Nenhum segmento foi retornado pela transcrição")
            
            logger.info("Transcrição concluída com sucesso")
            
            return {
                "transcription": transcription.strip(),
                "language": detected_language
            }
        except Exception as e:
            logger.error(f"Erro na transcrição: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Erro na transcrição: {str(e)}")

# Instância global da classe
agent_whisper = AgentWhisper()

@router.post("/transcribe")
async def transcribe_audio(file: UploadFile = File(...)):
    """Endpoint para transcrever arquivo de áudio"""
    try:
        # Validar tipo de arquivo
        allowed_extensions = ['.mp3', '.wav', '.m4a', '.flac', '.ogg']
        file_extension = os.path.splitext(file.filename)[1].lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Formato não suportado. Use: {', '.join(allowed_extensions)}"
            )
        
        # Salvar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Transcrever
            result = agent_whisper.transcribe(temp_file_path)
            return {
                "success": True,
                "filename": file.filename,
                "transcription": result["transcription"],
                "language": result["language"]
            }
        finally:
            # Limpar arquivo temporário
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no endpoint transcribe: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


if __name__ == "__main__":
    import asyncio
    import os
    
    async def teste_transcricao_simples():
        """Teste simples da classe AgentWhisper"""
        print("🎯 Teste de transcrição simples...")
        
        # Caminho do arquivo de teste
        audio_file_path = r"C:\Users\<USER>\Audio\_temp\olabdia.mp3"
        
        if not os.path.exists(audio_file_path):
            #print(f"❌ Arquivo não encontrado: {audio_file_path}")
            #print("💡 Ajuste o caminho no código para testar")
            return False
        
        try:
            # Criar instância e testar
            whisper_agent = AgentWhisper()
            resultado = whisper_agent.transcribe(audio_file_path)
            
            #print("✅ Transcrição realizada com sucesso!")
            #print(f"📝 Texto: {resultado['transcription']}")
            #print(f"🌍 Idioma: {resultado['language']}")
            return True
            
        except Exception as e:
            #print(f"❌ Erro no teste: {str(e)}")
            return False
    
    async def teste_modelo_carregado():
        """Teste se o modelo foi carregado corretamente"""
        #print("🔄 Testando carregamento do modelo...")
        
        try:
            whisper_agent = AgentWhisper()
            if whisper_agent.model is not None:
                #print("✅ Modelo carregado com sucesso!")
                return True
            else:
                #print("❌ Modelo não foi carregado")
                return False
                
        except Exception as e:
            #print(f"❌ Erro ao carregar modelo: {str(e)}")
            return False
    
    async def teste_arquivo_inexistente():
        """Teste com arquivo que não existe"""
        print("🚫 Testando arquivo inexistente...")
        
        try:
            whisper_agent = AgentWhisper()
            resultado = whisper_agent.transcribe("arquivo_que_nao_existe.mp3")
            print("❌ Deveria ter dado erro!")
            return False
            
        except Exception as e:
            print(f"✅ Erro esperado capturado: {type(e).__name__}")
            return True
    
    async def executar_todos_testes():
        """Executa todos os testes"""
        print("🎙️  TESTES INTERNOS - AGENTWHISPER 🎙️")
        print("=" * 50)
        
        testes = [
            ("Carregamento do modelo", teste_modelo_carregado()),
            ("Transcrição simples", teste_transcricao_simples()),
            ("Arquivo inexistente", teste_arquivo_inexistente())
        ]
        
        sucessos = 0
        total = len(testes)
        
        for nome, teste in testes:
            print(f"\n📋 {nome}:")
            try:
                if await teste:
                    sucessos += 1
            except Exception as e:
                print(f"❌ Erro inesperado: {str(e)}")
        
        print("\n" + "=" * 50)
        print(f"📊 RESULTADO: {sucessos}/{total} testes passaram")
        
        if sucessos == total:
            print("🎉 Todos os testes passaram!")
        else:
            print("⚠️  Alguns testes falharam")
        
        return sucessos == total
    
    # Executar testes
    asyncio.run(executar_todos_testes())