from datetime import datetime, timedelta
from typing import Union
import calendar

def _calculate_periods(data_inicio: datetime, data_termino: datetime, period_type: str) -> list:
        """
        Calcula todos os período
        Retorna uma lista de dicionários com informações de cada período.
        """
        periodos = []
        data_atual = data_inicio
        while data_atual <= data_termino:
            periodo = formatar_periodo(data_atual, period_type, data_inicio)
            periodo_inicio, periodo_fim = get_period_start_end(data_atual, period_type, data_inicio, data_termino)
            
            # Se o período ultrapassar data_termino, ajusta o fim
            if periodo_fim > data_termino:
                periodo_fim = data_termino
                
            # Só adiciona o período se a data inicial for menor ou igual à data final
            if periodo_inicio <= data_termino:
                periodos.append({
                    'periodo': periodo,
                    'inicio': periodo_inicio.strftime('%d/%m/%Y'),
                    'termino': periodo_fim.strftime('%d/%m/%Y')
                })
    
            # Avança para o próximo período
            if period_type == 'D':
                data_atual += timedelta(days=1)
            elif period_type == 'S':
                data_atual += timedelta(days=7)
            elif period_type == 'M':
                # Avança um mês
                if data_atual.month == 12:
                    data_atual = data_atual.replace(year=data_atual.year + 1, month=1)
                else:
                    data_atual = data_atual.replace(month=data_atual.month + 1)
            else:  # A
                data_atual = data_atual.replace(year=data_atual.year + 1)
                
        return periodos


def get_period_type(data_inicio: Union[str, datetime], data_termino: Union[str, datetime]):
        """Determina o tipo de período baseado no intervalo de datas"""
        # Converte para datetime se for string
        if isinstance(data_inicio, str):
            inicio = datetime.strptime(data_inicio, '%Y-%m-%d')
        else:
            inicio = data_inicio

        if isinstance(data_termino, str):
            termino = datetime.strptime(data_termino, '%Y-%m-%d')
        else:
            termino = data_termino

        dias = (termino - inicio).days + 1

        if dias <= 14:
            return 'D'  # Diário
        elif dias <= 90:
            return 'S'  # Semanal
        elif dias <= 365:
            return 'M'  # Mensal
        else:
            return 'A'  # Anual

def formatar_periodo(data: datetime, tipo_periodo: str, data_inicio: datetime = None) -> str:
        """Formata o período de acordo com o tipo
        D: DD/MM/YYYY
        S: NN (número sequencial da semana no período)
        M: mmm/YY (3 primeiras letras do mês em português)
        A: YYYY
        """
        if isinstance(data, str):
            try:
                data = datetime.strptime(data, '%Y-%m-%d')
            except ValueError:
                try:
                    data = datetime.strptime(data, '%d/%m/%Y')
                except ValueError:
                    return None

        if tipo_periodo == 'D':
            return data.strftime('%d_%m_%Y')
        elif tipo_periodo == 'S':
            # Se tiver data_inicio, calcula o número da semana a partir dela
            if data_inicio:
                dias_desde_inicio = (data - data_inicio).days
                numero_semana = (dias_desde_inicio // 7) + 1
            else:
                numero_semana = data.isocalendar()[1]
            return f"{numero_semana:02d}"
        elif tipo_periodo == 'M':
            meses = ['jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set', 'out', 'nov', 'dez']
            return f"{meses[data.month-1]}/{str(data.year)[2:]}"
        elif tipo_periodo == 'A':
            return str(data.year)
        return None

def get_period_start_end(data: datetime, period_type: str, data_inicio: datetime = None, data_termino: datetime = None) -> tuple:
    if period_type == 'S':
        # If we have a data_inicio, use it as the start date for the first period
        if data_inicio and data <= data_inicio + timedelta(days=6):
            inicio = data_inicio
        else:
            # Calculate start of week but don't go before data_inicio
            inicio = data - timedelta(days=data.weekday())
            if data_inicio and inicio < data_inicio:
                inicio = data_inicio
        
        fim = inicio + timedelta(days=6)
        # Adjust end date if it exceeds data_termino
        if data_termino and fim > data_termino:
            fim = data_termino
        return inicio, fim
    if period_type == 'D':
        return data, data
    elif period_type == 'M':
        if data_inicio and data.month == data_inicio.month and data.year == data_inicio.year:
            inicio = data_inicio
        else:
            inicio = data.replace(day=1)
        
        ultimo_dia = calendar.monthrange(data.year, data.month)[1]
        fim = data.replace(day=ultimo_dia)
        if data_termino and fim > data_termino:
            fim = data_termino
        return inicio, fim
    else:  # A
        if data_inicio and data.year == data_inicio.year:
            inicio = data_inicio
        else:
            inicio = data.replace(month=1, day=1)
        fim = data.replace(month=12, day=31)
        if data_termino and fim > data_termino:
            fim = data_termino
        return inicio, fim
