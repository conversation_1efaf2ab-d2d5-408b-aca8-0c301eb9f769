import asyncmy
import os
from datetime import datetime, date
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field 
from typing import List, Literal 
from .agent_llm import LLM
from ..cache import cache
from ..functions.util import generate_unique_id
from .agent_openai import OpenAi
from agents import function_tool
import pytz
from .agent_logger import AgentLogger
from .agent_secret import Secret
from threading import Lock
import json

router = APIRouter()
logger = AgentLogger()
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
secret = Secret()
oai = OpenAi()

# Modelos Pydantic para resolver problema de additionalProperties
class ConsultarTabelasResponse(BaseModel):
    class Config:
        extra = 'forbid'

class ConsultarEstruturaTabelaRequest(BaseModel):
    tabela: str
    
    class Config:
        extra = 'forbid'

class ConsultarEstruturaTabelaResponse(BaseModel):
    class Config:
        extra = 'forbid'

class ExecutarSQLConsultaRequest(BaseModel):
    query: str
    params: dict = None
    
    class Config:
        extra = 'forbid'

class ExecutarSQLConsultaResponse(BaseModel):
    class Config:
        extra = 'forbid'

class ExecutarSQLModificacaoRequest(BaseModel):
    query: str
    params: dict = None
    
    class Config:
        extra = 'forbid'

class ExecutarSQLModificacaoResponse(BaseModel):
    class Config:
        extra = 'forbid'

class BuscarDadosRequest(BaseModel):
    tabela: str
    colunas: str = "*"
    filtros: list = None
    ordenacao: str = None
    
    class Config:
        extra = 'forbid'

class BuscarDadosResponse(BaseModel):
    class Config:
        extra = 'forbid'

class InserirDadosRequest(BaseModel):
    tabela: str
    dados: dict
    
    class Config:
        extra = 'forbid'

class InserirDadosResponse(BaseModel):
    class Config:
        extra = 'forbid'

class AtualizarDadosRequest(BaseModel):
    tabela: str
    dados: dict
    
    class Config:
        extra = 'forbid'

class AtualizarDadosResponse(BaseModel):
    class Config:
        extra = 'forbid'


class Mysql:
    def __init__(self, database=None, table=None, user=None, password=None, host=None):
        self.host = host if host else secret.GPTALK_01_MYSQL_HOST
        self.password = password if password else secret.GPTALK_01_MYSQL_PASSWORD
        self.user = user if user else secret.GPTALK_01_MYSQL_USER
        self.table = table
        self.database = database if database else secret.GPTALK_01_MYSQL_DATABASE
        

    async def conecta(self):
        """
        Conecta ao banco MySQL com tratamento melhorado de erros
        """
        try:
            
            # Tentativa de conexão
            self.conn = await asyncmy.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
            )
            
            return self.conn
            
        except asyncmy.errors.OperationalError as e:
            error_code, error_msg = e.args
            logger.error(f"❌ ERRO OPERACIONAL MySQL: ({error_code}) {error_msg}")
            
            # Tratamento específico para diferentes códigos de erro
            if error_code == 4151:
                logger.error("🚨 CONTA MYSQL BLOQUEADA!")
                logger.error("💡 Possíveis soluções:")
                logger.error("   1. Verifique se a conta está bloqueada: SELECT User, Host, account_locked FROM mysql.user WHERE User = %s;", self.user)
                logger.error("   2. Desbloqueie a conta: ALTER USER '%s'@'%%' ACCOUNT UNLOCK;", self.user)
                logger.error("   3. Execute: FLUSH PRIVILEGES;")
                logger.error("   4. Verifique tentativas de login: SHOW STATUS LIKE 'Connection_errors%%';")
                logger.error("   5. Considere reiniciar o serviço MySQL")
                
            elif error_code == 1045:
                logger.error("🚨 ACESSO NEGADO - Credenciais incorretas")
                logger.error("💡 Verifique usuário e senha no arquivo .env")
                
            elif error_code == 2003:
                logger.error("🚨 SERVIDOR MySQL INACESSÍVEL")
                logger.error("💡 Verifique se o servidor MySQL está rodando")
                
            elif error_code == 1049:
                logger.error("🚨 DATABASE NÃO EXISTE")
                logger.error(f"💡 Verifique se o database '{self.database}' existe")
                
            else:
                logger.error(f"🚨 ERRO OPERACIONAL DESCONHECIDO: {error_code}")
            
            # Relançar a exceção para que o código chamador possa tratá-la
            raise e
            
        except asyncmy.errors.InterfaceError as e:
            logger.error(f"❌ ERRO DE INTERFACE MySQL: {str(e)}")
            logger.error("💡 Problema de configuração ou conectividade")
            raise e
            
        except asyncmy.errors.DatabaseError as e:
            logger.error(f"❌ ERRO DE BANCO DE DADOS: {str(e)}")
            raise e
            
        except Exception as e:
            logger.error(f"❌ ERRO DESCONHECIDO na conexão MySQL: {str(e)}")
            logger.error(f"   Tipo do erro: {type(e).__name__}")
            raise e

    async def fetch(self, columns: str, table: str, filters: list, andOr=None, order=None):
        logger.info(f"📋 fetch() chamado: columns={columns}, table={table}, filters={filters}")
        if isinstance(filters, list):
            andOr = andOr if andOr else "AND"
            filters = self.create_filters(filters, andOr)
        query = f"SELECT {columns} FROM {table} WHERE {filters}"
        if order:
            query = f"{query} order by {order}"
        logger.info(f"Query construída: {query}")
        result = await self.query(query)
        return result

    async def query(self, query: str, format='object', params=None):
        conn = await self.conecta()
        async with conn.cursor() as cursor:
            try:
                # Executar a query com ou sem parâmetros
                if params is not None:
                    await cursor.execute(query, params)
                else:
                    await cursor.execute(query)

                await conn.commit()  # Só necessário para INSERT/UPDATE/DELETE
                if cursor.description is None:
                    return []

                result = await cursor.fetchall()
                if not result:
                    return []

                columns = [col[0] for col in cursor.description]

                if format == "tuple":
                    return result

                resultObject = []
                for row in result:
                    row_dict = {}
                    for i, value in enumerate(row):
                        if isinstance(value, datetime):
                            row_dict[columns[i]] = value.strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(value, date):
                            row_dict[columns[i]] = value.strftime('%Y-%m-%d')
                        else:
                            row_dict[columns[i]] = value
                    resultObject.append(row_dict)

                return resultObject
            except Exception as e:
                # CORRIGIDO: Log do erro e relança a exceção em vez de mascarar
                logger.error(f"❌ ERRO DE BANCO DE DADOS: {str(e)}")
                logger.error(f"Query: {query}")
                logger.error(f"Params: {params}")
                
                # Relançar a exceção para que o código chamador possa tratá-la adequadamente
                raise e
            finally:
                await conn.ensure_closed()
   
    async def get_id(self, search_term: str, table_name: str, column_name: str, negocio_idx: str = None):
        """
        Busca exata pelo termo na coluna fornecida e retorna o ID correspondente.
        """
        query = f"""
        SELECT ID, {column_name}
        FROM {table_name}
        WHERE {column_name} = %s AND NEGOCIO_IDX = %s
        LIMIT 1
        """
        params = (search_term, negocio_idx)
        result = await self.query(query, format='object', params=params)
        if result:
            return result[0]['ID']
        return 0

    async def get_items_by_search(self, search_term: str, table_name: str, column_name: str, return_columns:str, negocio_idx: str = None):
        """
        Busca itens pelo termo informado, retornando todos os itens encontrados em ordem de prioridade/relevância.
        """
        logger.info(f"🔍 get_items_by_search: term='{search_term}', table='{table_name}', column='{column_name}'")
        # Gerar um padrão LIKE mais flexível
        if search_term:
            first_letter = search_term[0].lower()
            similar_letters = {'c': '[cs]', 's': '[cs]'}  # Adicione outros mapeamentos conforme necessário
            like_pattern = similar_letters.get(first_letter, first_letter) + search_term[1:] + '%'
        else:
            like_pattern = '%'

        # Query semelhante à get_first_id_by_search, mas retorna todos os itens relevantes
        query = f"""
        SELECT {return_columns}, 
               MATCH({column_name}) AGAINST(%s IN BOOLEAN MODE) AS relevancia
        FROM {table_name} 
        WHERE (MATCH({column_name}) AGAINST(%s IN BOOLEAN MODE)
           OR SUBSTRING(SOUNDEX({column_name}), 2) = SUBSTRING(SOUNDEX(%s), 2)
           OR LOWER({column_name}) LIKE LOWER(%s)) AND NEGOCIO_IDX = %s
        ORDER BY relevancia DESC, {column_name}
        """
        logger.info(f"Query construída: {query}")
        boolean_search_term = f'"{search_term}"'
        params = (boolean_search_term, boolean_search_term, search_term, like_pattern, negocio_idx)

        result = await self.query(query, format='object', params=params)
        return result if result else []

    async def get_first_id_by_search(self, search_term: str, table_name: str, column_name: str, negocio_idx: str = None) :
        # Gerar um padrão LIKE mais flexível
        if search_term:
            first_letter = search_term[0].lower()
            similar_letters = {'c': '[cs]', 's': '[cs]'}  # Adicione outros mapeamentos conforme necessário
            like_pattern = similar_letters.get(first_letter, first_letter) + search_term[1:] + '%'
        else:
            like_pattern = '%'

        # Query da Solução 5 com tabela e coluna dinâmicas
        query = f"""
        SELECT ID, {column_name}, 
               MATCH({column_name}) AGAINST(%s IN BOOLEAN MODE) AS relevancia
        FROM {table_name} 
        WHERE (MATCH({column_name}) AGAINST(%s IN BOOLEAN MODE)
           OR SUBSTRING(SOUNDEX(NOME), 2) = SUBSTRING(SOUNDEX(%s), 2)
           OR LOWER({column_name}) LIKE LOWER(%s)) AND NEGOCIO_IDX = %s
        ORDER BY relevancia DESC, {column_name}
        LIMIT 1
        """

        # Parâmetros para os %s
        boolean_search_term = f'"{search_term}"' 
        params = (boolean_search_term, boolean_search_term, search_term, like_pattern, negocio_idx)

        # Executar a query usando a função query adaptada
        result = await self.query(query, format='object', params=params)

        # Retornar o ID do primeiro resultado, se houver
        if result:
            return result[0]['ID']
        return 0

    async def add(self, table: str, data: dict):
        conn = await self.conecta()
        async with conn.cursor() as cursor:
            fields = ', '.join(data.keys())
            values_placeholders = ', '.join(['%s'] * len(data))
            query = f"INSERT INTO {table} ({fields}) VALUES ({values_placeholders})"
            try:
                await cursor.execute(query, tuple(data.values()))
                await conn.commit()
                inserted_id = cursor.lastrowid
                if inserted_id:
                    #logger.info(f"✅ Registro inserido com sucesso na tabela {table}. ID: {inserted_id}")
                    return inserted_id
                else:
                    logger.error(f"❌ Falha ao obter o ID do registro inserido na tabela {table}")
                    logger.error(f"Query: {query}")
                    logger.error(f"Data: {data}")
                    return None
            except Exception as e:
                logger.error(f"❌ ERRO AO INSERIR NA TABELA {table}: {str(e)}")
                logger.error(f"Tipo do erro: {type(e).__name__}")
                logger.error(f"Query: {query}")
                logger.error(f"Data: {data}")
                logger.error(f"Values: {tuple(data.values())}")
                
                # Mantém o comportamento original: retorna None em caso de erro
                return None
            finally:
                await conn.ensure_closed()

    async def add_with_detailed_response(self, table: str, data: dict):
        """
        Versão da função add() que retorna informações detalhadas sobre o resultado.
        """
        conn = await self.conecta()
        async with conn.cursor() as cursor:
            fields = ', '.join(data.keys())
            values_placeholders = ', '.join(['%s'] * len(data))
            query = f"INSERT INTO {table} ({fields}) VALUES ({values_placeholders})"
            try:
                await cursor.execute(query, tuple(data.values()))
                await conn.commit()
                inserted_id = cursor.lastrowid
                if inserted_id:
                    #logger.info(f"✅ Registro inserido com sucesso na tabela {table}. ID: {inserted_id}")
                    return {
                        "success": True,
                        "id": inserted_id,
                        "error": None,
                        "message": f"Registro inserido com sucesso na tabela {table}. ID: {inserted_id}"
                    }
                else:
                    logger.error(f"❌ Falha ao obter o ID do registro inserido na tabela {table}")
                    logger.error(f"Query: {query}")
                    logger.error(f"Data: {data}")
                    return {
                        "success": False,
                        "id": None,
                        "error": "Falha ao obter o ID do registro inserido",
                        "message": f"Falha ao obter o ID do registro inserido na tabela {table}"
                    }
            except Exception as e:
                error_msg = f"ERRO AO INSERIR NA TABELA {table}: {str(e)}"
                logger.error(f"❌ {error_msg}")
                logger.error(f"Tipo do erro: {type(e).__name__}")
                logger.error(f"Query: {query}")
                logger.error(f"Data: {data}")
                logger.error(f"Values: {tuple(data.values())}")
                
                return {
                    "success": False,
                    "id": None,
                    "error": str(e),
                    "message": error_msg
                }
            finally:
                await conn.ensure_closed()

    async def last_id(self, tabela):
        atual_id_result = await self.query(f"SELECT MAX(ID) as max_id FROM {tabela}")
        if atual_id_result and atual_id_result[0]['max_id'] is not None:
            atual_id = atual_id_result[0]['max_id']
            novo_auto_increment = atual_id + 1
            await self.query(f"ALTER TABLE {tabela} AUTO_INCREMENT = {novo_auto_increment}")
        else:
            await self.query(f"ALTER TABLE {tabela} AUTO_INCREMENT = 1")
        return atual_id

    async def update(self, table: str, data: dict):
        conn = await self.conecta()
        async with conn.cursor() as cursor:
            fields_values = ', '.join([f"{key} = %s" for key in data if key != 'ID'])
            values = tuple(data[key] for key in data if key != 'ID')
            values += (data['ID'],)
            query = f"UPDATE {table} SET {fields_values} WHERE ID = %s"
            try:
                await cursor.execute(query, values)
                await conn.commit()
                updated_id = data['ID']
                return True
            except asyncmy.errors.Error as e:
                logger.error(f"❌ Erro ao executar UPDATE: {e}")
                logger.error(f"Query: {query}")
                logger.error(f"Values: {values}")
                raise e  # Relançar exceção
            finally:
                await conn.ensure_closed()

    @staticmethod
    def create_filters(filters, andOr):
        formatted_filters = [filter.strip() for filter in filters if '=' in filter]
        return f' {andOr} '.join(formatted_filters)


# ===============================================================================
# CLASSE AGENTE MYSQL INTELIGENTE
# ===============================================================================

class AgentMysql:
    def __init__(
        self,
        name="mysql",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None):
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx
        
        self.instructions = f"""
        Você é um assistente especializado em MySQL que ajuda a converter consultas em linguagem natural 
        para queries SQL e executá-las no banco de dados relacional.

        Suas principais responsabilidades:
        1. **Interpretar consultas em linguagem natural** e convertê-las para SQL
        2. **Executar queries SQL** no banco MySQL usando as ferramentas apropriadas
        3. **Consultar estruturas de tabelas** quando necessário para entender o schema
        4. **Apresentar resultados** de forma clara e organizada

        ## 🛠️ **Ferramentas Disponíveis:**

        ### `consultar_tabelas_mysql()`
        - Use para listar todas as tabelas disponíveis no banco
        - Essencial para entender a estrutura do banco antes de criar queries

        ### `consultar_estrutura_tabela(tabela)`
        - Use para entender a estrutura de uma tabela específica
        - Mostra colunas, tipos de dados, chaves primárias, índices

        ### `executar_sql_consulta(query, params)`
        - Para queries de **consulta** (SELECT, SHOW, DESCRIBE, EXPLAIN)
        - Use quando precisar buscar ou listar dados

        ### `executar_sql_modificacao(query, params)`
        - Para queries de **modificação** (INSERT, UPDATE, DELETE, ALTER, CREATE)
        - **CUIDADO:** Estas operações modificam permanentemente o banco

        ### `buscar_dados_inteligente(tabela, colunas, filtros, ordenacao)`
        - Busca inteligente usando os métodos da classe Mysql
        - Permite busca com filtros complexos e ordenação

        ### `inserir_dados_mysql(tabela, dados)`
        - Insere dados em uma tabela usando os métodos seguros da classe Mysql
        - Retorna o ID do registro inserido

        ### `atualizar_dados_mysql(tabela, dados)`
        - Atualiza dados em uma tabela usando os métodos seguros da classe Mysql
        - Requer que os dados contenham um campo ID

        ## 👤 **Contexto da Sessão:**
        - Usuário: {usuario_nome}
        - Data/Hora: {data_hora}
        - Negócio: {negocio_idx}

        Responda sempre em português brasileiro e seja didático nas explicações.
        """

    def get_instructions(self):
        return self.instructions


# ===============================================================================
# FUNÇÕES DE FERRAMENTAS (TOOLS) PARA O AGENTE MYSQL
# ===============================================================================

@function_tool
async def consultar_tabelas_mysql():
    """
    Consulta e retorna todas as tabelas disponíveis no banco MySQL.
    """
    try:
        logger.info("=== 🔍 consultar_tabelas_mysql() FUNCTION TOOL ===")
        
        mysql = Mysql()
        query = "SHOW TABLES"
        resultado = await mysql.query(query)
        
        tabelas = []
        for row in resultado:
            for key, value in row.items():
                if 'table' in key.lower():
                    tabelas.append(value)
                    break
        
        return {
            "success": True,
            "tabelas": tabelas,
            "total": len(tabelas),
            "message": f"Encontradas {len(tabelas)} tabelas no banco MySQL"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao consultar tabelas MySQL: {e}")
        return {
            "success": False,
            "message": f"Erro ao consultar tabelas: {str(e)}"
        }


@function_tool
async def consultar_estrutura_tabela(tabela: str):
    """
    Consulta e retorna a estrutura de uma tabela específica no MySQL.
    """
    try:
        logger.info(f"=== 🔍 consultar_estrutura_tabela({tabela}) FUNCTION TOOL ===")
        
        mysql = Mysql()
        query = f"DESCRIBE {tabela}"
        resultado = await mysql.query(query)
        
        estrutura = []
        for row in resultado:
            estrutura.append({
                "Campo": row.get("Field"),
                "Tipo": row.get("Type"),
                "Nulo": row.get("Null"),
                "Chave": row.get("Key"),
                "Padrão": row.get("Default"),
                "Extra": row.get("Extra")
            })
        
        return {
            "success": True,
            "tabela": tabela,
            "estrutura": estrutura,
            "total_colunas": len(estrutura),
            "message": f"Estrutura da tabela {tabela} obtida com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao consultar estrutura da tabela {tabela}: {e}")
        return {
            "success": False,
            "tabela": tabela,
            "message": f"Erro ao consultar estrutura da tabela {tabela}: {str(e)}"
        }


        
        
    except Exception as e:
        logger.error(f"❌ Erro ao executar query de consulta: {e}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de consulta: {str(e)}"
        }


    except Exception as e:
        logger.error(f"❌ Erro ao executar query de modificação: {e}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de modificação: {str(e)}"
        }


@function_tool
async def buscar_dados_inteligente(tabela: str, colunas: str = "*", filtros: list = None, ordenacao: str = None):
    """
    Busca dados usando os métodos inteligentes da classe Mysql.
    """
    try:
        logger.info(f"=== 🔍 buscar_dados_inteligente() FUNCTION TOOL ===")
        logger.info(f"📊 Tabela: {tabela}")
        logger.info(f"📋 Colunas: {colunas}")
        logger.info(f"🔍 Filtros: {filtros}")
        logger.info(f"📈 Ordenação: {ordenacao}")
        
        mysql = Mysql()
        
        # Preparar filtros (se não fornecidos, usar filtro padrão)
        if not filtros:
            filtros = ["1 = 1"]  # Filtro que sempre é verdadeiro
        
        # Usar método fetch da classe Mysql
        resultado = await mysql.fetch(colunas, tabela, filtros, order=ordenacao)
        
        logger.info(f"✅ Busca inteligente executada com sucesso")
        logger.info(f"📊 Resultado: {len(resultado) if isinstance(resultado, list) else 'N/A'} registros")
        
        return {
            "success": True,
            "tabela": tabela,
            "colunas": colunas,
            "filtros": filtros,
            "ordenacao": ordenacao,
            "resultado": resultado,
            "total_registros": len(resultado) if isinstance(resultado, list) else 0,
            "message": f"Busca inteligente executada com sucesso na tabela {tabela}"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro na busca inteligente: {e}")
        return {
            "success": False,
            "tabela": tabela,
            "message": f"Erro na busca inteligente: {str(e)}"
        }






# ===============================================================================
# FUNÇÕES UTILITÁRIAS PARA GERENCIAMENTO DE CONVERSAS
# ===============================================================================

def get_conversa_key(negocio_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{negocio_idx}_{agente_nome}_{conversa_idx}"


def find_active_conversation(negocio_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{negocio_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


# ===============================================================================
# ENDPOINTS DA API
# ===============================================================================

@router.post("/send/text")
async def send_text_mysql(data: dict):
    """
    Endpoint principal para processar mensagens de texto para o agente MySQL
    Converte linguagem natural em queries SQL e executa no banco
    """
    logger.info("===== send_text_mysql() =====")
    logger.info(f"data: {data}")
    
    try:
        # Extrair dados necessários
        mensagem = data.get("mensagem", "")
        modelo = data.get("modelo", "")
        usuario_nome = data.get("usuario_nome", "MySQL User")
        negocio_idx = data.get("negocio_idx", "")
        
        if not mensagem:
            return {"success": False, "message": "Mensagem é obrigatória"}
        
        if not modelo:
            return {"success": False, "message": "Modelo é obrigatório"}
        
        # Verificar se existe conversa ativa
        conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "mysql")
        
        if not conversa_idx:
            conversa_idx = generate_unique_id()
            historico_mensagens = []
        
        # Adicionar mensagem do usuário ao histórico
        historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)
        
        # Criar agente MySQL
        agentMysql = AgentMysql(
            negocio_idx=negocio_idx,
            usuario_nome=usuario_nome
        )
        
        # Carregar modelo
        llm = LLM()
        model = llm.get_model_idx(modelo)
        logger.info(f"Modelo carregado: {model}")
        
        instructions = agentMysql.get_instructions()
        
        # Configurar agente
        agente_config = {
            "name": "MySQL Query Agent",
            "instructions": instructions,
            "model": model,
            "tools": [
                consultar_tabelas_mysql,
                consultar_estrutura_tabela,
                executar_sql_consulta,
                executar_sql_modificacao,
                buscar_dados_inteligente,
                inserir_dados_mysql,
                atualizar_dados_mysql,
            ],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }
        
        # Criar o agente
        agente_obj = await oai.agent_create(**agente_config)
        logger.info("Agente MySQL criado com sucesso")
        
        # Processar com o agente usando streaming
        async def event_stream():
            resposta_completa = ""
            nonlocal historico_mensagens
            try:
                async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                    
                    resposta_completa += chunk_str
                    yield chunk_str
                
                # Adicionar resposta ao histórico
                historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
                
                # Salvar histórico no cache
                cache_key = get_conversa_key(negocio_idx, "mysql", conversa_idx)
                cache[cache_key] = historico_mensagens
                
            except Exception as stream_error:
                logger.error(f"Erro durante o streaming: {str(stream_error)}")
                yield f"Erro: {str(stream_error)}"
        
        return StreamingResponse(event_stream(), media_type="text/plain")
        
    except Exception as e:
        logger.error(f"Erro no endpoint send_text_mysql: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


@router.post("/drop/messages")
async def drop_messages_mysql(data: dict):
    """
    Endpoint para limpar cache de conversas do agente MySQL
    """
    logger.info("===== 🗑️🚮 MySQL drop_messages() 🗑️🚮 =====")
    logger.info(f"data: {data}")
    
    negocio_idx = data.get("negocio_idx", "")
    
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    try:
        chaves_removidas = 0
        chaves_para_remover = []
        
        # Buscar chaves do cache que correspondem ao agente MySQL
        for key in list(cache.keys()):
            # Filtrar por conversas do agente mysql
            if key.startswith(f"conversa_{negocio_idx}_mysql_"):
                chaves_para_remover.append(key)
        
        # Remover as chaves identificadas do cache
        for key in chaves_para_remover:
            try:
                del cache[key]
                chaves_removidas += 1
                logger.info(f"Chave removida do cache: {key}")
            except KeyError:
                logger.warning(f"Chave já não existe no cache: {key}")
        
        logger.info(f"Cache limpo: {chaves_removidas} conversas removidas")
        
        return {
            "success": True,
            "message": f"Cache de conversas MySQL limpo com sucesso! {chaves_removidas} conversas removidas.",
            "detalhes": {
                "conversas_removidas": chaves_removidas,
                "filtro_usado": f"negocio_idx: {negocio_idx}"
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {str(e)}")
        return {
            "success": False,
            "message": f"Erro ao limpar cache: {str(e)}"
        }


if __name__ == "__main__":
    import asyncio

    async def main():
        from .agent_user import User
        from .agent_converter import Converter
        mql = Mysql()
        converter = Converter()
        usuario = User()
        telefone = "5531984784825"
        nome = "Carlos Silva"
        canal_id = "123"
        data_hora = converter.time_now_to_date_hour()
        id = await mql.add("USUARIO", {"TELEFONE": telefone, "NOME": nome, "ORIGEM": canal_id, "DATA_CAD": data_hora})
        result = await mql.query(f"SELECT ID, NOME, DATA_CAD FROM USUARIO WHERE ID = {id}")

    asyncio.run(main())