import asyncio
import logging
from agents import function_tool

print("🔵 MÓDULO conversa_nova.py CARREGADO")

@function_tool
async def conversa_nova(conversa_idx: str):
    print("\n🔵 FUNÇÃO conversa_nova CHAMADA")
    print(f"🔵 Parâmetro conversa_idx: {conversa_idx}")
    """
    Encerra a conversa atual e prepara para uma nova conversa.
    
    Use esta função quando o usuário solicitar:
    - "nova conversa"
    - "reiniciar conversa" 
    - "começar de novo"
    - "encerrar conversa atual"
    - "limpar histórico"
    - "apagar conversa"
    - "fechar conversa"
    - "sair"
    
    Esta função encerra a conversa atual no banco de dados e permite que uma nova conversa seja iniciada automaticamente.
    Não requer mensagem inicial ou ID específico - apenas execute quando o usuário solicitar.
    
    Args:
        conversa_idx: ID único da conversa atual que será encerrada
    """
    from api.agent.agent import nova_conversa
    
    print("==================================================")
    print("=========  FUNÇÃO NOVA CONVERSA CHAMADA ==========")
    print("@@@@@ conversa_nova()", conversa_idx)
    await nova_conversa(conversa_idx=conversa_idx)
    return {
        "status": "success",
        "message": "Conversa atual encerrada com sucesso. Pronto para uma nova conversa!",
    }

if __name__ == "__main__":
    async def testa_conversa_nova():
        await conversa_nova("123")
        print("Conversa nova encerrada")

    asyncio.run(testa_conversa_nova())
    #Execução
    #py -m api.agent.agent.functions.conversa_nova