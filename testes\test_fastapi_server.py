#!/usr/bin/env python3
"""
Servidor FastAPI simples para testar o endpoint de transcrição do AgentWhisper
"""

from fastapi import FastAPI
import uvicorn
import sys
import os

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Criar aplicação FastAPI
app = FastAPI(
    title="AgentWhisper Test API",
    description="API de teste para transcrição de áudio com Whisper",
    version="1.0.0"
)

# Incluir o router do AgentWhisper
try:
    from api.agent.agent_whisper import router as whisper_router
    app.include_router(whisper_router, prefix="/api/agent", tags=["whisper"])
    print("✅ Router do AgentWhisper incluído com sucesso!")
except ImportError as e:
    print(f"❌ Erro ao importar router: {e}")
    sys.exit(1)

@app.get("/")
async def root():
    """Endpoint raiz de teste"""
    return {
        "message": "AgentWhisper Test API está funcionando!",
        "endpoints": {
            "transcribe": "/api/agent/transcribe (POST)"
        }
    }

@app.get("/health")
async def health_check():
    """Health check do servidor"""
    return {"status": "ok", "service": "AgentWhisper API"}

def main():
    """Executar servidor FastAPI"""
    print("🚀 Iniciando servidor FastAPI...")
    print("📍 URL: http://localhost:8000")
    print("📖 Docs: http://localhost:8000/docs")
    print("🔗 Endpoint: http://localhost:8000/api/agent/transcribe")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False
    )

if __name__ == "__main__":
    main() 