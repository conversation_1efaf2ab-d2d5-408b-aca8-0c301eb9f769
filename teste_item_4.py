#!/usr/bin/env python3
"""
TESTE 4: Testando com a instância real MegaAPI
"""
import requests

# Novas configurações da instância real
url = 'https://apistart03.megaapi.com.br/rest/instance/megastart-MV1loESU4sN'
headers = {'Authorization': 'Bearer MV1loESU4sN'}

print('🔍 TESTE 4: Status da instância REAL')
print(f'URL: {url}')
print(f'Token: MV1loESU4sN')
print('-' * 50)

try:
    response = requests.get(url, headers=headers)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response completa: {data}')
        
        if data.get('error') == False:
            print('✅ SUCESSO: Instância real funcionando!')
            print('🎯 API REST está autorizada!')
        else:
            print('❌ FALHA: Erro na instância real')
            print(f'Erro: {data.get("message", "Erro desconhecido")}')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 4:')
print('Se STATUS CODE = 200 e error = False: ✅ SUCESSO')
print('Caso contrário: ❌ FALHA') 