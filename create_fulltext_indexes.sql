-- =============================================
-- SCRIPT PARA CRIAÇÃO DE ÍNDICES FULLTEXT
-- Baseado na análise dos arquivos da pasta /api/agent
-- =============================================

-- 1. TABELA PRODUTO - Colunas: NOME, CODIGO, DESCR
-- Usado em: agent_marykay.py, agent_product.py, agent_assistenciamk.py
ALTER TABLE PRODUTO ADD FULLTEXT(NOME);
ALTER TABLE PRODUTO ADD FULLTEXT(CODIGO);
ALTER TABLE PRODUTO ADD FULLTEXT(DESCR);

-- 2. TABELA COR - Colunas: NOME
-- Usado em: agent_marykay.py, agent_assistenciamk.py  
ALTER TABLE COR ADD FULLTEXT(NOME);

-- 3. TABELA SERVICO - Colunas: NOME, CODIGO, DESCR
-- Usado em: agent_service.py
ALTER TABLE SERVICO ADD FULLTEXT(NOME);
ALTER TABLE SERVICO ADD FULLTEXT(CODIGO);
ALTER TABLE SERVICO ADD FULLTEXT(DESCR);

-- 4. TABELA CATEGORIA_PRODUTO - Coluna: NOME
-- Usado em: agent_product.py (CATEGORIA_NOME)
ALTER TABLE CATEGORIA_PRODUTO ADD FULLTEXT(NOME);

-- 5. TABELA CATEGORIA_SERVICO - Coluna: NOME  
-- Usado em: agent_service.py (CATEGORIA_NOME)
ALTER TABLE CATEGORIA_SERVICO ADD FULLTEXT(NOME);

-- 6. TABELA CLIENTE - Colunas: NOME, CPF_CNPJ, TELEFONE, EMAIL, BAIRRO, CIDADE, UF
-- Usado em: agent_customer.py, agent_oficinatech.py, agent_mktcontact.py
ALTER TABLE CLIENTE ADD FULLTEXT(NOME);
ALTER TABLE CLIENTE ADD FULLTEXT(CPF_CNPJ);
ALTER TABLE CLIENTE ADD FULLTEXT(TELEFONE);
ALTER TABLE CLIENTE ADD FULLTEXT(EMAIL);
ALTER TABLE CLIENTE ADD FULLTEXT(BAIRRO);
ALTER TABLE CLIENTE ADD FULLTEXT(CIDADE);
ALTER TABLE CLIENTE ADD FULLTEXT(UF);

-- 7. TABELA NEGOCIO - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE NEGOCIO ADD FULLTEXT(NOME);

-- 8. TABELA VEICULO_CATEGORIA - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE VEICULO_CATEGORIA ADD FULLTEXT(NOME);

-- 9. TABELA PRODUTO_CATEGORIA - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE PRODUTO_CATEGORIA ADD FULLTEXT(NOME);

-- 10. TABELA PRODUTO_MARCA - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE PRODUTO_MARCA ADD FULLTEXT(NOME);

-- 11. TABELA PRODUTO_TIPO - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE PRODUTO_TIPO ADD FULLTEXT(NOME);

-- 12. TABELA PRODUTO_COR - Coluna: NOME
-- Usado em: agent_oficinatech.py
ALTER TABLE PRODUTO_COR ADD FULLTEXT(NOME);

-- 13. TABELA CONTABIL_CONTA - Colunas: NOME, CODIGO
-- Usado em: agent_accountant.py
ALTER TABLE CONTABIL_CONTA ADD FULLTEXT(NOME);
ALTER TABLE CONTABIL_CONTA ADD FULLTEXT(CODIGO);

-- 14. TABELA FINANCEIRO_CONTAS_LANCAMENTO - Colunas: DOCUMENTO, HISTORICO
-- Usado em: agent_accountant.py
ALTER TABLE FINANCEIRO_CONTAS_LANCAMENTO ADD FULLTEXT(DOCUMENTO);
ALTER TABLE FINANCEIRO_CONTAS_LANCAMENTO ADD FULLTEXT(HISTORICO);

-- 15. TABELA AGENTE - Colunas: NOME, IDX
-- Usado em: agent_atm.py
ALTER TABLE AGENTE ADD FULLTEXT(NOME);
ALTER TABLE AGENTE ADD FULLTEXT(IDX);

-- 16. TABELAS DE ORDEM DE SERVIÇO - Usado em: agent_workOrder.py
-- Para ITEM_NOME (se for uma tabela específica)
-- Verificar se existe tabela específica para itens de OS

-- 17. ÍNDICES COMPOSTOS para buscas mais complexas
-- Para busca em múltiplas colunas de PRODUTO
ALTER TABLE PRODUTO ADD FULLTEXT(NOME, CODIGO, DESCR);

-- Para busca em múltiplas colunas de SERVICO
ALTER TABLE SERVICO ADD FULLTEXT(NOME, CODIGO, DESCR);

-- Para busca em múltiplas colunas de CLIENTE
ALTER TABLE CLIENTE ADD FULLTEXT(NOME, EMAIL, TELEFONE);

-- Para busca em PRODUTO com COR (se houver relação direta)
-- Verificar estrutura das VIEWs: VISAO_PRODUTO_MARYKAY, VISAO_PRODUTO_CONSULTORA

-- =============================================
-- VERIFICAÇÃO DOS ÍNDICES CRIADOS
-- =============================================

-- Verificar índices FULLTEXT criados
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND INDEX_TYPE = 'FULLTEXT'
ORDER BY TABLE_NAME, INDEX_NAME;

-- =============================================
-- NOTAS IMPORTANTES:
-- =============================================

/*
1. Estes índices FULLTEXT otimizarão as consultas que usam:
   - LIKE '%termo%' 
   - MATCH() AGAINST() 

2. Para tabelas muito grandes, a criação dos índices pode demorar.

3. Os índices FULLTEXT consomem espaço adicional no disco.

4. Após criar os índices, considere modificar as consultas para usar:
   MATCH(coluna) AGAINST('termo' IN BOOLEAN MODE) 
   ao invés de LIKE '%termo%'

5. Para buscas em VIEWs, os índices nas tabelas base serão utilizados automaticamente.

6. Algumas tabelas podem não existir no seu banco. Neste caso, 
   comente ou remova as linhas correspondentes.

7. Verifique se alguma coluna já possui índice FULLTEXT antes de executar.
*/ 