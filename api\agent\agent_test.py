
esquema = {
    "database": "neo4j",
    "node_types": [
        {
            "label": "Agente",
            "properties": [
                {
                    "name": "estilo_conversa",
                    "type": "STRING"
                },
                {
                    "name": "identidade", 
                    "type": "STRING"
                },
                {
                    "name": "idx",
                    "type": "STRING"
                },
                {
                    "name": "instrucoes",
                    "type": "STRING"
                },
                {
                    "name": "llm_padrao",
                    "type": "STRING"
                },
                {
                    "name": "nome",
                    "type": "STRING"
                },
                {
                    "name": "personalidade",
                    "type": "STRING"
                }
            ]
        },
        {
            "label": "Negocio",
            "properties": [
                {
                    "name": "bairro",
                    "type": "STRING"
                },
                {
                    "name": "cep",
                    "type": "STRING"
                },
                {
                    "name": "cidade",
                    "type": "STRING"
                },
                {
                    "name": "cpf_cnpj",
                    "type": "STRING"
                },
                {
                    "name": "complemento",
                    "type": "STRING"
                },
                {
                    "name": "email",
                    "type": "STRING"
                },
                {
                    "name": "excluido",
                    "type": "INTEGER"
                },
                {
                    "name": "idx",
                    "type": "STRING"
                },
                {
                    "name": "logradouro",
                    "type": "STRING"
                },
                {
                    "name": "nome",
                    "type": "STRING"
                },
                {
                    "name": "numero",
                    "type": "STRING"
                },
                {
                    "name": "razao_social",
                    "type": "STRING"
                },
                {
                    "name": "telefone",
                    "type": "STRING"
                },
                {
                    "name": "uf",
                    "type": "STRING"
                },
                {
                    "name": "usuarioIdx",
                    "type": "STRING"
                },
            ]
        },
        {
            "label": "Produto",
            "properties": [
                {
                    "name": "codigo",
                    "type": "STRING"
                },
                {
                    "name": "descricao",
                    "type": "STRING"
                },
                {
                    "name": "estoque",
                    "type": "INTEGER"
                },
                {
                    "name": "excluido",
                    "type": "INTEGER"
                },
                {
                    "name": "idx",
                    "type": "STRING"
                },
                {
                    "name": "nome",
                    "type": "STRING"
                },
                {
                    "name": "preco",
                    "type": "FLOAT|STRING"
                }
            ]
        },
        {
            "label": "Servico",
            "properties": [
                {
                    "name": "categoria_id",
                    "type": "STRING"
                },
                {
                    "name": "codigo",
                    "type": "STRING"
                },
                {
                    "name": "descricao",
                    "type": "STRING"
                },
                {
                    "name": "excluido",
                    "type": "INTEGER"
                },
                {
                    "name": "id",
                    "type": "INTEGER"
                },
                {
                    "name": "idx",
                    "type": "STRING"
                },
                {
                    "name": "negocio_idx",
                    "type": "STRING"
                },
                {
                    "name": "nome",
                    "type": "STRING"
                },
                {
                    "name": "preco",
                    "type": "FLOAT|STRING"
                }
            ]
        },
        {
            "label": "Conta",
            "properties": [
                {
                    "name": "appIdx",
                    "type": "STRING"
                },
                {
                    "name": "dataAtualizacao",
                    "type": "DATE_TIME"
                },
                {
                    "name": "dataCriacao",
                    "type": "DATE_TIME"
                },
                {
                    "name": "data_cadastro",
                    "type": "DATE"
                },
                {
                    "name": "email",
                    "type": "STRING"
                },
                {
                    "name": "excluido",
                    "type": "INTEGER"
                },
                {
                    "name": "idx",
                    "type": "STRING"
                },
                {
                    "name": "nivelId",
                    "type": "INTEGER"
                },
                {
                    "name": "nome",
                    "type": "STRING"
                },
                {
                    "name": "planoId",
                    "type": "INTEGER"
                },
                {
                    "name": "planoInicio",
                    "type": "DATE"
                },
                {
                    "name": "senha",
                    "type": "STRING"
                },
                {
                    "name": "token",
                    "type": "STRING"
                },
                {
                    "name": "url",
                    "type": "STRING"
                },
                {
                    "name": "usuario",
                    "type": "STRING"
                },
                {
                    "name": "usuário",
                    "type": "STRING"
                }
            ]
        }
    ]
}