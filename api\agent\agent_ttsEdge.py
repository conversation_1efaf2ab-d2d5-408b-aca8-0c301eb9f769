# Arquivo: agent_tts.py
import edge_tts
import tempfile
import os
import asyncio
from datetime import datetime
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
try:
    from .agent_logger import AgentLogger
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from agent_logger import AgentLogger

router = APIRouter()
logger = AgentLogger()

class TTSRequest(BaseModel):
    text: str
    voice: str = "pt-BR-ThalitaNeural"  # Voz padrão da Thalita
    rate: str = "+0%"  # Velocidade normal
    volume: str = "+0%"  # Volume normal
    pitch: str = "+0Hz"  # Tom normal

class AgentTTSEdge:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AgentTTSEdge, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.voice = "pt-BR-ThalitaNeural"  # Voz padrão da Thalita
            self.rate = "+0%"
            self.volume = "+0%"
            self.pitch = "+0Hz"
            AgentTTSEdge._initialized = True
    
    async def synthesize(self, text: str, voice: str = None, rate: str = None, volume: str = None, pitch: str = None):
        """Sintetiza texto em áudio usando Edge-TTS"""
        try:
            # Use parâmetros fornecidos ou defaults da classe
            voice_to_use = voice or self.voice
            rate_to_use = rate or self.rate
            volume_to_use = volume or self.volume
            pitch_to_use = pitch or self.pitch
            
            # 📊 LOG 1: Início do processo com timestamp preciso
            inicio = datetime.now()
            timestamp_inicio = inicio.strftime("%H:%M:%S:%f")[:-3]
            #logger.info(f"🎙️ INÍCIO SÍNTESE TTS - Texto chegou em: {timestamp_inicio}")
            #logger.info(f"Sintetizando texto: '{text[:100]}{'...' if len(text) > 100 else ''}'")
            #logger.info(f"Configurações - Voz: {voice_to_use}, Rate: {rate_to_use}, Volume: {volume_to_use}, Pitch: {pitch_to_use}")
            
            # Criar arquivo temporário para o áudio
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_file_path = temp_file.name
            
            try:
                # Configurar comunicação com Edge-TTS
                communicate = edge_tts.Communicate(
                    text=text,
                    voice=voice_to_use,
                    rate=rate_to_use,
                    volume=volume_to_use,
                    pitch=pitch_to_use
                )
                
                # Salvar o áudio no arquivo temporário
                await communicate.save(temp_file_path)
                
                # 📊 LOG 2: Fim do processo com timestamp preciso
                fim = datetime.now()
                timestamp_fim = fim.strftime("%H:%M:%S:%f")[:-3]
                logger.info(f"✅ FIM SÍNTESE TTS - Processo concluído em: {timestamp_fim}")
                
                # 📊 LOG 3: Tempo total decorrido
                tempo_decorrido = fim - inicio
                total_segundos = tempo_decorrido.total_seconds()
                milissegundos = int(tempo_decorrido.microseconds / 1000)
                segundos = int(total_segundos)
                logger.info(f"⏱️ TEMPO DECORRIDO - Total: {segundos}.{milissegundos:03d}s ({total_segundos:.3f}s)")
                
                # Verificar se o arquivo foi criado com sucesso
                if not os.path.exists(temp_file_path) or os.path.getsize(temp_file_path) == 0:
                    raise Exception("Arquivo de áudio não foi gerado ou está vazio")
                
                file_size = os.path.getsize(temp_file_path)
                logger.info(f"Arquivo de áudio gerado com sucesso: {file_size} bytes")
                
                logger.info("Síntese TTS concluída com sucesso")
                
                return {
                    "success": True,
                    "audio_file_path": temp_file_path,
                    "voice_used": voice_to_use,
                    "text_length": len(text),
                    "file_size": file_size,
                    "processing_time": total_segundos
                }
                
            except Exception as e:
                # Limpar arquivo temporário em caso de erro
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                raise e
                
        except Exception as e:
            logger.error(f"Erro na síntese TTS: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Erro na síntese TTS: {str(e)}")
    
    async def synthesize_to_bytes(self, text: str, voice: str = None, rate: str = None, volume: str = None, pitch: str = None):
        """Sintetiza texto e retorna os bytes do áudio diretamente"""
        try:
            # Use parâmetros fornecidos ou defaults da classe
            voice_to_use = voice or self.voice
            rate_to_use = rate or self.rate
            volume_to_use = volume or self.volume
            pitch_to_use = pitch or self.pitch
            
            #logger.info(f"Sintetizando para bytes - Voz: {voice_to_use}")
            
            # Configurar comunicação com Edge-TTS
            communicate = edge_tts.Communicate(
                text=text,
                voice=voice_to_use,
                rate=rate_to_use,
                volume=volume_to_use,
                pitch=pitch_to_use
            )
            
            # Coletar os bytes do áudio
            audio_data = b""
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            logger.info(f"Áudio sintetizado em bytes: {len(audio_data)} bytes")
            
            return {
                "success": True,
                "audio_data": audio_data,
                "voice_used": voice_to_use,
                "text_length": len(text),
                "audio_size": len(audio_data)
            }
            
        except Exception as e:
            logger.error(f"Erro na síntese para bytes: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Erro na síntese para bytes: {str(e)}")

    async def get_available_voices(self):
        """Lista todas as vozes disponíveis do Edge-TTS"""
        try:
            logger.info("Buscando vozes disponíveis...")
            voices = await edge_tts.list_voices()
            
            # Filtrar apenas vozes brasileiras
            brazilian_voices = [
                voice for voice in voices 
                if voice["Locale"].startswith("pt-BR")
            ]
            
            logger.info(f"Encontradas {len(brazilian_voices)} vozes brasileiras")
            
            return {
                "success": True,
                "total_voices": len(voices),
                "brazilian_voices": len(brazilian_voices),
                "voices": brazilian_voices
            }
            
        except Exception as e:
            logger.error(f"Erro ao buscar vozes: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Erro ao buscar vozes: {str(e)}")

# Instância global da classe
agent_tts_edge = AgentTTSEdge()

@router.post("/synthesize")
async def synthesize_text(request: TTSRequest):
    """Endpoint para sintetizar texto em áudio"""
    try:
        # Validar entrada
        if not request.text.strip():
            raise HTTPException(status_code=400, detail="Texto não pode estar vazio")
        
        if len(request.text) > 10000:  # Limite de 10k caracteres
            raise HTTPException(status_code=400, detail="Texto muito longo (máximo 10.000 caracteres)")
        
        # Sintetizar
        result = await agent_tts_edge.synthesize(
            text=request.text,
            voice=request.voice,
            rate=request.rate,
            volume=request.volume,
            pitch=request.pitch
        )
        
        # Ler o arquivo gerado
        audio_file_path = result["audio_file_path"]
        
        try:
            with open(audio_file_path, "rb") as audio_file:
                audio_data = audio_file.read()
            
            # Converter para base64 para retorno JSON
            import base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            return {
                "success": True,
                "text": request.text,
                "voice": result["voice_used"],
                "audio_base64": audio_base64,
                "audio_size": len(audio_data),
                "processing_time": result["processing_time"]
            }
            
        finally:
            # Limpar arquivo temporário
            if os.path.exists(audio_file_path):
                os.unlink(audio_file_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no endpoint synthesize: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")

@router.get("/voices")
async def list_voices():
    """Endpoint para listar vozes disponíveis"""
    try:
        return await agent_tts_edge.get_available_voices()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no endpoint voices: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")

@router.post("/synthesize-stream")
async def synthesize_stream(request: TTSRequest):
    """Endpoint para síntese em bytes (sem arquivo temporário)"""
    try:
        # Validar entrada
        if not request.text.strip():
            raise HTTPException(status_code=400, detail="Texto não pode estar vazio")
        
        # Sintetizar para bytes
        result = await agent_tts_edge.synthesize_to_bytes(
            text=request.text,
            voice=request.voice,
            rate=request.rate,
            volume=request.volume,
            pitch=request.pitch
        )
        
        # Converter para base64
        import base64
        audio_base64 = base64.b64encode(result["audio_data"]).decode('utf-8')
        
        return {
            "success": True,
            "text": request.text,
            "voice": result["voice_used"],
            "audio_base64": audio_base64,
            "audio_size": result["audio_size"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no endpoint synthesize-stream: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


if __name__ == "__main__":
    import asyncio
    
    async def teste_sintese_simples():
        """Teste simples da síntese TTS"""
        print("🎯 Teste de síntese simples...")
        
        try:
            # Criar instância e testar
            tts_agent = AgentTTSEdge()
            resultado = await tts_agent.synthesize("Olá! Eu sou a Thalita, sua assistente virtual brasileira.")
            
            print("✅ Síntese realizada com sucesso!")
            print(f"📁 Arquivo: {resultado['audio_file_path']}")
            print(f"🎙️ Voz: {resultado['voice_used']}")
            print(f"📊 Tamanho: {resultado['file_size']} bytes")
            print(f"⏱️ Tempo: {resultado['processing_time']:.3f}s")
            
            # Limpar arquivo de teste
            if os.path.exists(resultado['audio_file_path']):
                os.unlink(resultado['audio_file_path'])
                print("🧹 Arquivo temporário limpo")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro no teste: {str(e)}")
            return False
    
    async def teste_vozes_disponiveis():
        """Teste para listar vozes disponíveis"""
        print("🔍 Testando lista de vozes...")
        
        try:
            tts_agent = AgentTTSEdge()
            resultado = await tts_agent.get_available_voices()
            
            print("✅ Vozes listadas com sucesso!")
            print(f"📊 Total de vozes: {resultado['total_voices']}")
            print(f"🇧🇷 Vozes brasileiras: {resultado['brazilian_voices']}")
            
            print("\n🎙️ Vozes brasileiras disponíveis:")
            for voice in resultado['voices']:
                gender = voice.get('Gender', 'N/A')
                name = voice.get('Name', voice.get('ShortName', 'N/A'))
                print(f"  • {name} ({gender})")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro no teste: {str(e)}")
            return False
    
    async def teste_sintese_bytes():
        """Teste de síntese para bytes"""
        print("💾 Testando síntese para bytes...")
        
        try:
            tts_agent = AgentTTSEdge()
            resultado = await tts_agent.synthesize_to_bytes("Teste de síntese para bytes.")
            
            print("✅ Síntese para bytes realizada!")
            print(f"🎙️ Voz: {resultado['voice_used']}")
            print(f"📊 Tamanho: {resultado['audio_size']} bytes")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro no teste: {str(e)}")
            return False
    
    async def teste_diferentes_vozes():
        """Teste com diferentes vozes brasileiras"""
        print("🎭 Testando diferentes vozes...")
        
        vozes_teste = [
            "pt-BR-ThalitaNeural",
            "pt-BR-FranciscaNeural", 
            "pt-BR-AntonioNeural"
        ]
        
        sucessos = 0
        for voz in vozes_teste:
            try:
                tts_agent = AgentTTSEdge()
                resultado = await tts_agent.synthesize(
                    f"Olá, eu sou a voz {voz.split('-')[-1].replace('Neural', '')}.",
                    voice=voz
                )
                
                print(f"✅ {voz}: {resultado['file_size']} bytes")
                
                # Limpar arquivo
                if os.path.exists(resultado['audio_file_path']):
                    os.unlink(resultado['audio_file_path'])
                
                sucessos += 1
                
            except Exception as e:
                print(f"❌ {voz}: {str(e)}")
        
        print(f"📊 {sucessos}/{len(vozes_teste)} vozes testadas com sucesso")
        return sucessos == len(vozes_teste)
    
    async def executar_todos_testes():
        """Executa todos os testes"""
        print("🎙️  TESTES INTERNOS - AGENTTTSSEDGE 🎙️")
        print("=" * 50)
        
        testes = [
            ("Lista de vozes", teste_vozes_disponiveis()),
            ("Síntese simples", teste_sintese_simples()),
            ("Síntese para bytes", teste_sintese_bytes()),
            ("Diferentes vozes", teste_diferentes_vozes())
        ]
        
        sucessos = 0
        total = len(testes)
        
        for nome, teste in testes:
            print(f"\n📋 {nome}:")
            try:
                if await teste:
                    sucessos += 1
            except Exception as e:
                print(f"❌ Erro inesperado: {str(e)}")
        
        print("\n" + "=" * 50)
        print(f"📊 RESULTADO: {sucessos}/{total} testes passaram")
        
        if sucessos == total:
            print("🎉 Todos os testes passaram!")
        else:
            print("⚠️  Alguns testes falharam")
        
        return sucessos == total
    
    # Executar testes
    asyncio.run(executar_todos_testes()) 