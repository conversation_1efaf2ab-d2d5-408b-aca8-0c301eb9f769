<html>
<head>
    <title>{nome da pagina, sem o .html}</title>
</head>
<body>
    <div id="cabecalho">
    </div>
    <div id="corpo">
    </div>
    <div id="rodape">
    </div>
<script>

document.addEventListener("DOMContentLoaded", function() {
    // Função para carregar conteúdo HTML em um elemento
    function carregarConteudo(url, elemento) {
        fetch(url)
            .then(response => response.text())  // Converte a resposta em texto
            .then(html => {
                elemento.innerHTML = html;  // Insere o HTML no elemento
            })
            .catch(error => console.error('Erro ao carregar o conteúdo:', error));
    }

    // Seleciona os elementos pelo ID
    var cabecalho = document.getElementById('cabecalho');
    var rodape = document.getElementById('rodape');
    // Carrega os arquivos HTML nos elementos
    carregarConteudo('cabecalho.html', cabecalho);
    carregar<PERSON><PERSON>udo('rodape.html', rodape);
});    
    
    </script>
</body>
</html>