<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>#nome da pagina , sem o .html</title>
</head>
<body>
    <div class='component' id="cabecalho">
    </div>
    <div id="corpo">
        Este é nosso site
    </div>
    <div class='component' id="rodape">
    </div>
<script>

document.addEventListener("DOMContentLoaded", function() {
    // Função para carregar conteúdo HTML em um elemento
    function carregarConteudo(url, elemento) {
        fetch(url)
            .then(response => response.text())  // Converte a resposta em texto
            .then(html => {
                elemento.innerHTML = html;  // Insere o HTML no elemento
            })
            .catch(error => console.error('Erro ao carregar o conteúdo:', error));
    }

    // Seleciona todos os elementos que têm a classe 'component'
    var componentes = document.querySelectorAll('.component');
    
    // Itera sobre cada elemento, carrega o arquivo HTML correspondente ao ID do elemento
    componentes.forEach(function(elemento) {
        carregarConteudo("componentes/" + elemento.id + '.htm', elemento);
    });
});    
    
</script>
</body>
</html>
