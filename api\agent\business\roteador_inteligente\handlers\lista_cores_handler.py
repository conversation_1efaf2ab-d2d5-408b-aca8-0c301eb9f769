# handlers/lista_cores_handler.py
import logging
from typing import Dict, Any, List
from datetime import datetime

class ListaCoresHandler:
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
    
    def processar(self, parametros: Dict[str, Any]) -> Dict[str, Any]:
        """Retorna lista de cores disponíveis formatada"""
        try:
            cores = self._buscar_cores()
            
            return {
                "tipo": "lista_cores",
                "dados": self._formatar_cores(cores),
                "total": len(cores),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao buscar cores: {str(e)}")
            return {
                "tipo": "erro",
                "mensagem": str(e),
                "dados": []
            }
    
    def _buscar_cores(self) -> List[Dict[str, Any]]:
        """Busca cores no banco de dados"""
        try:
            query = """
                MATCH (c:Cor) 
                WHERE c.disponivel = true 
                RETURN c.nome as nome, c.codigo_hex as hex, c.categoria as categoria
                ORDER BY c.nome
            """
            
            # Mock para testes - substituir pela query real
            return self._mock_cores()
            
        except Exception as e:
            self.logger.error(f"Erro na query: {str(e)}")
            return self._mock_cores()
    
    def _formatar_cores(self, cores: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Formata cores para visualização"""
        return [
            {
                "id": cor.get('nome', '').lower().replace(' ', '_'),
                "nome": cor.get('nome', ''),
                "codigo_hex": cor.get('hex', '#FFFFFF'),
                "categoria": cor.get('categoria', 'Padrão'),
                "amostra": self._gerar_amostra(cor.get('hex', '#FFFFFF'))
            }
            for cor in cores
        ]
    
    def _gerar_amostra(self, hex_color: str) -> Dict[str, str]:
        """Gera preview visual da cor"""
        return {
            "background": hex_color,
            "text_color": self._calcular_cor_texto(hex_color),
            "css": f"background-color: {hex_color}"
        }
    
    def _calcular_cor_texto(self, hex_color: str) -> str:
        """Calcula cor do texto baseada no fundo (preto/branco)"""
        # Remove # se existir
        hex_color = hex_color.lstrip('#')
        
        # Converte para RGB
        try:
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            
            # Calcula luminosidade
            luminosidade = (0.299 * r + 0.587 * g + 0.114 * b) / 255
            
            # Retorna preto ou branco
            return "#000000" if luminosidade > 0.5 else "#FFFFFF"
        except:
            return "#000000"
    
    def _mock_cores(self) -> List[Dict[str, Any]]:
        """Dados mock para testes"""
        return [
            {"nome": "Vermelho", "hex": "#FF0000", "categoria": "Primária"},
            {"nome": "Azul", "hex": "#0000FF", "categoria": "Primária"},
            {"nome": "Verde", "hex": "#008000", "categoria": "Primária"},
            {"nome": "Amarelo", "hex": "#FFFF00", "categoria": "Secundária"},
            {"nome": "Roxo", "hex": "#800080", "categoria": "Secundária"},
            {"nome": "Preto", "hex": "#000000", "categoria": "Neutro"},
            {"nome": "Branco", "hex": "#FFFFFF", "categoria": "Neutro"}
        ]

# Teste rápido
if __name__ == "__main__":
    handler = ListaCoresHandler(None)
    resultado = handler.processar({})
    print(f"Total cores: {resultado['total']}")
    for cor in resultado['dados'][:3]:
        print(f"Cor: {cor['nome']} ({cor['codigo_hex']})")