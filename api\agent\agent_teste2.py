from pydantic import BaseModel, Field
from typing import Optional, Literal, Dict, Any
from agents import function_tool
import json
from datetime import datetime
import pytz

# Evita importação circular importando apenas o que é necessário
try:
    from neo4j import GraphDatabase
    from api.config import settings
    
    # Configuração direta sem depender do AgentNeo4j
    NEO4J_URI = settings.NEO4J_URI
    NEO4J_USER = settings.NEO4J_USER
    NEO4J_PASSWORD = settings.NEO4J_PASSWORD
    NEO4J_DATABASE = getattr(settings, 'NEO4J_DATABASE', 'neo4j')
    
    # Driver Neo4j
    neo4j_driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
except ImportError:
    print("Aviso: Neo4j Python driver não encontrado. Instale com: pip install neo4j")
    neo4j_driver = None

def gera_idx():
    """Gera um ID único para os nós"""
    from datetime import datetime
    import random
    import string
    
    # Usando now() com fuso horário UTC para evitar avisos de depreciação
    timestamp = datetime.now(pytz.UTC).strftime("%Y%m%d%H%M%S")
    random_str = ''.join(random.choices(string.digits, k=6))
    return f"{timestamp}_{random_str}"

# Modelo para as propriedades do nó (campos fixos)
class NodeProperties(BaseModel):
    nome: str = Field(..., description="Nome completo do usuário")
    email: str = Field(..., description="E-mail do usuário")
    idade: Optional[int] = Field(None, description="Idade do usuário")
    ativo: bool = Field(True, description="Se o usuário está ativo")

# Modelo principal com campos fixos
class CypherWriteInput(BaseModel):
    query: str = Field(..., description="Consulta Cypher")
    node_type: str = Field(..., description="Tipo do nó (label)")
    properties: NodeProperties  # Modelo aninhado com campos fixos
    operation: Literal["CREATE", "UPDATE", "DELETE"] = "CREATE"

@function_tool
def executar_cypher_escrita(input: CypherWriteInput) -> dict:
    """
    Executa uma operação de escrita no Neo4j usando input validado por Pydantic.
    
    Args:
        input (CypherWriteInput): Dados para a operação de escrita
        
    Returns:
        dict: Resultado da operação com status e dados relevantes
    """
    try:
        # Gera um ID único para o nó
        node_idx = gera_idx()
        
        # Prepara as propriedades do nó
        props = input.properties.model_dump()
        props['idx'] = node_idx
        props['criado_em'] = datetime.now(pytz.timezone('America/Sao_Paulo')).isoformat()
        
        # Constrói a query Cypher
        props_str = ", ".join([f"{k}: ${k}" for k in props.keys()])
        cypher = f"""
        {input.operation} (n:{input.node_type} {{
            {props_str}
        }})
        RETURN n
        """
        
        # Executa a query de escrita
        result = None
        if neo4j_driver:
            with neo4j_driver.session(database=NEO4J_DATABASE) as session:
                result = session.run(cypher, **props).data()
        
        return {
            "success": True,
            "cypher_query": cypher,
            "node_type": input.node_type,
            "properties": props,
            "operation": input.operation,
            "node_idx": node_idx,
            "result": result,
            "message": f"Nó {input.node_type} criado com sucesso!"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "type": type(e).__name__,
            "message": f"Erro ao executar operação {input.operation} no Neo4j"
        }

# Versão não decorada para teste local
def executar_cypher_escrita_local(input: CypherWriteInput) -> dict:
    """Versão local da função sem o decorador para testes"""
    try:
        # Gera um ID único para o nó
        node_idx = gera_idx()
        
        # Prepara as propriedades do nó
        props = input.properties.model_dump()
        props['idx'] = node_idx
        props['criado_em'] = datetime.now(pytz.timezone('America/Sao_Paulo')).isoformat()
        
        # Constrói a query Cypher
        props_str = ", ".join([f"{k}: ${k}" for k in props.keys()])
        cypher = f"""
        {input.operation} (n:{input.node_type} {{
            {props_str}
        }})
        RETURN n
        """
        
        # Executa a query de escrita
        result = None
        if neo4j_driver:
            with neo4j_driver.session(database=NEO4J_DATABASE) as session:
                result = session.run(cypher, **props).data()
        
        return {
            "success": True,
            "cypher_query": cypher,
            "node_type": input.node_type,
            "properties": props,
            "operation": input.operation,
            "node_idx": node_idx,
            "result": result,
            "message": f"Nó {input.node_type} criado com sucesso!"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "type": type(e).__name__,
            "message": f"Erro ao executar operação {input.operation} no Neo4j"
        }

# Teste executável
if __name__ == '__main__':
    # Exemplo de input válido com campos fixos
    input_data = CypherWriteInput(
        query="Criar no de usuario de teste",
        node_type="UsuarioTeste",  # Usando um label específico para testes
        properties=NodeProperties(
            nome="Joao Silva",
            email="<EMAIL>",
            idade=35,
            ativo=True
        ),
        operation="CREATE"
    )
    
    print("=== Teste de Escrita no Neo4j com Pydantic ===")
    print("Dados de entrada:", json.dumps(input_data.model_dump(), indent=2, ensure_ascii=False))
    
    try:
        # Testa a versão local (sem decorador)
        print("\n[1] Testando versão local (sem decorador):")
        resultado_local = executar_cypher_escrita_local(input_data)
        
        if resultado_local.get('success'):
            print("  [OK] Operação realizada com sucesso!")
            print(f"  - Node IDX: {resultado_local.get('node_idx')}")
            print(f"  - Tipo do nó: {resultado_local.get('node_type')}")
            print(f"  - Operação: {resultado_local.get('operation')}")
            print("  - Propriedades:", json.dumps(resultado_local.get('properties', {}), indent=2, ensure_ascii=False))
            print("  - Query executada:", resultado_local.get('cypher_query', '').strip())
            print("  - Mensagem:", resultado_local.get('message', ''))
            
            # Se tivermos resultado da query, mostre
            if 'result' in resultado_local:
                print("  - Resultado da query:", json.dumps(resultado_local['result'], indent=2, ensure_ascii=False))
        else:
            print("  [ERRO] Falha na operação:")
            print(f"  - Erro: {resultado_local.get('error')}")
            print(f"  - Tipo: {resultado_local.get('type')}")
            print(f"  - Mensagem: {resultado_local.get('message')}")
        
        # Testa a versão com decorador (pode falhar)
        print("\n[2] Testando versão com @function_tool:")
        try:
            # Tenta obter a função original do decorador
            if hasattr(executar_cypher_escrita, 'function'):
                resultado_decorado = executar_cypher_escrita.function(input=input_data)
            else:
                resultado_decorado = executar_cypher_escrita(input=input_data)
                
            print("  [OK] Versão decorada executou com sucesso!")
            print("  - Resultado:", json.dumps(resultado_decorado, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"  [AVISO] Erro ao chamar versão decorada: {type(e).__name__} - {str(e)}")
            print("  [DICA] O decorador @function_tool é destinado ao uso com agentes, não para chamada direta.")
            
    except Exception as e:
        print("\n[ERRO] Erro inesperado durante o teste:")
        print(f"Tipo: {type(e).__name__}")
        print(f"Detalhes: {str(e)}")
    
    # Mostra o schema gerado pelo Pydantic
    print("\n[INFO] Schema Pydantic gerado:")
    print(json.dumps(CypherWriteInput.model_json_schema(), indent=2, ensure_ascii=False))
