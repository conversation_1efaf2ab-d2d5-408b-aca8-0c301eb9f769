import google.generativeai as genai
import os
import json
import asyncio 
from dotenv import load_dotenv
import openai
import langid
import json

load_dotenv()

#=======================================================
def getTemplates(nr):

 if nr ==1:
  return  """
  Você é um motor de resumo altamente inteligente, e consegue  criar um resumo de um texto fornecido pelo usuário.
  Retorne o resumo no idioma original do texto fornecido pelo usuário.
  Se o texto é em ingles, o resumo deve ser em ingles. Se o texto é em portugues, o resumo deve ser em portugues. Sempre resuma na lingua original do texto.

  """


 if nr == 2:
  return  """
Você é um motor de resumo altamente inteligente, e possui 10 modelos de sumarização, cada um  com um com suas diretrizes,
e escolhe um deles para sumarizar o texto fornecido pelo usuário. Você escolhe o modelo que mais se aplica ao conteúdo e
formato do texto a ser sumarizado. Se extremamente necessário, poderá usar mais de um modelo em um mesmo texto.
Estes são os seus 10 modelos de sumarização:

1 - Destaques Cronológicos:
Estrutura em pontos, resumindo eventos em ordem cronológica.
Foco em clareza, factualidade e eventos principais.
Segmentação do vídeo em seções com 2-3 frases concisas cada.

2 - Análise Temática:
Divide o texto em temas ou tópicos distintos.
Estilo educativo e cativante.
Cabeçalhos para cada tema e resumo de 2-3 frases abaixo.

3 - Arco Narrativo:
Narra como uma história cativante.
Enfatiza pontos de virada e emoções.
Segue a estrutura: Introdução, Ação Crescente, Clímax e Conclusão.

4 - Principais Conclusões:
Concentra-se em destilar insights ou mensagens centrais.
Estilo de conselho crucial, listando pontos essenciais.
Evita repetição e destaca conclusões valiosas.

5 - Análise Prós e Contras:
Avalia argumentos com visões opostas.
Imparcialidade, organizado em Vantagens e Desvantagens.
Representação equilibrada dos prós e contras.

6 - Resumo de Perguntas e Respostas:
Eficiente para perguntas específicas e respostas.
Respostas diretas, estilo Perguntas Frequentes.
Foco em perguntas relevantes e respostas objetivas.

7 - Voz do Influenciador:
Captura o estilo do autor original.
Citações diretas integradas com parafraseamento.
Destaque nas mensagens centrais do influenciador.

8 - Instantâneo Estatístico:
Destaca dados e estatísticas chave.
Abordagem analítica, ênfase em números significativos.
Contexto breve quando necessário.

9 - Visão Geral Baseada em Lista:
Adequado para conteúdos em forma de lista.
Vibração de uma lista de revista, foco em cada item.
Mantém fidelidade à sequência ou ordem do vídeo.

10 - Mergulho Técnico Detalhado:
Explica conceitos técnicos de forma acessível.
Começa com uma visão geral e mergulha em detalhes.
Precisão na explicação de termos técnicos.

11 - Sumarização Reflexiva:
Análise crítica e reflexiva do conteúdo.
Estrutura com introdução, análise crítica, conexões amplas e conclusão reflexiva.
Estimula o pensamento crítico e a reflexão, mantendo clareza na complexidade.


Não pergunte ao usuário qual o modelo. Você é capaz de escolher qual o melhor modelo e fazer o resumo com base nele.
Não explique ao usuário qual o modelo escolhido , nem porque. apenas escolha modelo a ser aplicado e faça o resumo.
 Não faça nenhum comentário adicional. Retorne apenas o json com o resumo e o modelo utilizado.

A resposta deve ser no seguinte formato json:
{"modelo":"numero do modelo utilizado","resumo": "resumo"}

Segue o texto a ser sumarizado:

"""



#=======================================================
async def summarizer_gemini(text,tnr):
  gemini_api_key = os.environ.get("GEMINI_API_KEY")
  print("gemini api key", gemini_api_key)
  genai.configure(api_key = gemini_api_key)
  model = genai.GenerativeModel('gemini-pro') 



  templates = getTemplates(tnr)
  #print("templates",templates)

  response =   model.generate_content(templates + text)

  return response.text


#=======================================================
async def summarizer_claude(text,tnr):
    import boto3

    system = getTemplates(tnr)
    user = text
    assistant = ""
    

    #print("system",system)
     # Call the bedrock client
    bedrock = boto3.client(service_name='bedrock-runtime',
                           region_name='us-east-1',
                           aws_access_key_id=os.environ.get("MY_AWS_ACCESS_KEY"),
                           aws_secret_access_key=os.environ.get("MY_AWS_SECRET_KEY"))
    # Tweak your preferred model parameters, prompt and assistant information
    body = json.dumps({
        "prompt": f"\n\n{system}\n\nHuman:{user}\n\nAssistant:{assistant}",
        "max_tokens_to_sample": 500,
        "temperature": 0.0,
        "top_p": 0.9,})

    # Define the type of model that will be used
    modelId = 'anthropic.claude-v2:1'
    accept = 'application/json'
    contentType = 'application/json'

    # Call the Bedrock API
    response = bedrock.invoke_model(body=body, modelId=modelId, accept=accept, contentType=contentType)
    response_body = json.loads(response.get('body').read())
    #print(response_body)
    return response_body.get('completion')



#=======================================================
async def summarizer_openai(text,tnr):
  from openai import OpenAI
  openai_api_key = os.environ.get("OPENAI_API_KEY")
  client = OpenAI()

  system = getTemplates(tnr)
  

  response = client.chat.completions.create(
  model="gpt-4-0125-preview",
  #model="gpt-3.5-turbo-1106",
  messages=[
    {"role": "system", "content": system},
    {"role": "user", "content":text},
  
  ]
)

  resposta = response.choices[0].message.content

  return resposta

#=======================================================
async def translator_gemini(text,idiom):
  
  #print("-----translator_gemini-----")
  #print("text",text)
  #print("idiom",idiom)
  gemini_api_key = os.environ.get("GEMINI_API_KEY")

  genai.configure(api_key = gemini_api_key)
  model = genai.GenerativeModel('gemini-pro') 



  
  response =   model.generate_content("Traduza  o texto " + text + " para o idioma " + idiom + ". Somente traduza o texto, sem nenhuma explicação adicional, sem comentarios, sem nenhum adição de texto. ")
  #print("translated:",response.text)
  return response.text

#=======================================================
def detect_idiom(text):
    # Take the first 30 characters if the text is longer
    phrase = text[:50] if len(text) > 50 else text
    language, confidence = langid.classify(phrase)
    return language