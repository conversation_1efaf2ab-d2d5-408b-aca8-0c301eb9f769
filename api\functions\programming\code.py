from ..llm.openai  import openai_chat
from ..llm.deepseek import deepseek_coder
from ..llm.gemini import gemini_generate


#==============================
async def code_completion(data):
  print("code_completion()",data)
 
  model_openai = ["gpt-4-0125-preview","gpt-3.5-turbo"]
  model_deepseek = ["deepseek-coder"]
  model_gemini = ["gemini.pro"]
  result = ""
  system = "Haja como um programador senior, que ja atua há 20 anos como programador  python, react e next , e que consegue explicar facilmente a linguagem para um leigo ou iniciante, e criar funcoes ou sistemas inteiros por mais complexos que sejam, devido ao seu vasto conhecimento.\n"
  
  code = data["code"]
  
  
  instruction = """

Complete o código acima e me retorne o codigo com as alterações feitas. Somente o codigo, dentro das tags originais, 
e sem comentarios adicionais antes ou depois e dentro das tags  originais que envolvem o codigo como no exemplo abaixo:
    <code>
    {codigo}
    </code>
    apenas um exemplo. Use as tags que estao no codigo orignal que te passei.
    
    IMPORTANTE:
    Não faça nenhuma refatoração ou modificação que não tenha sido solicitada. Somente as solicitadas e complementares ou necessárias para atender o que foi solicitado.
"""
  
  
  
  messages = [
     {
    "role": "system",
    "content": system
    },
    {
    "role": "user",
    "content": code + instruction
    },
]
  
  
  
  #-----------------------
  if data["model"] in model_openai:
    
    config = {
        "model":data["model"],
    }
    print("messages",messages)
    result = await openai_chat(messages ,config)
    return result
  #-----------------------
  if data["model"] in model_deepseek:
    
    
    config = {
        "model":data["model"],
        'temperature': 0.2,
        'top_p': 1,
        'n': 1,        
    }
    print("messages x",messages)
    result = await deepseek_coder(messages,config)
    print("result deepseek",result)

    return result
  #-----------------------------
  if data["model"] in model_gemini:
    #https://colab.research.google.com/drive/1kySkAjBsgUjiNIZCGiDqJ7wbqLmwnpOb#scrollTo=m62ZuI-TEsyz
    
    message = system + code + instruction
    result =  await gemini_generate(message)
    return result 


  return "codigo completado com sucesso"

