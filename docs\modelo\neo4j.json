{
  "generated_at": "2025-08-10T00:00:00Z",
  "database": "neo4j",
  "node_types": [
    {
      "label": "Agente",
      "properties": [
        {
          "name": "identidade",
          "type": "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "instrucoes",
          "type": "STRING"
        },
        {
          "name": "llm_padrao",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "personalidade",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Api",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Api",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Api",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "App",
      "properties": [
        {
          "name": "apelido",
          "type": "STRING"
        },
        {
          "name": "areaId",
          "type": "INTEGER"
        },
        {
          "name": "data_criacao",
          "type": "DATE"
        },
        {
          "name": "descricao",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "nomeId",
          "type": "STRING"
        },
        {
          "name": "rota",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Artigo",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "titulo",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Atividade",
      "properties": [
        {
          "name": "cnae",
          "type": "STRING"
        },
        {
          "name": "cnae_descricao",
          "type": "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "BancoDeDados",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "host",
          "type": "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Canal",
      "properties":[]
    },
    {
      "label": "CartaoCredito",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "CartaoDebito",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Categoria",
      "properties": [
        {
          "name": "descricao",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Cheque",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Cor",
      "properties":[
        {
          "name":"nome",
          "type":"STRING"
        },
        {
          "name":"codigo",
          "type":"STRING"
        },
        {
          "name":"hexadecimal",
          "type": "STRING"
        }
      ]
    },

    {
      "label": "Chunk",
      "properties": [
        {
          "name": "embedding",
          "type": "FLOAT_ARRAY"
        },
        {
          "name": "index",
          "type": "INTEGER"
        },
        {
          "name": "text",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Cliente",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Comercio",
      "properties": [
        {
          "name": "entrada",
          "type": "DATE_TIME"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "item_ano",
          "type": "STRING"
        },
        {
          "name": "item_categoria",
          "type": "STRING"
        },
        {
          "name": "item_cor",
          "type": "STRING"
        },
        {
          "name": "item_marca",
          "type": "STRING"
        },
        {
          "name": "item_nome",
          "type": "STRING"
        },
        {
          "name": "item_placa",
          "type": "STRING"
        },
        {
          "name": "numero",
          "type": "INTEGER"
        },
        {
          "name": "observacao",
          "type": "STRING"
        },
        {
          "name": "pago",
          "type": "INTEGER"
        },
        {
          "name": "parcela",
          "type": "INTEGER"
        },
        {
          "name": "qtde_parcelas_pagamento",
          "type": "INTEGER"
        },
        {
          "name": "total",
          "type": "FLOAT"
        },
        {
          "name": "total_produtos",
          "type": "FLOAT"
        },
        {
          "name": "total_servicos",
          "type": "FLOAT"
        },
        {
          "name": "valor",
          "type": "FLOAT"
        },
        {
          "name": "vencimento",
          "type": "DATE"
        }
      ]
    },
    {
      "label": "Conhecimento",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "titulo",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Conta",
      "properties": [
        {
          "name": "appIdx",
          "type": "STRING"
        },
        {
          "name": "dataAtualizacao",
          "type": "DATE_TIME"
        },
        {
          "name": "dataCriacao",
          "type": "DATE_TIME"
        },
        {
          "name": "data_cadastro",
          "type": "DATE"
        },
        {
          "name": "email",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nivelId",
          "type": "INTEGER"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "planoId",
          "type": "INTEGER"
        },
        {
          "name": "planoInicio",
          "type": "DATE"
        },
        {
          "name": "senha",
          "type": "STRING"
        },
        {
          "name": "token",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        },
        {
          "name": "usuario",
          "type": "STRING"
        },
        {
          "name": "usuário",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Conversa",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Dinheiro",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Empresa",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url_1",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "FormaPagamento",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Gerenciador de Arquivos",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Gerenciador de Arquivos",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Gestao",
      "properties": [
        {
          "name": "data_cadastro",
          "type": "DATE"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "LLM",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        },
        {
          "name": "url_1",
          "type": "STRING"
        },
        {
          "name": "url_2",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Llm",
      "properties": []
    },
    {
      "label": "Location",
      "properties": [
        {
          "name": "name",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "MCP",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        },
        {
          "name": "url_1",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Mensagem",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "texto",
          "type": "STRING"
        },
        {
          "data_hora": "data_hora",
          "type" : "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Negocio",
      "properties": [
        {
          "name": "areaId",
          "type": "INTEGER"
        },
        {
          "name": "bairro",
          "type": "STRING"
        },
        {
          "name": "cep",
          "type": "STRING"
        },
        {
          "name": "cidade",
          "type": "STRING"
        },
        {
          "name": "cnpjCpf",
          "type": "STRING"
        },
        {
          "name": "complemento",
          "type": "STRING"
        },
        {
          "name": "email",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "logradouro",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "numero",
          "type": "STRING"
        },
        {
          "name": "razaoSocial",
          "type": "STRING"
        },
        {
          "name": "responsavel",
          "type": "STRING"
        },
        {
          "name": "telefone",
          "type": "STRING"
        },
        {
          "name": "tipoId",
          "type": "INTEGER"
        },
        {
          "name": "uf",
          "type": "STRING"
        },
        {
          "name": "usuarioIdx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Operacao",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "OrdemServico",
      "properties": [
        {
          "name": "entrada",
          "type": "DATE_TIME"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "item_ano",
          "type": "STRING"
        },
        {
          "name": "item_categoria",
          "type": "STRING"
        },
        {
          "name": "item_cor",
          "type": "STRING"
        },
        {
          "name": "item_marca",
          "type": "STRING"
        },
        {
          "name": "item_nome",
          "type": "STRING"
        },
        {
          "name": "item_placa",
          "type": "STRING"
        },
        {
          "name": "numero",
          "type": "INTEGER"
        },
        {
          "name": "observacao",
          "type": "STRING"
        },
        {
          "name": "qtde_parcelas_pagamento",
          "type": "INTEGER"
        },
        {
          "name": "total",
          "type": "FLOAT"
        },
        {
          "name": "total_produtos",
          "type": "FLOAT"
        },
        {
          "name": "total_servicos",
          "type": "FLOAT"
        }
      ]
    },
    {
      "label": "Organization",
      "properties": [
        {
          "name": "name",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "OsNumerador",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "ultimo_numero",
          "type": "INTEGER"
        }
      ]
    },
    {
      "label": "Pagamento",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "pago",
          "type": "INTEGER"
        },
        {
          "name": "parcela",
          "type": "INTEGER"
        },
        {
          "name": "valor",
          "type": "FLOAT"
        },
        {
          "name": "vencimento",
          "type": "DATE"
        }
      ]
    },
    {
      "label": "PagamentoTipo",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Pago",
      "properties": [
        {
          "name": "dataCriacao",
          "type": "DATE"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Pendente",
      "properties": [
        {
          "name": "dataCriacao",
          "type": "DATE"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Person",
      "properties": [
        {
          "name": "name",
          "type": "STRING"
        }
      ]
    },
    {
      "label": ["Pessoa", "PessoaFisica"],
      "properties": [
        {
          "name": "NOME",
          "type": "STRING"
        },
        {
          "name": "bairro",
          "type": "STRING"
        },
        {
          "name": "cep",
          "type": "STRING"
        },
        {
          "name": "cidade",
          "type": "STRING"
        },
        {
          "name": "complemento",
          "type": "STRING"
        },
        {
          "name": "cpf_cnpj",
          "type": "STRING"
        },
        {
          "name": "dataCadastro",
          "type": "DATE_TIME|STRING"
        },
        {
          "name": "email",
          "type": "STRING"
        },
        {
          "name": "emailConfirmado",
          "type": "INTEGER"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "id",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "interacoes",
          "type": "INTEGER"
        },
        {
          "name": "logradouro",
          "type": "STRING"
        },
        {
          "name": "negocio_idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "numero",
          "type": "STRING"
        },
        {
          "name": "senha",
          "type": "STRING"
        },
        {
          "name": "senhaConfirmada",
          "type": "INTEGER"
        },
        {
          "name": "telefone",
          "type": "STRING"
        },
        {
          "name": "telefoneConfirmado",
          "type": "INTEGER"
        },
        {
          "name": "tksGastos",
          "type": "INTEGER"
        },
        {
          "name": "tksTotal",
          "type": "INTEGER"
        },
        {
          "name": "uf",
          "type": "STRING"
        }
      ]
    },

    {
      "label": "Pix",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Prioridade",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nivel",
          "type": "INTEGER"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Product",
      "properties": [
        {
          "name": "name",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Produto",
      "properties": [
        {
          "name": "codigo",
          "type": "STRING"
        },
        {
          "name": "descricao",
          "type": "STRING"
        },
        {
          "name": "estoque",
          "type": "INTEGER"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "negocioIdx",
          "type": "STRING"
        },
        {
          "name": "negocio_idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "preco",
          "type": "FLOAT|STRING"
        }
      ]
    },
    {
      "label": "ProdutoCategoria",
      "properties": []
    },
    {
      "label": "Projeto",
      "properties": [
        {
          "name": "data_cadastro",
          "type": "DATE"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Recurso",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "site",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        },
        {
          "name": "url_1",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Segmento",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Servico",
      "properties": [
        {
          "name": "categoria_id",
          "type": "STRING"
        },
        {
          "name": "codigo",
          "type": "STRING"
        },
        {
          "name": "descricao",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "id",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "negocio_idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "preco",
          "type": "FLOAT|STRING"
        }
      ]
    },
    {
      "label": "Site",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "rota",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Status",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nivel",
          "type": "INTEGER"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "StatusPagmento",
      "properties": []
    },
    {
      "label": "Tag",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Tarefa",
      "properties": [
        {
          "name": "data_inicio",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "hora_inicio",
          "type": "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "TesteEscrita",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "mensagem",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Tipo",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Transferencia",
      "properties": [
        {
          "name": "idx",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Usuario",
      "properties": [
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Veiculo",
      "properties": [
        {
          "name": "ano",
          "type": "STRING"
        },
        {
          "name": "categoria",
          "type": "STRING"
        },
        {
          "name": "cor",
          "type": "STRING"
        },
        {
          "name": "excluido",
          "type": "INTEGER"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "km",
          "type": "STRING"
        },
        {
          "name": "marca",
          "type": "STRING"
        },
        {
          "name": "nome",
          "type": "STRING"
        },
        {
          "name": "placa",
          "type": "STRING"
        },
        {
          "name": "serie",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "Video",
      "properties": [
        {
          "name": "canal",
          "type": "STRING"
        },
        {
          "name": "data_ingesta",
          "type": "DATE_TIME"
        },
        {
          "name": "descricao",
          "type": "STRING"
        },
        {
          "name": "idx",
          "type": "STRING"
        },
        {
          "name": "titulo",
          "type": "STRING"
        },
        {
          "name": "transcricao",
          "type": "STRING"
        },
        {
          "name": "url",
          "type": "STRING"
        },
        {
          "name": "url_canal",
          "type": "STRING"
        },
        {
          "name": "usuario_id",
          "type": "STRING"
        }
      ]
    },
    {
      "label": "__Entity__",
      "properties": []
    },
    {
      "label": "__KGBuilder__",
      "properties": []
    }
  ],
  "relationship_types": [
    {
      "type": "AO_AGENTE",
      "properties": []
    },
    {
      "type": "AO_APP",
      "properties": []
    },
    {
      "type": "ATUA_EM",
      "properties": [
        {
          "name": "ativo",
          "type": "BOOLEAN"
        },
        {
          "name": "data_inicio",
          "type": "DATE"
        }
      ]
    },
    {
      "type": "CLIENTE_DE",
      "properties": []
    },
    {
      "type": "CRIADO_POR",
      "properties": []
    },
    {
      "type": "DEVELOPED",
      "properties": []
    },
{
  "type": "EM_RESPOSTA",
  "properties": []
} ,
    {
      "type": "EMITIDA_POR",
      "properties": []
    },
    {

      "type": "ENVIADA_POR",
      "properties": []
    }
    {
      "type": "FOUNDED_BY",
      "properties": []
    },
    {
      "type": "FROM_CHUNK",
      "properties": []
    },
    {
      "type": "GERENCIADO_POR",
      "properties": []
    },
    {
      "type": "INCLUI_SERVICO",
      "properties": [
        {
          "name": "preco",
          "type": "FLOAT"
        },
        {
          "name": "quantidade",
          "type": "INTEGER"
        }
      ]
    },
    {
      "type": "INSTANCIA_DE",
      "properties": []
    },
    {
      "type": "LOCATED_IN",
      "properties": []
    },
    {
      "type": "PERTENCE_A",
      "properties": []
    },
    {
      "type": "POSSUI_CONHECIMENTO",
      "properties": []
    },
    {
      "type": "POSSUI_CONTA",
      "properties": []
    },
    {
      "type": "POSSUI_NEGOCIO",
      "properties": []
    },
    {
      "type": "POSSUI_PRODUTO",
      "properties": []
    },
    {
      "type": "POSSUI_TAREFA",
      "properties": []
    },
    {
      "type": "PRESTA_SERVICO",
      "properties": []
    },
    {
      "type": "REFERENTE_A",
      "properties": []
    },
    {
      "type": "SOBRE",
      "properties": []
    },
    {
      "type": "SOLICITADA_POR",
      "properties": []
    },
    {
      "type": "SUBRECURSO_DE",
      "properties": []
    },
    {
      "type": "TEM_ATIVIDADE",
      "properties": []
    },
    {
      "type": "TEM_CATEGORIA",
      "properties": []
    },
    {
      "type": "TEM_CLONE",
      "properties": []
    },
    {
      "type": "TEM_OS_NUMERADOR",
      "properties": []
    },
    {
      "type": "TEM_PAGAMENTO",
      "properties": []
    },
    {
      "type": "TEM_PRIORIDADE",
      "properties": [
        {
          "name": "ativo",
          "type": "INTEGER"
        },
        {
          "name": "entrada",
          "type": "DATE_TIME"
        },
        {
          "name": "saida",
          "type": "DATE_TIME"
        }
      ]
    },
    {
      "type": "TEM_STATUS",
      "properties": [
        {
          "name": "ativo",
          "type": "BOOLEAN"
        },
        {
          "name": "inicio",
          "type": "DATET"
        },
        {
          "name": "entrada",
          "type": "DATE_TIME"
        },
        {
          "name": "saida",
          "type": "DATE_TIME"
        }
      ]
    },
    {
      "type": "TEM_COR",
      "properties": []
    },  
    {
      "type": "TEM_TAG",
      "properties": []
    },
    {
      "type": "TEM_TIPO",
      "properties": []
    },
    {
      "type": "USANDO_O_CANAL",
      "properties": []
    },
    {
      "type": "UTILIZADA_PARA",
      "properties": []
    },
    {
      "type": "UTILIZADA_POR",
      "properties": [
        {
          "name": "desde",
          "type": "DATE"
        }
      ]
    },
    {
      "type": "TEM_COR",
      "properties": []
    },
    {
      "type": "UTILIZA_PRODUTO",
      "properties": [
        {
          "name": "preco",
          "type": "FLOAT"
        },
        {
          "name": "quantidade",
          "type": "INTEGER"
        }
      ]
    },
    {
      "type": "VENDE_PRODUTO",
      "properties": []
    },
    {
      "type": "É_SOBRE",
      "properties": []
    }
  ],
  "patterns": [
    ["Acesso", "AO_AGENTE", "Agente"],
    ["Acesso", "AO_APP", "App"],
    ["Acesso", "USANDO_O_CANAL", "Canal"],
    ["Agente", "CRIADO_POR", "Pessoa"],
    ["Agente", "ATUA_NO_APP", "App"],
    ["Api", "INSTANCIA_DE", "Api"],
    ["Api", "INSTANCIA_DE", "Recurso"],
    ["App", "CRIADO_POR", "Empresa"],
    ["Artigo", "SOBRE", "MCP"],
    ["Artigo", "SOBRE", "Recurso"],
    ["BancoDeDados", "INSTANCIA_DE", "BancoDeDados"],
    ["BancoDeDados", "INSTANCIA_DE", "MariaDb"],
    ["BancoDeDados", "INSTANCIA_DE", "Recurso"],
    ["Comercio", "EMITIDA_POR", "Negocio"],
    ["Comercio", "INCLUI_SERVICO", "Servico"],
    ["Comercio", "SOLICITADA_POR", "Cliente"],
    ["Comercio", "SOLICITADA_POR", "Pessoa"],
    ["Comercio", "SOLICITADA_POR", "PessoaFisica"],
    ["Comercio", "TEM_PAGAMENTO", "Pagamento"],
    ["Comercio", "UTILIZA_PRODUTO", "Produto"],
    ["Conhecimento", "SOBRE", "Recurso"],
    ["Conta", "ACESSO_A", "Api"],
    ["Conta", "ACESSO_A", "BancoDeDados"],
    ["Conta", "ACESSO_A", "Recurso"],
    ["Conta", "GERENCIADO_POR", "Negocio"],
    ["Conta", "REFERENTE_A", "App"],
    ["Conta", "REFERENTE_A", "Gerenciador de Arquivos"],
    ["Conta", "REFERENTE_A", "Recurso"],
    ["Conta", "UTILIZADA_PARA", "Operacao"],
    ["Conta", "UTILIZADA_POR", "Negocio"],
    ["Conversa", "É_SOBRE", "BancoDeDados"],
    ["Conversa", "É_SOBRE", "Recurso"],
    ["Conversa", "INICIADA_POR","Pessoa"],
    ["Conversa", "INICIADA_POR","Agente"],
    ["Conversa", "TEM_MENSAGEM", "Mensagem"],
    ["Conversa", "TEM_STATUS", "Status"],
    ["LLM", "CRIADO_POR", "Empresa"],
    ["Location", "FROM_CHUNK", "Chunk"],
    ["Mensagem", "ENVIADO_POR","Agente"],
    ["Mensagem","ENVIADO_POR","Pessoa"],
    ["Mensagem","ENVIADO_PARA","Pessoa"],
    ["Mensagem", "ENVIADO_PARA","Agente"],
    ["Mensagem","EM_RESPOSTA","Mensagem"],
    ["MCP", "SUBRECURSO_DE", "Recurso"],
    ["Negocio", "POSSUI_PRODUTO", "Produto"],
    ["Negocio", "DISPONIBILIZA_COR", "Cor"],
    ["Negocio", "PRESTA_SERVICO", "Servico"],
    ["Negocio", "TEM_ATIVIDADE", "Atividade"],
    ["Negocio", "TEM_CATEGORIA", "Categoria"],
    ["Negocio", "TEM_OS_NUMERADOR", "OsNumerador"],
    ["Negocio", "VENDE_PRODUTO", "Produto"],
    ["OrdemServico", "EMITIDA_POR", "Negocio"],
    ["OrdemServico", "INCLUI_SERVICO", "Servico"],
    ["OrdemServico", "SOLICITADA_POR", "Cliente"],
    ["OrdemServico", "SOLICITADA_POR", "Pessoa"],
    ["OrdemServico", "SOLICITADA_POR", "PessoaFisica"],
    ["OrdemServico", "TEM_PAGAMENTO", "Pagamento"],
    ["OrdemServico", "TEM_PRIORIDADE", "Prioridade"],
    ["OrdemServico", "TEM_STATUS", "Status"],
    ["OrdemServico", "UTILIZA_PRODUTO", "Produto"],
    ["Organization", "FOUNDED_BY", "Person"],
    ["Organization", "FROM_CHUNK", "Chunk"],
    ["Organization", "LOCATED_IN", "Location"],
    ["Pagamento", "TEM_TIPO", "CartaoCredito"],
    ["Pagamento", "TEM_TIPO", "PagamentoTipo"],
    ["Person", "FROM_CHUNK", "Chunk"],
    ["Pessoa", "TEVE_ACESSO", "Acesso"],
    ["Pessoa", "CLIENTE_DE", "Negocio"],
    ["Pessoa", "ATENDIDO_POR", "Pessoa"],
    ["Pessoa", "POSSUI_CONHECIMENTO", "Artigo"],
    ["Pessoa", "POSSUI_CONHECIMENTO", "Conhecimento"],
    ["Pessoa", "POSSUI_CONTA", "Conta"],
    ["Pessoa", "POSSUI_NEGOCIO", "Negocio"],
    ["Pessoa", "POSSUI_TAREFA", "Tarefa"],
    ["Pessoa", "TEM_CLONE", "Agente"],
    ["PessoaFisica", "CLIENTE_DE", "Negocio"],
    ["PessoaFisica", "POSSUI_CONHECIMENTO", "Conhecimento"],
    ["PessoaFisica", "POSSUI_CONHECIMENTO", "Video"],
    ["PessoaFisica", "POSSUI_CONTA", "Conta"],
    ["PessoaFisica", "POSSUI_TAREFA", "Tarefa"],
    ["PessoaFisica", "TEM_CLONE", "Agente"],
    ["Produto", "TEM_COR", "Cor"],
    ["Product", "DEVELOPED", "Person"],
    ["Product", "FROM_CHUNK", "Chunk"],
    ["Recurso", "CRIADO_POR", "Empresa"],
    ["Recurso", "SUBRECURSO_DE", "Recurso"],
    ["Recurso", "TEM_TAG", "Tag"],
    ["Site", "CRIADO_POR", "Empresa"],
    ["Site", "TEM_TAG", "Tag"],
    ["Tarefa", "TEM_STATUS", "Status"],
    ["Tipo", "PERTENCE_A", "Categoria"],
    ["Video", "SOBRE", "LLM"],
    ["Video", "SOBRE", "Recurso"]
  ]
}