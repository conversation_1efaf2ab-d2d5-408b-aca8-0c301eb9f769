# calc_tokens.py
# Arquivo único: expõe apenas a função que devolve a quantidade de tokens
# Dependência: tiktoken (pip install tiktoken)

import tiktoken

# ---------- VOCABULÁRIO COMPARTILHADO ----------
_encoder = tiktoken.get_encoding("cl100k_base")


def tokens_contador(texto: str) -> int:
    """
    Retorna o número **exato** de tokens que o texto consumirá
    nos modelos GPT-3.5/GPT-4 e DeepSeek (mesmo vocabulário).
    """
    if not texto:          # segurança para None ou ""
        return 0
    return len(_encoder.encode(texto))