from agents import function_tool
from ...agent_neo4j import AgentNeo4j
from ...agent_logger import AgentLogger
from ...util.functions.gera_id_unico import gera_id_unico

logger = AgentLogger()
neo4j = AgentNeo4j()

@function_tool
async def nota_nova(
    usuario_idx: str,
    conhecimento_idx: str=None,
    titulo: str=None,
    descricao: str=None,
    conteudo: str=None,
    url: str=None,
    ):
    """
    Adiciona uma nova nota no banco de dados.
    
    PARAMETROS:
    usuario_idx: str
    -Índice do usuário que está adicionando a nota.
    -Obrigatório. A função não deve ser executada sem esta informação.
    
    conhecimento_idx: str=None
    -Índice da base  de conhecimento onde a nota será adicionada.
    -Obrigatório. A função não deve ser executada sem esta informação.

    titulo: str=None
    -Título da nota.
    -Opcional. Se não for fornecido, a nota será adicionada sem titulo.

    descricao: str=None
    -Descrição da nota.
    -Opcional. Se não for fornecido, a nota será adicionada sem descrição.

    conteudo: str=None
    -Conteúdo da nota. O conteúdo poderá ser um texto, ou uma imagem.
    -Obrigatorio. A nota deve conter um conteúdo.
    

    url: str=None
    -URL da nota. A url ,quando fornecida, deve ser de um link para um recurso externo de onde a nota foi retirada ou ao qual se refere.
    -Opcional. Se não for fornecido, a nota será adicionada sem URL.
    -Se o conteúdo for um link, o URL deve ser fornecido.

    🚨 Solicite as informações uma a uma, mesmo as opcionais devem ser solicitadas. O usuário poderá responder a todas de uma vez, ou uma a uma. 
    As opcionais poderão ser respondidas com "não" ou "não sei, ou ignoradas.
    Se o usuário responder "não" ,"nada" ,"n" ou "-", a informação será ignorada.
    Se o usuário responder "não sei" ou "não tenho", a informação será ignorada.
    """
    
    try:
            logger.info(f"🆕 Adicionando nova nota pelo usuário: {usuario_idx}")
        
            # Validação obrigatória
            if not usuario_idx:
                return {"erro": "Usuário é obrigatório para criar uma nota"}
            
            if not conhecimento_idx:
                return {"erro": "Base de conhecimento é obrigatória para criar uma nota"}
            if not conteudo:
                return {"erro": "Conteúdo é obrigatório para criar uma nota"}


            if not conteudo:
                return {"erro": "Conteúdo é obrigatório para criar uma nota"}
        
            # Gerar IDX único para a nota
            nota_idx = gera_id_unico()
        
            query = """
            MERGE (u:Pessoa {idx: $usuario_idx})
            MERGE (n:Negocio {idx: $negocio_idx})
            MERGE (n)-[:POSSUI_CONHECIMENTO]->(bc:Conhecimento {idx: $negocio_idx + "_bc"})
            ON CREATE SET bc.idx = $bc_idx,  # Adicionar esta linha
                         bc.nome = "Base de Conhecimento - " + n.nome,
                         bc.excluido = 0
            CREATE (nota:Nota {
                idx: $nota_idx,
                titulo: $titulo,
                descricao: $descricao,
                conteudo: $conteudo,
                url: $url,
                excluido: 0,
                dataCriacao: datetime()
            })
            CREATE (bc)-[:CONTEM_NOTA]->(nota)
            RETURN nota.idx as nota_idx, bc.idx as base_conhecimento_idx, "negocio" as tipo_base
            """
            
            params = {
                "usuario_idx": usuario_idx,
                "bc_idx": conhecimento_idx,  # Adicionar este parâmetro
                "nota_idx": nota_idx,
                "titulo": titulo or "",
                "descricao": descricao or "",
                "conteudo": conteudo,
                "url": url or ""
            }
            
            # Para Pessoa:
            query = """
            MERGE (u:Pessoa {idx: $usuario_idx})
            MERGE (u)-[:POSSUI_CONHECIMENTO]->(bc:Conhecimento {idx: $conhecimento_idx})
            CREATE (nota:Nota {
                idx: $nota_idx,
                titulo: $titulo,
                descricao: $descricao,
                conteudo: $conteudo,
                url: $url,
                excluido : 0,
                data_criacao: datetime(),
                criador: $usuario_idx
            })
            CREATE (bc)-[:CONTEM_NOTA]->(nota)
            RETURN nota.idx as nota_idx
            """
            
            params = {
                "usuario_idx": usuario_idx,
                "conhecimento_idx": conhecimento_idx,
                "nota_idx": nota_idx,
                "titulo": titulo or "",
                "descricao": descricao or "",
                "conteudo": conteudo,
                "url": url or ""
            }
        
            # Executar a query
            resultado = await neo4j.execute_write_query(query, params)
        
            if resultado and len(resultado) > 0:
                logger.info(f"✅ Nota {nota_idx} adicionada com sucesso na base de conhecimento")
                return {
                    "sucesso": True,
                    "nota_idx": nota_idx,
                    "mensagem": "Nota adicionada com sucesso na base de conhecimento!"
                }
            else:
                logger.error("❌ Falha ao adicionar nota - resultado vazio")
                return {"erro": "Falha ao adicionar nota na base de conhecimento"}
            
    except Exception as e:
        logger.error(f"❌ Erro ao adicionar nota: {str(e)}")
        return {"erro": f"Erro ao adicionar nota: {str(e)}"}


if __name__ == "__main__":
    import asyncio
    import os

    async def testa_nota_nova():
        # Clear terminal screen
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # Teste 1: Nota para base de conhecimento específica
        print("=== TESTE 1: Nota para Base de Conhecimento ===")
        resultado1 = await nota_nova(
            usuario_idx="1122334455",
            conhecimento_idx="0707070707",  # Mudança aqui
            titulo="Reunião de Planejamento",
            descricao="Discussão sobre metas do próximo trimestre",
            conteudo="Definir estratégias de marketing e vendas para Q2. Focar em campanhas digitais e parcerias estratégicas.",
            url="https://exemplo.com/reuniao-planejamento"
        )
        print("Resultado:", resultado1)
        print()
        return
        

    
    asyncio.run(testa_nota_nova())
    # Execução:
    # python -m api.agent.brain.functions.nota_nova

    
