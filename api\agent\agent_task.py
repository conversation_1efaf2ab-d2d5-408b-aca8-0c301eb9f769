from .agent_mysql import Mysql
from .agent_functioncall import FunctionCall
from .agent_email import Email
from .agent_support import Support 
import json
from .agent_sopWriter import SopWriter
from .agent_converter import Converter
from .agent_llm import LLM
from .agent_user import User 
from datetime import datetime
from fastapi import APIRouter
from .agent_agent import Agent
from .agent_user import User
router = APIRouter()


import pytz

class Task:
    def __init__(self,user=None, model=None):
        self.llm = LLM()
        self.user = User()
        self.DESCRICAO = None,
        self.CHECKLIST = None,
        self.DT_PREVISAO_INICIO = None,
        self.ID=0,
        self.bd_table = 'TAREFA'
        self.mysql = Mysql()
        self.user = user if user else User()
        self.model = model if model else self.llm.model_gemini.geminipro15
        self.functionCaller = FunctionCall(model=self.model,user=self.user)
        self.email = Email(user=user)
        self.sopWriter = SopWriter()
        self.conv = Converter()
        self.agente = Agent()

    
    async def last_id(self):
        return await self.mysql.last_id("TAREFA")
    
    
    async def update(self,data):
        result = await self.mysql.update("TAREFA",data)
        return result

    async def query (self,query):
         result = await self.mysql.query(query)
         return result 

    async def save(self, tarefa):
               #print("==========save",tarefa)
               if 'ID' in tarefa:
                   #print("tarefa tem ID  - vou atualizar")
                   result = await self.mysql.update('TAREFA',tarefa)
                   return result 
               else:
                   #print('tarefa não tem ID - vou inserir')
                   id = await self.mysql.add('TAREFA',tarefa)
                   #print('tarefa inserida no bd',id)
                   return id
       
    async def fetch_actives(self):
        query = f"""

    SELECT 
    T.ID,
    T.PROJETO_ID,
    T.STATUS,
    T.ANTERIOR_ID,
    (SELECT STATUS FROM TAREFA WHERE ID = T.ANTERIOR_ID) AS ANTERIOR_STATUS,
    (SELECT SAIDA FROM TAREFA WHERE ID = T.ANTERIOR_ID) AS ANTERIOR_SAIDA,  
    T.PROXIMA_ID,
    (SELECT STATUS FROM TAREFA WHERE ID = T.PROXIMA_ID) AS PROXIMA_STATUS,
    T.DESCRICAO,
    T.AGENTE_NOME,
    T.AGENTE_FUNCAO,
    T.SEQUENCIA,
    T.TENTATIVA,
    T.ENTRADA,
    T.SOP,
    T.SOP_GERAR,
    T.AREA,
    T.SAIDA,
    T.TIPO,
    T.CHECKLIST,
    P.SITE_ID
FROM 
    TAREFA T
LEFT JOIN 
    PROJETO P ON T.PROJETO_ID = P.ID
WHERE 
    (
        T.STATUS IN ('A', 'T') 
        AND (T.PROJETO_ID = 0 OR T.PROJETO_ID IS NULL OR P.STATUS = 1)
        AND ((SELECT STATUS FROM TAREFA WHERE ID = T.ANTERIOR_ID) = 'R' OR (SELECT STATUS FROM TAREFA WHERE ID = T.ANTERIOR_ID) IS NULL)
        AND T.AGENTE_NOME IS NOT NULL 
        AND T.AGENTE_NOME <> ''
    )
    OR 
    (
        T.STATUS = 'P' 
        AND TIMESTAMPDIFF(SECOND, T.ATUALIZACAO, NOW()) > 180
        AND (T.PROJETO_ID = 0 OR T.PROJETO_ID IS NULL OR P.STATUS = 1)
        AND ((SELECT STATUS FROM TAREFA WHERE ID = T.ANTERIOR_ID) = 'R' OR (SELECT STATUS FROM TAREFA WHERE ID = T.ANTERIOR_ID) IS NULL)
        AND T.AGENTE_NOME IS NOT NULL 
        AND T.AGENTE_NOME <> ''
    )
ORDER BY 
    T.PROJETO_ID,T.PROCESSO_ID,T.SEQUENCIA,T.ID;
    

        """
        #print ("query",query)
        result = await self.mysql.query(query)
        return result


    async def fetch(self,query):
        return await self.mysql.query(query)

    async def remove(self,condition:str):
        query = f"UPDATE TAREFA SET EXCLUIDO = 1 WHERE {condition}"
        await self.mysql.query(query)
        
    async def add_process_tasks(self,projeto_id=0,processo_id=0,usuario_id="",entrada=""):
        #print("==========add_process_tasks")
        #print("----------user", self.user)
        if not projeto_id or not processo_id:
            return
        
        processo_tarefas = await self.fetch_process(processo_id)
        #print('processo_tarefas',processo_tarefas)
        anterior_id = 0   
        atual_id = await self.mysql.query("SELECT MAX(ID) FROM TAREFA")
        atual_id = atual_id[0]['MAX(ID)'] if atual_id[0]['MAX(ID)'] is not None else 0
        proxima_id = atual_id + 2 
        tarefas = []
        tot = 0
        #print("atual_id", atual_id)
        
        for proc_tarefa in processo_tarefas:
            
            tarefa = {}
            tarefa['ANTERIOR_ID'] = anterior_id
            tarefa['DESCRICAO'] = proc_tarefa['DESCRICAO']
            tarefa['CHECKLIST'] = proc_tarefa['CHECKLIST']
            tarefa['AGENTE_NOME'] = proc_tarefa['AGENTE_NOME']
            tarefa['AGENTE_FUNCAO'] = proc_tarefa['AGENTE_FUNCAO']
            tarefa['PROJETO_ID']= projeto_id
            tarefa['USUARIO_ID'] = usuario_id
            tarefa['PROCESSO_ID'] = proc_tarefa['PROCESSO_ID']
            tarefa['SEQUENCIA'] = proc_tarefa['SEQUENCIA']
            tarefa['SOP_GERAR'] = proc_tarefa['SOP_GERAR']
            tarefa['AREA'] = proc_tarefa['AREA']
            tarefa['TIPO'] = proc_tarefa['TIPO']
            tarefa['ENTRADA'] = f"{proc_tarefa['ENTRADA']} {entrada}"
             
            atual_id = atual_id + 1
            #tarefa['ID'] = atual_id
            proxima_id = atual_id + 1  if tot+1 < len(processo_tarefas) else 0
            tarefa['PROXIMA_ID'] = proxima_id
            tot = tot+1
            tarefa['ID'] = await self.save(tarefa)
            tarefas.append(tarefa)
            
            #print("anterior", anterior_id)
            #print("atual", atual_id)
            #print("proximo", proxima_id)
            #print("linha atual",tot)
            #print("")
            anterior_id = atual_id
            #print('########result tarefa##',tarefa['ID'])

        print ('tarefas',tarefas)

        return "Tarefas adicionadas com sucesso!"

    async def fetch_process(self,id):
        query = f"SELECT * FROM PROCESSO_TAREFA WHERE PROCESSO_ID = {id} ORDER BY SEQUENCIA"
        
        tasks = await self.mysql.query(query)
        return tasks 

    async def group_tasks_generate(self, tarefa):
        print("========== group-tasks_generate() ==========")
        #Gera uma lista de tarefas de um mesmo grupo
        #print("tipo",type(tasks))
        #print('tasks:')
        #print(tasks)
        #print('')
        #print(tarefaGrupo)
        #print('tarefaGrupo',tarefaGrupo)
        tasks = tarefa['ANTERIOR_SAIDA'] if tarefa['ANTERIOR_SAIDA'] else tarefa['ENTRADA']
        
        print('tasks',tasks)

        sequencia = str(int(tarefa['SEQUENCIA']))
        tarefas = json.loads(tasks)

        seq = 1
        atual_id = await self.last_id()
        proxima_id = atual_id + 2
        superior_id = tarefa['ID']
        anterior_id = tarefa['ID']
   
        tot = len(tarefas)
        if tot ==0:
             result = {}
             result['STATUS'] = "F"
             result['SAIDA'] = "Nenhuma subtarefa existente."
             return result



        for tf in tarefas:
            #print("sequencia",seq)
            #print("anterior",anterior_id)   
            #print("atual",atual_id)
            #print("proxima",proxima_id)
            if seq < tot:
                tf['PROJETO_ID'] = tarefa['PROJETO_ID'] 
                tf["SEQUENCIA"] = sequencia  + "." +  str(seq)
                tf["SUPERIOR_ID"] = superior_id
                tf["ANTERIOR_ID"] = anterior_id
                tf["SOP_GERAR"] = tarefa['SOP_GERAR']

           
                tf["PROXIMA_ID"] = proxima_id
                tf["AREA"] = tarefa['AREA']
                tf["AGENTE_NOME"] = tarefa['AGENTE_NOME']
                tf["AGENTE_FUNCAO"] = tarefa['AGENTE_FUNCAO']
                tf["TIPO"]=1
                proxima_id = proxima_id+1
                atual_id = await self.save(tf)
                anterior_id = atual_id
                #print("tarefa",tarefa)
    
                seq=seq+1
        
        
        #atualiza o id da próxima tarefa principal
        PROXIMA_TAREFA = {}
        PROXIMA_TAREFA["ID"] = tarefa['PROXIMA_ID'] #pega o id na tarefa  principal atual (grupo)
        PROXIMA_TAREFA["ANTERIOR_ID"] = atual_id #pega o id da ultima subtarefa adicionada
        #print("PROXIMA_TAREFA",PROXIMA_TAREFA)
        await self.update(PROXIMA_TAREFA)
        
        result = {}
        result['STATUS'] = "R"
        result['SAIDA'] = ""
        return result
     
                                           
    async def fetch_project(self,id):
        query = f"SELECT * FROM TAREFA  WHERE PROJETO_ID = {id} ORDER BY SEQUENCIA"
        
        tasks = await self.mysql.query(query)
        return tasks 

    async def gerar_sop(self,tarefa):
        #print("========== gerar_sop() ==========")
        if tarefa['AREA'] == 1:
            #print('VAMOS GERAR SOP? VAMOS')    
            sop  = await self.sopWriter.sop_website(tarefa['DESCRICAO'] + tarefa["CHECKLIST"])
            #print(sop)
            tarefa['SOP'] = sop
            #print('sop gerado')
            tarefa['SOP_GERAR'] = 0    
            if tarefa['AREA'] == 1:
                tarefa['ENTRADA'] =  json.dumps({"PAGINA":tarefa['DESCRICAO'],"REQUISITOS":tarefa['SOP'],"HTML":'',"SITE_ID":tarefa['SITE_ID']})
            
            
            #print("ENTRADA apos sop",tarefa['ENTRADA'])
            #taf = json.loads(tarefa['ENTRADA'])
            #print("taf pagina", taf['PAGINA'])
            #exit()
            
    async def execution(self):
        #print('========== agent_task.execution()==========')
        tarefas = await self.fetch_actives()
        # Uso do fuso horário padrão ao criar datetimes
        timezone = pytz.timezone('America/Bahia')
        #now = datetime.now(timezone)
        #print("Horário atual em Bahia:", now)

        
        if len(tarefas) == 0 :
            #print("No momento, nenhuma tarefa a ser executada")
            return 
        print('========== agent_task.execution()==========')
        print(f"Foram encontradas {len(tarefas)} a serem executadas")
        #for tarefa in tarefas:              
            #print('----------  TAREFA ATUAL ',tarefa['DESCRICAO'],"ID:",tarefa['ID'],"Sequencia: ",tarefa['SEQUENCIA'],)  
        #exit()
        for tarefa in tarefas:  
            print('----------  TAREFA ATUAL ',tarefa['DESCRICAO'],"ID:",tarefa['ID'],"Sequencia: ",tarefa['SEQUENCIA'],)  

            agente = tarefa['AGENTE_NOME']
            funcao_nome = tarefa['AGENTE_FUNCAO']
            status = tarefa['STATUS']
            #print(agente,agente_funcao,status)

            if status == "A" or status == "T" or status=="P":
            
                if tarefa['SOP_GERAR'] == 1 and tarefa['TIPO']==1:
                    #print("é para grar sop")
                    await self.gerar_sop(tarefa)
                    #print("tarefa após sop",tarefa)
            
                    
                #print("Status A: executando função...")
                if  agente and funcao_nome:
                    
                
                         #print(f"Tentativas ja realizadas: {tarefa['TENTATIVA']} " )
                         if  tarefa['TENTATIVA'] < 3:
                            tentativa = tarefa['TENTATIVA'] + 1
                            tarefa['TENTATIVA'] = tentativa
                            # Obtém a data e hora atuais
                            agora = datetime.now(timezone)
                            #print("agora",agora)
                            atualizacao = agora.strftime("%Y-%m-%d %H:%M:%S")
                            #print("atualizacao", atualizacao)
                            await self.update({"ID":tarefa["ID"],
                                         "TENTATIVA":tentativa,
                                         "ATUALIZACAO":atualizacao,
                                         "STATUS":"P"
                                         })

                            if tarefa["TIPO"] ==1:
                                    #print("vou executar a funcao ", funcao_nome)
                                    #print("tarefa",tarefa)                               
                                    #print("vou executar a functionCaller.call_function()", funcao_nome)
                                    #self.update({"ID":tarefa["ID"],"STATUS":"P"})
                                    result = await self.functionCaller.call_task_function(tarefa)
                                    tarefa['STATUS'] = result['STATUS']
                                    tarefa['SAIDA'] = result['SAIDA']

                                    #print("functionCaller.call_function executada com sucesso")
                            if tarefa["TIPO"] ==2:
                                   result =  await self.group_tasks_generate(tarefa)
                                   tarefa['STATUS'] = result['STATUS']
                                   tarefa['SAIDA'] = result['SAIDA']

                            #print("################ Resultado da chamada a funcao", tkUpdate)
                            #tkUpdate['ENTRADA'] = tarefa['ENTRADA'] if tarefa['ENTRADA'] else tarefa['ANTERIOR_SAIDA']
                        
                            #print("-----tkUpdate", tkUpdate)
                            #print("tarefa apos atualizacao", tarefa)
                            #Analisa status do retorno e toma ações aidicionais de acordo
                        
                            if tarefa['TENTATIVA'] > 2 and  tarefa['STATUS']!="R":                         
                                tarefa['STATUS']= "F"                       
                                #print("mudouo status para F")
                            
                            if tarefa['STATUS']=="F":
                                #print ('enviando o email final apos loop de tentativas')
                                await self.email.send_email_text_smtp(
                                    subject=f"GPTalk - Erro {tarefa['STATUS']}",
                                    body=f"""
                                    {tarefa['SAIDA']}
                                    """,
                                    recipient="<EMAIL>"
                                    )
                            
    
                           
                            if tarefa['STATUS']=="I":
                                #Email para o cliente solicitando informação ou aprovação
                                pass
    
                        
                           
                           
                            #print("Vou salvar a tarefa") 
                            tarefa.pop('ANTERIOR_STATUS')
                            tarefa.pop('ANTERIOR_SAIDA')
                            tarefa.pop('PROXIMA_STATUS')
                            tarefa.pop('SITE_ID')
                            
                            result = await self.update(tarefa)
                            #print('result final da tarefa (id)', result)
    
                            return "Tarefas executadas com sucesso"
                            #tarefa['ENTRADA'] = entrada
                            #tarefa['saida'] = saida 
                         else: #3 tentativas de execução com falha
                            tarefa["STATUS"]= "F"
                            tarefa["SAIDA"] = "3 tentativas de execução com falhas."
                            #print ('enviando o email final apos loop de 3 tentativas')
                            await self.email.send_email_text_smtp(
                              subject=f"GPTalk - Erro {tarefa['STATUS']}",
                              body=f"""
                              {tarefa['SAIDA']}
                             """,
                            recipient="<EMAIL>"
                            )
                            tarefa.pop('ANTERIOR_STATUS')
                            tarefa.pop('ANTERIOR_SAIDA')
                            tarefa.pop('PROXIMA_STATUS')

                            tarefa.pop('SITE_ID')
                            #print("salvando a situação final da taefa, registrando a falha")
                            #print(tarefa)
                            await self.update(tarefa) 

    async def task_get (self,query:str):
        #print("----- task_get() -----",query)
        result = await self.mysql.query(query)
        #print(result)
        result = await self.conv.convert_date_format_comma_hyphen(result)
        return result

    async def task_exclude (self,query:str):
         print("----- task_exclude() -----")
         #print("args", args)
         query = query
         print("query",query)
         tasks = await self.mysql.query(query)
         return {"Exclusão":"Realizada com sucesso"}

    async def task_add (self,query:str):
        #print("----- task_add() -----")
        #print("args", args)
        query = query
        # print("queryX",query)
        tasks = await self.mysql.query(query)
        
        return tasks

    async def task_update (self,query:str):
        #print("----- task_update() -----")
        #print("args", args)
        query = query
        # print("queryX",query)
        tasks = await self.mysql.query(query)
        
        return {"Resultado": "Tarefa atualizada com sucesso."}

@router.get("/execution")
async def  execution():
    llm = LLM()
   
    task = Task(model=llm.model_gemini.geminipro15)
    #print("vou executar o task execution")
    result = await task.execution()
    return result


if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map
    
    async def main():
        from datetime import date
        def default_serializable_string_converter(o):
            if isinstance(o, date):
                return o.strftime('%Y-%m-%d')
            raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable')

        # Convertendo o objeto em uma string JSON

        query = "SELECT DESCRICAO, DT_PREVISAO_INICIO, DT_PREVISAO_FIM, HORA_PREVISTA_INICIO, HORA_PREVISTA_FIM FROM TAREFA WHERE (DT_PREVISAO_INICIO = '2024-06-05' OR DT_PREVISAO_INICIO = '2024-06-06') AND TIPO = 1 AND STATUS = 'A' AND USUARIO_ID = 1"
        tsk = Task()
        conv = Converter

        result = await tsk.task_get(query)
        print(result)
        result = json.dumps(result,default=conv.default_serializable_string_converter)
        print(result)
    asyncio.run(main())