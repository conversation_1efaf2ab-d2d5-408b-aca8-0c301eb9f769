from ....agent_neo4j import AgentNeo4j
from agents.tool import function_tool 

neo4j = AgentNeo4j()    

@function_tool
async def compra_finalizar(compra_idx:str) -> dict:
    """
    finaliza uma compra. 
    """
    
    # Define os parâmetros  
    parametros = {
        "compra_idx": compra_idx,
    }

    query = """
    MATCH (c:Compra {idx: $compra_idx})
    MERGE (s:Status {nome: "Concluido"})
    MERGE (c)-[:TEM_STATUS]->(s)
    RETURN c
    """  
    
    compra_finalizada = await neo4j.execute_write_query(query, parametros)
    
    if compra_finalizada and len(compra_finalizada) > 0:
        return {"compra_finalizada": True}
    else:
        return {"compra_finalizada": False}
    
    
if __name__ == "__main__":
    import asyncio

    async def testa_compra_finalizar():
        compra_idx = "1122334455"
        result = await compra_finalizar(compra_idx)
        print(result)

    asyncio.run(testa_compra_finalizar())    