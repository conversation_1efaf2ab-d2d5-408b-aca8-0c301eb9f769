#Colab
#https://colab.research.google.com/drive/1Smk_EJ6YeGsXvssPMgVNGSta6N2VRBtN#scrollTo=K4BjGlwHeTlH&uniqifier=1

import requests


def cpanel_subdomain_exists(data):
    user = data["USER"]
    token = data["TOKEN"]
    domain = data["DOMAIN"]
    subdomain = data["SUBDOMAIN"]



    headers = {
        "Authorization": f"cpanel {user}:{token}"
    }

    url = f"https://{domain}:2083/execute/DomainInfo/list_domains"

    response = requests.get(url, headers=headers, verify=True)
    data = response.json()

    if 'sub_domains' in data['data']:
        for item in data['data']['sub_domains']:
            if subdomain in item:
                return True
    return False


async def cpanel_get_file_content(data):
    """
    Função para extrair o conteúdo de um arquivo específico hospedado em um subdomínio no cPanel.

    A chave `data` é um dicionário contendo:
        - DOMAIN: O domínio do cPanel
        - USER: O usuário do cPanel
        - TOKEN: O token de autenticação
        - FILEPATH: O caminho completo do arquivo que será lido

    Returns:
        dict: Resposta JSON da API, incluindo o conteúdo do arquivo.
    """
    domain = data['DOMAIN']
    user = data["USER"]
    token = data["TOKEN"]
    dir = data["SUBDIRECTORY"]
    file = data["FILENAME"]


    # Cabeçalhos necessários para a autenticação
    headers = {
        "Authorization": f"cpanel {user}:{token}"
    }

    # A URL para a API de Obter Conteúdo do Arquivo
    url = f"https://{domain}:2083/execute/Fileman/get_file_content"

    # Parâmetros para a requisição
    params = {
        'dir': dir,
        'file': file
    }

    # Faça a solicitação GET para obter o conteúdo do arquivo
    response = requests.get(url, headers=headers, params=params, verify=True)

    # Tentar decodificar a resposta JSON
    try:
        response_data = response.json()
    except ValueError:
        response_data = {'error': 'Failed to decode JSON response'}

    return response_data["data"]["content"]

#============================
async def cpanel_file_list(data):
 
    domain = data['DOMAIN']
    user = data["USER"]
    token = data["TOKEN"]
    directory = data["SUBDIRECTORY"]

    # Cabeçalhos necessários para a autenticação
    headers = {
        "Authorization": f"cpanel {user}:{token}"
    }

    # A URL para a API de Listar Arquivos
    url = f"https://{domain}:2083/execute/Fileman/list_files"

    # Parâmetros para a requisição
    params = {
        'dir': directory
    }

    # Faça a solicitação GET para listar os arquivos
    response = requests.get(url, headers=headers, params=params, verify=True)

    # Tentar decodificar a resposta JSON
    try:
        response_data = response.json()
        response_files = await extrair_nomes_arquivos(response_data)
        return response_files
    except ValueError:
        response_data = {'error': 'Failed to decode JSON response'}

    return response_data

#=============================================
async def extrair_nomes_arquivos(resposta_api):
    """
    Extrai os nomes dos arquivos a partir da resposta da API do cPanel.

    Args:
        resposta_api (dict): Resposta da API de listagem de arquivos do cPanel.

    Returns:
        list: Uma lista contendo apenas os nomes dos arquivos.
    """
    nomes_arquivos = []
    dados = resposta_api.get('data', [])

    for item in dados:
        if item.get('type') == 'file':  # Verifica se o item é um arquivo
            nome_arquivo = item.get('file')  # Obtém o nome do arquivo
            if nome_arquivo:
                nomes_arquivos.append(nome_arquivo)

    return nomes_arquivos

#=====================================
async def cpanel_subdomain_create(data):
    print("cpanel_subomain_create()")
    domain = data['DOMAIN']
    subdomain =  data['SUBDOMAIN']
    document_root = data["SUBDIRECTORY"]
    user = data["USER"]
    token  = data["TOKEN"]


    # Cabeçalhos necessários para a autenticação
    headers = {
    "Authorization": f"cpanel {user}:{token}"
    }


  # Codificar o document_root para uso na URL
  

    # A URL foi ajustada para incluir o parâmetro 'dir' para o document_root
    url = f"https://{domain}:2083/execute/SubDomain/addsubdomain?domain={subdomain}&rootdomain={domain}&dir={document_root}"
    print("url",url)
    # Faça a solicitação GET
    response = requests.get(url, headers=headers, verify=True)
    print("response", response)
    return response.json()

#=======================================
import requests
from io import BytesIO

async def cpanel_subdomain_file_save(data):
    print("cpanel_subdomain_file_save()")
    """
    Função para salvar um conteúdo como um arquivo específico em um subdomínio no cPanel.

    A chave `data` é um dicionário contendo:
        - DOMAIN: O domínio do cPanel
        - USER: O usuário do cPanel
        - TOKEN: O token de autenticação
        - CONTENT: O conteúdo a ser salvo no arquivo
        - SUBDIRECTORY: O diretório no subdomínio onde o arquivo será salvo
        - FILENAME: O nome do arquivo a ser criado/salvo

    Returns:
        dict: Resposta JSON da API.
    """
    domain = data['DOMAIN']
    user = data["USER"]
    token = data["TOKEN"]
    html_content = data["CONTENT"]
    upload_directory = data["SUBDIRECTORY"] + "/" + data["TARGET"]
    filename = data["FILENAME"]
    print("upload_directory", upload_directory)
    # Cabeçalhos necessários para a autenticação
    headers = {
        "Authorization": f"cpanel {user}:{token}"
    }



    # A URL para a API Fileman de upload de arquivo
    url = f"https://{domain}:2083/execute/Fileman/upload_files"

    # Criar um arquivo em memória com o conteúdo a ser salvo
    file_like_object = BytesIO(html_content.encode('utf-8'))

    # Preparar os dados do arquivo para upload
    files = {'file-1': (filename, file_like_object)}

    # Parâmetros para definir o diretório de destino do upload
    data = {
        'dir': upload_directory,
        'overwrite': 1  # Adiciona esta linha para sobrescrever o arquivo existente
    }

    # Faça a solicitação POST para upload do arquivo
    response = requests.post(url, headers=headers, files=files, data=data, verify=True)
    #print("response",response)
    # Fechar o arquivo em memória para liberar recursos
    file_like_object.close()

    return response.status_code

#====================================
async def cpanel_subdomain_exists(data):
    user = data["USER"] 
    token = data["TOKEN"]
    domain = data["DOMAIN"]
    subdomain = data["SUBDOMAIN"]
    
    headers = {
        "Authorization": f"cpanel {user}:{token}"
    }
    
    url = f"https://{domain}:2083/execute/DomainInfo/list_domains"

    response = requests.get(url, headers=headers, verify=True)
    data = response.json()

    if 'sub_domains' in data['data']:
        for item in data['data']['sub_domains']:
            if subdomain in item:
                return True
    return False
#================================
import asyncio

data = {}
data["DOMAIN"] = "site.gptalk.com.br"
data["SUBDOMAIN"] = "a1"
data["SUBDIRECTORY"] = "site/sites/sbt4"
data["USER"] = "gptalk"
data["TOKEN"] = "0ZIAKOBQD195AU35UQZXNUSC00CNHKW1"
data["CONTENT"] = "<h1>BOM DIA3333333</H1>"
data["FILENAME"] =  "index2.html"
data["TARGET"] = "images"

async def main():

   result = await cpanel_subdomain_create(data)
   print("result",result)
   if result["status"] == 1:
       print("Subominio criado com sucesso")
   else:
       print("Erro na criação do subdominio")
   
   
#asyncio.run(main())