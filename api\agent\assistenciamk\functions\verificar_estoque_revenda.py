import re
import platform

# Função para singularizar e normalizar termos compostos (adaptada de gera_cartao_produto)
def processar_termos_compostos(termos):
    if not termos or not isinstance(termos, str):
        return termos

    termos = termos.strip()

    if ' AND ' in termos.upper() or ' OR ' in termos.upper():
        return termos

    if termos.isdigit():
        return termos

    palavras_ignoradas = {
        'de', 'da', 'do', 'das', 'dos', 'a', 'o', 'as', 'os', 'um', 'uma', 'uns', 'umas',
        'para', 'por', 'per', 'com', 'sem', 'sob', 'sobre', 'em', 'no', 'na', 'nos', 'nas',
        'a', 'à', 'às', 'ao', 'aos', 'pelo', 'pela', 'pelos', 'pelas', 'até', 'entre',
        'durante', 'contra', 'desde', 'após', 'antes', 'depois', 'como', 'que',
        'se', 'e', 'ou', 'mas', 'mais', 'muito', 'pouco', 'muitos', 'poucos', 'este',
        'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas', 'aquele', 'aquela',
        'aqueles', 'aquelas', 'meu', 'minha', 'meus', 'minhas', 'seu', 'sua', 'seus', 'suas'
    }

    palavras_invariaveis = {'jeans'}
    preservar_plural_diante_de = {'sapatos'}
    preposicoes_de = {'de', 'do', 'da', 'dos', 'das'}

    regras_pt = [
        (r'(ões)$', 'ão'),
        (r'(ães)$', 'ão'),
        (r'(is)$', 'l'),
        (r'(éis)$', 'el'),
        (r'(eis)$', 'el'),
        (r'(óis)$', 'ol'),
        (r'(uis)$', 'ul'),
        (r'(ns)$', 'm'),
        (r'(rs)$', 'r'),
        (r'(is)$', 'il'),
        (r'(?<!e)s$', ''),
    ]

    regras_en = [
        (r'(ies)$', 'y'),
        (r'(ves)$', 'f'),
        (r'(ses)$', 's'),
        (r'(s)$', ''),
    ]

    def singularizar_palavra(palavra):
        palavra_lower = palavra.lower()
        if palavra_lower in palavras_invariaveis:
            return palavra_lower
        for plural, singular in regras_pt:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        for plural, singular in regras_en:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        return palavra_lower

    palavras = [p.strip() for p in termos.split()]
    palavras_processadas = []

    for i, palavra in enumerate(palavras):
        p_lower = palavra.lower()
        prox = palavras[i + 1].strip().lower() if i + 1 < len(palavras) else ''
        if p_lower in preservar_plural_diante_de and prox in preposicoes_de:
            palavra_final = p_lower
        else:
            palavra_final = singularizar_palavra(palavra)
        if palavra_final not in palavras_ignoradas:
            palavras_processadas.append(palavra_final)

    if not palavras_processadas:
        return ""
    if len(palavras_processadas) == 1:
        return palavras_processadas[0]
    return ' AND '.join(palavras_processadas)
from agents.tool import function_tool
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()

@function_tool
async def verificar_estoque_revenda(params: str, termo_consulta: str):
        """
        VERIFICA O ESTOQUE PESSOAL DA REVENDA do usuário.
        
        ⚠️⚠️⚠️ ATENÇÃO: ESTA FUNÇÃO NÃO DEVE SER USADA DURANTE O FLUXO DE COMPRA ⚠️⚠️⚠️
        
        Esta função deve ser usada APENAS para verificar o estoque PESSOAL do revendedor.
        NÃO use para consultar o catálogo geral de produtos.

        🚫 QUANDO NÃO USAR ESTA FUNÇÃO:
        - Durante o fluxo de compra de produtos (use consultar_catalogo_produtos em vez disso)
        - Para verificar disponibilidade antes de adicionar ao carrinho
        - Para consultar o catálogo geral de produtos
        - Para buscar informações técnicas sobre produtos
        
        ✅ QUANDO USAR ESTA FUNÇÃO:
        - Para verificar o que tem no seu estoque PESSOAL
        - Para verificar quantos itens VOCÊ tem disponíveis para venda
        - Para listar os produtos que VOCÊ está revendendo
        
        🔍 REGRAS IMPORTANTES:
        - NUNCA verifique o estoque durante o fluxo de compra
        - Ignore completamente a informação de estoque ao adicionar itens a uma compra
        - A informação de estoque zero NÃO deve impedir a adição de itens a uma compra
        - NÃO mostre mensagens sobre estoque zero durante o fluxo de compra
        
        🔍 REGRAS OBRIGATÓRIAS NA QUERY:
        - MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
        - MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
        - CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termo_consulta) YIELD node AS prod
        - MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
        - WHERE p.excluido <> 1
        - Retorna dados principais do produto e estoque da instância

        Parâmetros obrigatórios:
            - usuario_idx: ID do usuário revendedor
            - negocio_idx: ID do negócio (Mary Kay)
            - termo_consulta: termo de busca (ex: "batom AND matte")

        Exemplo de uso:
            params = '{"usuario_idx": "123", "negocio_idx": "456"}'
            verificar_estoque_revenda(params, termo_consulta="batom AND matte")
        """
        print("===== verificar_estoque_revenda() =====")
        print(f"Params: {params}")
        print(f"Termo consulta (original): {termo_consulta}")

        termo_normalizado = processar_termos_compostos(termo_consulta)
        print(f"Termo consulta (normalizado): {termo_normalizado}")
        try:
            p = json.loads(params)
            usuario_idx = p.get("usuario_idx")
            negocio_idx = p.get("negocio_idx")
            if not usuario_idx or not negocio_idx:
                return {"status": "error", "message": "usuario_idx e negocio_idx obrigatórios"}

            # Detecta se é para listar tudo
            termo_tudo = not termo_normalizado or termo_normalizado.strip() == "" or termo_normalizado.lower() in ["todos", "todo", "tudo", "all", "*"]
            if termo_tudo:
                query = """
                MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
                MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
                MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
                OPTIONAL MATCH (prod)-[:TEM_COR]->(cor:Cor)
                WHERE p.excluido <> 1
                RETURN prod.codigo AS codigo, prod.nome AS nome, prod.preco AS preco, pr.estoque AS estoque, prod.descricao AS descricao, cor.nome AS cor , prod.url_imagem
                ORDER BY prod.nome
                """
                p.pop("termo_consulta", None)
            else:
                p["termo_consulta"] = termo_normalizado
                query = """
                MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
                MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
                CALL db.index.fulltext.queryNodes('produtoNomeCodigoFT', $termo_consulta) YIELD node AS prod
                MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
                OPTIONAL MATCH (prod)-[:TEM_COR]->(cor:Cor)
                WHERE p.excluido <> 1
                RETURN prod.codigo AS codigo, prod.nome AS nome, prod.preco AS preco, pr.estoque AS estoque, prod.descricao AS descricao, cor.nome AS cor, prod.url_imagem
                ORDER BY prod.nome
                """
            print("@@@@@ query", query)
            print("@@@@@ params", p)

            result = await neo4j.execute_read_query(query, p)
            print("@@@@@ Resultado da consulta:", result)
            return {"status": "success", "data": result}
        except Exception as e:
            return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    import asyncio

    async def testa_produto_estoque():
        params = '{"usuario_idx": "1122334455", "negocio_idx": "5544332211"}'
        # Exemplo de termo de consulta: busca por "batom AND matte"
        #resultado = await verificar_estoque_revenda(params, termo_consulta="10221715")
        #resultado = await verificar_estoque_revenda(params, termo_consulta="kit noite")
        resultado = await verificar_estoque_revenda(params, termo_consulta="todos")
        print("Resultado da consulta:", resultado)

    asyncio.run(testa_produto_estoque())