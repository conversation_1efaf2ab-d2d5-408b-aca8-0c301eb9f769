# core/classificador.py
import re
from typing import Dict, Any
from .extrator import ExtratorParametros

class ClassificadorQuery:
    def __init__(self):
        self.extrator = ExtratorParametros()
        
        # Padrões hierárquicos com casos específicos primeiro
        self.hierarquia_classificacao = [
            {
                'tipo': 'produto_codigo',
                'regex': r'código?\s*\d+|produto\s+\d+|item\s+\d+',
                'keywords': ['código', 'cod', 'id', 'produto', 'item']
            },
            {
                'tipo': 'produto_categoria',
                'regex': r'categoria\s+\w+|tipo\s+\w+|classe\s+\w+',
                'keywords': ['categoria', 'tipo', 'classe', 'seção']
            },
            {
                'tipo': 'top_produtos',
                'regex': r'top\s+\d+\s+produtos?|\d+\s+mais\s+(caros?|vendidos?|baratos?)',
                'keywords': ['top', 'mais', 'menos']
            },
            {
                'tipo': 'lista_clientes',
                'regex': r'quantos\s+(clientes?|usuários?)|lista\s+de\s+(clientes?|usuários?)',
                'keywords': ['cliente', 'clientes', 'quantos', 'lista']
            },
            {
                'tipo': 'analise_dados',
                'regex': r'analisar|tendência|desempenho|qual\s+o?\s+(mais|menos)\s+(vendido|caro|barato)',
                'keywords': ['analisar', 'tendência', 'desempenho', 'qual', 'mais', 'menos', 'vendido', 'caro', 'barato']
            },
            {
                'tipo': 'lista_cores',
                'keywords': ['cor', 'cores', 'disponível', 'opções', 'paleta', 'variações', 'tons']
            },
            {
                'tipo': 'produto_visual',
                'keywords': ['produto', 'item', 'mercadoria', 'mostrar', 'listar', 'exibir', 'buscar', 'encontrar', 'ver', 'apresentar'],
                'excludes': ['quantos', 'quanto', 'total', 'média', 'analisar', 'tendência', 'comparação', 'projecao', 'estatística']
            }
        ]
    
    def classificar(self, query: str) -> Dict[str, Any]:
        """Versão final otimizada"""
        query_lower = query.lower().strip()
        params = self.extrator.extrair(query_lower)
        
        # 1. Casos diretos com regex mais abrangente
        if re.search(r'cor|cores?.*disponíveis?|quais.*cores?', query_lower):
            params['contexto'] = 'cor'
            return {
                "tipo": "lista_cores",
                "confianca": 0.95,
                "parametros": params
            }
        
        if re.search(r'ver.*produtos?.*preço?|produtos?.*até\s+R?\$?\d+', query_lower):
            params['contexto'] = 'produto'
            return {
                "tipo": "produto_visual",
                "confianca": 0.90,
                "parametros": params
            }
        
        # 2. Mantém os casos específicos já funcionando
        if re.search(r'top\s+\d+\s+produtos?', query_lower):
            params['contexto'] = 'produto'
            return {
                "tipo": "produto_visual",
                "confianca": 0.95,
                "parametros": params
            }
        
        if re.search(r'quantos\s+(clientes?|usuários?)', query_lower):
            params['contexto'] = 'cliente'
            return {
                "tipo": "lista_clientes",
                "confianca": 0.90,
                "parametros": params
            }
        
        if re.search(r'analisar|tendência|desempenho|qual.*mais.*vendido', query_lower):
            return {
                "tipo": "analise_dados",
                "confianca": 0.90,
                "parametros": params
            }
        
        # 3. Regex principais
        for padrao in self.hierarquia_classificacao:
            if 'regex' in padrao:
                if re.search(padrao['regex'], query_lower, re.IGNORECASE):
                    return {
                        "tipo": padrao['tipo'],
                        "confianca": 0.95,
                        "parametros": params
                    }
        
        # 4. Keywords por contexto
        if 'cor' in params.get('contexto', ''):
            return {
                "tipo": "lista_cores",
                "confianca": 0.85,
                "parametros": params
            }
        
        if 'cliente' in params.get('contexto', ''):
            return {
                "tipo": "lista_clientes",
                "confianca": 0.85,
                "parametros": params
            }
        
        if 'produto' in params.get('contexto', ''):
            return {
                "tipo": "produto_visual",
                "confianca": 0.80,
                "parametros": params
            }
        
        return {
            "tipo": "agente",
            "confianca": 0.0,
            "parametros": params
        }
        
# Teste rápido
if __name__ == "__main__":
    classificador = ClassificadorQuery()
    
    testes = [
        "Mostre produto código 123456",
        "Listar produtos categoria Lanches",
        "Quais cores disponíveis?",
        "Quantos clientes ativos?",
        "Top 10 produtos mais caros hoje",
        "Analisar tendências de vendas do mês",
        "Qual o produto mais vendido?",
        "Ver produtos preço até 50 reais"
    ]
    
    for teste in testes:
        resultado = classificador.classificar(teste)
        print(f"Query: {teste}")
        print(f"Tipo: {resultado['tipo']} | Confiança: {resultado['confianca']:.2f}")
        print(f"Params: {resultado['parametros']}")
        print("-" * 50)