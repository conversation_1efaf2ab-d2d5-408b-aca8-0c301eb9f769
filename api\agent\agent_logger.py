from fastapi import APIRouter
from fastapi.responses import StreamingResponse
import os
from pathlib import Path
import logging
import sys
from datetime import datetime, timezone, timedelta
import time
import threading
import glob
import asyncio
import aiofiles
import re

import pytz
    
USE_PYTZ = True

brasilia_tz = pytz.timezone('America/Sao_Paulo')
timestamp = datetime.now(brasilia_tz).strftime('%Y-%m-%d %H:%M:%S')


# Importação condicional do fcntl (não disponível no Windows)
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    # Windows não tem fcntl, vamos usar uma implementação alternativa
    fcntl = None
    HAS_FCNTL = False

# Importação condicional do pytz

# Lock global para operações no arquivo
file_lock = threading.Lock()

router = APIRouter()


class BrasiliaFormatter(logging.Formatter):
    """Formatter personalizado com timezone brasileiro"""
    def formatTime(self, record, datefmt=None):
        if USE_PYTZ:
            # Converte o timestamp do record para timezone de Brasília (com horário de verão)
            dt = datetime.fromtimestamp(record.created, tz=brasilia_tz)
        else:
            # Converte para UTC-3 fixo (sem horário de verão)
            dt = datetime.fromtimestamp(record.created, tz=brasilia_tz)
        
        if datefmt:
            return dt.strftime(datefmt)
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]  # Remove os últimos 3 dígitos dos microseconds


class DailyFileHandler(logging.FileHandler):
    """Handler personalizado para arquivo por dia"""
    def __init__(self, logs_dir, encoding='utf-8'):
        self.logs_dir = logs_dir
        self.encoding = encoding
        self.current_date = None
        self.current_file = None
        
        # Criar diretório de logs se não existir
        os.makedirs(logs_dir, exist_ok=True)
        
        # Inicializar com o arquivo atual
        super().__init__(self._get_current_filename(), encoding=encoding, mode='a')
    
    def _get_current_filename(self):
        """Retorna o nome do arquivo para a data atual"""
        if USE_PYTZ:
            current_date = datetime.now(brasilia_tz).strftime('%Y_%m_%d')
        else:
            current_date = datetime.now(brasilia_tz).strftime('%Y_%m_%d')
        
        return os.path.join(self.logs_dir, f"{current_date}.log")
    
    def _check_date_change(self):
        """Verifica se a data mudou e atualiza o arquivo se necessário"""
        if USE_PYTZ:
            current_date = datetime.now(brasilia_tz).strftime('%Y_%m_%d')
        else:
            current_date = datetime.now(brasilia_tz).strftime('%Y_%m_%d')
        
        if self.current_date != current_date:
            self.current_date = current_date
            new_filename = self._get_current_filename()
            
            # Fechar o arquivo atual
            if hasattr(self, 'stream') and self.stream:
                self.stream.close()
            
            # Abrir o novo arquivo
            self.baseFilename = new_filename
            self.stream = self._open()
    
    def emit(self, record):
        """Emite o log, verificando se precisa trocar de arquivo"""
        self._check_date_change()
        super().emit(record)


async def _safe_tail_lines(file_path, max_lines=100, chunk_size=8192, max_memory_mb=50):
    """
    Lê as últimas N linhas de um arquivo de forma eficiente sem carregar tudo na memória.
    
    Args:
        file_path: Caminho do arquivo
        max_lines: Número máximo de linhas a retornar
        chunk_size: Tamanho do chunk para leitura reversa
        max_memory_mb: Limite máximo de memória a usar (MB)
    
    Returns:
        Lista das últimas linhas do arquivo
    """
    try:
        # Calcula limite de memória em bytes
        max_memory_bytes = max_memory_mb * 1024 * 1024
        
        # Otimização para arquivos pequenos removida para garantir a leitura dos dados mais recentes e evitar cache.
        # A lógica a seguir (tail reverso) agora é usada para todos os arquivos.
        
        lines = []
        bytes_read = 0
        
        if not os.path.exists(file_path):
            return []

        async with aiofiles.open(file_path, 'rb') as f:
            # Começa do final do arquivo
            await f.seek(0, 2)  # SEEK_END
            file_size = await f.tell()
            
            # Buffer para linha parcial
            buffer = b""
            position = file_size
            
            while len(lines) < max_lines and position > 0 and bytes_read < max_memory_bytes:
                # Calcula próxima posição
                read_size = min(chunk_size, position)
                position -= read_size
                
                # Lê chunk
                await f.seek(position)
                chunk = await f.read(read_size)
                bytes_read += len(chunk)
                
                # Adiciona ao buffer
                chunk = chunk + buffer
                
                # Separa em linhas
                chunk_lines = chunk.split(b'\n')
                
                # A primeira "linha" pode ser parcial
                buffer = chunk_lines[0]
                
                # Processa linhas completas (de trás para frente)
                for line_bytes in reversed(chunk_lines[1:]):
                    try:
                        line = line_bytes.decode('utf-8', errors='ignore')
                        if line.strip():  # Ignora linhas vazias
                            lines.append(line)
                            if len(lines) >= max_lines:
                                break
                    except:
                        continue
            
            # Processa buffer final se houver
            if buffer and len(lines) < max_lines:
                try:
                    line = buffer.decode('utf-8', errors='ignore')
                    if line.strip():
                        lines.append(line)
                except:
                    pass
        
        # Retorna na ordem correta (mais antigas primeiro)
        return list(reversed(lines))
        
    except Exception as e:
        print(f"Erro em _safe_tail_lines para {file_path}: {str(e)}")
        return []


class AgentLogger:
    """
    Classe unificada de logging com arquivos por dia e API endpoints.
    
    Uso:
        from .agent_logger import AgentLogger
        logger = AgentLogger()
        logger.info("mensagem")
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        """Singleton pattern para garantir uma única instância"""
        if cls._instance is None:
            cls._instance = super(AgentLogger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, model=None, user=None, level=logging.INFO):
        # Evita reinicialização múltipla
        if self._initialized:
            return
            
        # Configuração de caminhos
        current_dir = os.path.dirname(os.path.abspath(__file__))  # /gptalk/server/api/agent/
        self.logs_dir = os.path.join(current_dir, "..", "..", "logs")  # /gptalk/server/logs/
        self.old_log_file_path = os.path.join(current_dir, "..", "..", "server.log")
        
        # Configuração do logger interno
        self._setup_logging(level)
        
        # Marca como inicializado
        self._initialized = True
        
        # Log de inicialização para testar se está funcionando
        #print("[AgentLogger] Inicializado com console e arquivo em DEBUG.")
    
    def _setup_logging(self, level):
        """Configura o sistema de logging interno"""
        # Cria logger interno
        self._logger = logging.getLogger('gptalk')
        self._logger.setLevel(level)
        
        # Remove handlers existentes para evitar duplicação
        for handler in self._logger.handlers[:]:
            self._logger.removeHandler(handler)
        
        # Cria file handler com arquivo por dia
        file_handler = DailyFileHandler(self.logs_dir, encoding='utf-8')
        file_formatter = BrasiliaFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        self._logger.addHandler(file_handler)

        # Cria (ou garante) handler de console em DEBUG
        console_handler = logging.StreamHandler(sys.stderr)
        console_handler.setLevel(logging.DEBUG)
        console_formatter = BrasiliaFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        self._logger.addHandler(console_handler)

        # Garante que o root logger também tenha pelo menos 1 handler de console
        root_logger = logging.getLogger()
        if not root_logger.handlers:
            root_console_handler = logging.StreamHandler(sys.stderr)
            root_console_handler.setLevel(logging.DEBUG)
            root_console_handler.setFormatter(console_formatter)
            root_logger.addHandler(root_console_handler)
        root_logger.setLevel(level)
        root_logger.propagate = True

        # O logger 'gptalk' deve propagar para o root
        self._logger.propagate = True

        
        # Evita propagação para o logger root
        self._logger.propagate = False
    
    # Métodos de logging públicos
    def debug(self, message, *args, **kwargs):
        """Log level DEBUG"""
        self._logger.debug(message, *args, **kwargs)
        print("DEBUG: [{timestamp}] {message}")
    
    def info(self, message, *args, **kwargs):
        """Log level INFO"""
        self._logger.info(message, *args, **kwargs)
        # SOLUÇÃO SIMPLES: print direto que SEMPRE funciona
        timestamp = datetime.now(brasilia_tz).strftime('%Y-%m-%d %H:%M:%S')
        #print(f"INFO: [{timestamp}] {message}")
    
    def warning(self, message, *args, **kwargs):
        """Log level WARNING"""
        self._logger.warning(message, *args, **kwargs)
        print(f"WARNING: [{timestamp}] {message}")
    
    def error(self, message, *args, **kwargs):
        """Log level ERROR"""
        self._logger.error(message, *args, **kwargs)
        print(f"ERROR: [{timestamp}] {message}")
    
    def critical(self, message, *args, **kwargs):
        """Log level CRITICAL"""
        self._logger.critical(message, *args, **kwargs)
        print(f"CRITICAL: [{timestamp}] {message}")
    
    def exception(self, message, *args, exc_info=True, **kwargs):
        """Log exception with traceback"""
        self._logger.error(message, *args, exc_info=exc_info, **kwargs)
        print(f"EXCEPTION: [{timestamp}] {message}")
    
    def set_level(self, level):
        """Altera o nível de log"""
        self._logger.setLevel(level)
    
    def debug_logging_levels(self):
        """Debug dos níveis de logging"""
        import sys
        # Forçar saída direta no stderr (não capturado pelo Uvicorn)
        sys.stderr.write("=== DEBUG LOGGING LEVELS ===\n")
        sys.stderr.write(f"Logger principal level: {self._logger.level}\n")
        sys.stderr.write(f"Logger principal effective level: {self._logger.getEffectiveLevel()}\n")
        
        for i, handler in enumerate(self._logger.handlers):
            sys.stderr.write(f"Handler {i}: {type(handler).__name__}\n")
            sys.stderr.write(f"  Level: {handler.level}\n")
            sys.stderr.write(f"  Formatter: {type(handler.formatter).__name__ if handler.formatter else 'None'}\n")
        
        # Teste direto
        sys.stderr.write("=== TESTE DIRETO ===\n")
        sys.stderr.write("TESTE INFO - deve aparecer no console\n")
        sys.stderr.write("=== FIM DEBUG ===\n")
        sys.stderr.flush()
    
    def force_console_logging(self):
        """Força logging no console"""
        import sys
        self._logger.setLevel(logging.DEBUG)
        # Garante pelo menos um handler de console
        has_console = any(isinstance(h, logging.StreamHandler) and not hasattr(h, 'baseFilename') for h in self._logger.handlers)
        if not has_console:
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setLevel(logging.DEBUG)
            console_formatter = BrasiliaFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            self._logger.addHandler(console_handler)
        for handler in self._logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not hasattr(handler, 'baseFilename'):
                handler.setLevel(logging.DEBUG)
                handler.stream = sys.stderr
                sys.stderr.write("Console handler level ajustado para DEBUG\n")
                sys.stderr.flush()
        # Garante propagação para root
        self._logger.propagate = True
        logging.getLogger().setLevel(logging.DEBUG)
        logging.getLogger().propagate = True

    

    
    # Métodos de gerenciamento de arquivos (compatibilidade com API)
    def get_logs_dir(self):
        """Retorna o caminho absoluto da pasta logs"""
        return Path(self.logs_dir).resolve()
    
    def get_log_file_for_date(self, date_str):
        """Retorna o caminho do arquivo de log para uma data específica (yyyy_mm_dd)"""
        filename = f"{date_str}.log"
        return os.path.join(self.logs_dir, filename)
    
    def get_all_log_files(self):
        """Retorna lista de todos os arquivos de log ordenados por data"""
        if not os.path.exists(self.logs_dir):
            return []
        
        # Busca todos os arquivos .log na pasta
        pattern = os.path.join(self.logs_dir, "*.log")
        log_files = glob.glob(pattern)
        
        # Ordena por nome (que corresponde à data devido ao formato yyyy_mm_dd)
        log_files.sort()
        return log_files
    
    def date_to_filename_format(self, date_obj):
        """Converte objeto datetime para formato de nome de arquivo (yyyy_mm_dd)"""
        return date_obj.strftime('%Y_%m_%d')
    
    def logs_dir_exists(self):
        """Verifica se a pasta logs existe"""
        return os.path.exists(self.logs_dir)
    
    def get_total_logs_size(self):
        """Retorna o tamanho total de todos os arquivos de log"""
        if not self.logs_dir_exists():
            return 0
        
        total_size = 0
        for log_file in self.get_all_log_files():
            if os.path.exists(log_file):
                total_size += os.path.getsize(log_file)
        return total_size
    
    # Métodos de compatibilidade com código antigo
    def get_log_file_path(self):
        """Retorna o caminho do arquivo mais recente ou cria um novo para hoje"""
        today = datetime.now().strftime('%Y_%m_%d')
        return self.get_log_file_for_date(today)
    
    def file_exists(self):
        """Verifica se existe pelo menos um arquivo de log"""
        return len(self.get_all_log_files()) > 0 or os.path.exists(self.old_log_file_path)
    
    def get_file_size(self):
        """Retorna o tamanho total de todos os logs"""
        return self.get_total_logs_size()


# Instância global para compatibilidade (REMOVER depois da migração completa)
logger = AgentLogger()

@router.get("/download")
async def download_server_log():
    """
    Endpoint para download de todos os logs em um arquivo consolidado
    Retorna todos os logs como stream para download eficiente
    """
    logger.info(f"🔄 INICIANDO PROCESSO DE DOWNLOAD dos logs")
    
    with file_lock:
        agent = AgentLogger()
        
        logger.info(f"=== DEBUG DOWNLOAD LOGS ===")
        logger.info(f"Pasta logs: {agent.logs_dir}")
        logger.info(f"Pasta logs existe? {agent.logs_dir_exists()}")
        
        log_files = agent.get_all_log_files()
        logger.info(f"Arquivos encontrados: {len(log_files)}")
        
        if not log_files:
            # Verifica se existe o arquivo antigo
            if os.path.exists(agent.old_log_file_path):
                logger.info("Usando arquivo antigo server.log para compatibilidade")
                log_files = [agent.old_log_file_path]
            else:
                error_msg = f"Nenhum arquivo de log encontrado"
                logger.error(f"❌ ERRO: {error_msg}")
                return {"error": error_msg, "status": "error"}
        
        # Calcula tamanho total
        total_size = sum(os.path.getsize(f) for f in log_files if os.path.exists(f))
        logger.info(f"📁 Tamanho total: {total_size} bytes ({round(total_size / (1024 * 1024), 2)} MB)")
        
        # Headers para download
        headers = {
            "Content-Disposition": "attachment; filename=logs_consolidados.log",
            "Content-Length": str(total_size),
            "Content-Type": "application/octet-stream"
        }
        
        # Função geradora para streaming consolidado
        def generate():
            try:
                for log_file in log_files:
                    logger.info(f"📄 Processando: {os.path.basename(log_file)}")
                    
                    # Adiciona separador entre arquivos
                    separator = f"\n{'='*50}\n=== {os.path.basename(log_file)} ===\n{'='*50}\n".encode()
                    yield separator
                    
                    # Lê e envia o arquivo
                    with open(log_file, "rb") as file:
                        while chunk := file.read(8192):
                            yield chunk
                    
                    yield b"\n"  # Nova linha entre arquivos
                    
            except Exception as e:
                error_msg = f"❌ ERRO durante streaming: {str(e)}"
                yield error_msg.encode()
        
        return StreamingResponse(
            generate(),
            media_type="application/octet-stream",
            headers=headers
        )


@router.get("/info")
async def get_log_info():
    """
    Endpoint para obter informações sobre os arquivos de log
    """
    logger.info(f"ℹ️ SOLICITAÇÃO DE INFO dos logs")
    agent = AgentLogger()
    
    log_files = agent.get_all_log_files()
    total_size = agent.get_total_logs_size()
    
    files_info = []
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            files_info.append({
                "filename": os.path.basename(log_file),
                "path": log_file,
                "size": size,
                "size_mb": round(size / (1024 * 1024), 2)
            })
    
    result = {
        "logs_dir": agent.logs_dir,
        "logs_dir_exists": agent.logs_dir_exists(),
        "total_files": len(files_info),
        "files": files_info,
        "total_size": total_size,
        "total_size_mb": round(total_size / (1024 * 1024), 2)
    }
    
    #logger.info(f"✅ INFO retornado: {len(files_info)} arquivos, {result['total_size_mb']} MB")
    return result


@router.delete("/clear")
async def clear_server_log():
    """
    Endpoint para limpar/deletar todos os arquivos de log
    """
    logger.info(f"🗑️ INICIANDO LIMPEZA de todos os logs")
    
    with file_lock:
        agent = AgentLogger()
        
        try:
            log_files = agent.get_all_log_files()
            
            if not log_files:
                logger.warning(f"⚠️ Nenhum arquivo de log encontrado para limpeza")
                return {"message": "Nenhum arquivo de log encontrado", "status": "success"}
            
            # Tamanho antes de limpar
            total_size_before = agent.get_total_logs_size()
            logger.info(f"📁 Total a limpar: {len(log_files)} arquivos, {round(total_size_before / (1024 * 1024), 2)} MB")
            
            # Remove cada arquivo
            removed_files = []
            errors = []
            
            for log_file in log_files:
                try:
                    if os.path.exists(log_file):
                        file_size = os.path.getsize(log_file)
                        os.remove(log_file)
                        removed_files.append({
                            "filename": os.path.basename(log_file),
                            "size": file_size
                        })
                        logger.info(f"✅ Removido: {os.path.basename(log_file)}")
                    
                except Exception as e:
                    error_msg = f"Erro ao remover {os.path.basename(log_file)}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
            
            # Verifica resultado
            remaining_files = agent.get_all_log_files()
            
            if not remaining_files and not errors:
                logger.info(f"✅ Limpeza COMPLETA - {len(removed_files)} arquivos removidos")
                return {
                    "status": "success",
                    "message": "Todos os logs foram limpos com sucesso!",
                    "files_removed": len(removed_files),
                    "removed_files": removed_files,
                    "total_size_cleaned_mb": round(total_size_before / (1024 * 1024), 2)
                }
            else:
                return {
                    "status": "partial",
                    "message": f"Limpeza parcial: {len(removed_files)} removidos, {len(remaining_files)} restantes",
                    "files_removed": len(removed_files),
                    "removed_files": removed_files,
                    "remaining_files": len(remaining_files),
                    "errors": errors
                }
                
        except Exception as e:
            error_msg = f"Erro geral na limpeza: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {"error": error_msg, "status": "error"}


@router.get("/debug")
async def debug_server_log():
    """
    Endpoint para debug - mostra informações detalhadas sobre logs
    """
    logger.info(f"🔍 INICIANDO DEBUG do sistema de logs")
    agent = AgentLogger()
    
    debug_info = {
        "timestamp": datetime.now().isoformat(),
        "logs_info": {},
        "system_info": {}
    }
    
    # Informações dos logs
    log_files = agent.get_all_log_files()
    
    debug_info["logs_info"] = {
        "logs_dir": agent.logs_dir,
        "logs_dir_exists": agent.logs_dir_exists(),
        "total_files": len(log_files),
        "total_size": agent.get_total_logs_size(),
        "files": [os.path.basename(f) for f in log_files],
        "old_log_exists": os.path.exists(agent.old_log_file_path)
    }
    
    # Informações do sistema
    debug_info["system_info"] = {
        "platform": os.name,
        "has_fcntl": HAS_FCNTL,
        "python_version": sys.version,
        "current_pid": os.getpid(),
        "current_working_dir": os.getcwd()
    }
    
    logger.info(f"📊 Debug coletado - {len(log_files)} arquivos de log")
    return debug_info


@router.get("/health")
async def health_check():
    """Endpoint simples para verificar se a API está no ar"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}


@router.get("/test-console")
async def test_console_logging():
    """Endpoint para testar se o console logging está funcionando"""
    
    # Teste através do logger
    logger.info("🧪 TESTE CONSOLE ENDPOINT - Testando se aparece no console do Uvicorn")
    logger.warning("⚠️ TESTE WARNING - Mensagem de aviso")
    logger.error("❌ TESTE ERROR - Mensagem de erro")
    
    return {
        "status": "success", 
        "message": "Teste de console executado - verifique o terminal do Uvicorn",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/tail")
async def tail_server_log(lines: int = 100, type: str = 'python', source: str = 'local', sort_order: str = 'asc', date_filter: str = None, start_time: str = None, end_time: str = None):
    """
    Endpoint OTIMIZADO para obter as últimas N linhas dos logs com filtros.
    - type: 'python' ou 'php' (padrão 'python')
    - source: 'local' ou 'online' (padrão 'local')
    - sort_order: 'asc' ou 'desc' (padrão 'asc')
    - lines: número de linhas (padrão 100)
    - date_filter: data no formato YYYY-MM-DD (opcional)
    - start_time: hora de início no formato HH:MM (opcional)
    - end_time: hora de fim no formato HH:MM (opcional)
    """
    # logger.info(f"TAIL - Solicitado: {lines} linhas, Tipo: {type}, Source: {source}, Ordem: {sort_order}, Data: {date_filter}, Início: {start_time}, Fim: {end_time}")
    
    agent = AgentLogger()
    log_files_to_read = []
    
    try:
        # Helper function to parse timestamp from a log line
        def get_timestamp_from_line(line):
            try:
                if type == 'php': # Formato do log do Apache: [Sat Jun 28 14:02:37.952322 2025]
                    match = re.search(r'\[(.*?)\]', line)
                    if match:
                        # Tenta vários formatos, incluindo o ano no final
                        date_str = match.group(1)
                        try:
                            # Ex: Sat Jun 28 14:02:37.952322 2025
                            return datetime.strptime(date_str, '%a %b %d %H:%M:%S.%f %Y')
                        except ValueError:
                             # Ex: sem microsegundos
                            return datetime.strptime(date_str, '%a %b %d %H:%M:%S %Y')
                    return None
                else: # Python: 2024-07-28 15:30:00,123
                    return datetime.fromisoformat(line[:23].replace(',', '.'))
            except (ValueError, IndexError):
                return None

        if type == 'php':
            if source == 'online':
                # Caminho para o log de erros do Apache no servidor de produção (Exemplo)
                # !! AJUSTE ESTE CAMINHO CONFORME A CONFIGURAÇÃO DO SEU SERVIDOR !!
                php_log_path = "/var/log/httpd/error_log" 
                if not os.path.exists(php_log_path):
                     # Caminho alternativo comum (Debian/Ubuntu)
                    php_log_path = "/var/log/apache2/error.log"
                # logger.info(f"TAIL - Usando arquivo de log PHP Online: {php_log_path}")
            else: # source == 'local'
                current_dir = os.path.dirname(os.path.abspath(__file__))
                php_log_path = os.path.abspath(os.path.join(current_dir, "..", "..", "..", "php_error.log"))
                # logger.info(f"TAIL - Usando arquivo de log PHP Local: {php_log_path}")

            if os.path.exists(php_log_path):
                log_files_to_read.append(php_log_path)
            else:
                logger.warning(f"TAIL - Arquivo de log PHP para source '{source}' não encontrado em: {php_log_path}")
        else: # python
            # Para Python, a fonte 'online' vs 'local' não altera o caminho, pois os logs estão sempre no mesmo lugar relativo ao app.
            # A distinção é feita pela URL da API que o front-end chama.
            if date_filter:
                try:
                    filter_date = datetime.strptime(date_filter, '%Y-%m-%d').strftime('%Y_%m_%d')
                    log_file_path = agent.get_log_file_for_date(filter_date)
                    if os.path.exists(log_file_path):
                        log_files_to_read.append(log_file_path)
                        # logger.info(f"TAIL - Lendo arquivo Python específico: {os.path.basename(log_file_path)}")
                    else:
                        logger.warning(f"TAIL - Arquivo de log Python para data {date_filter} não encontrado.")
                except ValueError:
                    return {"status": "error", "error": "Formato de data inválido. Use YYYY-MM-DD."}
            else:
                log_files_to_read = agent.get_all_log_files()
                if not log_files_to_read and os.path.exists(agent.old_log_file_path):
                    log_files_to_read.append(agent.old_log_file_path)
                # logger.info(f"TAIL - Lendo {len(log_files_to_read)} arquivos de log Python.")

        if not log_files_to_read:
            error_msg = f"Nenhum arquivo de log do tipo '{type}' encontrado."
            logger.warning(f"TAIL - {error_msg}")
            return {"status": "success", "lines": [error_msg], "total_lines": 0, "file_size_mb": 0, "filters_applied": {}}

        total_size = sum(os.path.getsize(f) for f in log_files_to_read if os.path.exists(f))
        total_size_mb = round(total_size / (1024 * 1024), 2)

        # 1. Coleta todas as linhas e seus timestamps
        all_lines_with_ts = []
        for log_file in log_files_to_read:
            # Lemos mais linhas para garantir que teremos o suficiente após a filtragem
            file_lines = await _safe_tail_lines(log_file, max_lines=lines * 10)
            for line in file_lines:
                all_lines_with_ts.append({
                    'ts': get_timestamp_from_line(line),
                    'line': line.rstrip('\n\r')
                })
        
        # 2. Ordena todas as linhas coletadas por timestamp
        # Linhas sem timestamp válido são tratadas como as mais antigas.
        all_lines_with_ts.sort(key=lambda x: x['ts'] or datetime.min)

        # 3. Aplica filtros de data/hora sobre a lista já ordenada
        filters_applied = {}
        filtered_items = []

        # Pre-processa os valores dos filtros
        filter_date_obj = None
        start_time_obj = None
        end_time_obj = None
        has_time_filter = start_time or end_time

        if date_filter:
            try:
                filter_date_obj = datetime.strptime(date_filter, '%Y-%m-%d').date()
                filters_applied['date_filter'] = date_filter
            except ValueError: pass
        elif has_time_filter and all_lines_with_ts:
            last_line_ts = all_lines_with_ts[-1]['ts']
            if last_line_ts:
                filter_date_obj = last_line_ts.date()
                filters_applied['date_filter'] = f"auto:{filter_date_obj.strftime('%Y-%m-%d')}"

        if start_time:
            try:
                start_time_obj = datetime.strptime(start_time, '%H:%M').time()
                filters_applied['start_time'] = start_time
            except ValueError: pass
        
        if end_time:
            try:
                end_time_obj = datetime.strptime(end_time, '%H:%M').time()
                filters_applied['end_time'] = end_time
            except ValueError: pass

        # Itera para filtrar
        for item in all_lines_with_ts:
            line_ts = item['ts']
            
            # Linhas sem timestamp são incluídas apenas se não houver filtro de data/hora
            if not line_ts:
                if not (filter_date_obj or start_time_obj or end_time_obj):
                    filtered_items.append(item)
                continue

            if filter_date_obj and line_ts.date() != filter_date_obj:
                continue
            
            line_time = line_ts.time()
            if start_time_obj and line_time < start_time_obj:
                continue
            if end_time_obj and line_time > end_time_obj:
                continue

            filtered_items.append(item)

        # Extrai as linhas de texto dos itens filtrados
        filtered_lines = [item['line'] for item in filtered_items]

        # Pega as últimas N linhas
        result_lines = filtered_lines[-lines:]
        
        # 4. Aplica a ordenação final solicitada pelo usuário
        if sort_order == 'desc':
            result_lines.reverse()
        
        filters_applied['sort_order'] = sort_order

        # Formatação especial para logs do PHP
        if type == 'php':
            processed_php_lines = []
            for line in result_lines:
                # Remove a parte de metadados do log do Apache/PHP
                cleaned_line = re.sub(r'\[php:.*?\]\s+\[pid.*?\]\s+\[client.*?\]\s+', '', line)
                processed_php_lines.append(cleaned_line)
                processed_php_lines.append('')  # Adiciona uma linha em branco
            
            # Remove a última linha em branco para não deixar um espaço no final
            if processed_php_lines:
                processed_php_lines.pop()
            
            result_lines = processed_php_lines

        return {
            "status": "success",
            "lines": result_lines,
            "total_lines": len(all_lines_with_ts),
            "file_size_mb": total_size_mb,
            "filters_applied": filters_applied
        }
        
    except Exception as e:
        error_msg = f"Erro ao processar o log: {str(e)}"
        logger.error(f"TAIL - {error_msg}")
        logger.exception("Detalhes da exceção em TAIL:")
        return {"status": "error", "error": error_msg, "lines": []}


@router.post("/logs/all")
async def get_all_logs(data: dict):
    """
    Endpoint para retornar todos os logs (implementar depois do download)
    """
    # Implementar depois da etapa de download
    return {"message": "Endpoint em desenvolvimento", "status": "pending"}


@router.post("/logs/last/{nr_logs}")
async def get_last_logs(nr_logs: int, data: dict):
    """
    Endpoint para retornar os últimos N logs (implementar depois do download)
    """
    # Implementar depois da etapa de download
    return {"message": f"Endpoint para últimos {nr_logs} logs em desenvolvimento", "status": "pending"}

