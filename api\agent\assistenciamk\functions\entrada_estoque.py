import logging
from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
from ...util.functions.gera_id_unico import gera_id_unico
from ...agent_evolutionzap import AgentEvolutionZap

# Configuração de logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


import json

try:
    print("Inicializando cliente Neo4j...")
    neo4j = AgentNeo4j()
    print("Cliente Neo4j inicializado com sucesso")
    
    print("Inicializando Evolution API...")
    evolutionApi = AgentEvolutionZap()
    instancia_id = evolutionApi.get_instace_id()
    print(f"Evolution API inicializada. Instância ID: {instancia_id}")
except Exception as e:
    logger.error(f"Erro ao inicializar dependências: {str(e)}")
    raise

from pydantic import BaseModel
from typing import List
import asyncio
from datetime import datetime


class ProdutoItem(BaseModel):
    idx: str
    codigo: str
    nome: str
    preco: float
    quantidade: int

@function_tool
async def entrada_estoque(
    usuario_idx: str,
    usuario_nome: str,
    usuario_whatsapp: str,
    conversa_idx: str,
    negocio_idx: str,
    negocio_nome: str,
    canal_idx: str,
    agente_idx: str,
    em_resposta_idx: str,
    produtos: List[ProdutoItem]
):
    """
Registra entrada de produtos no estoque da revenda.

═══════════════════════════════════════════════════════════════════════════
📋 FLUXO OBRIGATÓRIO - SIGA ESTA SEQUÊNCIA EXATA:
═══════════════════════════════════════════════════════════════════════════

PASSO 1: IDENTIFICAR O PRODUTO
------------------------------
Pergunte ao usuário: "Qual produto deseja adicionar? Informe o nome ou código."

PASSO 2: BUSCAR NO CATÁLOGO
----------------------------
🚨 OBRIGATÓRIO: Use consultar_produto_entrada() com o termo informado pelo usuário
- Esta busca retorna: nome completo, código, preço e variações disponíveis
- NUNCA pule esta etapa
- NUNCA invente ou assuma o preço
- ❌NUNCA use a função verificar_estoque_revenda() para buscar produtos

🆗🆗🆗 Somente a função consultar_produto_entrada() deve ser usada para pesquisar produto por nome ou código

PASSO 3: VERIFICAR VARIAÇÕES (APENAS SE HOUVER MAIS DE UMA)
-------------------------------------------------------
A função consultar_produto_entrada() pode retornar variações do produto. 

Se houver MAIS DE UMA variação (ex: cores diferentes):
```
Batom Líquido Matte com Manteiga de Karité e Vitamina E - Mary Kay At Play®
Preço: R$ 47,90
Variações disponíveis:
- Código: 10222452 - Desert Sunset
- Código: 10222450 - Raspberry Kiss (Matte)
- Código: 10222440 - Call Me Rose
- Código: 10222451 - Brick My Heart (Matte)
```

➤ Se HOUVER MAIS DE UMA variação:
   - Mostre as variações ao usuário
   - Pergunte: "Qual variação deseja? Informe o código ou nome da cor."
   - Após a resposta, faça NOVA busca com consultar_produto_entrada() usando o código/cor específica

➤ Se houver APENAS UMA variação (ou nenhuma):
   - NÃO mencione o termo "variação"
   - Pule para o próximo passo (PASSO 4) usando o produto retornado

PASSO 4: SOLICITAR QUANTIDADE
------------------------------
🚨🚨🚨 CRÍTICO - SEMPRE PERGUNTE A QUANTIDADE 🚨🚨🚨

Após identificar o produto (e variação, se houver), você DEVE perguntar:
"Quantas unidades de [NOME_DO_PRODUTO] deseja adicionar?"

⛔ NUNCA assuma quantidade = 1
⛔ NUNCA pule esta pergunta
⛔ NUNCA execute a função sem a quantidade informada pelo usuário

AGUARDE a resposta do usuário com a quantidade antes de prosseguir.

PASSO 5: ADICIONAR À LISTA E CONFIRMAR CONTINUAÇÃO
-------------------------------------------------
SOMENTE APÓS receber a quantidade do usuário, adicione o produto à lista produtos[] com:
- idx: do catálogo (obtido na busca)
- codigo: do catálogo (obtido na busca)
- nome: do catálogo (obtido na busca)
- preco: do catálogo (obtido na busca)
- quantidade: INFORMADO PELO USUÁRIO (NUNCA assuma!)


🚨🚨🚨 REGRA ABSOLUTAMENTE OBRIGATÓRIA:
APÓS ADICIONAR QUALQUER PRODUTO, SEMPRE PERGUNTE:
"Deseja adicionar mais algum produto ao estoque?"

⛔ NUNCA FINALIZE SEM FAZER ESTA PERGUNTA
⛔ NUNCA ASSUMA QUE O USUÁRIO QUER PARAR
⛔ NÃO IMPORTA QUANTOS PRODUTOS JÁ FORAM ADICIONADOS

AGUARDE a resposta do usuário antes de decidir:
- Se o usuário responder SIM/QUERO/MAIS/ADICIONAR: 
  • Volte ao PASSO 1 para adicionar outro produto
  • Continue adicionando quantos produtos o usuário quiser
  • NUNCA limite o número de produtos que podem ser adicionados

- Se o usuário responder NÃO/PRONTO/FINALIZAR/NÃO QUERO MAIS:
  • Apenas ENTÃO finalize o processo
  • Chame a função entrada_estoque() com TODOS os produtos
  • NUNCA finalize sem esta confirmação explícita

EXEMPLOS DE RESPOSTAS VÁLIDAS PARA CONTINUAR:
"sim", "quero", "mais", "adicionar", "outro", "quero adicionar mais"

EXEMPLOS DE RESPOSTAS VÁLIDAS PARA FINALIZAR:
"não", "pronto", "finalizar", "não quero mais", "é só isso"

═══════════════════════════════════════════════════════════════════════════
📦 PARÂMETROS
═══════════════════════════════════════════════════════════════════════════

produtos: List[ProdutoItem]
    Lista de produtos a adicionar ao estoque. Cada item contém:
    
    {
        "idx": "12345",              # ID do produto (do catálogo)
        "codigo": "10201859",        # Código do produto (do catálogo)
        "nome": "Batom Berry...",    # Nome completo (do catálogo)
        "preco": 49.90,              # Preço unitário (do catálogo)
        "quantidade": 10             # Quantidade (INFORMADA PELO USUÁRIO)
    }
    
    🚨 TODOS os dados exceto 'quantidade' vêm da busca no catálogo
    🚨 A 'quantidade' SEMPRE vem do usuário - NUNCA assuma!
    ⚠️  Lista obrigatória - mínimo 1 produto

O fluxo agora é gerenciado pelo agente, que deve manter o estado da conversa e perguntar se o usuário deseja adicionar mais itens após cada produto.

═══════════════════════════════════════════════════════════════════════════
✅ EXEMPLO DE USO CORRETO
═══════════════════════════════════════════════════════════════════════════

Conversa exemplo:

Agente: "Qual produto deseja adicionar ao estoque?"
Usuário: "batom matte"

[Agente chama: consultar_produto_entrada(termo="batom matte")]
[Retorno: 3 produtos encontrados, um deles com múltiplas variações]

Agente: "Encontrei o Batom Líquido Matte (R$ 47,90) com estas variações:
        - 10222452: Desert Sunset
        - 10222450: Raspberry Kiss
        - 10222440: Call Me Rose
        Qual variação deseja?"
Usuário: "raspberry kiss"

[Agente chama: consultar_produto_entrada(termo="10222450")]
[Retorno: dados completos da variação Raspberry Kiss]

Agente: "Quantas unidades de Batom Raspberry Kiss deseja adicionar?"
Usuário: "15"

Agente: "Deseja adicionar mais produtos ao estoque?"
Usuário: "não"k

[Agente chama a função:]
entrada_estoque(
    ...parametros_sistema...,
    produtos=[{
        "idx": "prod_123",
        "codigo": "10222450",
        "nome": "Batom Líquido Matte - Raspberry Kiss",
        "preco": 47.90,
        "quantidade": 15
    }]
)

═══════════════════════════════════════════════════════════════════════════
✅ EXEMPLO COM MÚLTIPLOS PRODUTOS
═══════════════════════════════════════════════════════════════════════════

Agente: "Qual produto deseja adicionar ao estoque?"
Usuário: "batom vermelho"

[Busca e identifica produto]

Agente: "Quantas unidades?"
Usuário: "10"

Agente: "Deseja adicionar mais produtos ao estoque?"
Usuário: "sim, quero adicionar máscara também"

[Agente NÃO chama a função ainda, volta ao PASSO 1]

Agente: "Qual máscara deseja adicionar?"
Usuário: "máscara de cílios"

[Busca e identifica produto]

Agente: "Quantas unidades?"
Usuário: "5"

Agente: "Deseja adicionar mais produtos ao estoque?"
Usuário: "não, só isso"

[AGORA SIM chama a função com os 2 produtos]

═══════════════════════════════════════════════════════════════════════════
❌ EXEMPLO DE USO INCORRETO (NÃO FAÇA ISSO!)
═══════════════════════════════════════════════════════════════════════════

❌ ERRADO - Assumindo quantidade:
Usuário: "peach party"
Agente: [chama entrada_estoque com quantidade=1 sem perguntar]

✅ CORRETO:
Usuário: "peach party"
Agente: "Quantas unidades de Peach Party deseja adicionar?"
Usuário: "20"
Agente: "Deseja adicionar mais produtos?"
[Aguarda resposta antes de chamar a função]

❌ ERRADO - Finalizando sem perguntar:
Usuário: "20 unidades"
Agente: [chama entrada_estoque]

✅ CORRETO:
Usuário: "20 unidades"
Agente: "Deseja adicionar mais produtos ao estoque?"
Usuário: "não"
Agente: [agora sim chama entrada_estoque]

═══════════════════════════════════════════════════════════════════════════
⚠️  ERROS COMUNS A EVITAR
═══════════════════════════════════════════════════════════════════════════

❌ NÃO assuma quantidade = 1 automaticamente
❌ NÃO execute a função sem perguntar a quantidade ao usuário
❌ NÃO finalize sem perguntar ao usuário
❌ NÃO assuma que o usuário terminou após adicionar um produto
❌ NÃO pergunte o preço ao usuário - use o do catálogo
❌ NÃO pule a busca com consultar_produto_entrada()
❌ NÃO adicione produtos sem buscar no catálogo primeiro
❌ NÃO esqueça de tratar produtos com múltiplas variações
❌ NÃO chame a função com produtos=[] (lista vazia)

═══════════════════════════════════════════════════════════════════════════
🔒 CHECKLIST OBRIGATÓRIO ANTES DE CHAMAR A FUNÇÃO
═══════════════════════════════════════════════════════════════════════════

Antes de executar entrada_estoque(), verifique TODOS os itens:

✓ Perguntei qual produto?
✓ Busquei no catálogo com consultar_produto_entrada()?
✓ Tratei variações (se houver)?
✓ PERGUNTEI A QUANTIDADE? ← CRÍTICO!
✓ PERGUNTEI SE QUER ADICIONAR MAIS? ← CRÍTICO!
✓ Recebi resposta do usuário sobre adicionar mais?

Se a resposta para QUALQUER item acima for NÃO, NÃO execute a função ainda!
Continue coletando as informações que faltam.

═══════════════════════════════════════════════════════════════════════════
🎯 REGRAS DE OURO
═══════════════════════════════════════════════════════════════════════════

1. NUNCA execute esta função sem ter perguntado a QUANTIDADE ao usuário
2. NUNCA finalize sem ter perguntado ao usuário
3. SEMPRE aguarde confirmação explícita do usuário antes de finalizar
4. SEMPRE volte ao PASSO 1 se o usuário quiser adicionar mais produtos

═══════════════════════════════════════════════════════════════════════════
 
    """
    print("=== entrada_estoque() ===")
    print(f"Usuário: {usuario_nome} ({usuario_idx})")
    print(f"Negócio: {negocio_nome} ({negocio_idx})")
    print(f"Produtos recebidos: {produtos}")


    # Se não há produtos, retorna erro
    if not produtos:
        error_msg = "Nenhum produto informado para compra"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }
    
    
    

    # Normaliza default para evitar lista mutável compartilhada
    if produtos is None:
        produtos = []
        logger.warning("Lista de produtos estava None, foi inicializada como vazia")

    # Gera ids necessários para Revenda (se for criada) e para a Compra
    print("Gerando IDs únicos...")
    try:
        revenda_idx = gera_id_unico()
        compra_idx = gera_id_unico()
        print(f"IDs gerados - Revenda: {revenda_idx}, Compra: {compra_idx}")
        total = 0
    except Exception as e:
        error_msg = f"Erro ao gerar IDs únicos: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }

    # Serializa os produtos para dicionários simples e gera idx de ProdutoRevenda por item
    produtos_payload = [
        {
            "idx": p.idx,
            "codigo": p.codigo,
            "nome": p.nome,
            "preco": p.preco,
            "quantidade": p.quantidade,
            "prv_idx": gera_id_unico(),
        }
        
        for p in produtos
    ] if produtos else []

    # Monta a query Cypher para registro de compra
    print("Montando query Cypher...")
    # IMPORTANTE: Esta query NÃO verifica estoque, apenas registra a entrada de produtos
    # Pessoa -[REALIZA_REVENDA]-> Revenda (cria se não existir; define idx no create)
    # Revenda -[COMPROU]-> Compra (Compra com idx e data_hora)
    # Compra -[COMPROU_PRODUTO]-> ProdutoRevenda (para cada item)
    
    # Log dos produtos que serão processados
    print(f"Produtos a serem processados: {json.dumps(produtos_payload, indent=2, ensure_ascii=False, default=str)}")
    
    # Validação dos dados dos produtos
    for produto in produtos_payload:
        if not all(key in produto for key in ["idx", "codigo", "nome", "preco", "quantidade", "prv_idx"]):
            error_msg = f"Dados do produto incompletos: {produto}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
    # 
    # REGRA: Não há verificação de estoque durante o registro de compra
    # Query otimizada para registrar compra SEM verificar estoque



    query = """
// 1. Mantenha r vivo durante toda a query
MERGE (p:Pessoa {idx: $usuario_idx})
MERGE (p)-[:REALIZA_REVENDA]->(r:Revenda)
  ON CREATE SET r.idx = $revenda_idx, r.created_at = datetime()

MERGE (n:Negocio {idx: $negocio_idx})
MERGE (r)-[:REFERENTE]->(n)

// 2. Encaminhe r explicitamente
WITH r, $compra_idx AS compra_idx, $produtos AS produtos
WITH r, compra_idx, produtos,
     reduce(total = 0.0, p IN produtos | total + toFloat(p.quantidade) * toFloat(p.preco)) AS total

CREATE (c:Compra {idx: compra_idx, data_hora: datetime(), total: total})
MERGE (r)-[:COMPROU]->(c)

WITH c, r, produtos     // ← r ainda está aqui!
UNWIND produtos AS prod
MATCH (pr:Produto {idx: prod.idx})

// 3. Agora r é garantidamente a Revenda real
MERGE (r)-[:REVENDE]->(prv:ProdutoRevenda {idx: prod.prv_idx})-[:INSTANCIA_DE]->(pr)
ON CREATE SET
  prv.estoque = toInteger(prod.quantidade),
  prv.preco_compra = toFloat(prod.preco),
  prv.created_at = datetime()
ON MATCH SET
  prv.estoque = prv.estoque + toInteger(prod.quantidade),
  prv.updated_at = datetime()

CREATE (c)-[:COMPROU_PRODUTO]->(prv)
    """


    params = {
        "usuario_idx": usuario_idx,
        "negocio_idx": negocio_idx,
        "revenda_idx": revenda_idx,
        "compra_idx": compra_idx,
        "produtos": produtos_payload,
    }

    # Log da query e parâmetros
    print("=== QUERY CYPHER ===")
    print(query)
    print("=== PARÂMETROS ===")
    print(json.dumps(params, indent=2, default=str))
    
    # Validação dos parâmetros
    required_params = ["usuario_idx", "negocio_idx", "revenda_idx", "compra_idx", "produtos"]
    missing_params = [p for p in required_params if p not in params]
    if missing_params:
        error_msg = f"Parâmetros obrigatórios faltando: {', '.join(missing_params)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "function": "entrada_estoque"
        }
    
    
    


    try:
        print("===== EXECUTANDO QUERY NO NEO4J =====")
        
        # Log detalhado dos parâmetros
        param_types = {k: type(v).__name__ for k, v in params.items()}
        print(f"Tipos dos parâmetros: {param_types}")
        
        # Log dos produtos em formato JSON para fácil leitura
        produtos_json = json.dumps(produtos_payload, indent=2, ensure_ascii=False, default=str)
        print(f"Produtos a serem processados:\n{produtos_json}")
        
        # Executa a query no Neo4j
        print("Iniciando execução da query no Neo4j...")
        start_time = datetime.now()
        
        resultado = await neo4j.execute_write_query(query=query, params=params)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print("===== RESULTADO DA QUERY =====")
        print(f"Tempo de execução: {execution_time:.2f} segundos")
        print(f"Tipo do resultado: {type(resultado).__name__}")
        
        # Log do resultado de forma mais legível
        if resultado:
            if isinstance(resultado, list):
                print(f"Query retornou {len(resultado)} registros")
                for i, item in enumerate(resultado[:5]):  # Limita a 5 itens para não poluir o log
                    print(f"Registro {i + 1}: {item}")
                if len(resultado) > 5:
                    print(f"... mais {len(resultado) - 5} registros não exibidos")
            else:
                print(f"Resultado: {resultado}")
        else:
            logger.warning("A query não retornou resultados (resultado vazio ou None)")
            
        # Verifica se o resultado tem a estrutura esperada
        if not resultado or not isinstance(resultado, list) or len(resultado) == 0:
            error_msg = "A query não retornou os resultados esperados"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
        
        # Erro, retorna mensagem de erro.
        if not resultado:
            mensagem = "Ocorreu um erro. A compra não foi finalizada. Nenhum resultado retornado do banco de dados."
            print("ERRO:", mensagem)
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
            return {
                "status": "error", 
                "message": json.dumps({"tipo":"compra","erro": mensagem}, ensure_ascii=False),
                "function": "entrada_estoque"
            }
        
        
        print("resultado", resultado)
       
        print("@@@ produtos", produtos)
       
        
        # Monta o retorno solicitado
        itens_compra = []
        total_geral = 0.0
        for p in produtos:
            print("@p", p)
            try:
                preco = float(p.preco) if p.preco else 0.0
                quantidade = int(p.quantidade) if p.quantidade else 0
                item_total = preco * quantidade
                total_geral += item_total
                produto_label = f"{p.codigo} - {p.nome}" if getattr(p, "codigo", None) and p.codigo else p.nome
                itens_compra.append({
                    "Produto": produto_label,
                    "Preço": preco,
                    "Qtde": quantidade,
                    "Total": round(item_total, 2),
                })
            except Exception as e:
                logger.error(f"Erro ao processar produto {p}: {str(e)}")
                continue
            
        mensagem_inicial = "Estes são os dados da compra registrada:"
        mensagem_final = ""





        # Enviar mensagens via WhatsApp
        # Data/hora da compra para exibir na mensagem (lado cliente)
        data_compra_str = datetime.now().strftime("%d/%m/%Y %H:%M")

        print('data_compra_str', data_compra_str)


        await enviar_mensagens_zap(
            whatsapp=usuario_whatsapp,
            mensagem_inicial=mensagem_inicial,
            mensagem_final=mensagem_final,
            dados=itens_compra,
            total_geral=round(total_geral, 2),
            data_hora=data_compra_str,
            instancia_id=instancia_id,
            conversa_idx=conversa_idx,
            usuario_idx=usuario_idx,
            usuario_nome=usuario_nome,
            negocio_nome=negocio_nome,
            agente_idx=agente_idx,
            em_resposta_idx=em_resposta_idx,
            canal_idx=canal_idx,
        )

      

        resposta = {
            "status": "success",
            "message": "Compra registrada com sucesso!",
            "data": {
                "compra_idx": compra_idx,
                "total_produtos": len(produtos),
                "total_geral": round(total_geral, 2)
            },
            "function": "entrada_estoque"
        }
        print("@@@@@ Resposta final:", resposta)
        return resposta
    except Exception as e:
        error_msg = f"Ocorreu uma exceção ao tentar registrar a compra de produtos: {str(e)}"
        print("===== ERRO DURANTE A EXECUÇÃO =====")
        print("Tipo do erro:", type(e).__name__)
        print("Mensagem de erro:", str(e))
        print("Traceback:", traceback.format_exc())
        
        # Envia mensagem de erro detalhada apenas para o log
        debug_msg = f"ERRO: {error_msg}\n\nTraceback:\n{traceback.format_exc()}"
        print(debug_msg)
        
        # Envia mensagem genérica para o usuário
        await evolutionApi.send_evolution_message(
            usuario_whatsapp, 
            "Ocorreu um erro ao processar a compra. Por favor, tente novamente.", 
            instancia_id
        )
        
        return {
            "status": "error",
            "message": json.dumps({"tipo": "compra", "erro": error_msg}, ensure_ascii=False),
            "debug": debug_msg  # Apenas para depuração, não enviar para o cliente final
        }


async def enviar_mensagens_zap(whatsapp, mensagem_inicial, mensagem_final, dados, total_geral, data_hora, instancia_id, conversa_idx=None, usuario_idx=None, usuario_nome=None,negocio_nome=None, agente_idx=None, em_resposta_idx=None, canal_idx=None):
            
            
            print (f"===== enviar_mensagens()=====")
            print("whatsapp", whatsapp)
            print("mensagem_inicial:" , mensagem_inicial)
            print("mensagem_final:" , mensagem_final)
            print("dados" , dados)
            print("total_geral", total_geral)
            print('data_hora', data_hora)
            print('instancia_id', instancia_id)   
            print('conversa_idx', conversa_idx)
            print("usuario_idx", usuario_idx)
            print("usuario_nome", usuario_nome)
            print("negocio_nome", negocio_nome)
            print("agent_idx", agente_idx)
            print("em_resposta_idx", em_resposta_idx)
            print("canal_idx", canal_idx)
            

            if mensagem_inicial:
                result = await evolutionApi.send_evolution_message(whatsapp, mensagem_inicial, instancia_id)
                          
            
            # Helper para formatar moeda no padrão brasileiro
            def fmt_brl(valor: float) -> str:
                try:
                    return ("R$ " + f"{float(valor):,.2f}").replace(",", "X").replace(".", ",").replace("X", ".")
                except Exception:
                    return f"R$ {valor}"

            # Monta a mensagem no formato solicitado, compatível com WhatsApp Markdown
            linhas = []
            linhas.append(f"*COMPRA EFETUADA EM {data_hora}*")
            if negocio_nome:
                linhas.append(f"*FORNECEDOR:* {negocio_nome}")
            if usuario_nome:
                linhas.append(f"*COMPRADOR:* {usuario_nome}")
            linhas.append("")
            linhas.append("PRODUTOS")

            for item in dados:
                produto = item.get("Produto", "Produto")
                preco = fmt_brl(item.get("Preço", 0))
                qtde = item.get("Qtde", 0)
                total_item = fmt_brl(item.get("Total", 0))
                linhas.append(f"*{produto}*")
                linhas.append(f"Preço: {preco}    Qtde: {qtde}    Total: {total_item}")

            linhas.append("=========================")
            linhas.append(f"*TOTAL: {fmt_brl(total_geral)}*")

            mensagem_whatsapp = "\n".join(linhas)
            
            
            
            
            result = await evolutionApi.send_evolution_message(
                whatsapp,
                mensagem_whatsapp,
                instancia_id
            )
                                    
            await asyncio.sleep(2)

                #if mensagem_final:
                    #message = {}
                    #message["status"] = "success"
                    #message["message"] = mensagem_final
                    #result = await evolutionApi.send_evolution_message(whatsapp,mensagem_final,#instancia_id)
                    #print("📩 mensagem final enviada", result)


            #print(" 📩📩📩📩📩 menagens enviadas")        

            return 




if __name__ == "__main__":

        async def teste_produto_entrada():  
            # Exemplo de uso
            produtos_exemplo = [                                               
            ProdutoItem(idx="10221715", codigo="10221715", nome="Kit Dia + Noite TimeWise®", preco=213.80, quantidade=2),
            ProdutoItem(idx="10221710", codigo="10221710", nome="nome: Kit Básico TimeWise®", preco=182.80, quantidade=2)
            ]

            resultado = await entrada_estoque(
                usuario_idx="1122334455",
                usuario_nome="Carlos Silva",
                usuario_whatsapp="553184198720",
                conversa_idx="42134123",
                negocio_idx="5544332211",
                negocio_nome="Mary Kay",
                canal_idx="245124231",
                agente_idx="3245124231",
                em_resposta_idx="124242323",
                produtos=produtos_exemplo,
                # Fluxo de adição de itens gerenciado pelo agente
            )
            print("RESUTADO FINAL\n ",resultado)

        asyncio.run(teste_produto_entrada())
        
        #execução