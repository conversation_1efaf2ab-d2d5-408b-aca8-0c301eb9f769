from datetime import datetime
import json


#===================
group_tasks_generate_map = {
  "agent":"agent_task",
  "class":"Task",
  "name": "subtasks_generate",
  "description": "Use esta função para gerar uma lista de tarefas de um mesmo grupo.",
  "parameters": {
    "type": "object",
    "properties": {
      "TAREFAS": {
        "type": "string",
        "description": "Array string de tarefas em forma de objetos jasvaascript"
      }
    },
    "required": ["TAREFAS"]
  }
}


database_schema_string = """
TABLE: TAREFAt
  COLUMNS:
  TABELA.ID int(11) NOT NULL AUTO_INCREMENT,
  TABELA.USUARIO_ID int(11) DEFAULT 0,
  TABELA.TIPO SMALLINT(1) DEFAULT NULL,
  TABELA.DESCRICAO varchar(200) DEFAULT NULL,
  TABELA.DT_PREVISAO_INICIO date DEFAULT NULL,
  TABELA.DT_PREVISAO_FIM date DEFAULT NULL,
  TABELA.DT_LEMBRETE date DEFAULT NULL,
  TABELA.HORA_PREVISTA_INICIO varchar(5) DEFAULT NULL,
  TABELA.HORA_PREVISTA_FIM varchar(5) DEFAULT NULL,
  TABELA.ORA_REAL_INICIO varchar(5) DEFAULT NULL,
  TABELA.HORA_REAL_FIM varchar(5) DEFAULT NULL,
  TABELA.HORA_LEMBRETE varchar(5) DEFAULT NULL,
  TABELA.PROJETO_ID int(11) DEFAULT 0,
  TABELA.DT_REAL date DEFAULT NULL,
  TABELA.REALIZADA SMALLINT(1) DEFAULT 0,
  TABELA.STATUS varchar(1) DEFAULT 'A'  # A - ativa, 1 - E - excluida
"""


#===================================================================================================

#task_get_map

task_get_map = {
    "agent":"agent_task",
    "class":"Task",
    "name": "task_get",
    "description": (
        "Use esta função para responder perguntas do usuário sobre suas tarefas apenas. "
        "A entrada deve ser uma Query SQL bem formatada. "
        "A saída poderá ser uma lista de tarefas, uma tarefa específica ou informações sobre uma ou mais tarefas. "
        "Não listar o PROJETO_ID, pois o usuário não sabe o ID do projeto. "
        "Só liste colunas que tiverem dados. Por exemplo, se não for informado a HORA_REAL de uma tarefa, não liste a coluna HORA_REAL. "
        "Só listar tarefas do TIPO 1. "
        "Para todos os processamentos esta é a data atual: " + datetime.now().isoformat()[:10] + ". "
        "Somente considerar tarefas 'Ativas', onde a coluna TABELA.STATUS = 'A'."
        "Datas , quando for incluidas na resposta em alguma coluna, deve ser no fomato yyyy-mm-dd . Exemplo: 2024-06-05. sen execeção. é muito importante que as datas tenham este formato: yyyy-mm-dd."
        
    ),
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": (
                    f"Consulta SQL para extrair informações não excluídas da tabela TAREFA a fim de responder à pergunta do usuário sobre suas tarefas. "
                    f"O SQL deve ser escrito utilizando este esquema de banco de dados: {database_schema_string}. "
                    f"A consulta deve ser retornada em texto simples, não em JSON. "
                    f"A consulta deve ser realizada na tabela TAREFA. "
                    f"Datas , quando for incluidas na resposta em alguma coluna, deve ser no fomato yyyy-mm-dd . Exemplo: 2024-06-05."
                    f"O resultado deve ser filtrado também pelo ID do usuário (USUARIO_ID), pelo TIPO e pela situação de status, onde TABELA.STATUS = 'A'. "
                    f"Não usar o nome da tabela ao se referir às colunas (TABELA.USUARIO_ID). Use apenas o nome da coluna (USUARIO_ID).\n"
                    f"Exemplos de consulta:\n"
                    f"SELECT DESCRICAO, DT_PREVISAO_INICIO, DT_PREVISAO_FIM FROM TAREFA WHERE DT_PREVISAO_INICIO = '2021-09-01' AND DT_PREVISAO_FIM = '2021-09-30' AND TIPO = 1 AND STATUS = 'A'.\n"
                    f"SELECT * FROM TAREFA WHERE STATUS = 'A'."
                )
            }
        },
        "required": ["query"]
    }
}


#===================================================================================================

#task_add_map

task_add_map = {
    "agent":"agent_task",
    "class":"Task",
    "name": "task_add",
    "description": "Adicionar ou criar uma nova tarefa. A entrada deve ser uma Query SQL bem formatada. A saída deverá ser o ID da nova tarefa.",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Script SQL para adicionar uma tarefa ou lembrete na tabela TAREFA. O SQL deve ser escrito utilizando este esquema de banco de dados: {database_schema_string}. A nova tarefa deverá ter seu status definido como 'A' de ativa e ter também o id do usuário. A consulta deve ser retornada em texto simples, não em JSON. Exemplos de scripts: INSERT INTO TAREFA (DESCRICAO, DT_PREVISAO_INICIO, DT_PREVISAO_FIM, TIPO, USUARIO_ID) VALUES ('Comprar café', '2021-09-01', '2021-09-30', 1, 'SRubc193HPdAK0e82JeNd7QjGnv1'); INSERT INTO TAREFA (DESCRICAO, DT_PREVISAO_INICIO, HORA_PREVISTA_INICIO, TIPO, USUARIO_ID) VALUES ('Tomar vitamina D', '2021-09-01','08:00', 5, 'asdasfdGnv1');"
            }
        },
        "required": ["query"]
    }
}


task_exclude_map = {
    "agent":"agent_task",
    "class":"Task",
    "name": "task_exclude",
    "description": "Use esta função para mudar o status de uma tarefa para 'E' (excluída), usando uma query SQL",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Script SQL para alterar o status de uma ou mais tarefas para 'E' (Excluído). O SQL deve ser escrito utilizando este esquema de banco de dados: {database_schema_string}. A resposta ou retorno deve ser uma mensagem de sucesso ou não da exclusão."
            }
        },
        "required": ["query"]
    }
}


task_update_map = {
    "agent":"agent_task",
    "class":"Task",
    "name": "task_update",
    "description": "Use esta função para atualizar ou alterar informações e dados das tarefas, como por exemplo, adicionar lembrentes, alterar datas e/ou horários, mudar a descrição, reincluir , mudando  o Status para 'A', e qualquer  outra alteração solicitada pelo usuário.",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Script SQL para alterar dados  de uma ou mais tarefas . O SQL deve ser escrito utilizando este esquema de banco de dados: {database_schema_string}. A resposta ou retorno deve ser uma mensagem de sucesso ou não da solicitação."
            }
        },
        "required": ["query"]
    }
}
