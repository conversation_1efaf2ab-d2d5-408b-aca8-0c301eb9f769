from ..agent_secret import Secret
from ..agent_neo4j import Agent<PERSON><PERSON>4j
from ...functions.util import generate_unique_id

import os
import platform
from agents import function_tool
import httpx
import json

os.system('cls')
secret = Secret()
neo4j = AgentNeo4j()


is_local = platform.system() == "Windows" 
EVOLUTION_API_INSTANCE_TOKEN = ""
EVOLUTION_API_WEBHOOK_URL = ""
EVOLUTION_API_KEY = ""



if is_local:
    EVOLUTION_API_INSTANCE_TOKEN = secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN_LOCAL")
    EVOLUTION_API_BASE_URL = "http://localhost:8081"
    EVOLUTION_API_KEY = secret.get_secret("EVOLUTION_API_KEY_LOCAL")
    EVOLUTION_API_WEBHOOK_URL = secret.get_secret("EVOLUTION_API_WEBHOOK_URL_LOCAL")


else:
    EVOLUTION_API_WEBHOOK_URL = secret.get_secret("EVOLUTION_API_WEBHOOK_URL")


    EVOLUTION_API_INSTANCE_TOKEN = secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN")
    EVOLUTION_API_BASE_URL = "https://evolutionapi.server.gptalk.com.br"
    EVOLUTION_API_KEY = secret.get_secret("EVOLUTION_API_KEY")
    EVOLUTION_API_WEBHOOK_URL = secret.get_secret("EVOLUTION_API_WEBHOOK_URL")


#print("===== EVOLUTION_API_KEY", EVOLUTION_API_KEY)
#print("===== EVOLUTION_API_INSTANCE_TOKEN", EVOLUTION_API_INSTANCE_TOKEN)
#print("===== EVOLUTION_API_BASE_URL", EVOLUTION_API_BASE_URL)


async def salva_dados_conta(usuario_idx: str, instancia_id: str):
    """
    Salva os dados da conta no Neo4j criando o nó Conta e os relacionamentos necessários.
    
    Args:
        usuario_idx: o código idx de 10 dígitos do usuário
        instancia_id: o ID da instância criada (ex: "atmzap-1252352656")
    
    Returns:
        dict: resultado da operação no Neo4j
    """
    try:
        # Gerar um idx único para a conta
        conta_idx = generate_unique_id()
        

        
        # Query para criar o nó Conta e os relacionamentos
        query = """
        // Criar o nó Conta
        CREATE (conta:Conta {
            idx: $conta_idx,
            nome: $nome_instancia
        })
        
        // Criar relacionamento Pessoa-POSSUI_CONTA->Conta
        WITH conta
        MATCH (pessoa:Pessoa {idx: $usuario_idx})
        CREATE (pessoa)-[:POSSUI_CONTA]->(conta)
        
        // Criar relacionamento Conta-ACESSO_AO_APP->App
        WITH conta
        MATCH (app:App {idx: "3212357650"})
        CREATE (conta)-[:ACESSO_AO_APP]->(app)
        
        RETURN conta.idx as conta_idx, conta.nome as nome_instancia
        """
        
        # Parâmetros para a query
        params = {
            "conta_idx": conta_idx,
            "nome_instancia": instancia_id,
            "usuario_idx": usuario_idx
        }
        
        print(f"===== Salvando conta no Neo4j: {params}")
        
        # Executar a query de escrita
        result = await neo4j.execute_write_query(query, params)
        
        print(f"===== Conta salva no Neo4j com sucesso: {result}")
        
        return {
            "status": "success",
            "message": "Conta salva no Neo4j com sucesso",
            "conta_idx": conta_idx,
            "nome_instancia": instancia_id,
            "neo4j_result": result
        }
        
    except Exception as e:
        print(f"===== Erro ao salvar conta no Neo4j: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro ao salvar conta no Neo4j: {str(e)}",
            "conta_idx": None
        }

@function_tool
async def cria_conta_zap( usuario_idx: str ):
    """
    Cria uma conta zap para o usuário na plataforma GPTALK@Atm-Zap Os seguintes parametros são esperados:
    usuario_idx: o código idx de 10 digitos do usuário. Exemplo: "1252352656"

    """
    print("===== cria_conta_zap", usuario_idx)

    instancia_id = "atmzap-" + usuario_idx
    print("===== instancia_id", instancia_id)
    
    # VERIFICAR SE A INSTÂNCIA JÁ EXISTE
    try:
        # URL do endpoint para listar instâncias
        list_url = f"{EVOLUTION_API_BASE_URL}/instance/fetchInstances"
        
        # Headers da requisição
        headers = {
            "apikey": EVOLUTION_API_KEY,
            "Content-Type": "application/json"
        }
        
        print(f"===== Verificando instâncias existentes na URL: {list_url}")
        
        # Fazer a requisição HTTP para listar instâncias
        async with httpx.AsyncClient(timeout=30.0) as client:
            list_response = await client.get(list_url, headers=headers)
            
            if list_response.status_code == 200:
                instances_data = list_response.json()
                print(f"===== Instâncias encontradas: {json.dumps(instances_data, indent=2)}")
                
                # Verificar se a instância já existe
                existing_instances = instances_data if isinstance(instances_data, list) else []
                
                for instance in existing_instances:
                    instance_name = instance.get("name") if isinstance(instance, dict) else str(instance)
                    if instance_name == instancia_id:
                        print(f"===== Instância {instancia_id} já existe")
                        return {
                            "status": "error",
                            "message": "A conta já existe.",
                            "instance_id": instancia_id
                        }
                
                print(f"===== Instância {instancia_id} não existe, prosseguindo com a criação")
            else:
                print(f"===== Erro ao listar instâncias: {list_response.status_code}")
                # Continuar com a criação mesmo se não conseguir listar (para não bloquear o processo)
                
    except Exception as e:
        print(f"===== Erro ao verificar instâncias existentes: {str(e)}")
        # Continuar com a criação mesmo se houver erro na verificação
    
    # Construir a URL do webhook dinamicamente
    webhook_url = EVOLUTION_API_WEBHOOK_URL.rstrip('/') + "/" + instancia_id
    print("===== webhook_url", webhook_url)
    
    try:
        # URL do endpoint para criar instância
        url = f"{EVOLUTION_API_BASE_URL}/instance/create"
        
        # Payload CORRIGIDO - webhook como objeto
        payload = {
            "instanceName": instancia_id,
            "integration": "WHATSAPP-BAILEYS",
            "qrcode": True,
            "webhook": {
                "url": webhook_url,
                "byEvents": False,
                "base64": False,
                "events": ["MESSAGES_UPSERT"]
            }
        }
        
        # Headers da requisição
        headers = {
            "apikey": EVOLUTION_API_KEY,
            "Content-Type": "application/json"
        }
        
        print(f"===== Criando instância na URL: {url}")
        print(f"===== Payload: {json.dumps(payload, indent=2)}")
        
        # Fazer a requisição HTTP
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"===== Status Code: {response.status_code}")
            print(f"===== Response Headers: {response.headers}")
            
            # Verificar se a requisição foi bem-sucedida
            if response.status_code == 201:
                response_data = response.json()
                print(f"===== Conta criada com sucesso: {json.dumps(response_data, indent=2)}")
                
                # Salvar a conta no Neo4j usando a função separada
                neo4j_result = await salva_dados_conta(usuario_idx, instancia_id)
                
                # Retornar dados da conta criada e perguntar sobre criação do agente
                return {
                    "message": "Conta criada com sucesso! Agora você gostaria de criar um agente para esta conta? Se sim, por favor forneça as seguintes informações:\n\n" +
                              "1. Nome do agente\n" +
                              "2. Identidade do agente (como ele se apresenta)\n" +
                              "3. Personalidade do agente\n" +
                              "4. Instruções específicas para o agente\n" +
                              "5. Descrição do seu negócio\n" +
                              "6. Produtos que você oferece\n" +
                              "7. Serviços que você oferece\n\n" +
                              "Com essas informações, posmo criar o agente usando a função criar_agente_conta.",
                    "Codigo": response_data.get("instance", {}).get("instanceName"),
                    "neo4j_status": neo4j_result.get("status"),
                    "conta_idx": neo4j_result.get("conta_idx"),
                    "instancia_id": instancia_id,
                    "usuario_idx": usuario_idx,
                    "next_step": "create_agent"
                }
            else:
                # Tentar obter detalhes do erro
                try:
                    error_data = response.json()
                    error_message = error_data.get("message", "Erro desconhecido")
                    print(f"===== Erro detalhado: {json.dumps(error_data, indent=2)}")
                except:
                    error_message = response.text
                
                print(f"===== Erro ao criar instância: {error_message}")
                
                return {
                    "status": "error",
                    "message": f"Erro ao criar conta: {error_message}",
                    "status_code": response.status_code,
                    "instance_id": instancia_id
                }

                
    except httpx.RequestError as e:
        print(f"===== Erro de conexão: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro de conexão: {str(e)}",
            "instance_id": instancia_id
        }
    except Exception as e:
        print(f"===== Erro inesperado: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro inesperado: {str(e)}",
            "instance_id": instancia_id
        }

async def configurar_webhook_instancia(instancia_id: str):
    """Configura webhook para uma instância específica após sua criação"""
    print(f"===== Configurando webhook para instância: {instancia_id}")
    
    # Construir a URL do webhook dinamicamente
    webhook_url = EVOLUTION_API_WEBHOOK_URL.rstrip('/') + "/" + instancia_id
    print(f"===== webhook_url: {webhook_url}")
    
    try:
        # URL do endpoint para configurar webhook
        url = f"{EVOLUTION_API_BASE_URL}/webhook/set/{instancia_id}"
        
        # Payload para configurar webhook
        payload = {
            "url": webhook_url,
            "webhook_by_events": False,
            "webhook_base64": False,
            "events": [
                "MESSAGES_UPSERT"
            ]
        }
        
        # Headers da requisição
        headers = {
            "apikey": EVOLUTION_API_KEY,
            "Content-Type": "application/json"
        }
        
        print(f"===== Configurando webhook na URL: {url}")
        print(f"===== Payload webhook: {json.dumps(payload, indent=2)}")
        
        # Fazer a requisição HTTP
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"===== Webhook Status Code: {response.status_code}")
            
            if response.status_code in [200, 201]:
                result = response.json()
                print(f"===== Webhook configurado com sucesso: {result}")
                return {
                    "status": "success",
                    "message": "Webhook configurado com sucesso",
                    "data": result,
                    "webhook_url": webhook_url
                }
            else:
                error_detail = response.text
                print(f"===== Erro ao configurar webhook: {error_detail}")
                return {
                    "status": "error",
                    "message": f"Erro ao configurar webhook: {error_detail}",
                    "status_code": response.status_code
                }
                
    except Exception as e:
        print(f"===== Erro na configuração do webhook: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro ao configurar webhook: {str(e)}"
        }
        
@function_tool
async def pausar_comunicacao_zap(usuario_idx: str):
    """Pausa a comunicação removendo apenas o evento MESSAGES_UPSERT"""
    print(f"===== Pausando comunicação para usuário: {usuario_idx}")
    
    instancia_id = "atmzap-" + usuario_idx
    print(f"===== instancia_id: {instancia_id}")
    
    try:
        # URL do endpoint para configurar webhook
        url = f"{EVOLUTION_API_BASE_URL}/webhook/set/{instancia_id}"
        
        # Payload CORRIGIDO - mantendo apenas eventos essenciais, sem MESSAGES_UPSERT
        payload = {
            "webhook": {
                "enabled": True,
                "url": EVOLUTION_API_WEBHOOK_URL.rstrip('/') + "/" + instancia_id,
                "byEvents": False,
                "base64": False,
                "events": [
                    "APPLICATION_STARTUP",
                    "QRCODE_UPDATED",
                    "CONNECTION_UPDATE"
                ]  # Apenas eventos essenciais, sem MESSAGES_UPSERT
            }
        }
        
        # Headers da requisição
        headers = {
            "apikey": EVOLUTION_API_KEY,
            "Content-Type": "application/json"
        }
        
        print(f"===== Pausando comunicação na URL: {url}")
        print(f"===== Payload: {json.dumps(payload, indent=2)}")
        
        # Fazer a requisição HTTP
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"===== Status Code: {response.status_code}")
            
            if response.status_code in [200, 201]:
                result = response.json()
                print(f"===== Comunicação pausada com sucesso: {result}")
                return {
                    "status": "success",
                    "message": "Comunicação pausada com sucesso (MESSAGES_UPSERT desabilitado)",
                    "instancia_id": instancia_id,
                    "events_ativos": ["APPLICATION_STARTUP", "QRCODE_UPDATED", "CONNECTION_UPDATE"]
                }
            else:
                error_detail = response.text
                print(f"===== Erro ao pausar comunicação: {error_detail}")
                return {
                    "status": "error",
                    "message": f"Erro ao pausar comunicação: {error_detail}",
                    "status_code": response.status_code
                }
                
    except Exception as e:
        print(f"===== Erro ao pausar comunicação: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro ao pausar comunicação: {str(e)}"
        }

@function_tool
async def total_de_vendas():
    """
    Retorna o total de vendas realizadas

    """

    total_vendas = 2040.25
    return total_vendas



@function_tool
async def reativar_comunicacao_zap(usuario_idx: str):
    """Reativa a comunicação habilitando o evento MESSAGES_UPSERT para uma instância"""
    print(f"===== Reativando comunicação para usuário: {usuario_idx}")
    
    instancia_id = "atmzap-" + usuario_idx
    print(f"===== instancia_id: {instancia_id}")
    
    try:
        # URL do endpoint para configurar webhook
        url = f"{EVOLUTION_API_BASE_URL}/webhook/set/{instancia_id}"
        
        # Payload CORRIGIDO - incluindo MESSAGES_UPSERT junto com outros eventos essenciais
        payload = {
            "webhook": {
                "enabled": True,
                "url": EVOLUTION_API_WEBHOOK_URL.rstrip('/') + "/" + instancia_id,
                "byEvents": False,
                "base64": False,
                "events": [
                    "APPLICATION_STARTUP",
                    "QRCODE_UPDATED",
                    "CONNECTION_UPDATE",
                    "MESSAGES_UPSERT"  # Reabilita o evento de mensagens
                ]
            }
        }
        
        # Headers da requisição
        headers = {
            "apikey": EVOLUTION_API_KEY,
            "Content-Type": "application/json"
        }
        
        print(f"===== Reativando comunicação na URL: {url}")
        print(f"===== Payload: {json.dumps(payload, indent=2)}")
        
        # Fazer a requisição HTTP
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"===== Status Code: {response.status_code}")
            
            if response.status_code in [200, 201]:
                result = response.json()
                print(f"===== Comunicação reativada com sucesso: {result}")
                return {
                    "status": "success",
                    "message": "Comunicação reativada com sucesso (MESSAGES_UPSERT habilitado)",
                    "instancia_id": instancia_id,
                    "events_ativos": ["APPLICATION_STARTUP", "QRCODE_UPDATED", "CONNECTION_UPDATE", "MESSAGES_UPSERT"]
                }
            else:
                error_detail = response.text
                print(f"===== Erro ao reativar comunicação: {error_detail}")
                return {
                    "status": "error",
                    "message": f"Erro ao reativar comunicação: {error_detail}",
                    "status_code": response.status_code
                }
                
    except Exception as e:
        print(f"===== Erro ao reativar comunicação: {str(e)}")
        return {
            "status": "error",
            "message": f"Erro ao reativar comunicação: {str(e)}"
        }

@function_tool
async def criar_agente_conta(
    usuario_idx: str,
    instancia_id: str,
    agente_nome: str,
    agente_identidade: str,
    agente_personalidade: str,
    agente_instrucoes: str,
    negocio: str,
    negocio_produtos: str,
    negocio_servicos: str
):
    """
            Esta função é utilizada para atender na conta do whatsapp. os seguintes dados deverão ser fornrecidos pelo usuario:
            agente_nome: (obrigatório)
                nome do agente
            agente_identidade: (obrigatório)
                Explicação resumida de quem ele é e sua área de atuação.  
                Exemplo: você é a secretaria atendente da empresa X. Tem 20 anos de idade, é poliglota, e domina a arte de atender clientes. 
            agente_personalidade: (opcional)
                Você é calma, tranquila, muito paciente. Também é cordial .
            agente_instrucoes: (obrigatório)
                instruções do que o agente deve fazer , como fazer e regras do que não fazer.    
            negocio: (opcional) 
                Negócio para quem o agente trabalha.
                -Nome do negócio
                -Telefone e email de contato
                -Area de atuação
                -Endereço completo
                -Horário de funcionamento
            negocio_produtos: (opcional)
                Lista de produtos do negócio 
                -Nome do produto
                -Descrição
                -Preço
            negocio_servicos: (opcional) 
                Lista de serviços do negócio
                -Nome do serviço
                -Descrição
                -Preço
        IMPORTANTE: PEÇA UMA INFORMAÇÃO DE CADA VEZ, NÃO JUNTE VÁRIAS INFORMAÇÕES EM UMA MESMA MENSAGEM.  
    
    Returns:
        dict: resultado da operação
    """
    
    try:
        # Gera um ID único para o agente
        agente_idx = generate_unique_id()
        
        # Query Cypher para criar o nó Agente e os relacionamentos
        query = """
        MERGE (conta:Conta {nome: $instancia_id})
        MERGE (pessoa:Pessoa {idx: $usuario_idx})
        CREATE (agente:Agente {
            idx: $agente_idx,
            nome: $agente_nome,
            identidade: $agente_identidade,
            personalidade: $agente_personalidade,
            instrucoes: $agente_instrucoes,
            negocio: $negocio,
            produtos: $negocio_produtos,
            servicos: $negocio_servicos
        })
        CREATE (agente)-[:ATUA_EM]->(conta)
        CREATE (agente)-[:CRIADO_POR]->(pessoa)
        RETURN agente.idx as agente_id, conta.nome as conta_id, pessoa.idx as pessoa_id
        """
        
        # Parâmetros para a query
        params = {
            "agente_idx": agente_idx,
            "instancia_id": instancia_id,
            "usuario_idx": usuario_idx,
            "agente_nome": agente_nome,
            "agente_identidade": agente_identidade,
            "agente_personalidade": agente_personalidade,
            "agente_instrucoes": agente_instrucoes,
            "negocio": negocio,
            "negocio_produtos": negocio_produtos,
            "negocio_servicos": negocio_servicos
        }
        
        # Conecta ao Neo4j
        await neo4j.conecta()
        
        # Executa a query no Neo4j usando o método correto
        result = await neo4j.execute_write_query(query, params)
        
        # Fecha a conexão
        await neo4j.close()
        
        if result:
            # Primeira mensagem: Confirmação da criação do agente
            mensagem_sucesso = f"🎉 *Agente {agente_nome} criado com sucesso!*\n\nAgora você precisa conectar sua conta ao WhatsApp para que o agente possa começar a atender seus clientes."
            
            return {
                "status": "success",
                "message": mensagem_sucesso,
                "agente_idx": agente_idx,
                "instancia_id": instancia_id,
                "usuario_idx": usuario_idx,
                "data": result,
                "next_action": "connect_whatsapp",
                "connect_instructions": "Use a função conectar_conta_whatsapp() para obter as instruções de conexão."
            }
        else:
            return {
                "status": "error",
                "message": f"Erro ao criar agente {agente_nome}",
                "error": "Nenhum resultado retornado"
            }
            
    except Exception as e:
        # Garante que a conexão seja fechada em caso de erro
        try:
            await neo4j.close()
        except:
            pass
            
        return {
            "status": "error",
            "message": f"Erro ao criar agente {agente_nome}: {str(e)}",
            "error": str(e)
        }




if __name__ == "__main__":
    import asyncio
    
    async def testa_cria_conta_zap():
        usuario_idx = '1122334455'
        result = await cria_conta_zap(usuario_idx)
        print("@@@@@ resultado final", result)

    async def testa_pausar_comunicacao_zap():
        usuario_idx = '1122334455'
        result = await pausar_comunicacao_zap(usuario_idx)
        print("@@@@@ resultado pausar comunicação", result)

    async def testa_reativar_comunicacao_zap():
        usuario_idx = '1122334455'
        result = await reativar_comunicacao_zap(usuario_idx)
        print("@@@@@ resultado reativar comunicação", result)

    async def executar_todos_testes():
        print("\n===== TESTANDO CRIAÇÃO DE CONTA ZAP =====")
        await testa_cria_conta_zap()
        
        print("\n===== TESTANDO PAUSAR COMUNICAÇÃO ZAP =====")
        await testa_pausar_comunicacao_zap()
        
        print("\n===== TESTANDO REATIVAR COMUNICAÇÃO ZAP =====")
        await testa_reativar_comunicacao_zap()
    
    async def testa_criar_agente_conta():
        usuario_idx = '1122334455'
        instancia_id = 'atmzap-1122334455'

        agente_nome = 'Agente Teste'
        agente_identidade = 'Identidade Teste'
        agente_personalidade = 'Personalidade Teste'
        agente_instrucoes = 'Instruções Teste'
        negocio = 'Negócio Teste'
        negocio_produtos = 'Produtos Teste'
        negocio_servicos = 'Serviços Teste'
        result = await criar_agente_conta(usuario_idx, instancia_id, agente_nome, agente_identidade, agente_personalidade, agente_instrucoes, negocio, negocio_produtos, negocio_servicos)
        print("@@@@@ resultado criar agente conta", result)
    

    # Descomente a linha abaixo para executar todos os testes
    # asyncio.run(executar_todos_testes())
    
    # Teste individual (mantendo o original)
    #asyncio.run(testa_cria_conta_zap())
    #asyncio.run(testa_pausar_comunicacao_zap())
    #asyncio.run(testa_reativar_comunicacao_zap())
    asyncio.run(testa_criar_agente_conta())


@function_tool
async def conectar_conta_whatsapp(usuario_idx:str):   
    """
    Fornece instruções para conectar a conta WhatsApp ao sistema.
    Esta função é chamada após a criação do agente para orientar o usuário
    sobre como conectar seu WhatsApp à conta criada.
    
    IMPORTANTE: O link de conexão deve ser retornado como texto simples,
    SEM formatação Markdown (sem crases, sem [texto](link), etc.).
    O WhatsApp detecta automaticamente URLs como links clicáveis quando
    são apresentadas como texto puro.
    """
    instancia_id = f"atmzap-{usuario_idx}"
    mensagem = (
        f"Para conectar sua conta ao WhatsApp, siga estes procedimentos:\n\n"
        f"📱 *Passos para conexão:*\n"
        f"• Acesse um computador ou outro celular\n"
        f"• No equipamento, acesse o link de conexão\n"
        f"• Siga as instruções para gerar e ler o QR Code. Será necessário informar o código da sua conta: {instancia_id}.\n"
        f"• Aguarde a confirmação da conexão\n\n"
        f"🔗 *Link para conexão:*\n"
        f"app.atmzap.gptalk.com.br/conector\n\n"
        f"Após conectar, seu agente estará pronto para atender seus clientes!"
    )
    

@function_tool
async def consulta_dados_gerais_e_estatisticos_agente(query: str, parameters: dict = None):
    """
    Consulta dados do agente ou negócio no banco Neo4j.
    
    Esta função recebe uma query Cypher pronta do agente e a executa no banco.
    O agente é responsável por criar a query apropriada baseada na pergunta do usuário.
    
    Args:
        query (str): Query Cypher completa para executar
        parameters (dict): Parâmetros da query (deve incluir usuario_idx)
    
    Returns:
        dict: Resultados da consulta
    
    Exemplos de queries que o agente pode criar:
    - "qual é a identidade do agente" → MATCH ... RETURN agente.identidade
    - "qual o nome do meu agente" → MATCH ... RETURN agente.nome
    - "quais são os produtos" → MATCH ... RETURN agente.produtos
    - "me diga tudo sobre meu negócio" → MATCH ... RETURN todos os campos
    """
    
    try:
        if not query or not isinstance(query, str):
            return {
                "status": "error",
                "message": "Query Cypher é obrigatória"
            }
        
        if not parameters or not isinstance(parameters, dict):
            return {
                "status": "error",
                "message": "Parâmetros são obrigatórios (deve incluir usuario_idx)"
            }
        
        if "usuario_idx" not in parameters:
            return {
                "status": "error",
                "message": "usuario_idx é obrigatório nos parâmetros"
            }
        
        # Importar funções necessárias
        from ..agent_neo4j import AgentNeo4j
        
        
        # Executar a query Cypher fornecida pelo agente
        resultado = await neo4j.execute_read_query(query, parameters)
        
        if not resultado:
            return {
                "status": "success",
                "dados": [],
                "message": "Nenhum dado encontrado"
            }
        
        # Processar resultados
        return {
            "status": "success",
            "dados": resultado,
            "quantidade": len(resultado),
            "message": f"Consulta executada com sucesso, {len(resultado)} registro(s) encontrado(s)"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Erro ao executar consulta: {str(e)}",
            "query": query,
            "parameters": parameters
        }
    
    return {
        "status": "success",
        "message": mensagem,
        "link_conexao": "https://app.atmzap.gptalk.com.br/conector"
    }

