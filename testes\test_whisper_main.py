#!/usr/bin/env python3
"""
Teste main para o AgentWhisper
Envia arquivo de áudio para o endpoint de transcrição
"""

import requests
import os
import sys
from pathlib import Path

# Configurações do teste
BASE_URL = "http://localhost:8000"  # Ajuste conforme sua configuração
ENDPOINT = "/api/agent/transcribe"
AUDIO_FILE_PATH = r"C:\Users\<USER>\Audio\_temp\olabdia.mp3"  # Caminho do arquivo de áudio

def test_transcribe_with_file():
    """Testa transcrição enviando arquivo de áudio"""
    print("🎯 Testando transcrição com arquivo local...")
    
    # Verificar se arquivo existe
    if not os.path.exists(AUDIO_FILE_PATH):
        print(f"❌ Arquivo não encontrado: {AUDIO_FILE_PATH}")
        print("💡 Dica: Ajuste o caminho AUDIO_FILE_PATH no código")
        return False
    
    url = f"{BASE_URL}{ENDPOINT}"
    
    try:
        # Preparar arquivo para upload
        with open(AUDIO_FILE_PATH, 'rb') as audio_file:
            files = {
                'file': (
                    os.path.basename(AUDIO_FILE_PATH),
                    audio_file,
                    'audio/mpeg'
                )
            }
            
            print(f"📤 Enviando arquivo: {os.path.basename(AUDIO_FILE_PATH)}")
            print(f"🌐 URL: {url}")
            
            # Fazer requisição
            response = requests.post(url, files=files, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Transcrição realizada com sucesso!")
                print(f"📝 Texto: {result.get('transcription', 'N/A')}")
                print(f"🌍 Idioma: {result.get('language', 'N/A')}")
                print(f"📁 Arquivo: {result.get('filename', 'N/A')}")
                return True
            else:
                print(f"❌ Erro na requisição: {response.status_code}")
                print(f"📄 Resposta: {response.text}")
                return False
                
    except requests.exceptions.ConnectionError:
        print("❌ Erro: Não foi possível conectar ao servidor")
        print("💡 Dica: Certifique-se de que o servidor FastAPI está rodando")
        return False
    except requests.exceptions.Timeout:
        print("❌ Erro: Timeout na requisição (>60s)")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {str(e)}")
        return False

def test_transcribe_with_url():
    """Testa transcrição com URL de arquivo (exemplo)"""
    print("\n🌐 Testando transcrição com URL...")
    print("⚠️  Funcionalidade não implementada ainda")
    print("💡 Dica: Implemente se necessário")
    return False

def main():
    """Função principal de teste"""
    print("🎙️  TESTE DO AGENTWHISPER 🎙️")
    print("=" * 40)
    
    # Informações do teste
    print(f"📍 Servidor: {BASE_URL}")
    print(f"🎯 Endpoint: {ENDPOINT}")
    print(f"🎵 Arquivo: {AUDIO_FILE_PATH}")
    print("-" * 40)
    
    # Executar testes
    success_count = 0
    total_tests = 1
    
    if test_transcribe_with_file():
        success_count += 1
    
    # Relatório final
    print("\n" + "=" * 40)
    print(f"📊 RESULTADO: {success_count}/{total_tests} testes passaram")
    
    if success_count == total_tests:
        print("🎉 Todos os testes passaram!")
        return 0
    else:
        print("⚠️  Alguns testes falharam")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 