import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== PRODUTOS NO ESTOQUE DA CONSULTORA ===")
    
    negocio_idx = "4344140157"  # ID da consultora
    
    try:
        # Listar produtos disponíveis no estoque
        query = f"""
        SELECT CODIGO, NOME, ESTOQUE, ID_PAI
        FROM PRODUTO 
        WHERE NEGOCIO_IDX = '{negocio_idx}' 
        AND EXCLUIDO = 0 
        ORDER BY ESTOQUE DESC
        LIMIT 10
        """
        
        produtos = await mysql.query(query)
        
        if produtos:
            print(f"Produtos encontrados no estoque:")
            for i, produto in enumerate(produtos, 1):
                print(f"  {i}. Código: {produto.get('CODIGO')}")
                print(f"     Nome: {produto.get('NOME', 'N/A')}")
                print(f"     Estoque: {produto.get('ESTOQUE')} unidades")
                print(f"     ID_PAI: {produto.get('ID_PAI')}")
                print()
        else:
            print("❌ Nenhum produto encontrado no estoque!")
            
    except Exception as e:
        print(f"❌ ERRO: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 