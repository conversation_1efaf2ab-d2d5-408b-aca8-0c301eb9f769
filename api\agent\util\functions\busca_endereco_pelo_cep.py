from agents.tool import function_tool
import httpx


@function_tool
async def busca_endereco_pelo_cep(cep: str):
    """
    Use esta função para obter um endereço completo e o ddd a partir do cep informado.
    Retorna um dicionário com success e dados do endereço ou mensagem de erro.

    Parametros:
    cep: (str) - O cep a ser consultado. Deve conter apenas números.
    se o usuario informar pontos ou traços, remova-os.
    Exemplo:
    Se for informado 31.910-630, os simbolos devem ser removidos e passado apenas 31910630.
    """
    print("===== busca_endereco_pelo_cep() =====" )
    print("@@@@@ cep",cep)
    
    # Remove caracteres especiais do CEP
    cep_limpo = cep.replace(".", "").replace("-", "").replace(" ", "")
    
    url = f"https://opencep.com/v1/{cep_limpo}"
    print("@@@@@ url",url)
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=10)
            print("@@@@@ response",response)
            response.raise_for_status()
            data = response.json()
            
            # Verificar se a API retornou erro
            if "erro" in data or data.get("erro") == True:
                return {
                    "success": False,
                    "message": f"CEP {cep} não encontrado ou inválido",
                    "error": data.get("erro", "CEP inválido")
                }
            
            # Verificar se os dados essenciais estão presentes
            if not data.get("cep") or not data.get("localidade"):
                return {
                    "success": False,
                    "message": "Dados incompletos retornados pela API",
                    "error": "Resposta da API incompleta"
                }
            
            return {
                "success": True,
                "message": "Endereço encontrado com sucesso",
                "data": {
                    "cep": data.get("cep", ""),
                    "logradouro": data.get("logradouro", ""),
                    "complemento": data.get("complemento", ""),
                    "bairro": data.get("bairro", ""),
                    "localidade": data.get("localidade", ""),
                    "uf": data.get("uf", "")
                    #"ddd": data.get("ddd", ""),
                }
            }
            
    except httpx.TimeoutException:
        return {
            "success": False,
            "message": "Timeout ao consultar o CEP",
            "error": "Tempo limite excedido na consulta"
        }
    except httpx.HTTPStatusError as e:
        return {
            "success": False,
            "message": f"Erro HTTP ao consultar CEP: {e.response.status_code}",
            "error": f"Status HTTP {e.response.status_code}"
        }
    except Exception as e:
        print(f"Erro ao consultar o CEP: {e}")
        return {
            "success": False,
            "message": f"Erro inesperado ao consultar o CEP: {str(e)}",
            "error": str(e)
        }


if __name__ == "__main__":
    import asyncio
    async def teste_busca_endereco_pelo_cep():
        # Teste com CEP válido
        endereco = await busca_endereco_pelo_cep("31910630")
        print("=== Resultado do Teste ===")
        print(f"Sucesso: {endereco.get('success', False)}")
        print(f"Mensagem: {endereco.get('message', 'Nenhuma mensagem')}")
        
        if endereco.get('success'):
            dados = endereco.get('data', {})
            print("Dados do endereço:")
            for chave, valor in dados.items():
                print(f"  {chave}: {valor}")
        else:
            print(f"Erro: {endereco.get('error', 'Erro desconhecido')}")
            
    asyncio.run(teste_busca_endereco_pelo_cep())
    #execução:
    #python busca_endereco_pelo_cep.py
    #py -m api.agent.util.functions.busca_endereco_pelo_cep