# Imports ajustados para execução standalone
import sys
import os

# Adicionar o diretório raiz do projeto ao Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from fastapi import APIRouter
from fastapi.responses import JSONResponse

try:
    from .agent_llm import LLM
    from ..cache import cache
    from ..functions.util import generate_unique_id
    from .agent_openai import OpenAi
    from .agent_neo4j import AgentNeo4j
    from ..functions.validation.cpf_cnpj import cpf_cnpj_valido
    from ..functions.agent.message import limpar_tags_agente, extrair_texto_puro, get_conversa_key, limpar_html
    from ..functions.youtube import get_youtube_metadata, youtube_getTranscript, youtube_get_video_ID
except ImportError:
    # Fallback para execução standalone
    from api.agent.agent_llm import LLM
    from api.cache import cache
    from api.functions.util import generate_unique_id
    from api.agent.agent_openai import OpenAi
    from api.agent.agent_neo4j import AgentNeo4j
    from api.functions.validation.cpf_cnpj import cpf_cnpj_valido
    from api.functions.agent.message import limpar_tags_agente, extrair_texto_puro, get_conversa_key, limpar_html
    from api.functions.youtube import get_youtube_metadata, youtube_getTranscript, youtube_get_video_ID

import requests
import json
import re
from datetime import datetime
import pytz
from collections import Counter
import asyncio
from api.agent.agent_logger import AgentLogger
from api.agent.agent_message import Message
from api.agent.agent_ttsEdge import AgentTTSEdge
from api.agent.agent_whisper import AgentWhisper
from threading import Lock
import tempfile
import os
import re
import base64
from api.agent.agent_product import Product
from api.agent.agent_workOrder import AgentWorkOrder

# Cria o router para os endpoints deste módulo
router = APIRouter()

logger = AgentLogger()
modo_resposta_ia = "texto"
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
agentProduct = Product()
oai = OpenAi()
neo4j = AgentNeo4j()

# Import duplicado removido

class AgentBrain:
    def __init__(
        self,
        name="oficinatech",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        negocio=None,
        plataforma_url=None):
        logger.info("===== AgentBrain() =====")
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.instructions = f"""
Instruções do Agente Brain - Segundo Cérebro Digital
Identidade e Propósito
Você é Brain, um assistente virtual que funciona como um segundo cérebro digital para o usuário. Sua missão é organizar, armazenar e recuperar informações de forma inteligente e eficiente.
Funções Principais
1. Gerenciamento de Memória

memorizar_informacoes(texto: str) - Armazena informações textuais na base de conhecimento
responder_pergunta(pergunta: str) - Recupera e responde baseado nas informações memorizadas

2. Processamento de Conteúdo

resumir_texto(texto: str) - Condensa textos longos mantendo as informações essenciais
gerar_mapa_mental(texto: str) - Cria mapas mentais visuais para facilitar compreensão e memorização

3. Armazenamento de Arquivos

guardar_arquivo(arquivo: str) - Armazena documentos, imagens, PDFs e outros tipos de arquivo

Diretrizes de Uso das Funções
Quando usar memorizar_informacoes():

Quando o usuário fornecer texto, links, artigos ou informações textuais
Quando solicitar explicitamente para "memorizar", "guardar" ou "lembrar" de algo
Ao receber conteúdo textual sem contexto específico

Quando usar responder_pergunta():

SEMPRE antes de responder qualquer pergunta do usuário
Para consultar a base de conhecimento memorizada
Quando o usuário fizer perguntas sobre informações previamente fornecidas

Quando usar resumir_texto():

Quando o usuário solicitar resumo explicitamente
Ao receber textos muito longos (mais de 500 palavras)
Quando o usuário pedir para "simplificar" ou "condensar" informações

Quando usar gerar_mapa_mental():

Quando solicitado explicitamente pelo usuário
Para organizar informações complexas com múltiplos tópicos
Quando o conteúdo se beneficiaria de visualização hierárquica

Quando usar guardar_arquivo():

Para qualquer arquivo não-textual (imagens, PDFs, documentos, etc.)
Quando o usuário enviar anexos sem contexto específico
Para preservar a integridade de documentos originais

Regras de Comportamento
⚠️ REGRA CRÍTICA - Verificação de Memória
ANTES de responder qualquer pergunta:

SEMPRE execute responder_pergunta() primeiro
Se não houver informações memorizadas relevantes, responda: "Não tenho informações suficientes para responder esta pergunta. Por favor, forneça o conteúdo relacionado para que eu possa memorizá-lo e ajudá-lo."
Nunca invente ou assuma informações não memorizadas

Processamento Automático de Conteúdo Sem Contexto
Quando o usuário enviar conteúdo sem instruções específicas:
Para arquivos (imagens, PDFs, documentos):
guardar_arquivo(arquivo) → Confirmar armazenamento
Para texto, links, artigos:
memorizar_informacoes(texto) → Confirmar memorização
Fluxo de Trabalho Padrão

Identificar o tipo de solicitação do usuário
Verificar se precisa consultar informações memorizadas (responder_pergunta())
Executar a função apropriada
Confirmar a ação realizada ao usuário
Oferecer próximos passos quando relevante

Exemplos de Uso
Cenário 1: Pergunta do usuário
Usuário: "Qual era o tema principal do artigo que te mandei ontem?"
Ação: responder_pergunta("tema principal do artigo") → Responder baseado no resultado
Cenário 2: Novo conteúdo
Usuário: [envia link de artigo]
Ação: memorizar_informacoes(conteúdo_do_link) → "Artigo memorizado com sucesso!"
Cenário 3: Solicitação de resumo
Usuário: "Resume este texto para mim: [texto longo]"
Ação: resumir_texto(texto) → Apresentar resumo conciso
Comunicação com o Usuário

Seja claro sobre qual função está sendo executada
Confirme sempre que uma informação foi memorizada ou arquivo foi guardado
Ofereça sugestões proativas (ex: "Posso criar um mapa mental deste conteúdo?")
Mantenha tom amigável e eficiente

        """

    def get_instructions(self):
        return self.instructions


def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history



async def process_with_agent(
    mensagem: str,
    negocio_idx: str,
    modelo: str,
    usuario_funcao: int,
    usuario_nome: str = "Carlos",
    usuario_idx: str = "",
    plataforma_url: str = "",
    imagem: str = "",
    is_audio_response: bool = False,
):
    """Função comum para processar mensagens com o agente Brain"""
    
    conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "oficinatech")
    
    if not conversa_idx:
        conversa_idx = generate_unique_id()
        historico_mensagens = []
    
    if imagem:
        mensagem = "imagem_link :" + imagem + ";mensagem:" + mensagem
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)

    if usuario_funcao == 0:
        usuario_funcao = "administrador"
    else:
        usuario_funcao = "funcionario"
    
    agentOfTech = AgentBrain(
        negocio_idx=negocio_idx,
        usuario_nome=usuario_nome,
        plataforma_url=plataforma_url,
        usuario_idx=usuario_idx,
    )
    
    llm = LLM()
    model = llm.get_model_idx(modelo)
    # Linha 246-246
    # Linha 246-247
    logger.info(f"Modelo carregado: {model} (modelo original: {modelo})")
    
    instructions = agentOfTech.get_instructions()
    
    intencao = await detectar_intencao_usuario(mensagem)
    
    if is_audio_response:
        logger.info("Resposta para áudio detectada - forçando formato de texto simples")
        instructions += """
        
        INSTRUÇÃO ESPECIAL PARA RESPOSTA DE ÁUDIO:
        Esta resposta será convertida para áudio (text-to-speech), portanto:
        - NUNCA use JSON estruturado ou HTML
        - SEMPRE responda em texto simples e natural
        - Converta valores em texto por extenso e informe no final a moeda utilizda. Exemplo: R$ 100,00 = cem reais. 
        - Para produtos, descreva-os de forma conversacional
        - Use frases completas e bem estruturadas
        - Evite citar códigos, símbolos especiais ou formatação complexa
        - Seja claro e direto na comunicação
        """
    
    output_type = None

    tools_list = [
    ]
    
    agenteTech = {
        "name": "MCP+",
        "instructions": instructions,
        "model": model,
        "tools": tools_list,
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": [],
    }
    
    agenteTech_obj = await oai.agent_create(**agenteTech)
    
    return agenteTech_obj, historico_mensagens, conversa_idx, intencao


async def process_agent_stream(
    agenteTech_obj,
    historico_mensagens: list,
    negocio_idx: str,
    conversa_idx: str
):
    """Função comum para processar o streaming do agente OficinatTech"""
    global messageChat
    
    logger.info("=== INICIANDO PROCESS_AGENT_STREAM OFICINATECH ===")
    resposta_completa = ""
    chunk_count = 0
    
    try:
        async for response in oai.agent_run(agenteTech_obj, historico_mensagens):
            chunk_count += 1
            
            if isinstance(response, bytes):
                chunk_str = response.decode('utf-8')
            else:
                chunk_str = str(response)
            
            resposta_completa += chunk_str
            
            if chunk_count == 1 and ('/tool_call>' in chunk_str or '/toolcall>' in chunk_str or '<tool' in chunk_str):
                chunk_limpo = limpar_tags_agente(chunk_str)
                yield chunk_limpo
            else:
                yield chunk_str
            
        resposta_limpa = limpar_tags_agente(resposta_completa)
        texto_para_historico = extrair_texto_puro(resposta_limpa)
        historico_mensagens = add_message_to_history(historico_mensagens, texto_para_historico, False)

        
        cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
        cache[cache_key] = historico_mensagens
        
        with messageChat_lock:
            
            messageChat["SAIDA"] = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
            messageChat["RECEBIDO"] = resposta_limpa
            messageChat["RECEBIDO_TKS"] = len(texto_para_historico.split())
            messageChat["TOTAL_TKS"] = messageChat.get("ENVIADO_TKS", 0) + messageChat["RECEBIDO_TKS"]
            
            
            messageId = await agentMessage.add(messageChat.copy())
            
            if messageId:
                pass
            else:
                logger.error("❌ ERRO: Mensagem não foi salva no banco de dados!")
                logger.error(f"Dados enviados: {messageChat}")

    except Exception as stream_error:
        logger.error(f"Erro durante o streaming: {str(stream_error)}")
        if resposta_completa:
            texto_puro = extrair_texto_puro(resposta_completa)
            historico_mensagens = add_message_to_history(historico_mensagens, texto_puro, False)
            cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
            cache[cache_key] = historico_mensagens
        raise stream_error


async def detectar_intencao_usuario(mensagem: str):
    return ""

@router.post("/send/text")
async def send_text(data: dict):
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL
    logger.info("===== send_text() =====")
    logger.info(f"data: {data}")

    # Extrair dados necessários
    mensagem = data.get("mensagem") or ""
    negocio_idx = data.get("negocio_idx") or ""
    modelo = data.get("modelo") or "1234567890"
    logger.info(f"modelo: {modelo}")
    imagem = data.get("imagem") or ""
    plataforma_url = data.get("plataforma_url") or ""
    usuario_nome = data.get("usuario_nome") or "Carlos"
    usuario_funcao = data.get("usuario_funcao") or 1
    usuario_idx = data.get("usuario_idx") or ""
    negocio = data.get("negocio") or {}
    canal = data.get("canal") or "web_app"  
    modo_resposta_ia = data.get("modo_resposta_ia") or "texto"
    if not modelo:
        return {"status": "fail", "message": "Modelo é obrigatório"}

    # ✅ MAPEAR CORRETAMENTE PARA OS CAMPOS DA TABELA MENSAGEM
    with messageChat_lock:  # 🔒 PROTEÇÃO CONTRA CONCORRÊNCIA
        messageChat.clear()  # ✅ LIMPAR DADOS ANTERIORES
        messageChat.update({
            "ENVIADO": mensagem,
            "LLM_IDX": modelo,
            "CANAL_ID": "WEB_APP",
            "ENTRADA": datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S"),
            "AGENTE_ID": 13,
            "CONVERSA_IDX": 0,
            "ENVIADO_TKS": 0,
            "RECEBIDO_TKS": 0,
            "TOTAL_TKS": 0,
            "FUNCAO_CHAMADA": 0,
            "NEGOCIO_IDX": negocio_idx,
            "USUARIO_IDX": negocio_idx,
            "IMAGEM": imagem if imagem else None,
        })

    try:
        # Usar a função comum para processar com o agente
        agentOficinatech_obj, historico_mensagens, conversa_idx, intencao = await process_with_agent(
            mensagem=mensagem,
            negocio_idx=negocio_idx,
            modelo=modelo,
            usuario_nome=usuario_nome,
            plataforma_url=plataforma_url,
            imagem=imagem,
            usuario_idx=usuario_idx,
            usuario_funcao=usuario_funcao,

        )

        # Função para extrair texto de respostas aninhadas
        def extrair_texto_resposta(msg):
            if isinstance(msg, dict):
                if "messages" in msg and isinstance(msg["messages"], list) and len(msg["messages"]) > 0:
                    msg = msg["messages"][0]
                return msg.get("message") or msg.get("resposta") or msg.get("resposta_texto") or str(msg)
            return str(msg)

        # Atualizar CONVERSA_IDX antes do streaming
        with messageChat_lock:
            messageChat["CONVERSA_IDX"] = conversa_idx

        # Processar a resposta com base no modo
        resposta_completa = ""
        async for chunk in process_agent_stream(
            agenteTech_obj=agentOficinatech_obj,
            historico_mensagens=historico_mensagens,
            negocio_idx=negocio_idx,
            conversa_idx=conversa_idx
        ):
            if isinstance(chunk, bytes):
                chunk_str = chunk.decode('utf-8')
            else:
                chunk_str = str(chunk)
            if chunk_str.startswith('data: '):
                chunk_str = chunk_str[6:]
            resposta_completa += chunk_str

        logger.info ("canal atual: " + canal)
        logger.info(f"resposta completa:")
        logger.info(resposta_completa)
        # Aplicar limpeza condicional baseada no canal
        if canal.lower() in ["web_app", "app_web"]:
            # Mantém a formatação original para web/app
            texto_resposta = resposta_completa
        else:
            # Remove apenas HTML para WhatsApp, preservando Markdown
            logger.info("vou tirar as tags HTML do texto")
            texto_resposta = limpar_html(resposta_completa)
        
        logger.info(f"Resposta processada: {texto_resposta}")

        # Formatar a resposta no padrão do agent_neo4j
        response_body = {
            "success": True,
            "message": texto_resposta
        }

        # Lógica para modos de áudio
        if modo_resposta_ia in ["audio", "voz_ao_vivo", "texto_voz_ao_vivo"]:
            audio_resposta = await text_to_speech(texto_resposta)
            if audio_resposta:
                response_body["resposta_audio"] = audio_resposta
                response_body["audio_format"] = "mp3"
                if modo_resposta_ia == "texto_voz_ao_vivo":
                    response_body["resposta_texto"] = texto_resposta
            else:
                response_body["message"] = f"{texto_resposta} (Falha na conversão para áudio)"

        return JSONResponse(content=response_body, headers={"Content-Type": "application/json"})

    except Exception as e:
        logger.error(f"Erro durante execução do agente: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return JSONResponse(content={
            "status": "fail",
            "message": f"Erro durante execução: {str(e)}"
        }, headers={"Content-Type": "application/json"})



    # Extrai apenas palavras (letras) do texto, convertendo para minúsculas
    words = re.findall(r'\b[a-z]+\b', text.lower())
    
    # Filtra as stopwords
    filtered_words = [word for word in words if word not in stopwords and len(word) > 3]
    
    # Conta a frequência das palavras restantes
    word_counts = Counter(filtered_words)
    
    # Retorna as 'num_keywords' palavras mais comuns
    most_common_words = [word for word, count in word_counts.most_common(num_keywords)]
    
    return most_common_words


#============================================
async def processa_e_ingere_video(url: str, usuario_id: str):
    """
    Orquestra o pipeline completo: extrai dados, analisa com IA e salva no Neo4j.
    """
    print("===== processa_e_ingere_video() =====")
    
    try:
        if not oai.secret.OPENAI_API_KEY:
            print("❌ ERRO: A chave da API da OpenAI não está configurada.")
            return
        print("chave da API da OpenAI configurada")
        
        # PASSO 1: Extração de Conteúdo
        print("\n--- PASSO 1: Extraindo dados do vídeo ---")
        video_data = get_youtube_metadata(url)  # Corrigido: era get_youtube_data
        
        if video_data.get("erro"):
            print(f"❌ Falha ao obter metadados do vídeo: {video_data['erro']}")
            return
        
        print("video_data:", video_data)

        # Corrigido: usar 'transcricao' ao invés de 'texto_completo'
        texto_para_analise = video_data.get("transcricao")
        print("texto_para_analise:", texto_para_analise)

        # Se não há transcrição, usar a descrição
        if not texto_para_analise:
            print("-> Aviso: Usando apenas a descrição do vídeo para análise.")
            texto_para_analise = video_data.get("descricao")

        if not texto_para_analise:
            print("❌ ERRO: Não foi possível obter nenhum conteúdo textual para análise.")
            return
        
        print(f"✅ Texto obtido para análise: {len(texto_para_analise)} caracteres")
        
        
        
        # Gerar IDX único para o vídeo
        video_idx = generate_unique_id()
        print(f"IDX único gerado para o vídeo: {video_idx}")
        
        # Criar estrutura de dados para salvar
        video_info = {
            "idx": video_idx,
            "titulo": video_data.get("titulo", "Título não disponível"),
            "descricao": video_data.get("descricao", "Descrição não disponível"),
            "transcricao": texto_para_analise,
            "url": url,
            "canal": video_data.get("nome_canal", "Canal não disponível"),
            "url_canal": video_data.get("url_canal", ""),
            "usuario_id": usuario_id
        }
        
        print("@@@@@ video_info:", video_info)
        
        # A transcrição já foi obtida pela função get_youtube_metadata
        if video_info.get("transcricao") and video_info["transcricao"].strip():
            print(f"✅ Transcrição já obtida: {len(video_info['transcricao'])} caracteres")
        else:
            print("⚠️ Nenhuma transcrição disponível - usando apenas descrição")
        
    except Exception as e:
        print(f"\n❌ ERRO durante a execução: {e}")
        import traceback
        print(f"Traceback completo: {traceback.format_exc()}")


  
if __name__ == "__main__":
    import asyncio






    async def  teste_chat():
        data = {
            "mensagem": "oi",
            "negocio_idx": "4015743441",
            "modelo": "1234567895",
            "usuario_idx": "4344140157",
            "agente": "@gptalkzap",
            "usuario_idx": "1122334455",
            "canal": "whatsapp",
            "modo_resposta_ia": "texto"
            
        }

        result = await send_text(data)
        print("result:", result)


    #asyncio.run(teste_chat())
    #asyncio.run(test_pipline_youtube_data())
    # --- Bloco de Execução Principal ---

    async def test_download1():
        import yt_dlp

        url = "https://www.youtube.com/watch?v=4CZLASQsQgw"
        ydl_opts = {
            'format': 'bestaudio/best',
'outtmpl': '../../temp/audio/%(title)s.%(ext)s',
            'extractaudio': True,
            'audioformat': 'mp3',
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])





    async def test_pipline_youtube_data() -> dict:
        print("test_youtube_validate_url()")
        url = "https://www.youtube.com/watch?v=4CZLASQsQgw"
        result = get_youtube_metadata(url)

        if not result or 'erro' in result:
            print("❌ Erro:", result.get('erro', 'Erro desconhecido'))
            return {"success": False, "message": "URL inválida"}

        print("\n" + "="*80)
        print("📺 METADADOS DO VÍDEO DO YOUTUBE")
        print("="*80)
        print(f"🎬 Título: {result.get('titulo', 'N/A')}")
        print(f"📺 Canal: {result.get('nome_canal', 'N/A')}")
        print(f"🔗 URL do Canal: {result.get('url_canal', 'N/A')}")
        print(f"🎥 URL do Vídeo: {result.get('url_video', 'N/A')}")
        print(f"✅ Status: {'Sucesso' if result.get('success') else 'Erro'}")
        print("\n📝 Descrição:")
        print("-" * 40)
        descricao = result.get('descricao', 'N/A')
        if len(descricao) > 300:
            print(descricao[:300] + "...")
        else:
            print(descricao)

        print("\n📜 Transcrição:")
        print("-" * 40)
        transcricao = result.get('transcricao', 'N/A')
        if transcricao and transcricao != 'N/A' and len(transcricao) > 300:
            print(transcricao[:300] + "...")
        elif transcricao and transcricao != 'N/A':
            print(transcricao)
        else:
            print("Transcrição não disponível")

        print("="*80)

        print("\n📜 Análise da IA:")
        analise = await get_ai_analysis(result)
        print("resultado da analise", analise)
        return analise
       
    





           


        

    video_url_teste = "https://www.youtube.com/watch?v=BpxydUIigJw&t=7s"
    usuario_id_teste = "user_12345"
    asyncio.run(processa_e_ingere_video(video_url_teste, usuario_id_teste))
    