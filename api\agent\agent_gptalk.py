from .agent_user import User
from .agent_agent import Agent
from .agent_mysql import Mysql
from .agent_llm import LLM
from .agent_functioncall import FunctionCall
import json,re 

class Gptalk:
    def __init__(self,agente:Agent=None,usuario:User=None):
        self.mysql = Mysql()

        self.llm = LLM()
        self.usuario = usuario if usuario else User()
        self.agente = agente if agente else Agent()
        

    async def recharge_credit(value):
        pass

    async def chat(self,historico=None,mensagem=dict,llm_modelo=str,llm:LLM=None):
       
        if not llm:
            llm = self.llm
            
        msgSistema = {"role":"system","content":await self.get_system()}    
        #print("mensagem recebida", mensagem)
        mensagemCompleta = []
        mensagemCompleta.append(msgSistema)
        if historico:
            mensagemCompleta.extend(historico)
        mensagemCompleta.append(mensagem)
        #print("tam mensamCompleta",len(mensagemCompleta))
  
        return await llm.run_model(message=mensagemCompleta,model=llm_modelo)

    async  def get_system(self):

        return f"""
        Você é um assistente virtual, uma inteligência artificial criada pela IAPN. Seu nome é GPTalk, e sua função é ajudar {self.usuario.NOME} , tirando suas dúvidas, se necessário usando suas funções, ou encaminhando o usuário para ser atendido por outros agentes da sua equipe, mais capacitado para atender o que o usuário solicitar. Comprimente o usuário e pergunte como você pode ajudar, mas somente na mensagem inicial da conversa.
        """

    async def intention_detect(self,
        historico,
        mensagemRecebida:dict,
        llm_modelo:str,
        llm:LLM,
        agente:Agent,
        usuario:User,
        conversa_id:int,
        canal_id:int,
        ):
        print("vou detectar a intenção do usuário  usando o agente",agente.ID,agente.NOME)
       
        mensagens = []
        functionCall = FunctionCall()

        msgSistema = {"role":"system","content":await self.get_system_intention()} 
        mensagens.append(msgSistema)
        #print("Historico",historico)
        mensagens.extend(historico)
        mensagens.append(mensagemRecebida)
        #print("mensagens",mensagens)
        
        # Envia mensagem para o modelo LLM e recebe resposta
        intencao = await llm.run_model(
        message=mensagens,
        model=llm_modelo,
        )
        #print("agente",agente.ID,agente.NOME)
        #print("agente.ARQUIVO",agente.ARQUIVO)
        #print("convera_id",conversa_id)
        print("----------INTENCAO:",intencao)
     
        #print("type of intencao",type(intencao))
        agente_id =self.extrair_numero(intencao)
        print("agente da intencao",agente_id)
        if agente_id == 0: #GPTalk
            print("----------Gerar resposta sem função")
            print("Agente:",agente.ID,agente.NOME)
            resposta = await functionCall.chat(
            historico=historico,
            mensagemRecebida=mensagemRecebida,
            llm_modelo=llm_modelo,
            llm=llm,
            agente=agente,
            usuario=usuario,
            conversa_id = conversa_id, 
            canal_id=canal_id)
            return resposta
       
        print("----------gerar resposta com função")
        agente2 = Agent()
        await agente2.fetch_agent_id("*",agente_id)
        print("agente",agente2.ID,agente2.NOME)
        #print("agente2.ARQUIVO",agente2.ARQUIVO)
        
        resposta = await functionCall.chat(
        historico=historico,
        mensagemRecebida=mensagemRecebida,
        llm_modelo=llm.model_openai.gpt_4_o,
        llm=llm,
        agente=agente2,
        usuario=usuario,
        conversa_id = conversa_id, 
        canal_id=canal_id)

        print("resposta", resposta)
        return resposta

    @staticmethod
    def extrair_numero(s):
        # Expressão regular para encontrar a parte inteira de um número decimal no início da string
        match = re.search(r'\d+', s)
        if match:
            return int(match.group())
        else:
            return 0

    async def get_system_intention(self): 
                return """
        Você é um especialista em analise de texto para detecção intenções. Consegue  analisar um dialog completo, entre um usuário e um assitente, e determinar qual a intenção final do usuário ainda nao solucionada , atendida ou respondida pelo assistente, não importa o tamanho do texto. Responda de forma objetiva ,sem rodeios, em 1 frase a intenção final pendente do usuario. A resposta deve ser o numero da intenção na lista. Caso haja mais de uma intenção, separar os numeros por virgual. Exemplos:
        Apenas uma itenção:
        1 
 
        Mais de uma intenção:   
        1.1,2.1  


        Verifique se cada itenção  na lista corresponde a uma destas intenções:

        1.1 Fazer Recarga de créditos para uso do GPTALK
        1.3 Aumentar, depositar ou recarregar  créditos para uso do GPTalk ou outras IAS da equipe GPTALK
        1.4 Verificar o saldo de créditos do app ou serviço GPTALK
        1.5 Consultar o extrato de créditos do app ou serviço GPTALK
        1.7 Localizar um agente pelo nome
        1.9 Falar com o suporte técnico do serviço GPTALK ou outro da IAPN
        1.10 Fazer uma reclamação
        1.11 Informar um problema com o serviço GPTalk ou em outro produto da IAPN

        2.1 Listar tarefas ou compromissos agendados.
        2.2 Criar uma tarefa a ser executada  em uma data e/ou hora específica
        2.1 Verificar ou  consultar a agenda de compromissos
        2.2 Alterar uma tarefa que ja foi agendada anteriormente. A alteração pode ser da descrição, data e hora da realização, pessoas envolvidas, etc.
        2.3 Excluir uma tarefa que ja estsava agendada anteriormente, mas que não será mais executada
        
        0.0 Nenhuma intenção da lista detectada

        Se nenhuma das intenções acima for detectada, retorne  o  numeo 0
        caso um item encontre correspondencia, retorne o item da o item correspondente.


        é obrigatório retornar um numero, seja o corresponde a intencao da lista, ou 0 se não encontrou correspondoenccia na lista.


        Analise o seguinte texto e determine a ou as intenções: \n
        """




if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        gpt = Gptalk()
        intencao = "bom dia" # texto com a intenção do usuário
        print (gpt.extrair_numero(intencao))
    asyncio.run(main())