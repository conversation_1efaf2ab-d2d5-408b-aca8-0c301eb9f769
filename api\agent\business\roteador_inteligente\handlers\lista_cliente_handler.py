# handlers/lista_clientes_handler.py
import logging
from typing import Dict, Any, List
from datetime import datetime

class ListaClientesHandler:
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
    
    def processar(self, parametros: Dict[str, Any]) -> Dict[str, Any]:
        """Retorna lista de clientes formatada"""
        try:
            clientes = self._buscar_clientes(parametros)
            
            return {
                "tipo": "lista_clientes",
                "dados": self._formatar_clientes(clientes),
                "total": len(clientes),
                "filtros_aplicados": parametros,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao buscar clientes: {str(e)}")
            return {
                "tipo": "erro",
                "mensagem": str(e),
                "dados": []
            }
    
    def _buscar_clientes(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Busca clientes no banco de dados"""
        try:
            # Query base
            base = "MATCH (c:Cliente)"
            filtros = []
            
            if params.get('status') == 'ativo':
                filtros.append("c.ativo = true")
            elif params.get('status') == 'inativo':
                filtros.append("c.ativo = false")
            
            where_clause = f" WHERE {' AND '.join(filtros)}" if filtros else ""
            
            query = f"""
                {base}{where_clause}
                RETURN 
                    c.id as id,
                    c.nome as nome,
                    c.email as email,
                    c.telefone as telefone,
                    c.cadastro as data_cadastro,
                    c.ativo as ativo,
                    c.total_compras as total_compras,
                    c.valor_total as valor_total
                ORDER BY c.nome
            """
            
            # Mock para testes
            return self._mock_clientes(params)
            
        except Exception as e:
            self.logger.error(f"Erro na query: {str(e)}")
            return self._mock_clientes(params)
    
    def _formatar_clientes(self, clientes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Formata clientes para visualização"""
        return [
            {
                "id": cliente.get('id', ''),
                "nome": cliente.get('nome', 'Cliente'),
                "email": cliente.get('email', ''),
                "telefone": cliente.get('telefone', ''),
                "data_cadastro": str(cliente.get('data_cadastro', '')),
                "status": "Ativo" if cliente.get('ativo', True) else "Inativo",
                "total_compras": cliente.get('total_compras', 0),
                "valor_total": float(cliente.get('valor_total', 0)),
                "badge": self._gerar_badge_cliente(cliente)
            }
            for cliente in clientes
        ]
    
    def _gerar_badge_cliente(self, cliente: Dict[str, Any]) -> str:
        """Gera badge baseado no perfil do cliente"""
        valor_total = cliente.get('valor_total', 0)
        total_compras = cliente.get('total_compras', 0)
        
        if valor_total > 1000:
            return "💎 VIP"
        elif total_compras > 10:
            return "⭐ Frequente"
        else:
            return "🆕 Novo"
    
    def _mock_clientes(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Dados mock para testes"""
        todos_clientes = [
            {
                "id": "001",
                "nome": "João Silva",
                "email": "<EMAIL>",
                "telefone": "(11) 9999-0001",
                "data_cadastro": "2024-01-15",
                "ativo": True,
                "total_compras": 25,
                "valor_total": 1250.50
            },
            {
                "id": "002",
                "nome": "Maria Santos",
                "email": "<EMAIL>",
                "telefone": "(11) 9999-0002",
                "data_cadastro": "2024-02-20",
                "ativo": True,
                "total_compras": 8,
                "valor_total": 450.00
            },
            {
                "id": "003",
                "nome": "Carlos Oliveira",
                "email": "<EMAIL>",
                "telefone": "(11) 9999-0003",
                "data_cadastro": "2024-03-10",
                "ativo": False,
                "total_compras": 3,
                "valor_total": 150.00
            }
        ]
        
        # Filtra por status se especificado
        if params.get('status') == 'ativo':
            return [c for c in todos_clientes if c['ativo']]
        elif params.get('status') == 'inativo':
            return [c for c in todos_clientes if not c['ativo']]
        
        return todos_clientes

# Teste rápido
if __name__ == "__main__":
    handler = ListaClientesHandler(None)
    
    testes = [
        {},  # Todos
        {"status": "ativo"},  # Apenas ativos
        {"status": "inativo"}  # Apenas inativos
    ]
    
    for params in testes:
        resultado = handler.processar(params)
        print(f"Parâmetros: {params}")
        print(f"Total: {resultado['total']} clientes")
        for cliente in resultado['dados'][:2]:
            print(f"  - {cliente['nome']} ({cliente['status']}) - {cliente['badge']}")
        print("-" * 30)