import requests
from .agent_secret import Secret


class Whm:
    def __init__(self,user_name=None,token=None,url=None):
        self.secret = Secret()
        self.USER_NAME = user_name if user_name else self.secret.WHM_USER
        self.TOKEN  = token if token else self.secret.WHM_TOKEN
        self.URL = url if url else self.secret.WHM_URL
        self.HEADERS = {
            "Authorization": f"whm {self.USER_NAME}:{self.TOKEN}"
            }

    async def test(self):
        # URL da API para obter a versão do WHM
        version_url = f"{self.URL}/json-api/version"
        # Fazendo a solicitação
        response = requests.get(version_url, headers=self.HEADERS, verify=False)
        # Verificando a resposta
        if response.status_code == 200:
            print("Conexão bem-sucedida!")
            print("Resposta da API:", response.json())
        else:
            print("Erro na conexão:", response.text)

    async def delete_subdomain(self,subdomain,domain):
        print("########## delete_subdomain", subdomain,domain)
        print(self.HEADERS)
        # URL da API para excluir o subdomínio

        # URL da API para excluir o subdomínio
        #delete_subdomain_url = f"{self.URL}/json-api/delsubdomain"
    
    
           # URL da API para excluir o domínio
        delete_domain_url = f"{self.URL}/json-api/delete_domain"

        # Parâmetros da solicitação
        params = {
            "api.version": "1",
            "domain": subdomain
        }

        # Fazendo a solicitação
        response = requests.get(delete_domain_url, headers=self.HEADERS, params=params, verify=False)
   
        
        print("----------response",response)
        # Verificando a resposta
        if response.status_code == 200:
            result = response.json()
            print("result",result)
            if result.get('status') == 1:
                print("Subdomínio excluído com sucesso.")
            else:
                print("Erro ao excluir o subdomínio:", result.get('statusmsg'))
        else:
            print("Erro na solicitação:", response.text)





import asyncio
async def main():
    whm = Whm()
    #await whm.test()
    await whm.delete_subdomain("petdocarlos.site.gptak.com.br","site.gptalk.com.br")

#asyncio.run(main())

