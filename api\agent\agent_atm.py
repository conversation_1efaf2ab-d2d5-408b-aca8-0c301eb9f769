from .agent_mysql import Mysql
from fastapi import APIRouter

from typing import List, Optional

router = APIRouter()


class Atm:

    def __init__(self):
        self.mysql = Mysql()

    async def automation_fetch(self, folder_idx: str, conta_idx: str):
        pastas = await self.mysql.query(f"""
            SELECT * FROM AUTOMACAO_PASTA 
            WHERE MAE_IDX = {folder_idx} AND CONTA_IDX = '{conta_idx}' AND EXCLUIDO = 0
        """)

        automacoes = await self.mysql.query(f"""
            SELECT ID,IDX, NOME,STATUS FROM AUTOMACAO 
            WHERE PASTA_IDX = {folder_idx} AND CONTA_IDX = '{conta_idx}' AND EXCLUIDO = 0
        """)

        result = {}
        result['pastas'] = pastas
        result['automacoes'] = automacoes

        return result


    async def atms_save(self, atms: list):
        print("===== self.atms_save()")
        for atm in atms:
            if(atm['status'] == ""):
                continue
            
            
            tarefa = {
         
                'IDX': atm['idx'],
                'AUTOMACAO_IDX': atm['automacao_idx'],
                'AGENTE_IDX': atm['idxAgent'],
                #'STATUS': atm['status'],
                'DIAGRAMA_POS_X': atm['x'],
                'DIAGRAMA_POS_Y': atm['y'],
            }
            if(atm['id'] == 0):   
                
                atm['id'] = await self.mysql.add('TAREFA',tarefa)
            else:  
                tarefa['ID'] = atm['id']
                if(atm['status'] == 'E'):
                    tarefa['EXCLUIDO'] = 1
                else:
                    tarefa['EXCLUIDO'] = ""
                result = await self.mysql.update('TAREFA',tarefa)  
                
            
        #excluir todos os itens do atms com status = "E"
        atms = [atm for atm in atms if atm['status'] != 'E']
        for atm in atms:
            atm['status'] = ""
                
        return atms


    async def automation_save(self, automacao: dict):
        print("===== self.automation_save()")
        if(automacao['ID'] == 0):
            #inserir
            del automacao['ID']
            automacao['ID'] = await self.mysql.add('AUTOMACAO',automacao)
        else:
            #atualizar
            result = await self.mysql.update('AUTOMACAO',automacao)
        return automacao

    async def atms_fetch(self, automation_idx: str):
        atms = []
        result = await self.mysql.query(f"""
            SELECT T.AUTOMACAO_IDX, 
            T.ANTERIOR_IDX,
            T.PROXIMA_IDX,
            T.IDX, 
            T.ID,
            T.DIAGRAMA_POS_X, 
            T.DIAGRAMA_POS_Y,
            A.NOME as AGENTE_NOME,
            A.ICONE,
            A.TIPO_ID
            FROM TAREFA T
            LEFT JOIN AGENTE A ON A.IDX = T.AGENTE_IDX
            WHERE T.AUTOMACAO_IDX = '{automation_idx}' 
            AND T.EXCLUIDO = 0
        """)
        atms = []
        if len(result) > 0:
                for tarefa in result:
            
                    atm = {}
                    atm['id'] = tarefa['ID']
                    atm['idx'] = tarefa['IDX']
                    atm['automation_idx'] = tarefa['AUTOMACAO_IDX']
                    atm['x'] = tarefa['DIAGRAMA_POS_X']
                    atm['y'] = tarefa['DIAGRAMA_POS_Y']
                    atm['name'] = tarefa['AGENTE_NOME']
                    atm['type'] = tarefa['TIPO_ID']
                    atm['icon'] = tarefa['ICONE']
                    atm['prevAtm'] = tarefa['ANTERIOR_IDX']
                    atm['nextAtm'] = tarefa['PROXIMA_IDX']
                    atm['status'] = ""
                    atms.append(atm)
                    #print("atm",atm)
                    #print("atms",atms)
        
        
        
        return {"atms": atms}
    

    async def atm_delete(self, atm_idx: str):
        print("===== atm_delete()")
        try:
            # Marca o atendimento como excluído
            result = await self.mysql.query(f"""
                UPDATE TAREFA 
                SET EXCLUIDO = 1 
                WHERE IDX = '{atm_idx}'
            """)

            return {"status": "success", "message": "Atendimento excluído com sucesso"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def automation_delete(self, automation_idx: str):
        print("===== automation_delete()")
        try:
            # Marca a automação como excluída
            result = await self.mysql.query(f"""
                UPDATE AUTOMACAO 
                SET EXCLUIDO = 1 
                WHERE IDX = '{automation_idx}'
            """)

            return {"status": "success", "message": "Automação excluída com sucesso"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def search_agents(self, search_term: str):
        agents = await self.mysql.query(f"""
            SELECT IDX, NOME, ICONE 
            FROM AGENTE 
            WHERE EXCLUIDO = 0 
            AND (NOME LIKE '%{search_term}%' OR IDX LIKE '%{search_term}%')
        ORDER BY NOME
            LIMIT 7
        """)
        return agents


#Carrega a lista de pastas e de automações
@router.get("/automations/fetch")
async def automations_fetch(folder_idx: str, conta_idx: str):
    print("automation_fetch")
    atm = Atm()
    result = await atm.automation_fetch(folder_idx, conta_idx)
    print("result", result)
    return result

#Carrega a lista de ATMs de uma automação
@router.get("/atms/fetch")
async def atms_fetch(automation_idx: str):
    print("atms_fetch")
    atm = Atm()
    result = await atm.atms_fetch(automation_idx)
    return result


#Salva a automação
@router.post("/automation/save")
async def save_automation(data: dict):
    print("===== save_automation()")
    atm = Atm()
    result = {}
    automacao = data.get("AUTOMACAO")
    atms = data.get("ATMS")
    print("automacao", automacao)
    print("atms", atms)
    result['AUTOMACAO'] = await atm.automation_save(automacao)
    result['ATMS'] = await atm.atms_save(atms)
    print("result", result)

    #print("atm", atm)
    #print("tasks", tasks)
    return result


@router.get("/automation/delete/{automation_idx}")
async def delete_automation(automation_idx: str):
    print("===== delete_automation()")
    atm = Atm()
    result = await atm.automation_delete(automation_idx)
    print("result", result)
    return {}


@router.get("/delete/{atm_idx}")
async def delete_atm(atm_idx: str):
    print("===== delete_atm()")
    atm = Atm()
    result = await atm.atm_delete(atm_idx)
    print("result", result)
    return {}


@router.get("/agents/search")
async def search_agents(searchTerm: str):
    atm = Atm()
    try:
        agents = await atm.search_agents(searchTerm)
        return  agents
    except Exception as e:
        return {"error": str(e)}


if __name__ == "__main__":
    import asyncio
    from fastapi.testclient import TestClient
    from api.main import app  # Importa o app principal

    # Cria uma única instância do TestClient para ser reutilizada
    client = TestClient(app)

    async def test_automations_fetch():

        # Teste com folder_idx válido
        test_folder_idx = 0

        try:
            print("\nTestando endpoint /api/agent/atm/automations/fetch")
            print("Enviando folder_idx:", test_folder_idx)

            response = client.get(
                f"/api/agent/atm/automations/fetch?folder_idx={test_folder_idx}&conta_idx=3231234562",
                headers={"accept": "application/json"}
            )

            print("Status code recebido:", response.status_code)
            print("Headers recebidos:", response.headers)

            # Verifica se a resposta foi bem-sucedida
            assert response.status_code == 200, "Erro na requisição: Status code não é 200"

            result = response.json()
            print("Resultado do teste de automations/fetch:")
            print(result)

            print("\n✓ Teste de automations/fetch executado com sucesso")

        except Exception as e:
            print("✕ Erro no teste de automations/fetch:", str(e))
            raise e

    #Testa o salvamento de uma automação
    async def test_automation_save():
        # Dados de teste para um automation completo
        test_data = {
            "AUTOMACAO": {
                "ID": 27,
                "IDX": "9699055762",
                "NOME": "TESTE27 - Minha segunda automação salva",
                "PASTA_IDX": "0",
                "STATUS": 1,
                "CONTA_IDX": "3231234562"
            },
            "ATMS": [
                {   "id": 52,
                    "idx": "1234567890",
                    "automacao_idx": "9699055762",
                    "idxAgent": "3231234562",
                    "status": "E",
                    "x": 50,
                    "y": 45,
                    "prevAtm": "0",
                    "nextAtm": "0"
                },
                {
                    "id": 51,
                    "idx": "1234567891",
                    "automacao_idx": "9699055762",
                    "idxAgent": "3231234562",
                    "status": "A",
                    "x": 201,
                    "y": 50,
                    "prevAtm": "0",
                    "nextAtm": "0"
                }
            ]
        }
        
        try:
            print("\nTestando endpoint /api/agent/atm/automation/save")
            print("Enviando automation:", test_data)

            response = client.post(
                "/api/agent/atm/automation/save",
                json=test_data,
                headers={"accept": "application/json"}
            )

            print("Status code recebido:", response.status_code)
            print("Headers recebidos:", response.headers)   

            # Verifica se a resposta foi bem-sucedida
            assert response.status_code == 200, "Erro na requisição: Status code não é 200"

            result = response.json()
            print("Resultado do teste de automation/save:")
            print(result)

            print("\n✓ Teste de automation/save executado com sucesso")

        except Exception as e:
            print("✕ Erro no teste de automation/save:", str(e))
            raise e
    
    async def test_atms_fetch():
        client = TestClient(app)
    
        # Teste com automation_idx válido
        test_automation_idx = "9699055762"
    
        try:
            print("\nTestando endpoint /api/agent/atm/atms/fetch")
            print("Enviando automation_idx:", test_automation_idx)
    
            response = client.get(
                f"/api/agent/atm/atms/fetch?automation_idx={test_automation_idx}",
                headers={"accept": "application/json"}
            )
    
            print("Status code recebido:", response.status_code)
            print("Headers recebidos:", response.headers)
    
            # Verifica se a resposta foi bem-sucedida
            assert response.status_code == 200, "Erro na requisição: Status code não é 200"
    
            result = response.json()
            print("Resultado do teste de atms/fetch:")
            print(result)
    
            print("\n✓ Teste de atms/fetch executado com sucesso")
    
        except Exception as e:
            print("✕ Erro no teste de atms/fetch:", str(e))
            raise e
    
    async def test_search_agents():
        with TestClient(app) as client:
            # Teste com termo de busca válido
            response = client.get("/api/agent/atm/agents/search?searchTerm=google")
            assert response.status_code == 200
            data = response.json()
            print("data", data)
            return 
        
            # Teste com termo de busca vazio
            response = client.get("/agent/atm/agents/search?searchTerm=")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "agents" in data["data"]

    #asyncio.run(test_automation_save())
    #asyncio.run(test_automations_fetch())
    #asyncio.run(test_atms_fetch())
    asyncio.run(test_search_agents())
