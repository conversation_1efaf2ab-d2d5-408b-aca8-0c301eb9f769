import requests
import asyncio

#=====================================================
async def link_valid(link):
  try:
   resposta = requests.head(link).status_code

    # Algumas URLs podem exigir uma solicitação GET para obter o Content-Type correto
    #if resposta.status_code >= 400:
    #resposta = requests.get(link, stream=True)
    #resposta


   return resposta

  except requests.exceptions.RequestException as e:
        # Trata exceções para erros de conexão, timeout, etc.
        #print('Erro ao verificar o link:', e)
        return {'erro':str(e)}
    
#===============================================
async def link_validate_type_format(link):
  print('link_validate_type_format()')
  print("link",link)
  # Verifica se o link é de um vídeo do YouTube
  valido = await link_valid(link)
  if (valido!=200):
      return "{'erro':'link inválido ou de conteúdo não suportado'}"
  if "youtube.com" in link or "youtu.be" in link:
    #print('O link é de um vídeo do YouTube.')
    return {"tipo":"video","formato":"youtube"}
  else:
    return '' # valido

#================================================
async def main():
    link = "https://www.youtube.com/watch?v=Xo5VXTRoL6Q"
    result = await link_valida_tipo_formato(link)
    print("result",result)

#asyncio.run(main())
