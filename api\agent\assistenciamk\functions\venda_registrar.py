from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
from ...util.functions.gera_id_unico import gera_id_unico
from ...agent_evolutionzap import AgentEvolutionZap


import json

neo4j = AgentNeo4j()
evolutionApi = AgentEvolutionZap()

instancia_id = evolutionApi.get_instace_id()

from pydantic import BaseModel
from typing import List, Optional
import asyncio
from datetime import datetime



# Modelos Pydantic
class ClienteData(BaseModel):
    idx: str
    nome: str
    email: str
    telefone: str
    cpf_cnpj: Optional[str] = None
    cep: Optional[str] = None
    logradouro: Optional[str] = None
    bairro: Optional[str] = None
    cidade: Optional[str] = None
    uf: Optional[str] = None
    numero: Optional[str] = None
    complemento: Optional[str] = None


class ProdutoItem(BaseModel):
    idx: str
    codigo: str
    nome: str
    preco: float
    quantidade: int

@function_tool
async def venda_registrar(
    usuario_idx: str,
    usuario_nome: str,
    usuario_whatsapp: str,
    conversa_idx: str,
    negocio_idx: str,
    negocio_nome: str,
    canal_idx: str,
    agente_idx: str,
    em_resposta_idx: str,
    cliente: ClienteData ,
    produtos: List[ProdutoItem]
):
    """
    Registra a VENDA de produtos e atualiza o estoque de cada produto.
    
    Não solicite todas as informações de uma vez. Solicite uma por vez.
    
    PARAMETROS:
    
    
    - usuario_idx: 
    ID da consultora.
    Obrigatório

    - usuario_nome:
    NOME do usuario.
    Obrigatório.
    
    - negocio_idx: 
    ID do negócio (Mary Kay) no banco de dados.
    Obrigatório.
    
    - negocio_nome: 
    NOME do negócio

    CLIENTE:
    -Objeto ClienteData com os dados completos do cliente
    -Obrigatório
    -Solicite que o usuário informe um destes dados: nome do cliente, cpf, cnpj, email ou telefone. Use a informação fornecida para localizar o cliente usando a funcao cliente_consultar(), e em seguida passe-o para a função com TODOS os campos obrigatórios preenchidos. Caso o cliente não seja encontrado, informe ao usuario que o cliente não foi encontrado e solicite que informe outra informação do cliente para uma nova busca, ou iniciar a inclusão de um novo , se for o caso.

    -PRODUTOS:
    Array de dicionarios com os produtos a serem vendidos. cada produto tera os seguintes dados:
    idx - indentificador unico do produto
    codigo - codigo do produto
    nome - nome completo
    preco - preço do produto
    quantidade - quantidade a ser vendida.
    Exemplo:
    [
        {"idx": "9835421345", "codigo": "Pneu aro 26","nome": "Pneu aro 26","preco": 80, "quantidade": 2},
    ]
    Obrigatorio.
    
    Solicite o nome ou código do produto e faça uma busca na tabela PRODUTO usando a função verificar_estoque_revenda() para encontrar os dados do produto. Caso não encontre, informe ao usuário que o produto não foi encontrado e solicite que informe o nome ou código do correto do produto ou se ele deseja incluir um novo produto.
    
    Após encontrar o produto, informe ao usuário  os seguintes dados do produto:
    - nom 
    - código 
    - preço 
    - estoque atual 

    Em seguida,  solicite ao usuário a quantidade, caso ele ainda não tenha informado. 


    🚨🚨🚨 IMPORTANTE:
     consulte o estoque atual do produto usando a função produto_consulta_estoque().
     
    Se a quantidade informada for maior que o estoque, informe ao usuário que o estoque é insuficiente e pergunte o que ele deseja fazer: Prosseguir mesmo assim ,  informar  uma quantidade igual ou menor ao estoque ou cancelar a inclusão deste produto.
    
    Caso haja prosseguimento, pergunte se ele deseja adicionar mais produtos.
    
    IMPORTANTE: Ao fazer buscas por produtos, verifque  se o texto informado é encontrado total ou parcialmente na coluna NOME ou CODIGO do produto. Em geral os produtos também possuem códigos, e o usuario pode optar em informar o codigo em vez do nome.
    Após pegar todos os dados do produto, pergunte ao usuário se ele deseja adicionar outro. Caso sim, solicite os dados do novo produto a ser adicionado. Caso não, execute a função com os dados obtidos.

    REGRAS IMUTÁVEIS (NÃO QUEBRAR)
    1) Esta função é EXCLUSIVAMENTE para VENDA de produtos.
    2) O estoque ou saldo do produto deve ser consultado usando a função produto_consulta_estoque().
    3) Foque SOMENTE em:
       - Identificar o produto (por nome ou código);
       - Coletar a quantidade  por item;
       - Permitir adicionar mais itens;
       - Executar o registro da venda;


    FORMATO DE RESPOSTA AO USUÁRIO
    Sempre retorne um JSON (como string) em resposta.message com a estrutura:
    {
      "tipo": "venda",
      "itens": [
        {"Produto": "<codigo - nome>", "Preço": <float>, "Qtde": <int>, "Total": <float> },
        ...
      ],
      "total_geral": <float>,
      "data_hora": "dd/mm/YYYY HH:MM"
    }
    E no objeto externo da função retorne:
    {"status":"success"|"error", "message":"<string JSON acima>", "function":"venda_registrar"}
    """
    print("========== produto_venda() ==========")
    print("negocio_idx", negocio_idx)
    print("negocio_nome" , negocio_nome)
    print("usuario_idx", usuario_idx)
    print("usuario_nome", usuario_nome)
    print("agente_idx", agente_idx)
    print("em_resposta-idx", em_resposta_idx )
    print("cliente", cliente)
    print("produtos", produtos)

    cliente_idx = cliente.idx if cliente else None

    # Normaliza default para evitar lista mutável compartilhada
    if produtos is None:
        produtos = []

    # Gera ids necessários para Revenda (se for criada) e para a Compra
    revenda_idx = gera_id_unico()
    venda_idx = gera_id_unico()
    total = 0

    # Serializa os produtos para dicionários simples e gera idx de ProdutoRevenda por item
    produtos_payload = [
        {
            "idx": p.idx,
            "codigo": p.codigo,
            "nome": p.nome,
            "preco": p.preco,
            "quantidade": p.quantidade,
            "prv_idx": gera_id_unico(),
        }
        
        for p in produtos
    ] if produtos else []

    # Monta a query Cypher conforme o mapa:
    # Pessoa -[REALIZA_REVENDA]-> Revenda (cria se não existir; define idx no create)
    # Revenda -[VENDEU]-> Venda (Venda com idx e data_hora)
    # Venda -[VENDEU_PRODUTO]-> ProdutoRevenda (para cada item)
    query = """
    MERGE (p:Pessoa {idx: $usuario_idx})
    MERGE (p)-[:REALIZA_REVENDA]->(r:Revenda)
      ON CREATE SET r.idx = $revenda_idx, r.created_at = datetime()

    MERGE (n:Negocio {idx: $negocio_idx})
    MERGE (r)-[:REFERENTE]->(n)

    WITH r, $venda_idx AS venda_idx, $produtos AS produtos
    WITH r, venda_idx, produtos,
         reduce(total = 0.0, p IN produtos | total - toFloat(p.quantidade) * toFloat(p.preco)) AS total
    CREATE (v:Venda {idx: venda_idx, data_hora: datetime(), total: total})
    MERGE (r)-[:VENDEU]->(v)

    WITH r, v, produtos
    UNWIND produtos AS prod
    MATCH (pr:Produto {idx: prod.idx})
    MERGE (r)-[:REVENDE]->(prv:ProdutoRevenda)-[:INSTANCIA_DE]->(pr)
      ON CREATE SET prv.idx = prod.prv_idx, prv.estoque = prod.quantidade, prv.created_at = datetime()
      ON MATCH SET prv.estoque = coalesce(prv.estoque, 0) - prod.quantidade

        MERGE (v)-[vp:VENDEU_PRODUTO]->(prv)
            ON CREATE SET vp.quantidade = prod.quantidade, vp.preco = prod.preco
            ON MATCH SET vp.quantidade = coalesce(vp.quantidade, 0) + prod.quantidade, vp.preco = prod.preco

            // Adiciona relacionamento Venda-VENDEU_PARA->Pessoa
            WITH v, produtos, r
            MATCH (cli:Pessoa {idx: $cliente_idx})
            MERGE (v)-[:VENDEU_PARA]->(cli)

            RETURN v.idx AS venda_idx, size(produtos) AS total_produtos
    """

    params = {      
        "usuario_idx": usuario_idx,
        "negocio_idx": negocio_idx,
        "revenda_idx": revenda_idx,
        "venda_idx": venda_idx,
        "produtos": produtos_payload,
            "cliente_idx": cliente_idx,
    }

    print ("@@@@@ Query", query)
    print("@@@@@ Params", params)
    
    
    


    try:
        resultado = await neo4j.execute_write_query(query=query, params=params)
        
        #Erro , retorna mensagem de erro.
        if not resultado:
            mensagem = "Ocorreu um erro. A venda não foi finalizada."
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
            return {"status": "error", 
                    "message": json.dumps({"tipo":"venda","erro": mensagem}, ensure_ascii=False),
                    "function": "produto_venda"}
        
        
        print("resultado", resultado)
       
        print("@@@ produtos", produtos)
       
        
        # Monta o retorno solicitado
        itens_venda = []
        total_geral = 0.0
        for p in produtos:
            print("@p", p)
            item_total = float(p.preco) * float(p.quantidade)
            total_geral += item_total
            produto_label = f"{p.codigo} - {p.nome}" if getattr(p, "codigo", None) else p.nome
            itens_venda.append({
                "Produto": produto_label,
                "Preço": float(p.preco),
                "Qtde": int(p.quantidade),
                "Total": round(item_total, 2),
            })
            

        mensagem_inicial = "Estes são os dados da venda registrada:"
        mensagem_final = ""





        # Enviar mensagens via WhatsApp
        # Data/hora da venda para exibir na mensagem (lado cliente)
        data_venda_str = datetime.now().strftime("%d/%m/%Y %H:%M")

        print('data_venda_str', data_venda_str)


        await enviar_mensagens_zap(
            whatsapp=usuario_whatsapp,
            mensagem_inicial=mensagem_inicial,
            mensagem_final=mensagem_final,
            dados=itens_venda,
            total_geral=round(total_geral, 2),
            data_hora=data_venda_str,
            instancia_id=instancia_id,
            conversa_idx=conversa_idx,
            usuario_idx=usuario_idx,
            usuario_nome=usuario_nome,
            cliente_nome=cliente.nome,
            agente_idx=agente_idx,
            em_resposta_idx=em_resposta_idx,
            canal_idx=canal_idx,
        )

        # Monta payload JSON para o usuário
        payload_usuario = {
            "tipo": "venda",
            "itens": itens_venda,
            "total_geral": round(total_geral, 2),
            "data_hora": data_venda_str,
        }

        resposta = {
            "status": "success",
            "message": "Venda finalizada com sucesso.", 
            "dados": json.dumps(payload_usuario, ensure_ascii=False),
            "function": "produto_venda"
        }
        print("@@@@@ resposta:", resposta)

        return resposta
    except Exception as e:
        return {"status": "error", 
                "message": json.dumps({"tipo":"venda","erro": f"Ocorreu uma exceção ao tentar registrar a venda de produtos: {str(e)}"}, ensure_ascii=False),
                "function": "venda_registrar"}


async def enviar_mensagens_zap(whatsapp, mensagem_inicial, mensagem_final, dados, total_geral, data_hora, instancia_id, conversa_idx=None, usuario_idx=None, usuario_nome=None,cliente_nome=None, agente_idx=None, em_resposta_idx=None, canal_idx=None):
            
            
            print (f"===== enviar_mensagens()=====")
            print("whatsapp", whatsapp)
            print("mensagem_inicial:" , mensagem_inicial)
            print("mensagem_final:" , mensagem_final)
            print("dados" , dados)
            print("total_geral", total_geral)
            print('data_hora', data_hora)
            print('instancia_id', instancia_id)   
            print('conversa_idx', conversa_idx)
            print("usuario_idx", usuario_idx)
            print("usuario_nome", usuario_nome)
            print("cliente_nome", cliente_nome)
            print("agent_idx", agente_idx)
            print("em_resposta_idx", em_resposta_idx)
            print("canal_idx", canal_idx)
            

            if mensagem_inicial:
                result = await evolutionApi.send_evolution_message(whatsapp, mensagem_inicial, instancia_id)
                          
            
            # Helper para formatar moeda no padrão brasileiro
            def fmt_brl(valor: float) -> str:
                try:
                    return ("R$ " + f"{float(valor):,.2f}").replace(",", "X").replace(".", ",").replace("X", ".")
                except Exception:
                    return f"R$ {valor}"

            # Monta a mensagem no formato solicitado, compatível com WhatsApp Markdown
            linhas = []
            linhas.append(f"*VENDA EFETUADA EM {data_hora}*")
            if usuario_nome:
                linhas.append(f"*VENDEDOR:* {usuario_nome}")
            if usuario_nome:
                linhas.append(f"*COMPRADOR:* {cliente_nome}")
            linhas.append("")
            linhas.append("PRODUTOS")

            for item in dados:
                produto = item.get("Produto", "Produto")
                preco = fmt_brl(item.get("Preço", 0))
                qtde = item.get("Qtde", 0)
                total_item = fmt_brl(item.get("Total", 0))
                linhas.append(f"*{produto}*")
                linhas.append(f"Preço: {preco}    Qtde: {qtde}    Total: {total_item}")

            linhas.append("=========================")
            linhas.append(f"*TOTAL: {fmt_brl(total_geral)}*")

            mensagem_whatsapp = "\n".join(linhas)
            
            
            
            
            result = await evolutionApi.send_evolution_message(
                whatsapp,
                mensagem_whatsapp,
                instancia_id
            )
                                    
            await asyncio.sleep(2)

                #if mensagem_final:
                    #message = {}
                    #message["status"] = "success"
                    #message["message"] = mensagem_final
                    #result = await evolutionApi.send_evolution_message(whatsapp,mensagem_final,#instancia_id)
                    #print("📩 mensagem final enviada", result)


            #print(" 📩📩📩📩📩 menagens enviadas")        

            return 




if __name__ == "__main__":

        async def teste_venda_registrar():  
            # Exemplo de uso
            produtos_exemplo = [                                               
            ProdutoItem(idx="10221715", codigo="10221715", nome="Kit Dia + Noite TimeWise®", preco=213.80, quantidade=2),
            ProdutoItem(idx="10221710", codigo="10221710", nome="nome: Kit Básico TimeWise®", preco=182.80, quantidade=2)
            ]

            cliente_exemplo = ClienteData(
                idx="3733062572",
                nome="Carlos Silva  ",
                email="<EMAIL>",
                telefone="55999999999",
                cpf_cnpj="123.456.789-00",
                cep="12345-678",
                logradouro="Rua Exemplo",
                bairro="Bairro Exemplo",
                cidade="Cidade Exemplo",
                uf="EX",
                numero="123",
                complemento="Apto 101"
            )

            resultado = await venda_registrar (
                usuario_idx="1122334455",                                             
                negocio_idx="5544332211",
                cliente = cliente_exemplo,
                produtos=produtos_exemplo,
                usuario_nome = "Carlos Silva",
                negocio_nome = "Mary Kay",
                usuario_whatsapp = "553184198720",
                conversa_idx = "42134123",
                agente_idx = "3245124231",
                canal_idx = "245124231"   ,
                em_resposta_idx = "124242323"
                        
            )
            print("RESUTADO FINAL\n ",resultado)

        asyncio.run(teste_venda_registrar())
        
        #execução