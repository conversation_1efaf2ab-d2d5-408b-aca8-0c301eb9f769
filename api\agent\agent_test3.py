"""
api/agent/agent_test3.py
Dependências:
  pip install openai-agents==0.28.3 pydantic python-dotenv neo4j
Variáveis de ambiente:
  OPENAI_API_KEY=
  NEO4J_URI=bolt://localhost:7687
  NEO4J_USER=neo4j
  NEO4J_PASSWORD=senha
"""

import asyncio
import os
import json
from typing import List

from pydantic import BaseModel
from dotenv import load_dotenv
from agents import Agent, Runner, ModelSettings, function_tool

load_dotenv()
from .agent_neo4j import AgentNeo4j
neo4j = AgentNeo4j()

# ---------- schemas compatíveis com strict ----------
class QueryIn(BaseModel):
    model_config = {"extra": "forbid"}
    query: str
    parameters: str | None = None   # JSON string (evita dict)


class QueryOut(BaseModel):
    model_config = {"extra": "forbid"}
    rows: str                       # JSON string (evita dict/list)


# ---------- tool ----------
@function_tool
async def run_cypher_query(params: QueryIn) -> QueryOut:
    """
    Executa uma query Cypher no Neo4j e retorna os resultados formatados.
    
    Args:
        params: Objeto QueryIn contendo a query e parâmetros
        
    Returns:
        QueryOut: Objeto contendo os resultados da query em formato JSON
    """
    print("🔍 [run_cypher_query] Iniciando execução da query")
    print(f"📝 [run_cypher_query] Query: {params.query}")
    print(f"📌 [run_cypher_query] Parâmetros: {params.parameters}")
    
    try:
        # Converte parâmetros para dicionário se fornecidos
        parameters = json.loads(params.parameters) if params.parameters else {}
        
        # Executa a query no Neo4j
        resultado = await neo4j.execute_read_query(params.query, parameters)
        #return json.dumps(resultado)
        # Processa o resultado de forma segura
        if not isinstance(resultado, list):
            resultado = [resultado] if resultado is not None else []
            
        print(f"✅ [run_cypher_query] Query executada. {len(resultado)} itens retornados")

        # Verifica se é um resultado de contagem/agregação
        is_count_query = any('count(' in params.query.lower() or 'sum(' in params.query.lower() or 
                            'avg(' in params.query.lower() or 'max(' in params.query.lower() or
                            'min(' in params.query.lower() for x in resultado if isinstance(x, dict))
        
        itens = []
        for item in resultado:
            if not isinstance(item, dict):
                itens.append(item)
                continue
                
            # Se for resultado de contagem/agregação
            if is_count_query:
                # Pega o primeiro valor do dicionário (que é o resultado da agregação)
                if item:
                    value = next(iter(item.values()))
                    itens.append(value)
            # Se o resultado estiver em uma propriedade 'p' (padrão do Neo4j)
            elif 'p' in item and isinstance(item['p'], dict):
                itens.append({k.upper(): v for k, v in item['p'].items()})
            # Se o item já for um dicionário com os dados
            elif any(key in item for key in ['codigo', 'nome', 'preco', 'id', 'idx']):
                itens.append({k.upper(): v for k, v in item.items()})
            # Se não reconhecer o formato, adiciona o item como está
            else:
                itens.append(item)
        
        # Formata a resposta final
        resposta = {
            "success": True,
            "data": itens,
            "total": len(itens),
            "message": f"Encontrados {len(itens)} itens"
        }
        
        # Converte para JSON string
        resposta_str = json.dumps(resposta, ensure_ascii=False, default=str)
        
        # Log do resultado formatado
        print(f"✅ [run_cypher_query] Resposta formatada: {resposta_str[:200]}...")
        
        # Retorna como QueryOut com a resposta serializada em JSON
        return QueryOut(rows=resposta_str)
        
    except json.JSONDecodeError as je:
        error_msg = f"❌ Erro ao decodificar parâmetros JSON: {str(je)}"
        print(error_msg)
        resposta_erro = {
            "success": False,
            "data": [],
            "total": 0,
            "message": f"Erro nos parâmetros da consulta: {str(je)}"
        }
        return QueryOut(rows=json.dumps(resposta_erro, ensure_ascii=False))
        
    except Exception as e:
        import traceback
        error_msg = f"❌ Erro ao executar consulta: {str(e)}"
        print(error_msg)
        print(f"🔍 Traceback: {traceback.format_exc()}")
        
        # Retorna um objeto QueryOut com erro formatado
        resposta_erro = {
            "success": False,
            "data": [],
            "total": 0,
            "message": f"Erro ao executar consulta: {str(e)}"
        }
        return QueryOut(rows=json.dumps(resposta_erro, ensure_ascii=False))


# ---------- agente ----------
inventory_agent = Agent(
    name="InventoryManager",
    instructions=("""
        Você é um agente que auxilia na administração de produtos e inventário. 
        
        REGRAS IMPORTANTES:
        1. Sempre retorne os dados estruturados no campo 'data' do JSON de resposta
        2. Use o campo 'message' apenas para comentários, explicações ou resumos sobre os dados
        3. Nunca formate os dados como texto no campo 'message'
        4. Mantenha a estrutura original dos dados retornados pelo banco de dados
        
        Exemplo de resposta CORRETA:
        {
          "success": true,
          "message": "Aqui estão os produtos encontrados",
          "data": [
            {"codigo": "001", "nome": "Produto 1", "preco": 100.00},
            {"codigo": "002", "nome": "Produto 2", "preco": 200.00}
          ]
        }
        
        Exemplo de uso incorreto a EVITAR:
        {
          "success": true,
          "message": "1. Produto 1 (R$ 100,00)\n2. Produto 2 (R$ 200,00)",
          "data": []
        }
        
        Para consultas, use a função run_cypher_query com a query Cypher apropriada.
        Exemplo para buscar um produto por SKU:
        "query: MATCH (p:Produto {codigo: $sku}) RETURN p "
        'com parameters="{\"sku\": \"002\"}"'
        
        Esquema do banco de dados:
        
     {
      "generated_at": "2025-08-10T00:00:00Z",
      "database": "neo4j",
      "node_types": [       
             {
          "label": "Produto",
          "properties": [
            {
              "name": "codigo",
              "type": "STRING"
            },
            {
              "name": "descricao",
              "type": "STRING"
            },
            {
              "name": "estoque",
              "type": "INTEGER"
            },
            {
              "name": "excluido",
              "type": "INTEGER"
            },
            {
              "name": "idx",
              "type": "STRING"
            },
            {
              "name": "negocioIdx",
              "type": "STRING"
            },
            {
              "name": "negocio_idx",
              "type": "STRING"
            },
            {
              "name": "nome",
              "type": "STRING"
            },
            {
              "name": "preco",
              "type": "FLOAT|STRING"
            }
            ]
           },
          }

        
        
        
        
    """),
    tools=[run_cypher_query],
    output_type=QueryOut,
    model_settings=ModelSettings(temperature=0.0),
)


# ---------- execução ----------
async def main() -> None:
    try:
        result = await Runner.run(
            inventory_agent,
            input="Quantos produtos diferentes tenho?",
            #input="me mostre o produto de sku 002",
        )
        print("\n=== RESULTADO FINAL ===")
        try:
            # Se for uma resposta direta em string, converte para JSON
            if hasattr(result, 'final_output') and hasattr(result.final_output, 'rows'):
                resposta = result.final_output.rows
                
                # Se for uma string, tenta converter para JSON
                if isinstance(resposta, str):
                    try:
                        # Tenta converter a string para JSON
                        resultado = json.loads(resposta)
                        
                        # Se for uma mensagem de texto, move para o campo message
                        if isinstance(resultado, str) or (isinstance(resultado, dict) and 'data' not in resultado):
                            resultado = {
                                "success": True,
                                "message": resultado if isinstance(resultado, str) else str(resultado),
                                "data": []
                            }
                    except json.JSONDecodeError:
                        # Se não for JSON, cria um JSON com a mensagem
                        resultado = {
                            "success": True,
                            "message": resposta,
                            "data": []
                        }
                else:
                    # Se já for um dicionário, garante que tem a estrutura correta
                    if isinstance(resposta, dict):
                        resultado = {
                            "success": True,
                            "message": resposta.get('message', ''),
                            "data": resposta.get('data', resposta)
                        }
                    else:
                        resultado = {
                            "success": True,
                            "message": "",
                            "data": resposta
                        }
                
                # Formata a saída para melhor legibilidade
                print(json.dumps(resultado, indent=2, ensure_ascii=False, default=str))
            else:
                # Se não tiver a estrutura esperada, retorna o resultado completo
                print(json.dumps({
                    "success": True,
                    "message": "Resposta recebida",
                    "data": str(result)
                }, indent=2, ensure_ascii=False, default=str))
        except Exception as e:
            print(json.dumps({
                "success": False,
                "error": str(e),
                "type": type(e).__name__
            }, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Erro na execução do agente: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        raise

   
if __name__ == "__main__":
    if not os.getenv("OPENAI_API_KEY"):
        raise RuntimeError("OPENAI_API_KEY não definida.")
    asyncio.run(main())