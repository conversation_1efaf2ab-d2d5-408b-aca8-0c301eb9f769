from agents.tool import function_tool
from api.functions.util import generate_unique_id
from ...agent_logger import AgentLogger
from ...agent_neo4j import AgentNeo4j
import os
import json

schema_path = os.path.join(os.path.dirname(__file__), '..', 'schemas', 'cor_adicionar.json')
with open(schema_path, 'r', encoding='utf-8') as f:
    cor_adicionar_json = json.load(f)
    #print ("cor_adicionar_json" ,cor_adicionar_json)

@function_tool
async def cor_adicionar(
    nome: str,
    codigo: str,
    hexadecimal: str,
    negocio_idx: str
):
    """
    Adiciona uma nova cor no banco de dados.

    NOME:
    - Nome da cor.
    - Exemplo: "Vermelho"
    - Obrigatório

    CODIGO:
    - Código da cor.
    - Exemplo: "red"
    - Obrigatório

    HEXADECIMAL:
    - Código hexadecimal da cor.
    - Exemplo: "#FF0000"
    - Obrigatório
    
    ESQUEMA DE DADOS
    {cor_adicionar_json}
    
    
    """
    logger = AgentLogger()
    neo4j = AgentNeo4j()
    logger.info("===== cor_adicionar ======")
    logger.info(f"nome: {nome}")
    logger.info(f"codigo: {codigo}")

    try:
        # Gera um ID único para a cor (caso queira usar idx)
        cor_idx = generate_unique_id()

        query = """
        // Cria o nó da Cor
        CREATE (c:Cor {
            idx: $cor_idx,
            nome: $nome,
            codigo: $codigo,
            hexadecimal: $hexadecimal
        })
        WITH c
        // Busca o Negocio já existente
        MATCH (n:Negocio {idx: $negocio_idx})
        // Cria o relacionamento DISPONIBILIZA_COR
        CREATE (n)-[:DISPONIBILIZA_COR]->(c)
        RETURN c
        """
        parametros = {
            'cor_idx': cor_idx,
            'nome': nome,
            'codigo': codigo,
            'hexadecimal': hexadecimal,
            'negocio_idx': negocio_idx
        }

        result = await neo4j.execute_write_query(query, parametros)
        
        if result is None:
            logger.error("❌ ERRO: Falha na inserção da cor - mysql.add() retornou None")
            return {"success": False, "message": "Erro ao inserir cor no banco de dados. Verifique os logs para mais detalhes."}
        
        logger.info("===== cor_adicionar SUCESSO =====")
        return {"success": True, "message": f"Cor adicionada com sucesso! ID: {result}"}
    except Exception as e:
        logger.error(f"===== ERRO EM cor_adicionar() =====")
        logger.error(f"ERRO TIPO: {type(e).__name__}")
        logger.error(f"ERRO MENSAGEM: {str(e)}")
        logger.error(f"NOME: {nome}")
        logger.error(f"CODIGO: {codigo}")
        logger.error(f"HEXADECIMAL: {hexadecimal}")
        logger.error(f"negocio_idx: {negocio_idx}")
        logger.error("===== FIM ERRO cor_adicionar() =====")
        return {"success": False, "error": str(e)}
    
    
    
    
if __name__ == "__main__":
    import asyncio
    import uuid

    async def testa_adicionar_cor():
        # Dados de teste
        cor = {
            "nome": "Vermelho Teste " + str(uuid.uuid4())[:8],
            "codigo": "red" + str(uuid.uuid4())[:4],
            "hexadecimal": "#FF0000",
            "negocio_idx": "5544332211"  # ID de negócio de teste
        }

        print("=== Teste de Inclusão de Cor ===")
        print(f"Dados da cor de teste:\n{cor}\n")

        try:
            resultado = await cor_adicionar(
                nome=cor["nome"],
                codigo=cor["codigo"],
                hexadecimal=cor["hexadecimal"],
                negocio_idx=cor["negocio_idx"]
            )

            print("=== Resultado do Teste ===")
            print(f"Sucesso: {resultado.get('success', False)}")
            print(f"Mensagem: {resultado.get('message', 'Nenhuma mensagem retornada')}")

            if not resultado.get('success', False):
                print(f"Erro: {resultado.get('error', 'Erro desconhecido')}")

            return resultado

        except Exception as e:
            print(f"Erro durante o teste: {str(e)}")
            raise

    # Executar o teste
    asyncio.run(testa_adicionar_cor())
    
    #execução:
    #py -m api.agent.business.functions.cor_adicionar
    