import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
from api.agent.agent_mysql import Mysql
from api.agent.agent_secret import Secret
from app_sl.geral.util import generate_unique_id

async def testar_conexao_loja247():
    secret = Secret()
    
    if not all([secret.LOJA247_MYSQL_HOST, secret.LOJA247_MYSQL_USER, 
                secret.LOJA247_MYSQL_PASSWORD, secret.LOJA247_MYSQL_DATABASE]):
        print("Erro: As variáveis de ambiente para LOJA247 não estão completamente definidas.")
        return None
    
    loja247_db = Mysql(
        host=secret.LOJA247_MYSQL_HOST,
        user=secret.LOJA247_MYSQL_USER,
        password=secret.LOJA247_MYSQL_PASSWORD,
        database=secret.LOJA247_MYSQL_DATABASE
    )
    
    try:
        conn = await loja247_db.conecta()
        print("Conexão com o banco LOJA247 estabelecida com sucesso!")
        return loja247_db
    except Exception as e:
        print(f"Erro ao conectar com o banco LOJA247: {e}")
        return None

async def testar_conexao_gptalk():
    gptalk_db = Mysql()  # Usa as configurações padrão do GPTALK_01
    
    try:
        conn = await gptalk_db.conecta()
        print("Conexão com o banco GPTALK_01 estabelecida com sucesso!")
        return gptalk_db
    except Exception as e:
        print(f"Erro ao conectar com o banco GPTALK_01: {e}")
        return None

async def importar_categorias(loja247_db, gptalk_db):
    LOJA_ID = 39
    negocio_idx = "9876543210"

    # 0. Deletar registros existentes
    delete_query = f"DELETE FROM PRODUTO_CATEGORIA WHERE NEGOCIO_IDX = '{negocio_idx}'"
    await gptalk_db.query(delete_query)

    # 1. Carregar categorias da LOJA247
    query = f"SELECT * FROM CATEGORIA WHERE LOJA_ID = {LOJA_ID}"
    aCategorias = await loja247_db.query(query)

    # 2. Criar array aProduto_Categorias
    aProduto_Categorias = []

    # 3. Processar categorias
    for registro in aCategorias:
        categoria = {
            "IDX": generate_unique_id(),
            "NOME": registro['NOME'],
            "NEGOCIO_IDX": negocio_idx,
            "ID_ANTIGO": registro['ID'],
            "ID_PAI": registro['ID_PAI']
        }

        # 3.1. Inserir na tabela PRODUTO_CATEGORIA
        novo_id = await gptalk_db.add("PRODUTO_CATEGORIA", categoria)

        # 3.2. Adicionar ID do novo item
        categoria["ID"] = novo_id
        registro["ID_NOVO"] = categoria["IDX"]

        # 3.3. Adicionar ao array aProduto_Categorias
        aProduto_Categorias.append(categoria)

    # 4. Atualizar ID_PAI
    for registro in aCategorias:
        if int(registro['ID_PAI']) > 0:
            for categoria in aProduto_Categorias:
                if int(categoria['ID_ANTIGO']) == int(registro['ID_PAI']):
                    categoria['ID_PAI'] = registro['ID_NOVO']

    # 5. Criar e executar query de atualização
    update_query = "UPDATE PRODUTO_CATEGORIA SET ID_PAI = CASE "
    for categoria in aProduto_Categorias:
        update_query += f"WHEN IDX = '{categoria['IDX']}' THEN '{categoria['ID_PAI']}' "
    update_query += "ELSE ID_PAI END WHERE NEGOCIO_IDX = '{negocio_idx}'"

    await gptalk_db.query(update_query)

    print("Importação de categorias concluída com sucesso!")

async def main():
    loja247_db = await testar_conexao_loja247()
    gptalk_db = await testar_conexao_gptalk()

    if loja247_db and gptalk_db:
        print("\nIniciando importação de categorias...")
        await importar_categorias(loja247_db, gptalk_db)
    else:
        print("Não foi possível estabelecer conexão com um ou ambos os bancos de dados.")

if __name__ == "__main__":
    asyncio.run(main())
