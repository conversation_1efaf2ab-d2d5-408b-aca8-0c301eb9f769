"""
Funções MCP para integração com Neo4j
CORRIGIDO: Agora usa conexão direta com Neo4j ao invés de depender das funções MCP injetadas
"""
import logging
from neo4j import GraphDatabase
from ..agent.agent_secret import Secret

logger = logging.getLogger(__name__)

class Neo4jConnector:
    """
    Conector direto com Neo4j usando as credenciais do sistema
    """
    def __init__(self):
        self.secret = Secret()
        self.driver = None
        
    def connect(self):
        """Conecta ao banco Neo4j"""
        try:
            #logger.info("=== 🔗 CONECTANDO DIRETAMENTE AO NEO4J ===")
            #logger.info(f"🌐 URI: {self.secret.GPTALK_01_NEO4J_URI}")
            #logger.info(f"👤 User: {self.secret.GPTALK_01_NEO4J_USER}")
            #logger.info(f"🗄️ Database: {self.secret.GPTALK_01_NEO4J_DATABASE}")
            
            self.driver = GraphDatabase.driver(
                self.secret.GPTALK_01_NEO4J_URI,
                auth=(self.secret.GPTALK_01_NEO4J_USER, self.secret.GPTALK_01_NEO4J_PASSWORD),
                database=self.secret.GPTALK_01_NEO4J_DATABASE
            )
            
            # Testar a conexão
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                #logger.info(f"✅ Conexão testada com sucesso! Test value: {test_value}")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar ao Neo4j: {e}")
            return False
    
    def execute_read_query(self, query, params=None):
        """Executa query de leitura"""
        try:
            #logger.info(f"=== 🔍 EXECUTANDO QUERY DE LEITURA ===")
            #logger.info(f"📝 Query: {query}")
            #logger.info(f"📊 Parâmetros: {params}")
            
            if not self.driver:
                if not self.connect():
                    return [{"erro": "Falha na conexão com Neo4j"}]
            
            with self.driver.session() as session:
                result = session.run(query, params or {})
                records = []
                
                for record in result:
                    record_dict = dict(record)
                    records.append(record_dict)
                    #logger.info(f"📋 Registro encontrado: {record_dict}")
                
                #logger.info(f"✅ Query executada! Total de registros: {len(records)}")
                #logger.info(f"📈 Resultado completo: {records}")
                
                return records
                
        except Exception as e:
            logger.error(f"❌ Erro ao executar query: {e}")
            return [{"erro": str(e), "query": query, "params": params}]
    
    def close(self):
        """Fecha a conexão"""
        if self.driver:
            self.driver.close()

# Instância global do conector
_neo4j_connector = None

def get_neo4j_connector():
    """Retorna instância do conector Neo4j"""
    global _neo4j_connector
    if _neo4j_connector is None:
        _neo4j_connector = Neo4jConnector()
    return _neo4j_connector

def mcp_neo4j_get_neo4j_schema(random_string):
    """
    Obtém o schema do banco Neo4j usando conexão direta
    """
    try:
        logger.info("=== 🔍 mcp_neo4j_get_neo4j_schema() ===")
        logger.info(f"Parâmetro recebido: {random_string}")
        
        connector = get_neo4j_connector()
        
        # Query para obter schema completo
        schema_query = """
        CALL db.schema.visualization()
        YIELD nodes, relationships
        RETURN nodes, relationships
        """
        
        resultado = connector.execute_read_query(schema_query)
        logger.info(f"📊 Schema obtido com sucesso!")
        
        return resultado
        
    except Exception as e:
        logger.error(f"❌ Erro em mcp_neo4j_get_neo4j_schema: {e}")
        return [{"erro": str(e)}]

def mcp_neo4j_read_neo4j_cypher(query, params):
    """
    Executa query Cypher de leitura usando conexão direta
    """
    try:
        #logger.info("=== 🔍 mcp_neo4j_read_neo4j_cypher() ===")
        #logger.info(f"📝 Query recebida: {query}")
        #logger.info(f"📊 Parâmetros recebidos: {params}")
        
        connector = get_neo4j_connector()
        resultado = connector.execute_read_query(query, params)
        
        #logger.info("✅ Query Cypher executada com sucesso!")
        return resultado
        
    except Exception as e:
        logger.error(f"❌ Erro em mcp_neo4j_read_neo4j_cypher: {e}")
        return [{"erro": str(e), "query": query, "params": params}]

def mcp_neo4j_write_neo4j_cypher(query, params):
    """
    Executa query Cypher de escrita usando conexão direta
    """
    try:
        #logger.info("=== ✏️ mcp_neo4j_write_neo4j_cypher() ===")
        #logger.info(f"📝 Query recebida: {query}")
        #logger.info(f"📊 Parâmetros recebidos: {params}")
#
        connector = get_neo4j_connector()

        if not connector.driver:
            if not connector.connect():
                return [{"erro": "Falha na conexão com Neo4j"}]

        with connector.driver.session() as session:
            result = session.run(query, params or {})
            summary = result.consume()

            #logger.info("✅ Query de escrita executada!")
            #logger.info(f"📊 Estatísticas: {summary.counters}")

            # conversão manual, compatível com todas as versões
            counters = summary.counters
            counters_dict = {
                "nodes_created": counters.nodes_created,
                "nodes_deleted": counters.nodes_deleted,
                "relationships_created": counters.relationships_created,
                "relationships_deleted": counters.relationships_deleted,
                "properties_set": counters.properties_set,
                "labels_added": counters.labels_added,
                "labels_removed": counters.labels_removed,
                "indexes_added": getattr(counters, "indexes_added", 0),
                "indexes_removed": getattr(counters, "indexes_removed", 0),
                "constraints_added": getattr(counters, "constraints_added", 0),
                "constraints_removed": getattr(counters, "constraints_removed", 0),
            }

            return [{
                "success": True,
                "counters": counters_dict,
                "query": query,
                "params": params
            }]

    except Exception as e:
        logger.error(f"❌ Erro em mcp_neo4j_write_neo4j_cypher: {e}")
        return [{"erro": str(e), "query": query, "params": params}]

