import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== TESTE BAIXA AUTOMÁTICA DE ESTOQUE ===")
    
    # Informações para o teste
    negocio_idx = "4344140157"  # ID do negócio da consultora
    produto_codigo = "10173108"  # Código do produto real no estoque
    quantidade_baixar = 1  # Quantidade a ser baixada (conservativa)
    
    try:
        # 1. Verificar estoque ANTES da baixa
        print(f"1️⃣ VERIFICANDO ESTOQUE ANTES:")
        query_estoque_antes = f"""
        SELECT CODIGO, NOME, ESTOQUE 
        FROM PRODUTO 
        WHERE CODIGO = '{produto_codigo}' 
        AND NEGOCIO_IDX = '{negocio_idx}' 
        AND EXCLUIDO = 0
        """
        
        estoque_antes = await mysql.query(query_estoque_antes)
        if estoque_antes:
            produto = estoque_antes[0]
            print(f"   📦 Produto: {produto.get('NOME')}")
            print(f"   📦 Código: {produto.get('CODIGO')}")
            print(f"   📦 Estoque atual: {produto.get('ESTOQUE')} unidades")
            
            # 2. Executar baixa de estoque (simular o que a venda faz)
            print(f"\n2️⃣ EXECUTANDO BAIXA DE {quantidade_baixar} UNIDADES:")
            query_baixa = f"""
            UPDATE PRODUTO 
            SET ESTOQUE = ESTOQUE - {quantidade_baixar}
            WHERE CODIGO = '{produto_codigo}' 
            AND NEGOCIO_IDX = '{negocio_idx}' 
            AND EXCLUIDO = 0
            """
            
            print(f"   📝 Query: {query_baixa}")
            await mysql.query(query_baixa)
            print(f"   ✅ Baixa executada com sucesso!")
            
            # 3. Verificar estoque DEPOIS da baixa
            print(f"\n3️⃣ VERIFICANDO ESTOQUE DEPOIS:")
            estoque_depois = await mysql.query(query_estoque_antes)
            if estoque_depois:
                produto_depois = estoque_depois[0]
                novo_estoque = produto_depois.get('ESTOQUE')
                print(f"   📈 Estoque atualizado: {novo_estoque} unidades")
                
                # Calcular diferença
                diferenca = produto.get('ESTOQUE') - novo_estoque
                print(f"   📊 Diferença: -{diferenca} unidades")
                
                # Alertas
                if novo_estoque <= 0:
                    print(f"   ⚠️  ALERTA: Estoque ZERO ou NEGATIVO!")
                elif novo_estoque <= 2:
                    print(f"   ⚠️  ALERTA: Estoque BAIXO!")
                else:
                    print(f"   ✅ Estoque em nível adequado")
            
        else:
            print(f"   ❌ Produto {produto_codigo} não encontrado no estoque")
            
    except Exception as e:
        print(f"❌ ERRO no teste: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 