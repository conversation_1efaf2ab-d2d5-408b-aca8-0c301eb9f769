import boto3
import os
import json
from dotenv import load_dotenv
import asyncio
import logging
from botocore.exceptions import ClientError

load_dotenv()


async def claude_completion(system,user,assistant,config=None):
    
    
    if config is None:
        config = {
            "model":"anthropic.claude-v2:1",
            "max_tokens_to_sample": 500,
            "temperature": 0.0,
            "top_p": 0.9
        }
    
    
    bedrock = boto3.client(service_name='bedrock-runtime',
        region_name='us-east-1',
        aws_access_key_id=os.environ.get("MY_AWS_ACCESS_KEY"),
        aws_secret_access_key=os.environ.get("MY_AWS_SECRET_KEY"))
    # Tweak your preferred model parameters, prompt and assistant information
    body = json.dumps({
        "prompt": f"\n\n{system}\n\nHuman:{user}\n\nAssistant:{assistant}",
        "max_tokens_to_sample": 500,
        "temperature": 0.0,
        "top_p": 0.9,})

    # Define the type of model that will be used
     #modelId = 'anthropic.claude-v2:1'
    modelId = 'anthropic.claude-3-sonnet-20240229-v1:0'
    accept = 'application/json'
    contentType = 'application/json'

    # Call the Bedrock API
    response = bedrock.invoke_model(body=body, modelId=modelId, accept=accept, contentType=contentType)
    response_body = json.loads(response.get('body').read())
    #print(response_body)
    return response_body.get('completion')  
    
  #=========  
    
async def claude_messages(system,messages,config=None):
    if config is None:
      config = {
        "model":"anthropic.claude-3-sonnet-20240229-v1:0",
        "max_tokens": 1000,
        "temperature": 0.0,
        "top_p": 0.9,
        "anthropic_version": "bedrock-2023-05-31"
      }

      body=json.dumps(
        {
            "anthropic_version": config["anthropic_version"],
            "max_tokens": config["max_tokens"],
            "system": system,
            "messages": messages
        }  
      )  

      model_id = config["model"]
      max_tokens = config["max_tokens"]

      try:

        bedrock_runtime = boto3.client(service_name='bedrock-runtime',
                                        region_name='us-east-1',
                                        aws_access_key_id=os.environ.get("MY_AWS_ACCESS_KEY"),
                                        aws_secret_access_key=os.environ.get("MY_AWS_SECRET_KEY"))
                                       
        response = bedrock_runtime.invoke_model(body=body, modelId=model_id)
        response_body = json.loads(response.get('body').read())
        response_text = response_body
        return response_body["content"][0]["text"]
      except ClientError as err:
        message=err.response["Error"]["Message"]
        #logger.error("A client error occurred: %s", message)
        print("A client error occured: " +
            format(message))
        return "A client error occured: " + format(message)

    
    
    
    
    
    
async def main():
    
    messages = [
    {"role": "user", "content": "Ola, qual o seu nome"}

    ]
    
    system = ""
    result = await claude_messages(system,messages)
    print("result", result)
    
asyncio.run(main())