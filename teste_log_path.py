#!/usr/bin/env python3
"""
Script para mostrar onde os arquivos de log serão salvos
"""

import os
import sys
from datetime import datetime

# Simula o mesmo caminho usado no agent_logger.py
current_dir = os.path.dirname(os.path.abspath(__file__))
print(f"📁 Diretório atual do script: {current_dir}")

# Caminho para a pasta logs (mesmo cálculo do agent_logger.py)
logs_dir = os.path.join(current_dir, "logs")
print(f"📁 Pasta de logs: {logs_dir}")
print(f"📁 Pasta de logs (absoluta): {os.path.abspath(logs_dir)}")

# Nome do arquivo para hoje
today = datetime.now().strftime('%Y_%m_%d')
log_file_today = os.path.join(logs_dir, f"{today}.log")
print(f"📄 Arquivo de log para hoje: {log_file_today}")

# Arquivo de log antigo (compatibilidade)
old_log_file = os.path.join(current_dir, "server.log")
print(f"📄 Arquivo de log antigo: {old_log_file}")

print("\n" + "="*50)
print("RESUMO:")
print("="*50)
print(f"✅ Pasta de logs: {os.path.abspath(logs_dir)}")
print(f"✅ Arquivo de hoje: {today}.log")
print(f"✅ Caminho completo: {os.path.abspath(log_file_today)}")

# Verifica se a pasta existe
if os.path.exists(logs_dir):
    print(f"✅ Pasta existe: SIM")
    
    # Lista arquivos existentes
    log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
    if log_files:
        print(f"📋 Arquivos existentes ({len(log_files)}):")
        for log_file in sorted(log_files):
            file_path = os.path.join(logs_dir, log_file)
            size = os.path.getsize(file_path)
            print(f"   - {log_file} ({size} bytes)")
    else:
        print("📋 Nenhum arquivo de log encontrado")
else:
    print(f"❌ Pasta existe: NÃO")
    print(f"💡 Será criada automaticamente quando o primeiro log for gravado") 