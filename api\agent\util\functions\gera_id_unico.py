import time
import secrets

def gera_id_unico():
    # Pega os últimos 5 dígitos do timestamp em milissegundos
    timestamp = int(time.time() * 1000) % 100000
    
    # Gera um número aleatório de até 5 dígitos
    random_part = secrets.randbelow(100000)
    
    # Combina timestamp e parte aleatória para formar um ID de 10 dígitos
    unique_id = f"{timestamp:05}{random_part:05}"
    return unique_id