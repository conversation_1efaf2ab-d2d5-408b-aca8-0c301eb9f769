from pytubefix import YouTube
import os

# URL do vídeo
url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"

# Caminho absoluto para salvar o arquivo
output_dir = r"C:\Users\<USER>\Documents\GitHub\gptalk\temp\audio"

# Criar o diretório se não existir
os.makedirs(output_dir, exist_ok=True)

# Iniciar o download com client WEB para evitar detecção de bot
yt = YouTube(url, client='WEB')
print(f"Título do vídeo: {yt.title}")

# Selecionar apenas o áudio
ys = yt.streams.get_audio_only()
ys.download(output_path=output_dir)

print("Download concluído!")

# Listar arquivos no diretório
print(f"\nArquivos na pasta {output_dir}:")
for file in os.listdir(output_dir):
    file_path = os.path.join(output_dir, file)
    size = os.path.getsize(file_path)
    print(f"  - {file} ({size} bytes)")

