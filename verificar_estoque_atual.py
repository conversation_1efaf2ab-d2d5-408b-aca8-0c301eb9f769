import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== VERIFICAÇÃO ESTOQUE ATUAL ===")
    
    # Verificar estoque do produto 10173108
    result = await mysql.query("SELECT CODIGO, NOME, ESTOQUE FROM PRODUTO WHERE CODIGO = '10173108' AND NEGOCIO_IDX = '4344140157'")
    
    if result:
        produto = result[0]
        print(f"✅ Produto encontrado:")
        print(f"   📦 Código: {produto.get('CODIGO')}")
        print(f"   📝 Nome: {produto.get('NOME')}")
        print(f"   📊 Estoque atual: {produto.get('ESTOQUE')} unidades")
    else:
        print("❌ Produto não encontrado!")

if __name__ == "__main__":
    asyncio.run(main()) 