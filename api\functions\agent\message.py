import re

def limpar_tags_agente(texto: str) -> str:
    """Remove tags incompletas ou malformadas que podem aparecer nas respostas do agente"""
    if not texto:
        return texto
    
    texto = re.sub(r'^/toolcall>', '', texto.strip())
    texto = re.sub(r'^/tool_call>', '', texto.strip())
    texto = re.sub(r'^<toolcall[^>]*>', '', texto.strip())
    texto = re.sub(r'^</toolcall>', '', texto.strip())
    texto = re.sub(r'^<tool_call[^>]*>', '', texto.strip())
    texto = re.sub(r'^<tool[^>]*>', '', texto.strip())
    texto = re.sub(r'^/function>', '', texto.strip())
    texto = re.sub(r'^<function[^>]*>', '', texto.strip())
    texto = texto.strip()
    
    return texto

def extrair_texto_puro(conteudo: str) -> str:
    """Extrai o texto puro de um conteúdo, removendo formatação HTML e Markdown"""
    texto_sem_tags = limpar_tags_agente(conteudo)
    texto_sem_html = re.sub(r'<[^>]+>', '', texto_sem_tags)
    texto_sem_markdown = re.sub(r'(\*\*|__|~~|`|\*|_)+', '', texto_sem_html)
    linhas_limpas = [linha.strip() for linha in texto_sem_markdown.splitlines() if linha.strip()]
    texto_puro_final = "\n".join(linhas_limpas)
    return texto_puro_final

def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{conversa_idx}"

def limpar_html(conteudo: str) -> str:
    """
    Remove apenas tags HTML preservando formatação Markdown para WhatsApp.
    WhatsApp suporta formatação básica em Markdown, então mantemos essa formatação.
    """
    if not conteudo:
        return conteudo

    # Primeiro, limpar tags malformadas do agente
    texto_sem_tags = limpar_tags_agente(conteudo)

    # Remover apenas tags HTML, preservando Markdown
    texto_sem_html = re.sub(r'<[^>]+>', '', texto_sem_tags)

    # Processar linhas mantendo quebras de linha
    linhas_limpas = [linha.strip() for linha in texto_sem_html.splitlines() if linha.strip()]
    texto_final = "\n".join(linhas_limpas)

    return texto_final
