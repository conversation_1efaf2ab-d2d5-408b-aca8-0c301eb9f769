from datetime import datetime
import pytz
import re
import importlib
import inspect
from urllib3 import response
from .agent_logger import <PERSON><PERSON><PERSON><PERSON>
from .agent_neo4j import AgentNeo4j
from .agent_openai import OpenAi
from fastapi import APIRouter, Request
from .agent_llm import LLM
from fastapi.responses import StreamingResponse, JSONResponse
from typing import Optional, Any, Dict, AsyncGenerator
from .agent_openai import OpenAi
# ✅ ADICIONAR IMPORTAÇÕES NECESSÁRIAS
from ..cache import cache
from ..functions.util import generate_unique_id
from .agent_message import Message
from threading import Lock
# Adicione esta importação no topo do arquivo (próximo às outras importações)
from agents import ModelSettings


# Inicialização de componentes globais
logger = AgentLogger()
router = APIRouter()
neo4j = AgentNeo4j()
llm = LLM()
oai = OpenAi()

# ✅ ADICIONAR VARIÁVEIS GLOBAIS DO CACHE
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()

fuso_brasilia = pytz.timezone('America/Sao_Paulo')

# ✅ ADICIONAR FUNÇÕES DE CACHE DE MENSAGENS
def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{conversa_idx}"

def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None

def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history

def limpar_tags_agente(texto: str) -> str:
    """Remove tags incompletas do agente"""
    if not texto:
        return ""
    
    # Remove tags HTML incompletas no final
    texto = re.sub(r'<[^>]*$', '', texto)
    
    # Remove tags de abertura sem fechamento
    texto = re.sub(r'<([^/>]+)>(?![^<]*</\1>)', '', texto)
    
    return texto.strip()

def extrair_texto_puro(html_text: str) -> str:
    """Extrai texto puro removendo todas as tags HTML"""
    if not html_text:
        return ""
    
    # Remove todas as tags HTML
    texto_limpo = re.sub(r'<[^>]+>', '', html_text)
    
    # Remove espaços extras e quebras de linha desnecessárias
    texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
    
    return texto_limpo.strip()

def limpar_markdown_whatsapp(texto: str) -> str:
    """Remove formatação Markdown para WhatsApp, mantendo apenas URLs dos links"""
    if not texto:
        return ""
    
    import re
    
    # Remove formatação Markdown mantendo apenas URLs dos links
    # Links [texto](url) -> url
    texto = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'\2', texto)
    
    # Remove negrito **texto** ou __texto__
    #texto = re.sub(r'\*\*([^*]+)\*\*', r'\1', texto)
    #texto = re.sub(r'__([^_]+)__', r'\1', texto)
    
    # Remove itálico *texto* ou _texto_
    #texto = re.sub(r'\*([^*]+)\*', r'\1', texto)
    #texto = re.sub(r'_([^_]+)_', r'\1', texto)
    
    # Remove código `texto`
    #texto = re.sub(r'`([^`]+)`', r'\1', texto)
    
    # Remove cabeçalhos # ## ###
    #texto = re.sub(r'^#{1,6}\s*', '', texto, flags=re.MULTILINE)
    
    # Remove listas - ou *
    #texto = re.sub(r'^[\s]*[-*]\s*', '', texto, flags=re.MULTILINE)
    
    # Remove espaços extras
    #texto = re.sub(r'\s+', ' ', texto)
    
    return texto.strip()

class Agent:
    """
    Classe base Agent - Estrutura genérica para criação de agentes
    Baseada no padrão do AgentOficinatech
    """
    def __init__(
        self,
        modelo = None,
        agente_idx: Optional[str] = None,
        agente_nome: Optional[str] = None,
        agente_instrucoes: Optional[str] = None,
        agente_identidade: Optional[str] = None,
        agente_personalidade: Optional[str] = None,
        agente_estilo_conversa: Optional[str] = None,
        usuario_nome: Optional[str] = None,
        usuario_idx: Optional[str] = None,
        usuario_telefone: Optional[str] = None,
        imagem: Optional[str] = None,
        negocio_idx: Optional[str] = None,
        negocio_nome: Optional[str] = None,
        plataforma_url: Optional[str] = None,
        ferramentas: Optional[list] = None,
    ):
        """
        Inicializa o agente com parâmetros básicos
        
        Args:
            modelo: Modelo de IA a ser usado
            agente_idx: ID do agente
            agente_nome: Nome do agente
            agente_instrucoes: Instruções do agente
            agente_identidade: Identidade do agente
            agente_personalidade: Personalidade do agente   
            agente_estilo_conversa: Estilo de conversa do agente
            usuario_nome: Nome do usuário
            usuario_idx: ID do usuário
            usuario_telefone: Telefone do usuário
            imagem: Imagem do usuário   
            negocio_idx: ID do negócio
            negocio_nome: Nome do negócio
            plataforma_url: URL da plataforma
            ferramentas: Ferramentas do agente
        """
        #logger.info(f"===== Agent({agente_nome}) =====")
        
        # Data e hora atual no fuso de Brasília
        data_hora = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
        
        # Atributos básicos do agente
        self.modelo = modelo  # ← ADICIONAR ESTA LINHA
        self.agente_idx = agente_idx
        self.agente_nome = agente_nome
        self.agente_instrucoes = agente_instrucoes
        self.agente_personalidade = agente_personalidade
        self.agente_identidade = agente_identidade
        self.agente_estilo_conversa = agente_estilo_conversa
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.usuario_telefone = usuario_telefone
        self.imagem = imagem
        self.negocio_idx = negocio_idx
        self.plataforma_url = plataforma_url
        self.data_hora = data_hora
        self.ferramentas = ferramentas

        self.runner = {}
        # Componentes do agente
        self.router = APIRouter()
        self.oai = OpenAi()
        self.neo4j = AgentNeo4j()

        self.seta_instrucoes()

        
    def seta_instrucoes(self):
        negocio_idx = "negocio_idx = " + self.negocio_idx if self.negocio_idx else ""
        usuario_telefone = "O meu telefone é " + self.usuario_telefone if self.usuario_telefone else ""
        usuario_idx = "O meu usuario_idx é " + self.usuario_idx if self.usuario_idx else ""
        data_hora = "A data e hora atual é " + self.data_hora if self.data_hora else ""
        plataforma_url = "A url da plataforma é " + self.plataforma_url if self.plataforma_url else ""


        self.instrucoes = f"""
        Seu nome é {self.agente_nome}
        {self.agente_identidade}
        {self.agente_personalidade}
        {self.agente_instrucoes}
        No momento você está atendendo a mim. Meu nome é  {self.usuario_nome}.
        Outras informações úteis que  deverá utilizar sempre que uma das suas funções solicitar:
        {negocio_idx}
        {usuario_idx}
        {usuario_telefone}
        {plataforma_url}
        {data_hora}
        
        """

    async def get_agent_openai(self) -> Any:
        # Configurar ModelSettings para baixa criatividade
        model_settings = ModelSettings(
            temperature=0.1,          # Muito baixo para respostas determinísticas
            top_p=0.3,               # Limita as opções de palavras
            frequency_penalty=0.6,    # Evita repetições
            presence_penalty=0.3,     # Encoraja consistência
            max_tokens=1000          # Limita o tamanho da resposta
        )
        
        agent_openai = {
            "name": self.agente_nome,
            "instructions": self.instrucoes,
            "model": self.modelo,
            "model_settings": model_settings,  # Adicione esta linha
            "tools": self.ferramentas or [],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }

        agente_openai_objeto = await self.oai.agent_create(**agent_openai)
        return agente_openai_objeto

def _to_str_dict(data: Any) -> Dict[str, Any]:
    """Converte um mapping possivelmente com chaves/valores bytes para dict[str, Any]."""
    try:
        if isinstance(data, dict):
            return {str(k): (v.decode('utf-8', 'ignore') if isinstance(v, (bytes, bytearray)) else v) for k, v in data.items()}
        if hasattr(data, 'items'):
            obj = dict(data)
            return {str(k): (v.decode('utf-8', 'ignore') if isinstance(v, (bytes, bytearray)) else v) for k, v in obj.items()}
    except Exception:
        pass
    return {}




async def carrega_dados_agente(agente_idx: str) -> Optional[Dict[str, Any]]:
    """
    Carrega os dados de um agente do Neo4j usando o agente_idx.
    Retorna apenas os dados do agente em JSON em caso de sucesso.
    """
    query = """
        MATCH (a:Agente {idx: $idx})
        RETURN a
        """
    parameters = {"idx": agente_idx}
    try:
        # Usa execute_read_query que já trata a execução e retorno
        result = await neo4j.execute_read_query(query, parameters)
       #logger.info(f"Resultado da consulta do agente {agente_idx}: {result}")
        
        # Verifica se encontrou o agente
        if result and len(result) > 0:
            # Extrai apenas as propriedades do nó do agente
            agente_node = result[0].get('a')
            if agente_node:
                # Converte as propriedades do nó para um dicionário Python
                agente_data = _to_str_dict(agente_node)
                #logger.info(f"Dados do agente extraídos: {agente_data}")
                return agente_data
            else:
                logger.warning(f"Nó do agente não encontrado na resposta para idx: {agente_idx}")
                return None
        else:
            logger.warning(f"Nenhum agente encontrado com idx: {agente_idx}")
            return None
            
    except Exception as e:
        logger.error(f"Erro ao carregar dados do agente {agente_idx}: {e}")
        return None

@router.post("/send/text")
async def send_text(data: dict):
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL
    print("===== send_text() =====")
    print(f"data: {data}")
    
    # Extrair dados necessários
    mensagem = data.get("mensagem", "")
    modo_resposta_ia = data.get("modo_resposta_ia") or "texto"
    negocio_idx = data.get("negocio_idx")
    canal = data.get("canal") or "web_app"
    usuario_nome = data.get("usuario_nome", "")
    plataforma_url = data.get("plataforma_url", "")
    agente_idx = data.get("agente_idx")
    usuario_idx = data.get("usuario_idx")
    usuario_telefone = data.get("usuario_telefone") or ""
    imagem = data.get("imagem") or ""

    if not mensagem:
        return {"success": False, "message": "Mensagem é obrigatória"}

    if not isinstance(agente_idx, str) or not agente_idx:
        return {"success": False, "message": "agente_idx é obrigatório"}

    agente_data = await carrega_dados_agente(agente_idx)
    if not agente_data:
        return {"success": False, "message": "Agente não encontrado"}
    
    modelo = data.get("modelo") or "1234567890"
    print("modelo",modelo)

    modelo = llm.get_model_idx(modelo)
    codigo = agente_data.get("codigo")
    print("vou carregar as ferramentas")
    ferramentas = await carrega_ferramentas(codigo)
    print("ferramentas",ferramentas)

    # ✅ BUSCAR CONVERSA ATIVA NO CACHE
    agente_nome = agente_data.get("nome", "agente_generico")
    conversa_idx, historico_mensagens = find_active_conversation(usuario_idx or negocio_idx, agente_nome)
    
    if not conversa_idx:
        conversa_idx = generate_unique_id()
        historico_mensagens = []
    
    # ✅ ADICIONAR MENSAGEM DO USUÁRIO AO HISTÓRICO
    if imagem:
        mensagem_completa = f"imagem_link :{imagem};mensagem:{mensagem}"
    else:
        mensagem_completa = mensagem
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem_completa, True)

    # ✅ CONFIGURAR messageChat PARA SALVAR NO BANCO
    with messageChat_lock:  # 🔒 PROTEÇÃO CONTRA CONCORRÊNCIA
        messageChat.clear()  # ✅ LIMPAR DADOS ANTERIORES
        messageChat.update({
            "ENVIADO": mensagem,
            "LLM_IDX": data.get("modelo"),  # ✅ USAR O VALOR ORIGINAL
            "CANAL_ID": canal.upper(),
            "ENTRADA": datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S"),
            #"AGENTE_ID": agente_idx,
            "CONVERSA_IDX": conversa_idx,
            "ENVIADO_TKS": len(mensagem.split()),
            "RECEBIDO_TKS": 0,
            "TOTAL_TKS": 0,
            "FUNCAO_CHAMADA": 0,
            "NEGOCIO_IDX": negocio_idx,
            "USUARIO_IDX": usuario_idx or negocio_idx,
            "IMAGEM": imagem if imagem else None,
        })

    # Criar agente
    agent = Agent(
        agente_idx=agente_data.get("idx"),
        agente_nome=agente_nome,
        agente_instrucoes=agente_data.get("instrucoes"),
        agente_identidade=str(agente_data.get("identidade")),
        agente_personalidade=str(agente_data.get("personalidade")),
        agente_estilo_conversa=str(agente_data.get("estilo_conversa")),
        modelo=modelo,
        usuario_nome=usuario_nome,
        usuario_idx=usuario_idx,
        usuario_telefone=usuario_telefone,
        imagem=imagem,
        negocio_idx=negocio_idx,
        plataforma_url=plataforma_url,
        ferramentas=ferramentas,
    )

    agente_openai = await agent.get_agent_openai()

    # ✅ CORRIGIR AS CHAMADAS DAS FUNÇÕES COM OS PARÂMETROS CORRETOS
    if canal == "web_app":  
        if modo_resposta_ia == "text": # Resposta pode ter streaming
            return await resposta_texto_streaming(
                agente_openai, 
                historico_mensagens, 
                usuario_idx or negocio_idx, 
                agente_nome, 
                conversa_idx,
                canal  # ✅ ADICIONAR PARÂMETRO CANAL
            )
    elif canal == "whatsapp": # Resposta não pode ter streaming
        if modo_resposta_ia == "audio": 
            return await resposta_texto(
                agente_openai, 
                historico_mensagens, 
                usuario_idx or negocio_idx, 
                agente_nome, 
                conversa_idx,
                canal  # ✅ ADICIONAR PARÂMETRO CANAL
            )
    
    # Fallback para resposta sem streaming
    return await resposta_texto(
        agente_openai, 
        historico_mensagens, 
        usuario_idx or negocio_idx, 
        agente_nome, 
        conversa_idx,
        canal  # ✅ ADICIONAR PARÂMETRO CANAL
    )

async def resposta_texto_streaming(agente_openai, historico_mensagens, usuario_idx: str, agente_nome: str, conversa_idx: str, canal: str = "web_app"):
    """Retorna resposta com streaming para web app"""
    global messageChat
    
    try:
        async def event_stream() -> AsyncGenerator[bytes, None]:
            resposta_completa = ""
            try:
                async for chunk in oai.agent_run(agente_openai, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_bytes = chunk
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                        chunk_bytes = chunk_str.encode('utf-8')
                    resposta_completa += chunk_str
                    yield chunk_bytes
                
                # ✅ PROCESSAR RESPOSTA PARA HISTÓRICO
                resposta_limpa = limpar_tags_agente(resposta_completa)
                # Lógica condicional baseada no canal
                if canal.lower() in ["web_app", "app_web"]:
                    texto_para_historico = extrair_texto_puro(resposta_limpa)
                else:
                    # Para WhatsApp, remover markdown mantendo apenas links simples
                    texto_para_historico = limpar_markdown_whatsapp(resposta_limpa)
                historico_mensagens_atualizado = add_message_to_history(historico_mensagens, texto_para_historico, False)
                
                # ✅ SALVAR NO CACHE
                cache_key = get_conversa_key(usuario_idx, agente_nome, conversa_idx)
                cache[cache_key] = historico_mensagens_atualizado
                
                # ✅ SALVAR NO BANCO DE DADOS
                with messageChat_lock:
                    messageChat["SAIDA"] = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
                    messageChat["RECEBIDO"] = resposta_limpa
                    messageChat["RECEBIDO_TKS"] = len(texto_para_historico.split())
                    messageChat["TOTAL_TKS"] = messageChat.get("ENVIADO_TKS", 0) + messageChat["RECEBIDO_TKS"]
                    
                    messageId = await agentMessage.add(messageChat.copy())
                    
                    if messageId:
                        logger.info(f"✅ Mensagem salva no banco com ID: {messageId}")
                    else:
                        logger.error("❌ ERRO: Mensagem não foi salva no banco de dados!")
                        logger.error(f"Dados enviados: {messageChat}")
                        
            except Exception as stream_error:
                logger.error(f"Erro durante o streaming: {str(stream_error)}")
                yield f"Erro: {str(stream_error)}".encode('utf-8')

        return StreamingResponse(event_stream(), media_type="text/plain; charset=utf-8")
    
    except Exception as e:
        logger.error(f"Erro na resposta streaming: {str(e)}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


async def resposta_texto(agente_openai, historico_mensagens, usuario_idx: str, agente_nome: str, conversa_idx: str, canal: str = "web_app"):
    """Retorna resposta única para WhatsApp (sem streaming)"""
    global messageChat

    try:
        # Usar agent_run_sync para obter resposta completa
        result = await oai.agent_run_sync(agente_openai, historico_mensagens)
        
        # Extrair o texto da resposta
        resposta_texto = ""
        if hasattr(result, 'final_output') and result.final_output:
            resposta_texto = str(result.final_output)
        else:
            resposta_texto = str(result)
        
        print("resposta original:", resposta_texto)

        # ✅ PROCESSAR RESPOSTA E SALVAR NO CACHE/BANCO
        resposta_limpa = limpar_tags_agente(resposta_texto)
        print("resposta limpa (sem tag do agente):", resposta_limpa)

        
        # Aplicar limpeza de Markdown para WhatsApp na resposta ao usuário
        if canal.lower() not in ["web_app", "app_web"]:
            resposta_final = limpar_markdown_whatsapp(resposta_limpa)
        else:
            resposta_final = resposta_limpa
            
        # Lógica condicional baseada no canal para histórico
        if canal.lower() in ["web_app", "app_web"]:
            texto_para_historico = extrair_texto_puro(resposta_limpa)
        else:
            texto_para_historico = limpar_markdown_whatsapp(resposta_limpa)
        
        historico_mensagens_atualizado = add_message_to_history(historico_mensagens, texto_para_historico, False)
        
        # ✅ SALVAR NO CACHE
        cache_key = get_conversa_key(usuario_idx, agente_nome, conversa_idx)
        cache[cache_key] = historico_mensagens_atualizado
        
        # ✅ SALVAR NO BANCO DE DADOS
        with messageChat_lock:
            messageChat["SAIDA"] = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
            messageChat["RECEBIDO"] = resposta_limpa
            messageChat["RECEBIDO_TKS"] = len(texto_para_historico.split())
            messageChat["TOTAL_TKS"] = messageChat.get("ENVIADO_TKS", 0) + messageChat["RECEBIDO_TKS"]
            
            messageId = await agentMessage.add(messageChat.copy())
            
            if messageId:
                logger.info(f"✅ Mensagem salva no banco com ID: {messageId}")
            else:
                logger.error("❌ ERRO: Mensagem não foi salva no banco de dados!")
                logger.error(f"Dados enviados: {messageChat}")
        
        return {
            "success": True,
            "message": resposta_final  # Usar resposta_final limpa
        }
    
    except Exception as e:
        logger.error(f"Erro na resposta texto: {str(e)}")
        return {
            "success": False,
            "message": f"Erro durante execução: {str(e)}"
        }

async def carrega_conta(instanceId):

      #MATCH (origem:Conta {nome: "amzap-3184198720"})-[r]-(destino)
      #RETURN labels(origem)[0] AS label_origem,
      #       origem.idx AS origem_idx,
      #       type(r) AS relacionamento,
      #       destino.idx AS destino_idx,
      #       labels(destino)[0] AS label_destino;


    #logger.info(f"====== carrega_conta: {instanceId} =====")

    conta = {}

    # Garantir unicidade da Conta pelo nome (sem gerar notification)
    try:
        check_query = """
            SHOW CONSTRAINTS YIELD name
            WHERE name = $constraint_name
            RETURN name
        """
        exists_rows = await neo4j.execute_read_query(check_query, {"constraint_name": "conta_nome_unique"})
        constraint_exists = bool(exists_rows and len(exists_rows) > 0)
        if not constraint_exists:
            constraint_query = """
                CREATE CONSTRAINT conta_nome_unique
                FOR (c:Conta) REQUIRE c.nome IS UNIQUE
            """
            await neo4j.execute_write_query(constraint_query)
    except Exception as e:
        logger.warning(f"Falha ao garantir constraint de unicidade de Conta.nome: {e}")
    # Consulta única: retorna a conta (como mapa de propriedades) e os relacionamentos agregados
    query = """
        MATCH (c:Conta {nome: $instanceId})
        WITH c,
             [(c)-[r]-(destino) |
                 {
                   label_origem: labels(c)[0],
                   origem_idx: c.idx,
                   relacionamento: type(r),
                   destino_idx: destino.idx,
                   label_destino: labels(destino)[0],
                   propriedades: properties(r)
                 }
             ] AS relacionamentos
        RETURN properties(c) AS conta, relacionamentos
        LIMIT 1
    """
    #logger.info(f"query unica: {query}")
    parameters = {"instanceId": instanceId}
    result = await neo4j.execute_read_query(query, parameters)

    record = result[0] if result else None
    if isinstance(record, dict):
        conta_raw = record.get("conta")
        relacionamentos_raw = record.get("relacionamentos")
    else:
        conta_raw = None
        relacionamentos_raw = []

    conta = _to_str_dict(conta_raw) if conta_raw else {}
    relacionamentos = relacionamentos_raw if isinstance(relacionamentos_raw, list) else []

    #logger.info(f"conta: {conta}")
    #logger.info(f"relacionamentos: {relacionamentos}")

    # Determinar agente_idx a partir do relacionamento ATUA_EM com ativo=true
    agente_idx_rel = None
    negocio_idx_rel = None
    operacao_idx_rel = []

    
    try:
        for rel in relacionamentos:
            if not isinstance(rel, dict):
                continue
            tipo = rel.get("relacionamento")
            # Agente ativo na conta
            if tipo == "ATUA_EM":
                props = rel.get("propriedades") or {}
                if bool(props.get("ativo", False)) is True:
                    agente_idx_rel = rel.get("destino_idx")
            # Negócio que utiliza a conta
            elif tipo == "UTILIZADA_POR" and rel.get("label_destino") == "Negocio":
                negocio_idx_rel = rel.get("destino_idx")
            # Operações (coletar todos destino_idx)
            elif tipo == "UTILIZADA_PARA" and rel.get("destino_idx"):
                operacao_idx_rel.append(rel.get("destino_idx"))
        # Remover duplicados preservando ordem
        if operacao_idx_rel:
            seen = set()
            operacao_idx_rel = [x for x in operacao_idx_rel if not (x in seen or seen.add(x))]
    except Exception:
        agente_idx_rel = None
        negocio_idx_rel = None
        operacao_idx_rel = []


    conta["agente_idx"] = agente_idx_rel
    conta["negocio_idx"] = negocio_idx_rel
    conta["operacao_idx"] = operacao_idx_rel


    # Linha 49-50
    print(f"\n@@@@@ conta final: {conta}")


    return conta
    
   
@router.post("/evolution_zap_send")
async def evolution_zap_send(data: dict):
    """
    Endpoint para receber dados do webhook Evolution API
    Por enquanto retorna resposta padrão
    """
    logger.info("===== evolution_zap_send() =====")
    #logger.info(f"data recebida: {data}")

    # Usar o identificador de instância correto e aguardar a função assíncrona
    evo_instance_id = data.get("evo_instance_id") or data.get("instanceId")
    conta = await carrega_conta(evo_instance_id)

    data2 = {
        "mensagem": data.get("message") or "",
        "modo_resposta_ia": data.get("modo_resposta_ia") or "texto",
        #"negocio_idx": conta.get("negocio_idx") or "",
        "canal": data.get("canal") or "whatsapp",
        "usuario_nome": data.get("sender_name") or "",
        "plataforma_url": data.get("plataforma_url") or "",
        "agente_idx": conta.get("agente_idx") or "",
        "usuario_idx": data.get("usuario_idx") or "",
        "evo_instance_id": data.get("evo_instance_id") or "",
        "from": data.get("from") or "",
        "to": data.get("to") or "",
        "canal": "whatsapp",
        "usuario_telefone": data.get("sender_phone") or "",
    }

    # Linha 61-63
    logger.info(f"@@@@@ data2: {data2}")


    return await send_text(data2)

async def carrega_ferramentas(codigo: str) -> list:
    ferramentas = []
    print("carrega_ferramentas")
    try:
        if codigo and codigo.startswith("agent_"):
            # Verificar se o código tem o formato esperado (agent_subpasta)
            partes = codigo.split("_")
            if len(partes) >= 2:
                # Segunda parte do código (ex: 'atmzap' de 'agent_atmzap')
                subpasta = partes[1]
                
                # Construir o caminho do módulo
                module_path = f"api.agent.{subpasta}.functions"
                
                try:
                    pass
                    # Importar o módulo functions.py da subpasta
                    module = importlib.import_module(module_path)
                    
                    logger.info(f"Módulo {module_path} importado com sucesso")

                    
                    # Obter todas as funções do módulo que têm o decorator @function_tool
                    for name, obj in inspect.getmembers(module):
                        # Verificar se é uma função normal ou um objeto FunctionTool
                        is_function = inspect.isfunction(obj)
                        is_function_tool = hasattr(obj, '__class__') and 'FunctionTool' in str(type(obj))
                        
                        if (is_function or is_function_tool) and not name.startswith('_') and name != 'function_tool':
                            if is_function_tool:
                                # É um objeto FunctionTool (função decorada)
                                ferramentas.append(obj)
                                #logger.info(f"✅ FunctionTool carregada: {name}")
                            elif is_function:
                                #logger.info(f"Analisando função: {name}")
                                try:
                                    # Verificar se a função foi decorada com @function_tool
                                    # através do código fonte
                                    source = inspect.getsource(obj)
                                    #logger.info(f"Código fonte da função {name} obtido")

                                    
                                    if '@function_tool' in source:
                                        ferramentas.append(obj)
                                        logger.info(f"✅ Função carregada: {name}")
                                    else:
                                        pass
                                        #logger.info(f"❌ Função {name} não tem decorator @function_tool")
                                except (OSError, TypeError) as e:
                                    pass
                                    # Se não conseguir obter o código fonte, pular
                                    #logger.info(f"❌ Não foi possível obter código fonte da função {name}: {e}")

                        else:
                            if not name.startswith('_') and name != 'function_tool':
                                pass
                                #logger.info(f"Objeto {name} ignorado (tipo: {type(obj)})")
                    
                    print(f"Total de {len(ferramentas)} funções carregadas do módulo {subpasta}")
                    
                except ImportError as e:
                    logger.error(f"Erro ao importar módulo {module_path}: {e}")
                except Exception as e:
                    logger.error(f"Erro ao carregar funções do módulo {module_path}: {e}")
            else:
                logger.warning(f"Código do agente '{codigo}' não tem formato esperado (agent_subpasta)")
        else:
            logger.info(f"Código do agente '{codigo}' não começa com 'agent_', nenhuma função carregada")
            
    except Exception as e:
            logger.error(f"Erro geral ao carregar funções para código '{codigo}': {e}")
        
    return ferramentas

 
#==============================================
if __name__ == "__main__":
    import asyncio 
    import os
    # Substitua a linha 526:
    # os.system('cls')
    
    # Por esta versão mais robusta:
    os.system('cls')


    async def testa_chat():
        data = {
            "agente_idx": "1508250458",
            "mensagem": "Qual o seu nome",
            "modo_resposta_ia": "texto",
            "negocio_idx": "5544332211",
            "modelo": "1234567890",
            "usuario_nome": "",
            "usuario_idx": "1122334455",
            "plataforma_url": "https://www.plataforma.com",
            "canal": "whatsapp"  # Para retornar dict
        }
    
        try:
            response = await send_text(data)
            #print(response)
            
            # Verificar o tipo de resposta corretamente
            if isinstance(response, dict):
                if response.get("success"):
                    print("Resposta recebida com sucesso")
                    print(f"Mensagem: {response.get('message')}")
                else:
                    print(f"Erro na resposta: {response.get('message')}")
            elif getattr(response, "status_code", None) is not None:
                status = getattr(response, "status_code", None)
                if status == 200:
                    print("StreamingResponse criada com sucesso")
                else:
                    print(f"Erro na StreamingResponse: {status}")
            else:
                print(f"Tipo de resposta inesperado: {type(response)}")
                
        except Exception as e:
            print(f"Erro durante execução: {str(e)}")
            import traceback
            traceback.print_exc()

    async def carrega_conta_teste():
        conta = await carrega_conta("amzap-3184198720")
        #print(f"conta: {conta}")


    async def evolution_zap_teste():
        data = {
            "evo_instance_id": "amzap-3184198720",
            "message": "Qual a sua função?",
            "modo_resposta_ia": "texto",
            "canal": "whatsapp",
            "plataforma_url": "https://www.plataforma.com",
            "sender_name": "Glayson Carlos da Silva",
            "sender_phone": "553184198720"
        }


        result = await evolution_zap_send(data)
        print("@@@@ REsultado FIM", result)   
        
                
                

    asyncio.run(testa_chat())
    #asyncio.run(carrega_conta_teste())
    #asyncio.run(evolution_zap_teste())

    







