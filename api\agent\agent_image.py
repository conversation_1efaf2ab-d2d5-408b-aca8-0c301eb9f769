import requests
import io
import base64
from PIL import Image

class Images:
    def __init__(self):
        pass

    @staticmethod
    async def urlImage_to_base64(url):
        response = requests.get(url)
        image = Image.open(io.BytesIO(response.content))
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        img_str = "data:image/png;base64," + base64.b64encode(buffered.getvalue()).decode()
        return img_str

    
    @staticmethod
    def get_image_placeholder_size(url, type):
        #print("get_image_placeholder_size", url, type)
        # Extrai a parte da URL que contém as dimensões
        size_part = url.split('/')[-1]
        
        # Remove a query string se houver
        if '?' in size_part:
            size_part = size_part.split('?')[0]
        
        # Separa a largura e a altura
        width, height = size_part.split('x')
        
        if type == 'w':
            return int(width)
        elif type == 'h':
            return int(height)
        return None  # Retorna None se o placeholder não corresponder    

    @staticmethod
    def classify_dimension(width, height):
     
     # Calcula a razão entre largura e altura
     ratio = width / height
 
     # Define um limiar para considerar a imagem como quadrada
     threshold = 0.1  # Porcentagem de tolerância para considerar como quadrado
 
     # Compara a razão para determinar o tipo de imagem
     if abs(ratio - 1) <= threshold:
         return "square"
     elif ratio > 1:
         return "landscape"
     else:
         return "portrait"



if __name__ == "__main__":
    import asyncio

    async def main():
        image = Images()
        url = "https://imageplaceholder.net/600x400"
        #url = "https://imageplaceholder.net/600x400?text=Produto+1"

        result = image.get_image_placeholder_size( url, "w")
        print(f"The width is: {result}")

    async def test_urlImage_to_base64():
        image = Images()
        url = "https://gptalk.com.br/negocios/0068108573/documentos/contabilidade/0901795889.png"
        #url = "https://imageplaceholder.net/600x400?text=Produto+1"

        result = await image.urlImage_to_base64(url)
        print(result)

    #asyncio.run(main())
    asyncio.run(test_urlImage_to_base64())
