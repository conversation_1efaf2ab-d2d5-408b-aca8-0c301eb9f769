from fastapi import APIRouter
from .agent_mysql import Mysql
from typing import List
router = APIRouter()


class MktContact:
    def __init__(self):
        self.mysql = Mysql()

    async def fetch_business(self, id: int):
        result = await self.mysql.fetch("ID,NOME,EMAIL,TELEFONE,EXCLUIDO", "MKT_CONTATO", [f"NEGOCIO_ID = {id}", "EXCLUIDO = 0"])
        return result

    async def create(self, data: dict):

        print("create() data", data)

        async def persist_data(record, table):
            inserts = []
            print("--------------------- persist_data()", table)
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO {table} ({columns}) VALUES ({values});"
            inserts.append(query)
            # Combine all the insert queries into a single statement
            query_insert = " ".join(inserts)
            result = await self.mysql.query(query_insert)
            return result

        for record in data['adicionados']:
            if 'LOGRADOURO' in record:
                # ... (código para lidar com endereço)
                record_address = {
                    "LOGRADOURO": record['LOGRADOURO'],
                    "NUMERO": record['NUMERO'],
                    "COMPLEMENTO": record['COMPLEMENTO'],
                    "BAIRRO": record['BAIRRO'],
                    "CIDADE": record['CIDADE'],
                    "UF": record['UF'],
                    "CEP": record['CEP'],

                }
                # apagar as chaves de endereço do dicionário
                for key in record_address.keys():
                    del record[key]

                record_address['MKT_CONTATO_IDX'] = record['IDX']
                print("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx vou persistir os dados   ")
                result1 = await persist_data(record, "MKT_CONTATO")
                result2 = await persist_data(record_address, "MKT_ENDERECO")
                return result1, result2
            else:
                # Se não houver endereço, apenas insere o contato
                result = await persist_data(data['contatos'][0], "MKT_CONTATO")
                return result


    async def update(self, data: dict):
        async def update_data(record, table, filter):
            updates = []
            #deletar a chave ID se ela existir em record
            if 'ID' in record:
                del record['ID']  
            print(f"--------------------- update_data() {table}")
            values = ", ".join([f"{key} = '{value}'" for key, value in record.items() if key != 'IDX'])
            query = f"UPDATE {table} SET {values} WHERE {filter};"
            updates.append(query)
            query_update = " ".join(updates)
            result = await self.mysql.query(query_update)
            return result

        results = []
        for record in data['atualizados']:
            if 'LOGRADOURO' in record:
                record_address = {
                    "LOGRADOURO": record['LOGRADOURO'],
                    "NUMERO": record['NUMERO'],
                    "COMPLEMENTO": record['COMPLEMENTO'],
                    "BAIRRO": record['BAIRRO'],
                    "CIDADE": record['CIDADE'],
                    "UF": record['UF'],
                    "CEP": record['CEP']
                }
                # Remover as chaves de endereço do dicionário de contato
                for key in record_address.keys():
                        del record[key]
                
                result1 = await update_data(record, "MKT_CONTATO", "IDX='"+record['IDX']+"'")
                result2 = await update_data(record_address, "MKT_ENDERECO", "MKT_CONTATO_IDX='"+record['IDX']+"'")
                results.extend([result1, result2])
            else:
                # Se não houver endereço, apenas atualiza o contato
                result = await update_data(record, "MKT_CONTATO", "ID")
                results.append(result)
        
        return results



     

   


    async def search(self, business_idx: str, keywords: str, columns: str):
        print("===== mkcontacts_search() =====", business_idx, keywords, columns)
        query = f"""
        SELECT {columns} 
        FROM VISAO_CONTATO_ENDERECO 
        WHERE NEGOCIO_IDX = '{business_idx}'
        AND (NOME LIKE '%{keywords}%' OR TELEFONE LIKE '%{keywords}%' OR EMAIL LIKE '%{keywords}%')
        AND EXCLUIDO = 0;
        """
        print ("query",query)
        result = await self.mysql.query(query)
        print("result",result)
        return result


    

@router.get("/fetch/business")
async def fetch_business(id: int):
    print("fetch_business()")
    mktcontact = MktContact()
    result = await mktcontact.fetch_business(id)
    print("result",result)
    return result

@router.post("/create")
async def create(data: dict):
    print("========================== create() =============")
    print("create() data",data)
    mktContact = MktContact()
    result =await mktContact.create(data)
    return result
    

@router.post("/update")
async def update(data: dict):
    mktContact = MktContact()
    result = await mktContact.update(data)
    return result

@router.post("/search")
async def search(business_idx: str, keywords: str, columns: dict):
    
    mktcontact = MktContact()
    columns = ",".join(columns["colunas_nome"])
    result = await mktcontact.search(business_idx, keywords, columns)
    return result

@router.post("/addUpdate")
async def addUpdate(data: dict):
    print("addUpdate()",data)
    if "ID" in data:
        print("update")
        result = await update({"atualizados":[data]})
        print("result",result)
    else:
        print("create")
        result = await create({"adicionados":[data]})
        print("result",result)

    return result

        
if __name__ == "__main__":
    import asyncio


    async def main():


        data = {
                "ID": 83,
                "IDX": "0445178864",
                "NOME": "Erasmo 2",
                "EMAIL": "<EMAIL>",
                "TELEFONE": "***********",
                "CPF_CNPJ": "***********",
                "TIPO_PESSOA": 1,
                "NEGOCIO_IDX": "0068108573"
   
        }
        
        result = await addUpdate(data)
        
        print("result",result)

    asyncio.run(main())


    