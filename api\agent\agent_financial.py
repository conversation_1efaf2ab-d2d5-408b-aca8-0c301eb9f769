from fastapi import APIRouter
from .agent_mysql import Mysql
from ..functions.util import generate_unique_id
from datetime import datetime


class AgentFinancial:
    def __init__(self):
        self.mysql = Mysql()


    
    async def acha_fornecedor(self, razao_social, nome_fantasia):
        return self.mysql.get_fornecedor(razao_social, nome_fantasia)

    
    async def set_accounts_payable(self, document):

        document = {
            "TIPO": document.get("TIPO"),
            "TOTAL": document.get("TOTAL"),
            "NR_PARCELAS": document.get("NR_PARCELAS"),
            "FORNECEDOR_IDX": self.acha_fornecedor(document.get("RAZAO_SOCIAL"), document.get("NOME_FANTASIA")),
        }
        installments = document.get("parcelas")

        for installment in installments:
            account = {
                "IDX": generate_unique_id(),
                "DATA_REG": datetime.now().strftime("%Y-%m-%d"),
                "DATA_VENC": installment.get("VENCIMENTO"),
                "VALOR": installment.get("VALOR"),
                "JA_PAGO": installment.get("JA_PAGO"),
                "DATA_PAGAMENTO": installment.get("DATA_PAGAMENTO"),
                "NEGOCIO_IDX": document.get("NEGOCIO_IDX"),
                "FORNECEDOR_IDX": document.get("FORNECEDOR_IDX"),
                "PARCELA_ATUAL": installment.get("PARCELA_ATUAL"),
                "PARCELA_TOTAL": installment.get("PARCELA_TOTAL"),
        }

    async def set_accounts_receivable(self, document):
        pass
    
    