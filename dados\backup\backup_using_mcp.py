import os
import sys
import datetime

# Adicionar o diretório pai (server) ao sys.path para importar os módulos necessários
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Importar a função para executar SQL
from api.agent.agent_mysql import Mysql

async def backup_database():
    # Nome do arquivo de backup com data atual
    date_str = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    backup_filename = f"gptalkco_01_{date_str}.sql"
    backup_path = os.path.join(os.path.dirname(__file__), backup_filename)
    
    # Instância do MySQL
    mysql = Mysql()
    
    try:
        print(f"Iniciando backup do banco de dados para {backup_path}...")
        
        # Obter uma lista de todas as tabelas
        result = await mysql.query("SHOW TABLES")
        tables = [row[0] for row in result]
        
        # Arquivo para salvar o backup
        with open(backup_path, 'w', encoding='utf-8') as f:
            # Cabeçalho
            f.write("-- Backup do banco de dados gptalkco_01\n")
            f.write(f"-- Data: {date_str}\n")
            f.write("SET FOREIGN_KEY_CHECKS = 0;\n\n")
            
            # Processar cada tabela
            for table in tables:
                print(f"Fazendo backup da tabela: {table}")
                
                # Obter esquema da tabela
                create_table = await mysql.query(f"SHOW CREATE TABLE `{table}`")
                create_stmt = create_table[0][1]
                f.write(f"{create_stmt};\n\n")
                
                # Obter dados da tabela
                rows = await mysql.query(f"SELECT * FROM `{table}`")
                if rows:
                    # Obter nomes das colunas
                    columns = await mysql.query(f"SHOW COLUMNS FROM `{table}`")
                    column_names = [col[0] for col in columns]
                    
                    # Escrever inserções
                    f.write(f"-- Data for table `{table}`\n")
                    for row in rows:
                        values = []
                        for value in row:
                            if value is None:
                                values.append("NULL")
                            elif isinstance(value, (int, float)):
                                values.append(str(value))
                            else:
                                # Escapar strings
                                escaped = str(value).replace("'", "''")
                                values.append(f"'{escaped}'")
                        
                        f.write(f"INSERT INTO `{table}` (`{'`, `'.join(column_names)}`) VALUES ({', '.join(values)});\n")
                    f.write("\n")
            
            # Rodapé
            f.write("SET FOREIGN_KEY_CHECKS = 1;\n")
        
        # Verificar se o arquivo foi criado
        if os.path.exists(backup_path):
            file_size = os.path.getsize(backup_path) / (1024 * 1024)  # Tamanho em MB
            print(f"Backup concluído com sucesso! Tamanho do arquivo: {file_size:.2f} MB")
        else:
            print("Erro: O arquivo de backup não foi criado.")
    
    except Exception as e:
        print(f"Erro ao executar o backup: {str(e)}")

# Para executar o script
if __name__ == "__main__":
    import asyncio
    asyncio.run(backup_database()) 