from .agent_mysql import Mysql
from .agent_task import Task
from .agent_functioncall import FunctionCall

from .agent_process import Process
process = Process

class Project:
    
    def __init__(self, task=None, mysql=None,user=None):
        self.ID  = 0
        self.task = task if task else Task()
        self.mysql = mysql if mysql else Mysql()
        self.functionCall = FunctionCall()
        self.user = user

    #==========
    async def new(self,projeto:dict):
        #print('')
        #print('#========== new_project==========')
        #print('')
        id = await self.save(projeto)
                    
        return id    
            



    #=========
    async def update(self,data):
        result = await self.mysql.mysql_update_data("PROJETO",data)

    #=========
    async def save(self,projeto:dict):
        
        if 'ID' in projeto: #atualiza
            await self.mysql.mysql_update_data('PROJETO',projeto)
            return True
        else:
            id = await self.mysql.mysql_add_data('PROJETO',projeto)
            return id

    #==========
    async def remove(self,condition:str):
        #print("remove ",condition)
        query = f"UPDATE PROJETO SET EXCLUIDO = 1 WHERE {condition}"
        #print("query",query)
        await self.mysql.mysql_query(query)
        projeto = await self.fetch(condition)
        #print("projeto",projeto)
        await self.task.remove(f"PROJETO_ID= {projeto[0]['ID']}")

    #==========
    async def fetch(self,condition:str):
        #print("##### fetch() #####",condition)
        query = f"SELECT * FROM PROJETO WHERE {condition}"
        #print("query",query)
        return await  self.mysql.mysql_query(query)
    


import asyncio, json

async def main():
    from .agent_llm import LLM
    data = {}
    data['ID']= 0
    data['NOME'] = 'petdocarlos'
    data["AREA"] = "Pet shop"
    data["OBJETIVO"] = "vendas"
    data["PUBLICO_ALVO"] = "donos de bichos de estimação"
    data["DESIGN_ESTILO"] = "fundoazul com texto amarelo"
    data["USUARIO_ID"]  = "ei5Vm2BLTLPcgzK6tGnf7aP2a1I3"
    data["FUNCIONALIDADES"] = """
    link para redes sociais.
    formulario para recebimento de newsletter.
    """
    data['MODEL'] = 'gemini-pro'

    entrada = json.dumps(data, indent=4)
    saida = """
    Tem saída
""" 
    llm = LLM()
    status = "R"
    project = Project(model=llm.model_gemini.geminipro15)
    result = await project.execution(75,entrada,saida,status)
    print("result",result)
#asyncio.run(main())