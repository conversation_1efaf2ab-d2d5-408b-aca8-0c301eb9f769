from datetime import datetime, date
import pytz


class Time:
    def __init__ (self):
        pass

    @staticmethod
    def get_day_of_week(data_str):
       # Converte a string de data para um objeto datetime
       data = datetime.strptime(data_str, "%Y-%m-%d")

       # Lista de dias da semana em português
       dias_da_semana = ["Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira", "Sábado", "Domingo"]
    
       # Retorna o dia da semana correspondente
       return dias_da_semana[data.weekday()]
    


    @staticmethod
    def time_now_to_timestamp(only_int=False):
        # Obtém o timestamp atual
        timestamp = datetime.now().timestamp()
        # Converte o timestamp para inteiro
        if only_int:
            timestamp = int(timestamp)
        return timestamp 


    @staticmethod
    def get_number_day_of_week(data_str:str=None):
       # Converte a string de data para um objeto datetime
       if not data_str:
           data_str = datetime.now().strftime("%Y-%m-%d")
       data = datetime.strptime(data_str, "%Y-%m-%d")

       return data.weekday()+1

    
    from datetime import datetime

    @staticmethod
    def today():
        # Obtém a data atual
        data_atual = datetime.now()
        
        # Formata a data como string no formato desejado
        data_formatada = data_atual.strftime("%Y-%m-%d")
        
        return data_formatada
    
    @staticmethod
    def get_date_hour(timeZone="America/Bahia"):
        timezone = pytz.timezone(timeZone)
        tnow = datetime.now(timezone)
        date_hour = tnow.strftime("%Y-%m-%d %H:%M:%S")
        return date_hour

    @staticmethod
    def get_hour(timeZone="America/Bahia"):
        timezone = pytz.timezone(timeZone)
        tnow = datetime.now(timezone)
        hour = tnow.strftime("%H:%M:%S")
        return hour



if __name__ == "__main__":
    import asyncio
    async def main():
        tm = Time()
        print(tm.get_number_day_of_week())
    asyncio.run(main())