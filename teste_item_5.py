#!/usr/bin/env python3
"""
TESTE 5: Testando envio de mensagem com a instância REAL
"""
import requests

# Configurações da instância real
url = 'https://apistart03.megaapi.com.br/rest/sendMessage/megastart-MV1loESU4sN/text'
headers = {
    'Authorization': 'Bearer MV1loESU4sN',
    'Content-Type': 'application/json'
}

# Enviando para o próprio número da instância
payload = {
    "number": "<EMAIL>",
    "messageData": {
        "text": "🎉 TESTE 5: Mensagem de teste da instância REAL - Sucesso!"
    }
}

print('🔍 TESTE 5: Envio de mensagem com instância REAL')
print(f'URL: {url}')
print(f'Token: MV1loESU4sN')
print(f'Destinatário: {payload["number"]}')
print(f'Mensagem: {payload["messageData"]["text"]}')
print('-' * 50)

try:
    response = requests.post(url, headers=headers, json=payload)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response completa: {data}')
        
        if data.get('error') == False:
            print('✅ SUCESSO: Mensagem enviada com sucesso!')
            print('🎯 A API de envio está funcionando!')
        else:
            print('❌ FALHA: Erro no envio')
            print(f'Erro: {data.get("message", "Erro desconhecido")}')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 5:')
print('Se STATUS CODE = 200 e error = False: ✅ SUCESSO')
print('Caso contrário: ❌ FALHA') 