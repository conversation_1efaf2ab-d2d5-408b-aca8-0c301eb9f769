#!/usr/bin/env python3
"""
Teste simples para verificar se o AgentLogger está funcionando no console
"""

import sys
import os

# Adiciona o diretório api ao path para importar o módulo
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from api.agent.agent_logger import AgentLogger

def test_logger():
    print("=== TESTE DO AGENTLOGGER ===")
    
    # Cria instância do logger
    logger = AgentLogger()
    
    print("Logger criado, testando níveis...")
    
    # Testa diferentes níveis
    logger.debug("TESTE DEBUG - deve aparecer no console")
    logger.info("TESTE INFO - deve aparecer no console")
    logger.warning("TESTE WARNING - deve aparecer no console")
    logger.error("TESTE ERROR - deve aparecer no console")
    
    print("Forçando console logging...")
    logger.force_console_logging()
    
    logger.info("TESTE APÓS FORCE_CONSOLE_LOGGING - deve aparecer no console")
    
    print("Debug dos níveis de logging...")
    logger.debug_logging_levels()
    
    print("=== FIM DO TESTE ===")

if __name__ == "__main__":
    test_logger()