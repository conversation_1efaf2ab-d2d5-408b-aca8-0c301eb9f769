from .agent_geminivertex import GeminiVert<PERSON>
from .agent_gemini import Gemini
from .agent_openai import OpenAi
from .agent_deepseek import DeepSeek
from .agent_secret import Secret
from .agent_logger import AgentLogger
import json

logger = AgentLogger()


class LLM:
  def __init__(self,model=None, user=None):
    self.model = model
    self.model_gemini = Gemini()
    self.model_openai =  OpenAi()
    self.user = user 
    self.model_geminiVertex = GeminiVertex()
    self.model_default = self.model_gemini.geminipro15
    self.TOKEN_PROMPT = 0
    self.TOKEN_RESPOSTA = 0
    self.TOKEN_TOTAL = 0
    self.RESPONSE = None
    self.MODELO = None
    self.FUNCAO_CHAMADA = False
    self.FUNCAO_NOME = None
    self.FUNCAO_RESPOSTA = None
    self.AGENTE_RESPOSTA_DIRETA = False
    self.secret = Secret()
  async def run_model(
    self,
    history=None,
    tools=None,
    functions=None,
    model = None,
    message=None,
    llmtype =None
    ):
     #print("llm_model",model)
    # print("")
     #print("history",history)   
     #print("")
     #print("message",message)
     model =  model if model else self.model_default
     self.MODELO = model
     result = ""
     
     #================== GEMINI ===========================
     #Verifica / executa modelo Gemini
     if self.is_model_in_models(model,self.model_gemini) :
          #print(f"modelo gemini:{model}")
          
          if llmtype == "image":
              gemini = GeminiVertex()
          else: 
              gemini = Gemini()
        
          result = await gemini.chat(
          llm=model,
          llmtype = llmtype,
          tools= tools,
          functions=functions,
          message=message
           
          )

          self.RESPOSTA = result['candidates'][0]['content']['parts'][0]['text']
          self.TOKEN_PROMPT = result['usageMetadata']['promptTokenCount']
          self.TOKEN_RESPOSTA = result['usageMetadata']['candidatesTokenCount']
          self.TOKEN_TOTAL = result['usageMetadata']['totalTokenCount']
     

          return self.RESPOSTA


    #================== OPENAI ===========================
     if self.is_model_in_models(model,self.model_openai) :
          #print(f"modelo openai:{model}")
          #print("message", message)
          #print("functions", functions)
          
          if llmtype == "image":
            pass
          else: 
             
             
              oai = OpenAi()    
              result = await oai.chat(
              model=model,
              llmtype = llmtype,
              functions=functions,
              message=message          
          )
          #print("")
          #print("result",result)
          #print("")
          if result.choices[0].finish_reason == 'stop':
              self.RESPOSTA = result.choices[0].message.content

          #verifica se a resposta é uma chamada de funcao
          if result.choices[0].finish_reason == 'function_call':
              self.FUNCAO_CHAMADA = True
              self.FUNCAO_PARAMETROS = json.loads(result.choices[0].message.function_call.arguments)
              self.RESPOSTA = result.choices[0].message.function_call.name
              #print("")
              #print("chamar funcao:" + self.RESPOSTA)
              #print("parametros:", self.FUNCAO_PARAMETROS)
              
          self.TOKEN_PROMPT = result.usage.prompt_tokens
          self.TOKEN_RESPOSTA = result.usage.completion_tokens
          self.TOKEN_TOTAL = result.usage.total_tokens


          # Mostrando as informações
          #print ("resposta", self.RESPOSTA)
          #print("Tokens do Prompt:", self.TOKEN_PROMPT)
          #print("Tokens da resposta:", self.TOKEN_RESPOSTA)
          #print("Total de Tokens:", self.TOKEN_TOTAL)

          
          #print("")
          #print("result llm_execute openai", result)
          return self.RESPOSTA
     return f'o modelo {model} não existe ou ainda não foi adicioando aos modelos em uso no GPtalk'
        
     
  def is_model_in_models(self,model,llm):
    # Varrer todos os atributos do modelo e verificar se algum tem o valor 'model'
    for attr_value in llm.__dict__.values():
        if attr_value == model:
            return True
    return False

  def get_client_openai(self, model):
        #logger.info("===== get_client_openai() =====")
        #logger.info("model %s", model)
        oai = OpenAi()


        match model:

            
            case "gemini-2.0-flash":
                api_key = self.model_gemini.api_key
                #logger.info("api_key %s", api_key)
                model = oai.get_client_openAi("https://generativelanguage.googleapis.com/v1beta/models",api_key,model)
                return model
            case "deepseek-chat":
                deepseek = DeepSeek()
                model = oai.get_client_openAi(deepseek.base_url,deepseek.api_key,deepseek.model_chat_name)
                return model
            case "gpt-4.1-openrouter":
                logger.info("openrouter")
                openrouter_gpt41_base_url = self.secret.get_secret("OPENROUTER_GPT41_BASE_URL")
                openrouter_gpt41_api_key = self.secret.get_secret("OPENROUTER_GPT41_API_KEY")
                logger.info("openrouter_gpt41_base_url %s" , openrouter_gpt41_base_url)
                logger.info("openrouter_gpt41_api_key %s" , openrouter_gpt41_api_key)
                model = oai.get_client_openAi(openrouter_gpt41_base_url, openrouter_gpt41_api_key, "gpt-4.1")
                return model

    

  def get_model_idx(self, modelo_idx):
       #logger.info("*****modelo_idx %s", modelo_idx)
       # Linha 163-163
       if modelo_idx == "1234567890":
            model = self.get_client_openai("deepseek-chat")
            return model
       elif modelo_idx == "1234567891":
            model = self.get_client_openai("gpt-4.1-openrouter")
            return model

       elif modelo_idx == "1234567892":
            model = "deepseek/deepseek-chat-v3-0324"
            logger.info("model %s", model)
            base_url =  self.secret.get_secret("OPENROUTER_BASE_URL")
            api_key = self.secret.get_secret("OPENROUTER_DEEPSEEKCHATV3_KEY")
            model =  self.model_openai.get_client_openAi(base_url, api_key, model)
            return model
       elif modelo_idx == "0987654321":
            model = "gpt-4.1"
            return model
       elif modelo_idx == "1234567893": #qwen3_32b
            model = "qwen/qwen3-32b"
            logger.info("model %s", model)
            base_url =  self.secret.get_secret("OPENROUTER_BASE_URL")
            api_key = self.secret.get_secret("OPENROUTER_QWEN3_32B_API_KEY")
            model =  self.model_openai.get_client_openAi(base_url, api_key, model)
            return model
       elif  modelo_idx == "1234567894": #qwen3_32b
            model = "moonshotai/kimi-k2:free"
            logger.info("model %s", model)
            base_url =  self.secret.get_secret("OPENROUTER_BASE_URL")
            api_key = self.secret.get_secret("OPENROUTER_KIMIK2FREE_KEY")
            model =  self.model_openai.get_client_openAi(base_url, api_key, model)
            return model
       elif modelo_idx == "1234567895":
            model = "gpt-4.1-nano"
            return model;
       elif modelo_idx == "1234567896":
            model = "deepseek/deepseek-chat:free"
            logger.info("model %s", model)
            base_url =  self.secret.get_secret("OPENROUTER_BASE_URL")
            api_key = self.secret.get_secret("OPENROUTER_DEEPSEEKCHATV3_KEY_FREE")
            model =  self.model_openai.get_client_openAi(base_url, api_key, model)
            return model
       elif  modelo_idx == "1234567897":
            model = "gpt-4o-mini"
            return model
        
       # Adicionar suporte direto para modelos OpenAI
       elif modelo_idx in ["gpt-4o", "gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"]:
            return modelo_idx
        
       else:
          return self.model_default

    
 #testes 
import asyncio

llm = LLM()

if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():



        system = ""
        messages = [
           
            {"role":"assistant", "content": system},
            {"role":"user", "content": "Olá, bom dia. tudo bem com vc?"},
            {"role":"assistant", "content": "Olá, tudo bem e você?"},
            {"role":"user", "content": "aqui chove muito, e por aí/"},
            {"role":"assistant", "content": "Aqui também chove bastante"},
            {"role":"user", "content": "Quero recarregar meus créditos"},
            {"role":"assistant", "content": "deposite 10 reais em nossa conta."},
            {"role":"user", "content": "crie uma letra de pagode "}
            ]
        


       
              
        response = await llm.run_model(model=llm.model_gemini.geminipro15, message=messages, llmtype="text")
        print("gemini1.5: \n",response)  


    asyncio.run(main())
