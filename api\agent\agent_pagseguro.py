#fonte: https://dev.pagbank.uol.com.br/reference/criar-pedido-pedido-com-qr-code
import requests
import json
from datetime import datetime, timedelta
from .agent_business import Business
from .agent_converter import Converter
from .agent_entity import Entity 
from fastapi import APIRouter
router = APIRouter()


@router.post("/pix/qrcode")
async def pix_qrcode(data: dict):
    cv = Converter()

    print("========== api/pagseguro/pix/qrcode ==========")
    print("data type", type(data))
    print("data",data)
    telefone = cv.country_unmasked_telephone(data["telefone"],"55")
    valor = data["valor"]
    descricao = data["descricao"]
    cpf_cnpj = data["cpf_cnpj"]
    nome = data["nome"]
    email = data["email"]
    valor = cv.decimal_to_integer(data["valor"])
    tokens = data["tokens"]
    negocio_token = data["negocio_token"]

    print("nome",nome)
    print("email",email)
    print("telfone",telefone)
    print("cpf_cnpj",cpf_cnpj)
    print("descricao",descricao)
    print("valor",valor)
    print("tokens",tokens)
    print("negocio_token",negocio_token)

    bs = Business()
    await bs.fetch("*",["TOKEN='"+negocio_token+"'"])
    if bs.ID:
        pg = Pagseguro(negocio=bs,modo="producao")
        pix = await pg.gerar_pix_qrcode(valor, descricao,cpf_cnpj=cpf_cnpj,nome=nome,email=email,telefone=telefone)
        return pix
    else:
        return {"erro":"Negócio não encontrado"}


class Pagseguro:
    def __init__(self, negocio: Business,modo:str="producao"):
        self.negocio = negocio
        self.base_url = "https://api.pagseguro.com/orders"  if modo == "producao" else  "https://sandbox.api.pagseguro.com/orders"
        self.TOKEN = negocio.PAGSEGURO_TOKEN_PRODUCAO if modo == "producao" else negocio.PAGSEGURO_TOKEN_SANDBOX
        print("token pagseguro",self.TOKEN)
        self.converter = Converter()
   
    async def gerar_pix_qrcode(self, 
                                   valor:str, 
                                   descricao:str, 
                                   nome:str,
                                   cpf_cnpj:str,
                                   telefone:str,
                                   email:str,
                                   endereco:dict=None,
                                ):
            cpf_cnpj = "***********"
            nome = "Iraci da Silva"
            print("========== gerar_pix_qrcode_pix ==========")
            print("valor",valor)
            print("descricao",descricao)
            print("nome",nome)
            print("cpf_cnpj",cpf_cnpj)
            print("email",email)
            print("telefone",telefone)
            # Define a data de expiração como um dia após a data atual
            expiration_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%S-03:00')
            print("expiration_date",expiration_date)
      
            # Subobjeto: Referência e Cliente
            reference_id = self.converter.time_now_to_timestamp(only_int=True)
            customer = {
                "name": nome if nome else "Nome não informado",
                "email": email,
                "tax_id": cpf_cnpj,
                "phones": [
                    {
                        "country": telefone[:2],
                        "area": telefone[2:4],
                        "number": telefone[4:],
                        "type": "MOBILE"
                    }
                ]
            }
            
            # Subobjeto: Itens
            items = [
                {
                    "name": descricao,
                    "quantity": 1,
                    "unit_amount": valor
                }
            ]
            
            # Subobjeto: QR Codes
            qr_codes = [
                {
                    "amount": {
                        "value": valor
                    },
                    "expiration_date": expiration_date
                }
            ]
            
            # Subobjeto: Endereço de Entrega
            shipping = {
                "address": {
                    "street": "Avenida Brigadeiro Faria Lima",
                    "number": "1384",
                    "complement": "apto 12",
                    "locality": "Pinheiros",
                    "city": "São Paulo",
                    "region_code": "SP",
                    "country": "BRA",
                    "postal_code": "01452002"
                }
            }
            
            # Subobjeto: URLs de Notificação
            notification_urls = [
                "https://meusite.com/notificacoes"
            ]
            
            # Objeto principal (payload)
            payload = {
                "reference_id": reference_id,
                "customer": customer,
                "items": items,
                "qr_codes": qr_codes,
                "notification_urls": notification_urls
            }
            if endereco:
                payload["shipping"] = shipping
            headers = {
                "Authorization": f"Bearer {self.TOKEN}",
                "accept":"application/json",
                "content-type": "application/json"
            }
            print("self.TOKEN",self.TOKEN)
            print("headers",headers)
            print("payload",payload)
            response = requests.post(self.base_url, json=payload, headers=headers)
            status = response.status_code
            resposta = response.json()
            print("status",status)
            print("resposta",resposta)
            if status == 201: #este status indica que a requisição foi bem sucedida
                id = resposta["id"]
                qrcode = resposta["qr_codes"][0]["links"][0]["href"]
                qrcode_text = resposta["qr_codes"][0]["text"]   
                qrcode_base64 = resposta["qr_codes"][0]["links"][1]["href"]
    
                pix = {}
                pix["qrcode"] = {}
                pix["qrcode"]["url"] = qrcode
                pix["qrcode"]["text"] = qrcode_text
                print("pix",pix )
                return pix
     


if __name__ == "__main__":
    import asyncio

    async def main():
        cv = Converter()    
        data = {'nome': 'carlos', 'email': '<EMAIL>', 'telefone': '(31)99114-9571', 'cpf_cnpj': '***********', 'valor': "25,00", 'tokens': '25000000', 'descricao': 'Recarga de GPTokens', 'negocio_token': '2c624232cdd221771294dfbb310aca000a0df6ac8b66b696d90ef06fdefb64a3'}
        telefone = cv.country_unmasked_telephone(data["telefone"],"55")
        valor = data["valor"]
        descricao = data["descricao"]
        cpf_cnpj = data["cpf_cnpj"]
        nome = data["nome"]
        email = data["email"]
        valor = cv.decimal_to_integer(data["valor"])
        print("valor final",valor)  
        
        tokens = data["tokens"]
        negocio_token = data["negocio_token"]

        print("nome",nome)
        print("email",email)
        print("telfone",telefone)
        print("cpf_cnpj",cpf_cnpj)
        print("descricao",descricao)
        print("valor",valor)
        print("tokens",tokens)
        print("negocio_token",negocio_token)


        bs = Business()
        await bs.fetch("*",["TOKEN='"+negocio_token+"'"])
        if bs.ID:
            print("Negócio encontrado", bs.NOME)
            pg = Pagseguro(negocio=bs,modo="producao")
            pix = await pg.gerar_pix_qrcode(valor, descricao,cpf_cnpj=cpf_cnpj,nome=nome,email=email,telefone=telefone)
            print(pix['qrcode']['url'])
            print(pix['qrcode']['text'])
        else:
            print("Negócio não encontrado")
    
    async def mainX():
        cv = Converter()    
        data = {
           "nome": "carlos",
            "email": "<EMAIL>",
            "telefone": "(31)99114-9571",
            "cpf_cnpj": "***********",
            "valor": "1000",
            "tokens": "10000",
            "descricao": "Recarga de GPTokens",
            "negocio_token": "2c624232cdd221771294dfbb310aca000a0df6ac8b66b696d90ef06fdefb64a3",         
        }
        result = await pix_qrcode(data)
        print ("result",result)
    asyncio.run(main())