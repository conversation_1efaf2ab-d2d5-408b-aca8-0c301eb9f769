Directory structure:
└── absadiki-pywhispercpp/
    ├── README.md
    ├── CMakeLists.txt
    ├── LICENSE
    ├── MANIFEST.in
    ├── mkdocs.yml
    ├── pyproject.toml
    ├── requirements.txt
    ├── setup.py
    ├── .appveyor.yml
    ├── .directory
    ├── .pre-commit-config.yaml
    ├── docs/
    │   └── index.md
    ├── pybind11/
    ├── pywhispercpp/
    │   ├── __init__.py
    │   ├── constants.py
    │   ├── model.py
    │   ├── utils.py
    │   └── examples/
    │       ├── __init__.py
    │       ├── assistant.py
    │       ├── livestream.py
    │       ├── main.py
    │       └── recording.py
    ├── src/
    │   └── main.cpp
    ├── tests/
    │   ├── test_c_api.py
    │   └── test_model.py
    ├── whisper.cpp/
    └── .github/
        ├── dependabot.yml
        └── workflows/
            ├── docs.yml
            ├── pip.yml
            └── wheels.yml

================================================
FILE: README.md
================================================
# pywhispercpp
Python bindings for [whisper.cpp](https://github.com/ggerganov/whisper.cpp) with a simple Pythonic API on top of it.

[![License: MIT](https://img.shields.io/badge/license-MIT-blue.svg)](https://opensource.org/licenses/MIT)
[![Wheels](https://github.com/absadiki/pywhispercpp/actions/workflows/wheels.yml/badge.svg?branch=main&event=push)](https://github.com/absadiki/pywhispercpp/actions/workflows/wheels.yml)
[![PyPi version](https://badgen.net/pypi/v/pywhispercpp)](https://pypi.org/project/pywhispercpp/)
[![Downloads](https://static.pepy.tech/badge/pywhispercpp)](https://pepy.tech/project/pywhispercpp)

# Table of contents
<!-- TOC -->
* [Installation](#installation)
    * [From source](#from-source)
    * [Pre-built wheels](#pre-built-wheels)
    * [NVIDIA GPU support](#nvidia-gpu-support)
    * [CoreML support](#coreml-support)
    * [Vulkan support](#vulkan-support)
* [Quick start](#quick-start)
* [Examples](#examples)
  * [CLI](#cli)
  * [Assistant](#assistant)
* [Advanced usage](#advanced-usage)
* [Discussions and contributions](#discussions-and-contributions)
* [License](#license)
<!-- TOC -->

# Installation

### From source
* For the best performance, you need to install the package from source:
```shell
pip install git+https://github.com/absadiki/pywhispercpp
```
### Pre-built wheels
* Otherwise, Basic Pre-built CPU wheels are available on PYPI

```shell
pip install pywhispercpp # or pywhispercpp[examples] to install the extra dependencies needed for the examples
```

[Optional] To transcribe files other than wav, you need to install ffmpeg:  
```shell
# on Ubuntu or Debian
sudo apt update && sudo apt install ffmpeg

# on Arch Linux
sudo pacman -S ffmpeg

# on MacOS using Homebrew (https://brew.sh/)
brew install ffmpeg

# on Windows using Chocolatey (https://chocolatey.org/)
choco install ffmpeg

# on Windows using Scoop (https://scoop.sh/)
scoop install ffmpeg
```

### NVIDIA GPU support
To Install the package with CUDA support, make sure you have [cuda](https://developer.nvidia.com/cuda-downloads) installed and use `GGML_CUDA=1`:

```shell
GGML_CUDA=1 pip install git+https://github.com/absadiki/pywhispercpp
```
### CoreML support

Install the package with `WHISPER_COREML=1`:

```shell
WHISPER_COREML=1 pip install git+https://github.com/absadiki/pywhispercpp
```

### Vulkan support

Install the package with `GGML_VULKAN=1`:

```shell
GGML_VULKAN=1 pip install git+https://github.com/absadiki/pywhispercpp
```

### OpenBLAS support

If OpenBLAS is installed, you can use `GGML_BLAS=1`. The other flags ensure you're installing fresh with the correct flags, and printing output for sanity checking.
```shell
GGML_BLAS=1 pip install git+https://github.com/absadiki/pywhispercpp --no-cache --force-reinstall -v
```

### OpenVINO support

Follow the the steps to download correct OpenVINO package (https://github.com/ggerganov/whisper.cpp?tab=readme-ov-file#openvino-support).

Then init the OpenVINO environment and build.
```
source ~/l_openvino_toolkit_ubuntu22_2023.0.0.10926.b4452d56304_x86_64/setupvars.sh 
WHISPER_OPENVINO=1 pip install git+https://github.com/absadiki/pywhispercpp --no-cache --force-reinstall
```

Note that the toolkit for Ubuntu22 works on Ubuntu24


** __Feel free to update this list and submit a PR if you tested the package on other backends.__


# Quick start

```python
from pywhispercpp.model import Model

model = Model('base.en')
segments = model.transcribe('file.wav')
for segment in segments:
    print(segment.text)
```

You can also assign a custom `new_segment_callback`

```python
from pywhispercpp.model import Model

model = Model('base.en', print_realtime=False, print_progress=False)
segments = model.transcribe('file.mp3', new_segment_callback=print)
```


* The model will be downloaded automatically, or you can use the path to a local model.
* You can pass any `whisper.cpp` [parameter](https://absadiki.github.io/pywhispercpp/#pywhispercpp.constants.PARAMS_SCHEMA) as a keyword argument to the `Model` class or to the `transcribe` function.
* Check the [Model](https://absadiki.github.io/pywhispercpp/#pywhispercpp.model.Model) class documentation for more details.

# Examples

## CLI
Just a straightforward example Command Line Interface. 
You can use it as follows:

```shell
pwcpp file.wav -m base --output-srt --print_realtime true
```
Run ```pwcpp --help``` to get the help message

```shell
usage: pwcpp [-h] [-m MODEL] [--version] [--processors PROCESSORS] [-otxt] [-ovtt] [-osrt] [-ocsv] [--strategy STRATEGY]
             [--n_threads N_THREADS] [--n_max_text_ctx N_MAX_TEXT_CTX] [--offset_ms OFFSET_MS] [--duration_ms DURATION_MS]
             [--translate TRANSLATE] [--no_context NO_CONTEXT] [--single_segment SINGLE_SEGMENT] [--print_special PRINT_SPECIAL]
             [--print_progress PRINT_PROGRESS] [--print_realtime PRINT_REALTIME] [--print_timestamps PRINT_TIMESTAMPS]
             [--token_timestamps TOKEN_TIMESTAMPS] [--thold_pt THOLD_PT] [--thold_ptsum THOLD_PTSUM] [--max_len MAX_LEN]
             [--split_on_word SPLIT_ON_WORD] [--max_tokens MAX_TOKENS] [--audio_ctx AUDIO_CTX]
             [--prompt_tokens PROMPT_TOKENS] [--prompt_n_tokens PROMPT_N_TOKENS] [--language LANGUAGE] [--suppress_blank SUPPRESS_BLANK]
             [--suppress_non_speech_tokens SUPPRESS_NON_SPEECH_TOKENS] [--temperature TEMPERATURE] [--max_initial_ts MAX_INITIAL_TS]
             [--length_penalty LENGTH_PENALTY] [--temperature_inc TEMPERATURE_INC] [--entropy_thold ENTROPY_THOLD]
             [--logprob_thold LOGPROB_THOLD] [--no_speech_thold NO_SPEECH_THOLD] [--greedy GREEDY] [--beam_search BEAM_SEARCH]
             media_file [media_file ...]

positional arguments:
  media_file            The path of the media file or a list of filesseparated by space

options:
  -h, --help            show this help message and exit
  -m MODEL, --model MODEL
                        Path to the `ggml` model, or just the model name
  --version             show program's version number and exit
  --processors PROCESSORS
                        number of processors to use during computation
  -otxt, --output-txt   output result in a text file
  -ovtt, --output-vtt   output result in a vtt file
  -osrt, --output-srt   output result in a srt file
  -ocsv, --output-csv   output result in a CSV file
  --strategy STRATEGY   Available sampling strategiesGreefyDecoder -> 0BeamSearchDecoder -> 1
  --n_threads N_THREADS
                        Number of threads to allocate for the inferencedefault to min(4, available hardware_concurrency)
  --n_max_text_ctx N_MAX_TEXT_CTX
                        max tokens to use from past text as prompt for the decoder
  --offset_ms OFFSET_MS
                        start offset in ms
  --duration_ms DURATION_MS
                        audio duration to process in ms
  --translate TRANSLATE
                        whether to translate the audio to English
  --no_context NO_CONTEXT
                        do not use past transcription (if any) as initial prompt for the decoder
  --single_segment SINGLE_SEGMENT
                        force single segment output (useful for streaming)
  --print_special PRINT_SPECIAL
                        print special tokens (e.g. <SOT>, <EOT>, <BEG>, etc.)
  --print_progress PRINT_PROGRESS
                        print progress information
  --print_realtime PRINT_REALTIME
                        print results from within whisper.cpp (avoid it, use callback instead)
  --print_timestamps PRINT_TIMESTAMPS
                        print timestamps for each text segment when printing realtime
  --token_timestamps TOKEN_TIMESTAMPS
                        enable token-level timestamps
  --thold_pt THOLD_PT   timestamp token probability threshold (~0.01)
  --thold_ptsum THOLD_PTSUM
                        timestamp token sum probability threshold (~0.01)
  --max_len MAX_LEN     max segment length in characters
  --split_on_word SPLIT_ON_WORD
                        split on word rather than on token (when used with max_len)
  --max_tokens MAX_TOKENS
                        max tokens per segment (0 = no limit)
  --audio_ctx AUDIO_CTX
                        overwrite the audio context size (0 = use default)
  --prompt_tokens PROMPT_TOKENS
                        tokens to provide to the whisper decoder as initial prompt
  --prompt_n_tokens PROMPT_N_TOKENS
                        tokens to provide to the whisper decoder as initial prompt
  --language LANGUAGE   for auto-detection, set to None, "" or "auto"
  --suppress_blank SUPPRESS_BLANK
                        common decoding parameters
  --suppress_non_speech_tokens SUPPRESS_NON_SPEECH_TOKENS
                        common decoding parameters
  --temperature TEMPERATURE
                        initial decoding temperature
  --max_initial_ts MAX_INITIAL_TS
                        max_initial_ts
  --length_penalty LENGTH_PENALTY
                        length_penalty
  --temperature_inc TEMPERATURE_INC
                        temperature_inc
  --entropy_thold ENTROPY_THOLD
                        similar to OpenAI's "compression_ratio_threshold"
  --logprob_thold LOGPROB_THOLD
                        logprob_thold
  --no_speech_thold NO_SPEECH_THOLD
                        no_speech_thold
  --greedy GREEDY       greedy
  --beam_search BEAM_SEARCH
                        beam_search

```

## Assistant

This is a simple example showcasing the use of `pywhispercpp` to create an assistant like example. 
The idea is to use a Voice Activity Detector (VAD) to detect speech (in this example, we used webrtcvad), and when some speech is detected, we run the transcription. 
It is inspired from the [whisper.cpp/examples/command](https://github.com/ggerganov/whisper.cpp/tree/master/examples/command) example.

You can check the source code [here](https://github.com/absadiki/pywhispercpp/blob/main/pywhispercpp/examples/assistant.py) 
or you can use the class directly to create your own assistant:


```python
from pywhispercpp.examples.assistant import Assistant

my_assistant = Assistant(commands_callback=print, n_threads=8)
my_assistant.start()
```
Here, we set the `commands_callback` to a simple print function, so the commands will just get printed on the screen.

You can also run this example from the command line.
```shell
$ pwcpp-assistant --help

usage: pwcpp-assistant [-h] [-m MODEL] [-ind INPUT_DEVICE] [-st SILENCE_THRESHOLD] [-bd BLOCK_DURATION]

options:
  -h, --help            show this help message and exit
  -m MODEL, --model MODEL
                        Whisper.cpp model, default to tiny.en
  -ind INPUT_DEVICE, --input_device INPUT_DEVICE
                        Id of The input device (aka microphone)
  -st SILENCE_THRESHOLD, --silence_threshold SILENCE_THRESHOLD
                        he duration of silence after which the inference will be running, default to 16
  -bd BLOCK_DURATION, --block_duration BLOCK_DURATION
                        minimum time audio updates in ms, default to 30
```
-------------

* Check the [examples folder](https://github.com/absadiki/pywhispercpp/tree/main/pywhispercpp/examples) for more examples.

# Advanced usage
* First check the [API documentation](https://absadiki.github.io/pywhispercpp/) for more advanced usage.
* If you are a more experienced user, you can access the exposed C-APIs directly from the binding module `_pywhispercpp`.

```python
import _pywhispercpp as pwcpp

ctx = pwcpp.whisper_init_from_file('path/to/ggml/model')
```

# Discussions and contributions
If you find any bug, please open an [issue](https://github.com/absadiki/pywhispercpp/issues).

If you have any feedback, or you want to share how you are using this project, feel free to use the [Discussions](https://github.com/absadiki/pywhispercpp/discussions) and open a new topic.

# License

This project is licensed under the same license as [whisper.cpp](https://github.com/ggerganov/whisper.cpp/blob/master/LICENSE) (MIT  [License](./LICENSE)).




================================================
FILE: CMakeLists.txt
================================================
cmake_minimum_required(VERSION 3.4...3.18)
project(pywhispercpp)

add_subdirectory(pybind11)
add_subdirectory(whisper.cpp)

pybind11_add_module(_pywhispercpp
	src/main.cpp
)

target_link_libraries (_pywhispercpp PRIVATE whisper)




================================================
FILE: LICENSE
================================================
MIT License

Copyright (c) 2023 Abdeladim Sadiki

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.



================================================
FILE: MANIFEST.in
================================================
include README.md LICENSE pybind11/LICENSE version.txt
graft pybind11/include
graft pybind11/tools
graft src
global-include CMakeLists.txt *.cmake

graft whisper.cpp/cmake
graft whisper.cpp/ggml
graft whisper.cpp/grammars
graft whisper.cpp/include
graft whisper.cpp/spm-headers
graft whisper.cpp/src
exclude whisper.cpp/**/*.o
exclude whisper.cpp/**/*.so
exclude whisper.cpp/**/*.a
exclude whisper.cpp/**/*.dylib
exclude whisper.cpp/**/*.dll
exclude whisper.cpp/**/*.lib


================================================
FILE: mkdocs.yml
================================================
site_name: PyWhisperCpp
repo_url: https://github.com/absadiki/pywhispercpp
repo_name: absadiki/pywhispercpp
theme:
  name: material
  language: en
  features:
    - navigation.tabs
    - navigation.sections
    - toc.integrate
    - navigation.top
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
  palette:
    - scheme: default
      toggle:
        icon: material/toggle-switch-off-outline
        name: Switch to dark mode
    - scheme: slate
      toggle:
        icon: material/toggle-switch
        name: Switch to light mode


extra:
  social:
    - icon: fontawesome/brands/github-alt
      link: https://github.com/absadiki/pywhispercpp

markdown_extensions:
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - admonition
  - pymdownx.arithmatex:
      generic: true
  - footnotes
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.mark
  - attr_list
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.highlight:
      use_pygments: true
      pygments_lang_class: true

copyright: |
  &copy; 2023 <a href="https://github.com/absadiki"  target="_blank" rel="noopener">absadiki</a>

plugins:
  - search
  - macros:
      include_dir: .
  - mkdocstrings:
      handlers:
        python:
          paths: [src]
          options:
            separate_signature: true
            docstring_style: sphinx
            docstring_section_style: list
            members_order: source
            merge_init_into_class: true
            show_bases: true
            show_if_no_docstring: false
            show_root_full_path: true
            show_root_heading: true
            show_submodules: true
            filters:
              - "!^_"
      watch:
        - pywhispercpp/




================================================
FILE: pyproject.toml
================================================
[build-system]
requires = [
    "setuptools>=42",
    "wheel",
    "ninja",
    "cmake>=3.12",
    "repairwheel",
    "setuptools-scm>=8"
]
build-backend = "setuptools.build_meta"

[tool.mypy]
files = "setup.py"
python_version = "3.8"
strict = true
show_error_codes = true
enable_error_code = ["ignore-without-code", "redundant-expr", "truthy-bool"]
warn_unreachable = true

[[tool.mypy.overrides]]
module = ["ninja"]
ignore_missing_imports = true


[tool.pytest.ini_options]
minversion = "6.0"
addopts = ["-ra", "--showlocals", "--strict-markers", "--strict-config"]
xfail_strict = true
filterwarnings = ["error"]
testpaths = ["tests"]

[tool.cibuildwheel]
#test-command = "pytest {project}/tests"
#test-extras = ["test"]
test-skip = ["*universal2:arm64"]
# Setuptools bug causes collision between pypy and cpython artifacts
before-build = "rm -rf {project}/build"

[tool.ruff]
extend-select = [
  "B",    # flake8-bugbear
  "B904",
  "I",    # isort
  "PGH",  # pygrep-hooks
  "RUF",  # Ruff-specific
  "UP",   # pyupgrade
]
extend-ignore = [
  "E501",   # Line too long
]
target-version = "py39"

[tool.setuptools_scm]
version_file = "_version.py"


================================================
FILE: requirements.txt
================================================
numpy
sounddevice~=0.4.6
webrtcvad~=2.0.10
requests~=2.28.2
tqdm~=4.65.0
platformdirs~=3.1.1


================================================
FILE: setup.py
================================================

# This setup.py is used to build the pywhispercpp package.
# The environment variables you may find interesting are:
#
# PYWHISPERCPP_VERSION 
# if set, it will be used as the version number.
#
# GGML_VULKAN=1
# if set, whisper.cpp will be build with vulkan support.
#
# WHISPER_COREML=1
# WHISPER_COREML_ALLOW_FALLBACK=1
# if set, whisper.cpp will be build with coreml support which requires special models
# It is best used with WHISPER_COREML_ALLOW_FALLBACK=1


import os
import re
import subprocess
import sys
from pathlib import Path
import subprocess

from setuptools import Extension, setup, find_packages
from setuptools.command.build_ext import build_ext
from setuptools.command.bdist_wheel import bdist_wheel
# Convert distutils Windows platform specifiers to CMake -A arguments
PLAT_TO_CMAKE = {
    "win32": "Win32",
    "win-amd64": "x64",
    "win-arm32": "ARM",
    "win-arm64": "ARM64",
}


# A CMakeExtension needs a sourcedir instead of a file list.
# The name must be the _single_ output extension from the CMake build.
# If you need multiple extensions, see scikit-build.
class CMakeExtension(Extension):
    def __init__(self, name: str, sourcedir: str = "") -> None:
        super().__init__(name, sources=[])
        self.sourcedir = os.fspath(Path(sourcedir).resolve())

dll_folder = 'unset'

class CMakeBuild(build_ext):
    def build_extension(self, ext: CMakeExtension) -> None:
        # Must be in this form due to bug in .resolve() only fixed in Python 3.10+
        ext_fullpath = Path.cwd() / self.get_ext_fullpath(ext.name)  # type: ignore[no-untyped-call]
        extdir = ext_fullpath.parent.resolve()

        # Using this requires trailing slash for auto-detection & inclusion of
        # auxiliary "native" libs

        debug = int(os.environ.get("DEBUG", 0)) if self.debug is None else self.debug
        cfg = "Debug" if debug else "Release"

        # CMake lets you override the generator - we need to check this.
        # Can be set with Conda-Build, for example.
        cmake_generator = os.environ.get("CMAKE_GENERATOR", "")

        # Set Python_EXECUTABLE instead if you use PYBIND11_FINDPYTHON
        # EXAMPLE_VERSION_INFO shows you how to pass a value into the C++ code
        # from Python.
        cmake_args = [
            f"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY={extdir}{os.sep}",
            f"-DPYTHON_EXECUTABLE={sys.executable}",
            f"-DCMAKE_BUILD_TYPE={cfg}",  # not used on MSVC, but no harm
        ]
        if self.editable_mode:
            # Platform-specific rpath settings
            if sys.platform.startswith('darwin'):
                # macOS-specific settings
                cmake_args += [
                    "-DCMAKE_INSTALL_RPATH=@loader_path",
                    "-DCMAKE_BUILD_WITH_INSTALL_RPATH=ON"
                ]
            elif sys.platform.startswith('linux'):
                # Linux-specific settings
                cmake_args += [
                    "-DCMAKE_INSTALL_RPATH=$ORIGIN",
                    "-DCMAKE_BUILD_WITH_INSTALL_RPATH=ON"
                ]

        build_args = []
        # Adding CMake arguments set as environment variable
        # (needed e.g. to build for ARM OSx on conda-forge)
        if "CMAKE_ARGS" in os.environ:
            cmake_args += [item for item in os.environ["CMAKE_ARGS"].split(" ") if item]

        # In this example, we pass in the version to C++. You might not need to.
        cmake_args += [f"-DEXAMPLE_VERSION_INFO={self.distribution.get_version()}"]  # type: ignore[attr-defined]

        if self.compiler.compiler_type != "msvc":
            # Using Ninja-build since it a) is available as a wheel and b)
            # multithreads automatically. MSVC would require all variables be
            # exported for Ninja to pick it up, which is a little tricky to do.
            # Users can override the generator with CMAKE_GENERATOR in CMake
            # 3.15+.
            if not cmake_generator or cmake_generator == "Ninja":
                try:
                    import ninja

                    ninja_executable_path = Path(ninja.BIN_DIR) / "ninja"
                    cmake_args += [
                        "-GNinja",
                        f"-DCMAKE_MAKE_PROGRAM:FILEPATH={ninja_executable_path}",
                    ]
                except ImportError:
                    pass

        else:
            # Single config generators are handled "normally"
            single_config = any(x in cmake_generator for x in {"NMake", "Ninja"})

            # CMake allows an arch-in-generator style for backward compatibility
            contains_arch = any(x in cmake_generator for x in {"ARM", "Win64"})

            # Specify the arch if using MSVC generator, but only if it doesn't
            # contain a backward-compatibility arch spec already in the
            # generator name.
            if not single_config and not contains_arch:
                cmake_args += ["-A", PLAT_TO_CMAKE[self.plat_name]]

            # Multi-config generators have a different way to specify configs
            if not single_config:
                cmake_args += [
                    f"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY_{cfg.upper()}={extdir}"
                ]
                build_args += ["--config", cfg]

        if sys.platform.startswith("darwin"):
            # Cross-compile support for macOS - respect ARCHFLAGS if set
            archs = re.findall(r"-arch (\S+)", os.environ.get("ARCHFLAGS", ""))
            if archs:
                cmake_args += ["-DCMAKE_OSX_ARCHITECTURES={}".format(";".join(archs))]
            
        # Set CMAKE_BUILD_PARALLEL_LEVEL to control the parallel build level
        # across all generators.
        if "CMAKE_BUILD_PARALLEL_LEVEL" not in os.environ:
            # self.parallel is a Python 3 only way to set parallel jobs by hand
            # using -j in the build_ext call, not supported by pip or PyPA-build.
            if hasattr(self, "parallel") and self.parallel:
                # CMake 3.12+ only.
                build_args += [f"-j{self.parallel}"]

        build_temp = Path(self.build_temp) / ext.name
        if not build_temp.exists():
            build_temp.mkdir(parents=True)

        for key, value in os.environ.items():
            cmake_args.append(f'-D{key}={value}')

        subprocess.run(
            ["cmake", ext.sourcedir, *cmake_args], cwd=build_temp, check=True
        )
        subprocess.run(
            ["cmake", "--build", ".", *build_args], cwd=build_temp, check=True
        )
    
        # store the dll folder in a global variable to use in repairwheel
        global dll_folder
        cfg = "Debug" if self.debug else "Release"
        dll_folder = os.path.join(self.build_temp, '_pywhispercpp', 'bin', cfg)
        print("dll_folder in build_extension", dll_folder)
        #self.copy_extensions_to_source()

    def copy_extensions_to_source(self):
        super().copy_extensions_to_source()
       
        if self.editable_mode:
            build_lib = Path(self.build_lib)
            for ext in self.extensions:
                extdir = Path(self.get_ext_fullpath(ext.name)).parent.resolve()
                # Assuming all shared libraries are in the same directory
                shared_lib_files = [*build_lib.glob('**/*.dylib'), *build_lib.glob('**/*.so*')]
                for shared_lib in shared_lib_files:
                    self.copy_file(shared_lib, extdir)


# read the contents of your README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()


class RepairWheel(bdist_wheel):
    def run(self):
        super().run()
        if os.environ.get('NO_REPAIR', '0') == '1':
            print("Skipping wheel repair")
            return
        if os.environ.get('CIBUILDWHEEL', '0') == '0' or sys.platform.startswith('win'):
            # for linux and macos we use the default wheel repair command from cibuildwheel, for windows we need to do it manually as there is no repair command
            self.repair_wheel()

    def repair_wheel(self):
        # on windows the dlls are in D:\a\pywhispercpp\pywhispercpp\build\temp.win-amd64-cpython-311\Release\_pywhispercpp\bin\Release\whisper.dll
        global dll_folder
        print("dll_folder in repairwheel",dll_folder) 
        print("Files in dll_folder:", *Path(dll_folder).glob('*'))
        #build\temp.win-amd64-cpython-311\Release\_pywhispercpp\bin\Release\whisper.dll
       
        wheel_path = next(Path(self.dist_dir).glob(f"{self.distribution.get_name()}*.whl"))
        # Create a temporary directory for the repaired wheel
        import tempfile
        with tempfile.TemporaryDirectory(prefix='repaired_wheel_') as tmp_dir:
            tmp_dir = Path(tmp_dir)
            subprocess.call(['repairwheel', wheel_path, '-o', tmp_dir, '-l', dll_folder])
            print("Repaired wheel: ", *tmp_dir.glob('*.whl'))
            # We need to glob as repairwheel may change the name of the wheel 
            # on linux from pywhispercpp-1.2.0-cp312-cp312-linux_aarch64.whl 
            #            to pywhispercpp-1.2.0-cp312-cp312-manylinux_2_34_aarch64.whl
            repaired_wheel = next(tmp_dir.glob("*.whl"))
            self.copy_file(repaired_wheel, wheel_path)
            print(f"Copied repaired wheel to: {wheel_path}")
            
def get_local_version() -> str:
    try:
        git_sha = subprocess.check_output(["git", "rev-parse", "HEAD"]).strip().decode("utf-8")
        return f"+git{git_sha[:7]}"
    except (FileNotFoundError, subprocess.CalledProcessError):
        return ""
    
def get_version() -> str:
    try:
        return os.environ['PYWHISPERCPP_VERSION']
    except KeyError:
        pass
    with open("version.txt") as f:
        version = f.read().strip()
    return f"{version}{get_local_version()}"

# The information here can also be placed in setup.cfg - better separation of
# logic and declaration, and simpler if you include description/version in a file.
setup(
    name="pywhispercpp",
    author="absadiki",
    description="Python bindings for whisper.cpp",
    long_description=long_description,
    ext_modules=[CMakeExtension("_pywhispercpp")],
    cmdclass={"build_ext": CMakeBuild,
             'bdist_wheel': RepairWheel,},
    zip_safe=False,
    # extras_require={"test": ["pytest>=6.0"]},
    python_requires=">=3.8",
    packages=find_packages('.'),
    package_dir={'': '.'},
    include_package_data=True,
    package_data={'pywhispercpp': []},
    long_description_content_type="text/markdown",
    license='MIT',
    entry_points={
        'console_scripts': ['pwcpp=pywhispercpp.examples.main:main',
                            'pwcpp-assistant=pywhispercpp.examples.assistant:_main',
                            'pwcpp-livestream=pywhispercpp.examples.livestream:_main',
                            'pwcpp-recording=pywhispercpp.examples.recording:_main']
    },
    project_urls={
        'Documentation': 'https://absadiki.github.io/pywhispercpp/',
        'Source': 'https://github.com/absadiki/pywhispercpp',
        'Tracker': 'https://github.com/absadiki/pywhispercpp/issues',
    },
    install_requires=['numpy', "requests", "tqdm", "platformdirs"],
    extras_require={"examples": ["sounddevice", "webrtcvad"]},
)



================================================
FILE: .appveyor.yml
================================================
version: '{build}'
image: Visual Studio 2019
platform:
- x86
- x64
environment:
  global:
    DISTUTILS_USE_SDK: 1
    PYTHONWARNINGS: ignore:DEPRECATION
    MSSdk: 1
  matrix:
  - PYTHON: 37
install:
- cmd: '"%VS140COMNTOOLS%\..\..\VC\vcvarsall.bat" %PLATFORM%'
- ps: |
    git submodule update -q --init --recursive
    if ($env:PLATFORM -eq "x64") { $env:PYTHON = "$env:PYTHON-x64" }
    $env:PATH = "C:\Python$env:PYTHON\;C:\Python$env:PYTHON\Scripts\;$env:PATH"
    python -m pip install --disable-pip-version-check --upgrade --no-warn-script-location pip build pytest
build_script:
- ps: |
    python -m build -s
    cd dist
    python -m pip install --verbose cmake_example-0.0.1.tar.gz
    cd ..
test_script:
- ps: python -m pytest



================================================
FILE: .directory
================================================
[Dolphin]
HeaderColumnWidths=499,145,72
Timestamp=2023,3,9,19,49,1.313
Version=4
ViewMode=1

[Settings]
HiddenFilesShown=true



================================================
FILE: .pre-commit-config.yaml
================================================
# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

ci:
  autoupdate_commit_msg: "chore: update pre-commit hooks"
  autofix_commit_msg: "style: pre-commit fixes"

repos:
# Standard hooks
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.4.0
  hooks:
  - id: check-added-large-files
  - id: check-case-conflict
  - id: check-merge-conflict
  - id: check-symlinks
  - id: check-yaml
    exclude: ^conda\.recipe/meta\.yaml$
  - id: debug-statements
  - id: end-of-file-fixer
  - id: mixed-line-ending
  - id: requirements-txt-fixer
  - id: trailing-whitespace

# Black, the code formatter, natively supports pre-commit
- repo: https://github.com/psf/black
  rev: 23.1.0
  hooks:
  - id: black
    exclude: ^(docs)

- repo: https://github.com/charliermarsh/ruff-pre-commit
  rev: "v0.0.253"
  hooks:
    - id: ruff
      args: ["--fix", "--show-fixes"]

# Checking static types
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: "v1.0.1"
  hooks:
    - id: mypy
      files: "setup.py"
      args: []
      additional_dependencies: [types-setuptools]

# Changes tabs to spaces
- repo: https://github.com/Lucas-C/pre-commit-hooks
  rev: v1.4.2
  hooks:
  - id: remove-tabs
    exclude: ^(docs)

# CMake formatting
- repo: https://github.com/cheshirekow/cmake-format-precommit
  rev: v0.6.13
  hooks:
  - id: cmake-format
    additional_dependencies: [pyyaml]
    types: [file]
    files: (\.cmake|CMakeLists.txt)(.in)?$

# Suggested hook if you add a .clang-format file
# - repo: https://github.com/pre-commit/mirrors-clang-format
#  rev: v13.0.0
#  hooks:
#  - id: clang-format



================================================
FILE: docs/index.md
================================================
# PyWhisperCpp API Reference


::: pywhispercpp.model

::: pywhispercpp.constants
    options:
        show_if_no_docstring: true

::: pywhispercpp.utils

::: pywhispercpp.examples
    options:
        show_if_no_docstring: false



================================================
FILE: pywhispercpp/__init__.py
================================================




================================================
FILE: pywhispercpp/constants.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Constants
"""
from pathlib import Path
from typing import Tuple

import _pywhispercpp as _pwcpp
from platformdirs import user_data_dir


WHISPER_SAMPLE_RATE = _pwcpp.WHISPER_SAMPLE_RATE
# MODELS URL MODELS_BASE_URL+ '/' + MODELS_PREFIX_URL+'-'+MODEL_NAME+'.bin'
# example = "https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin"
MODELS_BASE_URL = "https://huggingface.co/ggerganov/whisper.cpp"
MODELS_PREFIX_URL = "resolve/main/ggml"


PACKAGE_NAME = 'pywhispercpp'


MODELS_DIR = Path(user_data_dir(PACKAGE_NAME)) / 'models'


AVAILABLE_MODELS = [
                "base",
                "base-q5_1",
                "base-q8_0",
                "base.en",
                "base.en-q5_1",
                "base.en-q8_0",
                "large-v1",
                "large-v2",
                "large-v2-q5_0",
                "large-v2-q8_0",
                "large-v3",
                "large-v3-q5_0",
                "large-v3-turbo",
                "large-v3-turbo-q5_0",
                "large-v3-turbo-q8_0",
                "medium",
                "medium-q5_0",
                "medium-q8_0",
                "medium.en",
                "medium.en-q5_0",
                "medium.en-q8_0",
                "small",
                "small-q5_1",
                "small-q8_0",
                "small.en",
                "small.en-q5_1",
                "small.en-q8_0",
                "tiny",
                "tiny-q5_1",
                "tiny-q8_0",
                "tiny.en",
                "tiny.en-q5_1",
                "tiny.en-q8_0",
                ]
PARAMS_SCHEMA = {  # as exactly presented in whisper.cpp
    'n_threads': {
            'type': int,
            'description': "Number of threads to allocate for the inference"
                           "default to min(4, available hardware_concurrency)",
            'options': None,
            'default': None
    },
    'n_max_text_ctx': {
            'type': int,
            'description': "max tokens to use from past text as prompt for the decoder",
            'options': None,
            'default': 16384
    },
    'offset_ms': {
            'type': int,
            'description': "start offset in ms",
            'options': None,
            'default': 0
    },
    'duration_ms': {
            'type': int,
            'description': "audio duration to process in ms",
            'options': None,
            'default': 0
    },
    'translate': {
            'type': bool,
            'description': "whether to translate the audio to English",
            'options': None,
            'default': False
    },
    'no_context': {
            'type': bool,
            'description': "do not use past transcription (if any) as initial prompt for the decoder",
            'options': None,
            'default': False
    },
    'single_segment': {
            'type': bool,
            'description': "force single segment output (useful for streaming)",
            'options': None,
            'default': False
    },
    'print_special': {
            'type': bool,
            'description': "print special tokens (e.g. <SOT>, <EOT>, <BEG>, etc.)",
            'options': None,
            'default': False
    },
    'print_progress': {
            'type': bool,
            'description': "print progress information",
            'options': None,
            'default': True
    },
    'print_realtime': {
            'type': bool,
            'description': "print results from within whisper.cpp (avoid it, use callback instead)",
            'options': None,
            'default': False
    },
    'print_timestamps': {
            'type': bool,
            'description': "print timestamps for each text segment when printing realtime",
            'options': None,
            'default': True
    },
    # [EXPERIMENTAL] token-level timestamps
    'token_timestamps': {
            'type': bool,
            'description': "enable token-level timestamps",
            'options': None,
            'default': False
    },
    'thold_pt': {
            'type': float,
            'description': "timestamp token probability threshold (~0.01)",
            'options': None,
            'default': 0.01
    },
    'thold_ptsum': {
            'type': float,
            'description': "timestamp token sum probability threshold (~0.01)",
            'options': None,
            'default': 0.01
    },
    'max_len': {
            'type': int,
            'description': "max segment length in characters, note: token_timestamps needs to be set to True for this to work",
            'options': None,
            'default': 0
    },
    'split_on_word': {
            'type': bool,
            'description': "split on word rather than on token (when used with max_len)",
            'options': None,
            'default': False
    },
    'max_tokens': {
            'type': int,
            'description': "max tokens per segment (0 = no limit)",
            'options': None,
            'default': 0
    },
    'audio_ctx': {
            'type': int,
            'description': "overwrite the audio context size (0 = use default)",
            'options': None,
            'default': 0
    },
    'initial_prompt': {
                'type': str,
                'description': "Initial prompt, these are prepended to any existing text context from a previous call",
                'options': None,
                'default': None
        },
    'prompt_tokens': {
            'type': Tuple,
            'description': "tokens to provide to the whisper decoder as initial prompt",
            'options': None,
            'default': None
    },
    'prompt_n_tokens': {
            'type': int,
            'description': "tokens to provide to the whisper decoder as initial prompt",
            'options': None,
            'default': 0
    },
    'language': {
            'type': str,
            'description': 'for auto-detection, set to None, "" or "auto"',
            'options': None,
            'default': ""
    },
    'suppress_blank': {
            'type': bool,
            'description': 'common decoding parameters',
            'options': None,
            'default': True
    },
    'suppress_non_speech_tokens': {
            'type': bool,
            'description': 'common decoding parameters',
            'options': None,
            'default': False
    },
    'temperature': {
            'type': float,
            'description': 'initial decoding temperature',
            'options': None,
            'default': 0.0
    },
    'max_initial_ts': {
            'type': float,
            'description': 'max_initial_ts',
            'options': None,
            'default': 1.0
    },
    'length_penalty': {
            'type': float,
            'description': 'length_penalty',
            'options': None,
            'default': -1.0
    },
    'temperature_inc': {
            'type': float,
            'description': 'temperature_inc',
            'options': None,
            'default': 0.2
    },
    'entropy_thold': {
            'type': float,
            'description': 'similar to OpenAI\'s "compression_ratio_threshold"',
            'options': None,
            'default': 2.4
    },
    'logprob_thold': {
            'type': float,
            'description': 'logprob_thold',
            'options': None,
            'default': -1.0
    },
    'no_speech_thold': {  # not implemented
            'type': float,
            'description': 'no_speech_thold',
            'options': None,
            'default': 0.6
    },
    'greedy': {
            'type': dict,
            'description': 'greedy',
            'options': None,
            'default': {"best_of": -1}
    },
    'beam_search': {
            'type': dict,
            'description': 'beam_search',
            'options': None,
            'default': {"beam_size": -1, "patience": -1.0}
    }
}



================================================
FILE: pywhispercpp/model.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
This module contains a simple Python API on-top of the C-style
[whisper.cpp](https://github.com/ggerganov/whisper.cpp) API.
"""
import importlib.metadata
import logging
import shutil
import sys
from pathlib import Path
from time import time
from typing import Union, Callable, List, TextIO, Tuple
import _pywhispercpp as pw
import numpy as np
import pywhispercpp.utils as utils
import pywhispercpp.constants as constants
import subprocess
import os
import tempfile
import wave

__author__ = "absadiki"
__copyright__ = "Copyright 2023, "
__license__ = "MIT"
__version__ = importlib.metadata.version('pywhispercpp')

logger = logging.getLogger(__name__)


class Segment:
    """
    A small class representing a transcription segment
    """

    def __init__(self, t0: int, t1: int, text: str):
        """
        :param t0: start time
        :param t1: end time
        :param text: text
        """
        self.t0 = t0
        self.t1 = t1
        self.text = text

    def __str__(self):
        return f"t0={self.t0}, t1={self.t1}, text={self.text}"

    def __repr__(self):
        return str(self)


class Model:
    """
    This classes defines a Whisper.cpp model.

    Example usage.
    ```python
    model = Model('base.en', n_threads=6)
    segments = model.transcribe('file.mp3')
    for segment in segments:
        print(segment.text)
    ```
    """

    _new_segment_callback = None

    def __init__(self,
                 model: str = 'tiny',
                 models_dir: str = None,
                 params_sampling_strategy: int = 0,
                 redirect_whispercpp_logs_to: Union[bool, TextIO, str, None] = False,
                 **params):
        """
        :param model: The name of the model, one of the [AVAILABLE_MODELS](/pywhispercpp/#pywhispercpp.constants.AVAILABLE_MODELS),
                        (default to `tiny`), or a direct path to a `ggml` model.
        :param models_dir: The directory where the models are stored, or where they will be downloaded if they don't
                            exist, default to [MODELS_DIR](/pywhispercpp/#pywhispercpp.constants.MODELS_DIR) <user_data_dir/pywhsipercpp/models>
        :param params_sampling_strategy: 0 -> GREEDY, else BEAM_SEARCH
        :param redirect_whispercpp_logs_to: where to redirect the whisper.cpp logs, default to False (no redirection), accepts str file path, sys.stdout, sys.stderr, or use None to redirect to devnull
        :param params: keyword arguments for different whisper.cpp parameters,
                        see [PARAMS_SCHEMA](/pywhispercpp/#pywhispercpp.constants.PARAMS_SCHEMA)
        """
        if Path(model).is_file():
            self.model_path = model
        else:
            self.model_path = utils.download_model(model, models_dir)
        self._ctx = None
        self._sampling_strategy = pw.whisper_sampling_strategy.WHISPER_SAMPLING_GREEDY if params_sampling_strategy == 0 else \
            pw.whisper_sampling_strategy.WHISPER_SAMPLING_BEAM_SEARCH
        self._params = pw.whisper_full_default_params(self._sampling_strategy)
        # assign params
        self._set_params(params)
        self.redirect_whispercpp_logs_to = redirect_whispercpp_logs_to
        # init the model
        self._init_model()

    def transcribe(self,
                   media: Union[str, np.ndarray],
                   n_processors: int = None,
                   new_segment_callback: Callable[[Segment], None] = None,
                   **params) -> List[Segment]:
        """
        Transcribes the media provided as input and returns list of `Segment` objects.
        Accepts a media_file path (audio/video) or a raw numpy array.

        :param media: Media file path or a numpy array
        :param n_processors: if not None, it will run the transcription on multiple processes
                             binding to whisper.cpp/whisper_full_parallel
                             > Split the input audio in chunks and process each chunk separately using whisper_full()
        :param new_segment_callback: callback function that will be called when a new segment is generated
        :param params: keyword arguments for different whisper.cpp parameters, see ::: constants.PARAMS_SCHEMA

        :return: List of transcription segments
        """
        if type(media) is np.ndarray:
            audio = media
        else:
            if not Path(media).exists():
                raise FileNotFoundError(media)
            audio = self._load_audio(media)
        # update params if any
        self._set_params(params)

        # setting up callback
        if new_segment_callback:
            Model._new_segment_callback = new_segment_callback
            pw.assign_new_segment_callback(self._params, Model.__call_new_segment_callback)

        # run inference
        start_time = time()
        logger.info("Transcribing ...")
        res = self._transcribe(audio, n_processors=n_processors)
        end_time = time()
        logger.info(f"Inference time: {end_time - start_time:.3f} s")
        return res

    @staticmethod
    def _get_segments(ctx, start: int, end: int) -> List[Segment]:
        """
        Helper function to get generated segments between `start` and `end`

        :param start: start index
        :param end: end index

        :return: list of segments
        """
        n = pw.whisper_full_n_segments(ctx)
        assert end <= n, f"{end} > {n}: `End` index must be less or equal than the total number of segments"
        res = []
        for i in range(start, end):
            t0 = pw.whisper_full_get_segment_t0(ctx, i)
            t1 = pw.whisper_full_get_segment_t1(ctx, i)
            bytes = pw.whisper_full_get_segment_text(ctx, i)
            text = bytes.decode('utf-8',errors='replace')
            res.append(Segment(t0, t1, text.strip()))
        return res

    def get_params(self) -> dict:
        """
        Returns a `dict` representation of the actual params

        :return: params dict
        """
        res = {}
        for param in dir(self._params):
            if param.startswith('__'):
                continue
            try:
                res[param] = getattr(self._params, param)
            except Exception:
                # ignore callback functions
                continue
        return res

    @staticmethod
    def get_params_schema() -> dict:
        """
        A simple link to ::: constants.PARAMS_SCHEMA
        :return: dict of params schema
        """
        return constants.PARAMS_SCHEMA

    @staticmethod
    def lang_max_id() -> int:
        """
        Returns number of supported languages.
        Direct binding to whisper.cpp/lang_max_id
        :return:
        """
        return pw.whisper_lang_max_id()

    def print_timings(self) -> None:
        """
        Direct binding to whisper.cpp/whisper_print_timings

        :return: None
        """
        pw.whisper_print_timings(self._ctx)

    @staticmethod
    def system_info() -> None:
        """
        Direct binding to whisper.cpp/whisper_print_system_info

        :return: None
        """
        return pw.whisper_print_system_info()

    @staticmethod
    def available_languages() -> list[str]:
        """
        Returns a list of supported language codes

        :return: list of supported language codes
        """
        n = pw.whisper_lang_max_id()
        res = []
        for i in range(n):
            res.append(pw.whisper_lang_str(i))
        return res

    def _init_model(self) -> None:
        """
        Private method to initialize the method from the bindings, it will be called automatically from the __init__
        :return:
        """
        logger.info("Initializing the model ...")
        with utils.redirect_stderr(to=self.redirect_whispercpp_logs_to):
            self._ctx = pw.whisper_init_from_file(self.model_path)

    def _set_params(self, kwargs: dict) -> None:
        """
        Private method to set the kwargs params to the `Params` class
        :param kwargs: dict like object for the different params
        :return: None
        """
        for param in kwargs:
            setattr(self._params, param, kwargs[param])

    def _transcribe(self, audio: np.ndarray, n_processors: int = None):
        """
        Private method to call the whisper.cpp/whisper_full function
    
        :param audio: numpy array of audio data
        :param n_processors: if not None, it will run whisper.cpp/whisper_full_parallel with n_processors
        :return:
        """

        if n_processors:
            pw.whisper_full_parallel(self._ctx, self._params, audio, audio.size, n_processors)
        else:
            pw.whisper_full(self._ctx, self._params, audio, audio.size)
        n = pw.whisper_full_n_segments(self._ctx)
        res = Model._get_segments(self._ctx, 0, n)
        return res

    @staticmethod
    def __call_new_segment_callback(ctx, n_new, user_data) -> None:
        """
        Internal new_segment_callback, it just calls the user's callback with the `Segment` object
        :param ctx: whisper.cpp ctx param
        :param n_new: whisper.cpp n_new param
        :param user_data: whisper.cpp user_data param
        :return: None
        """
        n = pw.whisper_full_n_segments(ctx)
        start = n - n_new
        res = Model._get_segments(ctx, start, n)
        for segment in res:
            Model._new_segment_callback(segment)

    @staticmethod
    def _load_audio(media_file_path: str) -> np.array:
        """
         Helper method to return a `np.array` object from a media file
         If the media file is not a WAV file, it will try to convert it using ffmpeg

        :param media_file_path: Path of the media file
        :return: Numpy array
        """

        def wav_to_np(file_path):
            with wave.open(file_path, 'rb') as wf:
                num_channels = wf.getnchannels()
                sample_width = wf.getsampwidth()
                sample_rate = wf.getframerate()
                num_frames = wf.getnframes()

                if num_channels not in (1, 2):
                    raise Exception(f"WAV file must be mono or stereo")

                if sample_rate != pw.WHISPER_SAMPLE_RATE:
                    raise Exception(f"WAV file must be {pw.WHISPER_SAMPLE_RATE} Hz")

                if sample_width != 2:
                    raise Exception(f"WAV file must be 16-bit")

                raw = wf.readframes(num_frames)
                wf.close()
                audio = np.frombuffer(raw, dtype=np.int16).astype(np.float32)
                n = num_frames
                if num_channels == 1:
                    pcmf32 = audio / 32768.0
                else:
                    audio = audio.reshape(-1, 2)
                    # Averaging the two channels
                    pcmf32 = (audio[:, 0] + audio[:, 1]) / 65536.0
                return pcmf32

        if media_file_path.endswith('.wav'):
            return wav_to_np(media_file_path)
        else:
            if shutil.which('ffmpeg') is None:
                raise Exception(
                    "FFMPEG is not installed or not in PATH. Please install it, or provide a WAV file or a NumPy array instead!")

            temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_file_path = temp_file.name
            temp_file.close()
            try:
                subprocess.run([
                    'ffmpeg', '-i', media_file_path, '-ac', '1', '-ar', '16000',
                    temp_file_path, '-y'
                ], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                return wav_to_np(temp_file_path)
            finally:
                os.remove(temp_file_path)

    def auto_detect_language(self,  media: Union[str, np.ndarray], offset_ms: int = 0, n_threads: int = 4) -> Tuple[Tuple[str, np.float32], dict[str, np.float32]]:
        """
        Automatic language detection using whisper.cpp/whisper_pcm_to_mel and whisper.cpp/whisper_lang_auto_detect

        :param media: Media file path or a numpy array
        :param offset_ms: offset in milliseconds
        :param n_threads: number of threads to use
        :return: ((detected_language, probability), probabilities for all languages)
        """
        if type(media) is np.ndarray:
            audio = media
        else:
            if not Path(media).exists():
                raise FileNotFoundError(media)
            audio = self._load_audio(media)

        pw.whisper_pcm_to_mel(self._ctx, audio, len(audio), n_threads)
        lang_max_id = self.lang_max_id()
        probs = np.zeros(lang_max_id, dtype=np.float32)
        auto_detect = pw.whisper_lang_auto_detect(self._ctx, offset_ms, n_threads, probs)
        langs = self.available_languages()
        lang_probs = {langs[i]: probs[i] for i in range(lang_max_id)}
        return (langs[auto_detect], probs[auto_detect]), lang_probs

    def __del__(self):
        """
        Free up resources
        :return: None
        """
        pw.whisper_free(self._ctx)


================================================
FILE: pywhispercpp/utils.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Helper functions
"""
import contextlib
import logging
import os
import sys
from pathlib import Path
import requests
from tqdm import tqdm
from pywhispercpp.constants import MODELS_BASE_URL, MODELS_PREFIX_URL, AVAILABLE_MODELS, MODELS_DIR


logger = logging.getLogger(__name__)


def _get_model_url(model_name: str) -> str:
    """
    Returns the url of the `ggml` model
    :param model_name: name of the model
    :return: URL of the model
    """
    return f"{MODELS_BASE_URL}/{MODELS_PREFIX_URL}-{model_name}.bin"


def download_model(model_name: str, download_dir=None, chunk_size=1024) -> str:
    """
    Helper function to download the `ggml` models
    :param model_name: name of the model, one of ::: constants.AVAILABLE_MODELS
    :param download_dir: Where to store the models
    :param chunk_size: size of the download chunk

    :return: Absolute path of the downloaded model
    """
    if model_name not in AVAILABLE_MODELS:
        logger.error(f"Invalid model name `{model_name}`, available models are: {AVAILABLE_MODELS}")
        return
    if download_dir is None:
        download_dir = MODELS_DIR
        logger.info(f"No download directory was provided, models will be downloaded to {download_dir}")

    os.makedirs(download_dir, exist_ok=True)

    url = _get_model_url(model_name=model_name)
    file_path = Path(download_dir) / os.path.basename(url)
    # check if the file is already there
    if file_path.exists():
        logger.info(f"Model {model_name} already exists in {download_dir}")
    else:
        # download it from huggingface
        resp = requests.get(url, stream=True)
        total = int(resp.headers.get('content-length', 0))

        progress_bar = tqdm(desc=f"Downloading Model {model_name} ...",
                            total=total,
                            unit='iB',
                            unit_scale=True,
                            unit_divisor=1024)

        try:
            with open(file_path, 'wb') as file, progress_bar:
                for data in resp.iter_content(chunk_size=chunk_size):
                    size = file.write(data)
                    progress_bar.update(size)
            logger.info(f"Model downloaded to {file_path.absolute()}")
        except Exception as e:
            # error download, just remove the file
            os.remove(file_path)
            raise e
    return str(file_path.absolute())


def to_timestamp(t: int, separator=',') -> str:
    """
    376 -> 00:00:03,760
    1344 -> 00:00:13,440

    Implementation from `whisper.cpp/examples/main`

    :param t: input time from whisper timestamps
    :param separator: seprator between seconds and milliseconds
    :return: time representation in hh: mm: ss[separator]ms
    """
    # logic exactly from whisper.cpp

    msec = t * 10
    hr = msec // (1000 * 60 * 60)
    msec = msec - hr * (1000 * 60 * 60)
    min = msec // (1000 * 60)
    msec = msec - min * (1000 * 60)
    sec = msec // 1000
    msec = msec - sec * 1000
    return f"{int(hr):02,.0f}:{int(min):02,.0f}:{int(sec):02,.0f}{separator}{int(msec):03,.0f}"


def output_txt(segments: list, output_file_path: str) -> str:
    """
    Creates a raw text from a list of segments

    Implementation from `whisper.cpp/examples/main`

    :param segments: list of segments
    :return: path of the file
    """
    if not output_file_path.endswith('.txt'):
        output_file_path = output_file_path + '.txt'

    absolute_path = Path(output_file_path).absolute()

    with open(str(absolute_path), 'w') as file:
        for seg in segments:
            file.write(seg.text)
            file.write('\n')
    return absolute_path


def output_vtt(segments: list, output_file_path: str) -> str:
    """
    Creates a vtt file from a list of segments

    Implementation from `whisper.cpp/examples/main`

    :param segments: list of segments
    :return: path of the file

    :return: Absolute path of the file
    """
    if not output_file_path.endswith('.vtt'):
        output_file_path = output_file_path + '.vtt'

    absolute_path = Path(output_file_path).absolute()

    with open(absolute_path, 'w') as file:
        file.write("WEBVTT\n\n")
        for seg in segments:
            file.write(f"{to_timestamp(seg.t0, separator='.')} --> {to_timestamp(seg.t1, separator='.')}\n")
            file.write(f"{seg.text}\n\n")
    return absolute_path


def output_srt(segments: list, output_file_path: str) -> str:
    """
    Creates a srt file from a list of segments

    :param segments: list of segments
    :return: path of the file

    :return: Absolute path of the file
    """
    if not output_file_path.endswith('.srt'):
        output_file_path = output_file_path + '.srt'

    absolute_path = Path(output_file_path).absolute()

    with open(absolute_path, 'w') as file:
        for i in range(len(segments)):
            seg = segments[i]
            file.write(f"{i+1}\n")
            file.write(f"{to_timestamp(seg.t0, separator=',')} --> {to_timestamp(seg.t1, separator=',')}\n")
            file.write(f"{seg.text}\n\n")
    return absolute_path


def output_csv(segments: list, output_file_path: str) -> str:
    """
    Creates a srt file from a list of segments

    :param segments: list of segments
    :return: path of the file

    :return: Absolute path of the file
    """
    if not output_file_path.endswith('.csv'):
        output_file_path = output_file_path + '.csv'

    absolute_path = Path(output_file_path).absolute()

    with open(absolute_path, 'w') as file:
        for seg in segments:
            file.write(f"{10 * seg.t0}, {10 * seg.t1}, \"{seg.text}\"\n")
    return absolute_path


@contextlib.contextmanager
def redirect_stderr(to=False) -> None:
    """
    Redirect stderr to the specified target.

    :param to:
        - None to suppress output (redirect to devnull),
        - sys.stdout to redirect to stdout,
        - A file path (str) to redirect to a file,
        - False to do nothing (no redirection).
    """

    if to is False:
        # do nothing
        yield
        return

    sys.stderr.flush()
    try:
        original_stderr_fd = sys.stderr.fileno()
        has_fileno = True
    except (AttributeError, OSError):
        # Jupyter or non-standard stderr implementations
        has_fileno = False

    if has_fileno:
        if to is None:
            target_fd = os.open(os.devnull, os.O_WRONLY)
        elif isinstance(to, str):
            file = open(to, 'w')
            target_fd = file.fileno()
        elif hasattr(to, 'fileno'):
            target_fd = to.fileno()
        else:
            raise ValueError("Invalid `to` parameter; must be None, a filepath string, or sys.stdout/sys.stderr.")
        os.dup2(target_fd, original_stderr_fd)
        try:
            yield
        finally:
            os.dup2(original_stderr_fd, original_stderr_fd)
            if isinstance(to, str):
                file.close()
            elif to is None:
                os.close(target_fd)
    else:
        # Replace sys.stderr directly
        original_stderr = sys.stderr
        if to is None:
            sys.stderr = open(os.devnull, 'w')
        elif isinstance(to, str):
            sys.stderr = open(to, 'w')
        elif hasattr(to, 'write'):
            sys.stderr = to
        try:
            yield
        finally:
            sys.stderr = original_stderr
            if isinstance(to, str) or to is None:
                sys.stderr.close()



================================================
FILE: pywhispercpp/examples/__init__.py
================================================



================================================
FILE: pywhispercpp/examples/assistant.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
A simple example showcasing the use of `pywhispercpp` as an assistant.
The idea is to use a `VAD` to detect speech (in this example we used webrtcvad), and when speech is detected
we run the inference.
"""
import argparse
import importlib.metadata
import queue
import time
from typing import Callable
import numpy as np
import sounddevice as sd
import pywhispercpp.constants as constants
import webrtcvad
import logging
from pywhispercpp.model import Model

__version__ = importlib.metadata.version('pywhispercpp')

__header__ = f"""
=====================================
PyWhisperCpp
A simple assistant using Whisper.cpp
Version: {__version__}               
=====================================
"""


class Assistant:
    """
    Assistant class

    Example usage
    ```python
    from pywhispercpp.examples.assistant import Assistant

    my_assistant = Assistant(commands_callback=print, n_threads=8)
    my_assistant.start()
    ```
    """

    def __init__(self,
                 model='tiny',
                 input_device: int = None,
                 silence_threshold: int = 8,
                 q_threshold: int = 16,
                 block_duration: int = 30,
                 commands_callback: Callable[[str], None] = None,
                 **model_params):

        """
        :param model: whisper.cpp model name or a direct path to a`ggml` model
        :param input_device: The input device (aka microphone), keep it None to take the default
        :param silence_threshold: The duration of silence after which the inference will be running
        :param q_threshold: The inference won't be running until the data queue is having at least `q_threshold` elements
        :param block_duration: minimum time audio updates in ms
        :param commands_callback: The callback to run when a command is received
        :param model_log_level: Logging level
        :param model_params: any other parameter to pass to the whsiper.cpp model see ::: pywhispercpp.constants.PARAMS_SCHEMA
        """

        self.input_device = input_device
        self.sample_rate = constants.WHISPER_SAMPLE_RATE  # same as whisper.cpp
        self.channels = 1  # same as whisper.cpp
        self.block_duration = block_duration
        self.block_size = int(self.sample_rate * self.block_duration / 1000)
        self.q = queue.Queue()

        self.vad = webrtcvad.Vad()
        self.silence_threshold = silence_threshold
        self.q_threshold = q_threshold
        self._silence_counter = 0

        self.pwccp_model = Model(model,
                                 print_realtime=False,
                                 print_progress=False,
                                 print_timestamps=False,
                                 single_segment=True,
                                 no_context=True,
                                 **model_params)
        self.commands_callback = commands_callback

    def _audio_callback(self, indata, frames, time, status):
        """
        This is called (from a separate thread) for each audio block.
        """
        if status:
            logging.warning(F"underlying audio stack warning:{status}")

        assert frames == self.block_size
        audio_data = map(lambda x: (x + 1) / 2, indata)  # normalize from [-1,+1] to [0,1]
        audio_data = np.fromiter(audio_data, np.float16)
        audio_data = audio_data.tobytes()
        detection = self.vad.is_speech(audio_data, self.sample_rate)
        if detection:
            self.q.put(indata.copy())
            self._silence_counter = 0
        else:
            if self._silence_counter >= self.silence_threshold:
                if self.q.qsize() > self.q_threshold:
                    self._transcribe_speech()
                    self._silence_counter = 0
            else:
                self._silence_counter += 1

    def _transcribe_speech(self):
        logging.info(f"Speech detected ...")
        audio_data = np.array([])
        while self.q.qsize() > 0:
            # get all the data from the q
            audio_data = np.append(audio_data, self.q.get())
        # Appending zeros to the audio data as a workaround for small audio packets (small commands)
        audio_data = np.concatenate([audio_data, np.zeros((int(self.sample_rate) + 10))])
        # running the inference
        self.pwccp_model.transcribe(audio_data,
                                    new_segment_callback=self._new_segment_callback)

    def _new_segment_callback(self, seg):
        if self.commands_callback:
            self.commands_callback(seg.text)

    def start(self) -> None:
        """
        Use this function to start the assistant
        :return: None
        """
        logging.info(f"Starting Assistant ...")
        with sd.InputStream(
                device=self.input_device,  # the default input device
                channels=self.channels,
                samplerate=constants.WHISPER_SAMPLE_RATE,
                blocksize=self.block_size,
                callback=self._audio_callback):

            try:
                logging.info(f"Assistant is listening ... (CTRL+C to stop)")
                while True:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                logging.info("Assistant stopped")

    @staticmethod
    def available_devices():
        return sd.query_devices()


def _main():
    parser = argparse.ArgumentParser(description="", allow_abbrev=True)
    # Positional args
    parser.add_argument('-m', '--model', default='tiny.en', type=str, help="Whisper.cpp model, default to %(default)s")
    parser.add_argument('-ind', '--input_device', type=int, default=None,
                        help=f'Id of The input device (aka microphone)\n'
                             f'available devices {Assistant.available_devices()}')
    parser.add_argument('-st', '--silence_threshold', default=16, type=int,
                        help=f"he duration of silence after which the inference will be running, default to %(default)s")
    parser.add_argument('-bd', '--block_duration', default=30,
                        help=f"minimum time audio updates in ms, default to %(default)s")

    args = parser.parse_args()

    my_assistant = Assistant(model=args.model,
                             input_device=args.input_device,
                             silence_threshold=args.silence_threshold,
                             block_duration=args.block_duration,
                             commands_callback=print)
    my_assistant.start()


if __name__ == '__main__':
    _main()



================================================
FILE: pywhispercpp/examples/livestream.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Quick and dirty realtime livestream transcription.

Not fully satisfying though :)
You are welcome to make it better.
"""
import argparse
import logging
import queue
from multiprocessing import Process
import ffmpeg
import numpy as np
import pywhispercpp.constants as constants
import sounddevice as sd
from pywhispercpp.model import Model
import importlib.metadata


__version__ = importlib.metadata.version('pywhispercpp')

__header__ = f"""
========================================================
PyWhisperCpp
A simple Livestream transcription, based on whisper.cpp
Version: {__version__}               
========================================================
"""

class LiveStream:
    """
    LiveStream class

    ???+ note

        It heavily depends on the machine power, the processor will jump quickly to 100% with the wrong parameters.

    Example usage
    ```python
    from pywhispercpp.examples.livestream import LiveStream

    url = ""  # Make sure it is a direct stream URL
    ls = LiveStream(url=url, n_threads=4)
    ls.start()
    ```
    """

    def __init__(self,
                 url,
                 model='tiny.en',
                 block_size: int = 1024,
                 buffer_size: int = 20,
                 sample_size: int = 4,
                 output_device: int = None,
                 model_log_level=logging.CRITICAL,
                 **model_params):

        """
        :param url: Live stream url <a direct stream URL>
        :param model: whisper.cpp model
        :param block_size: block size, default to 1024
        :param buffer_size: number of blocks used for buffering, default to 20
        :param sample_size: sample size
        :param output_device: the output device, aka the speaker, leave it None to take the default
        :param model_log_level: logging level
        :param model_params: any other whisper.cpp params
        """
        self.url = url
        self.block_size = block_size
        self.buffer_size = buffer_size
        self.sample_size = sample_size
        self.output_device = output_device

        self.channels = 1
        self.samplerate = constants.WHISPER_SAMPLE_RATE

        self.q = queue.Queue(maxsize=buffer_size)
        self.audio_data = np.array([])

        self.pwccp_model = Model(model,
                                 log_level=model_log_level,
                                 print_realtime=True,
                                 print_progress=False,
                                 print_timestamps=False,
                                 single_segment=True,
                                 **model_params)

    def _transcribe_process(self):
        self.pwccp_model.transcribe(self.audio_data, n_processors=None)

    def _audio_callback(self, outdata, frames, time, status):
        assert frames == self.block_size
        if status.output_underflow:
            logging.error('Output underflow: increase blocksize?')
            raise sd.CallbackAbort
        assert not status
        try:
            data = self.q.get_nowait()
        except queue.Empty as e:
            logging.error('Buffer is empty: increase buffer_size?')
            raise sd.CallbackAbort from e
        assert len(data) == len(outdata)
        outdata[:] = data
        audio = np.frombuffer(data[:], np.float32)
        audio = audio.reshape((audio.size, 1)) / 2 ** 5
        self.audio_data = np.append(self.audio_data, audio)
        if self.audio_data.size > self.samplerate:
            # Create a separate process for transcription
            p1 = Process(target=self._transcribe_process,)
            p1.start()
            self.audio_data = np.array([])

    def start(self):
        process = ffmpeg.input(self.url).output(
            'pipe:',
            format='f32le',
            acodec='pcm_f32le',
            ac=self.channels,
            ar=self.samplerate,
            loglevel='quiet',
        ).run_async(pipe_stdout=True)

        out_stream = sd.RawOutputStream(
            device=self.output_device,
            samplerate=self.samplerate,
            blocksize=self.block_size,
            channels=self.channels,
            dtype='float32',
            callback=self._audio_callback)

        read_size = self.block_size * self.channels * self.sample_size

        logging.info('Buffering ...')
        for _ in range(self.buffer_size):
            self.q.put_nowait(process.stdout.read(read_size))

        with out_stream:
            logging.info('Starting Playback ... (CTRL+C) to stop')
            try:
                timeout = self.block_size * self.buffer_size / self.samplerate
                while True:
                    buffer_data = process.stdout.read(read_size)
                    self.q.put(buffer_data, timeout=timeout)
            except KeyboardInterrupt:
                logging.info("Interrupted!")

    @staticmethod
    def available_devices():
        return sd.query_devices()


def _main():
    print(__header__)
    parser = argparse.ArgumentParser(description="", allow_abbrev=True)
    # Positional args
    parser.add_argument('url', type=str, help=f"Stream URL")

    parser.add_argument('-nt', '--n_threads', type=int, default=3,
                        help="number of threads, default to %(default)s")
    parser.add_argument('-m', '--model', default='tiny.en', type=str, help="Whisper.cpp model, default to %(default)s")
    parser.add_argument('-od', '--output_device', type=int, default=None,
                        help=f'the output device, aka the speaker, leave it None to take the default\n'
                             f'available devices {LiveStream.available_devices()}')
    parser.add_argument('-bls', '--block_size', type=int, default=1024,
                        help=f"block size, default to %(default)s")
    parser.add_argument('-bus', '--buffer_size', type=int, default=20,
                        help=f"number of blocks used for buffering, default to %(default)s")
    parser.add_argument('-ss', '--sample_size', type=int, default=4,
                        help=f"Sample size, default to %(default)s")
    args = parser.parse_args()


    # url = "http://n03.radiojar.com/t2n88q0st5quv?rj-ttl=5&rj-tok=AAABhsR2u6MAYFxz69dJ6eQnww"  # VOA english
    ls = LiveStream(url=args.url,
                    model=args.model,
                    block_size=args.block_size,
                    buffer_size=args.buffer_size,
                    sample_size=args.sample_size,
                    output_device=args.output_device,
                    n_threads=args.n_threads)
    ls.start()


if __name__ == '__main__':
    _main()



================================================
FILE: pywhispercpp/examples/main.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
A simple Command Line Interface to test the package
"""
import argparse
import importlib.metadata
import logging

import pywhispercpp.constants as constants

__version__ = importlib.metadata.version('pywhispercpp')

__header__ = f"""
PyWhisperCpp
A simple Command Line Interface to test the package
Version: {__version__}               
====================================================
"""

from pywhispercpp.model import Model
import pywhispercpp.utils as utils


def _get_params(args) -> dict:
    """
    Helper function to get params from argparse as a `dict`
    """
    params = {}
    for arg in args.__dict__:
        if arg in constants.PARAMS_SCHEMA.keys() and getattr(args, arg) is not None:
            if constants.PARAMS_SCHEMA[arg]['type'] is bool:
                if getattr(args, arg).lower() == 'false':
                    params[arg] = False
                else:
                    params[arg] = True
            else:
                params[arg] = constants.PARAMS_SCHEMA[arg]['type'](getattr(args, arg))
    return params


def run(args):
    logging.info(f"Running with model `{args.model}`")
    params = _get_params(args)
    logging.info(f"Running with params {params}")
    m = Model(model=args.model, **params)
    logging.info(f"System info: n_threads = {m.get_params()['n_threads']} | Processors = {args.processors} "
                 f"| {m.system_info()}")
    for file in args.media_file:
        logging.info(f"Processing file {file} ...")
        segs = m.transcribe(file, n_processors=int(args.processors) if args.processors else None)
        m.print_timings()
        # output stuff
        if args.output_txt:
            logging.info(f"Saving result as a txt file ...")
            txt_file = utils.output_txt(segs, file)
            logging.info(f"txt file saved to {txt_file}")
        if args.output_vtt:
            logging.info(f"Saving results as a vtt file ...")
            vtt_file = utils.output_vtt(segs, file)
            logging.info(f"vtt file saved to {vtt_file}")
        if args.output_srt:
            logging.info(f"Saving results as a srt file ...")
            srt_file = utils.output_srt(segs, file)
            logging.info(f"srt file saved to {srt_file}")
        if args.output_csv:
            logging.info(f"Saving results as a csv file ...")
            csv_file = utils.output_csv(segs, file)
            logging.info(f"csv file saved to {csv_file}")


def main():
    print(__header__)
    parser = argparse.ArgumentParser(description="", allow_abbrev=True)
    # Positional args
    parser.add_argument('media_file', type=str, nargs='+', help="The path of the media file or a list of files"
                                                                "separated by space")

    parser.add_argument('-m', '--model', default='tiny', help="Path to the `ggml` model, or just the model name")

    parser.add_argument('--version', action='version', version=f'%(prog)s {__version__}')
    parser.add_argument('--processors', help="number of processors to use during computation")
    parser.add_argument('-otxt', '--output-txt', action='store_true', help="output result in a text file")
    parser.add_argument('-ovtt', '--output-vtt', action='store_true', help="output result in a vtt file")
    parser.add_argument('-osrt', '--output-srt', action='store_true', help="output result in a srt file")
    parser.add_argument('-ocsv', '--output-csv', action='store_true', help="output result in a CSV file")

    # add params from PARAMS_SCHEMA
    for param in constants.PARAMS_SCHEMA:
        param_fields = constants.PARAMS_SCHEMA[param]
        parser.add_argument(f'--{param}',
                            help=f'{param_fields["description"]}')

    args = parser.parse_args()
    run(args)


if __name__ == '__main__':
    main()



================================================
FILE: pywhispercpp/examples/recording.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
A simple example showcasing how to use pywhispercpp to transcribe a recording.
"""
import argparse
import logging
import sounddevice as sd
import pywhispercpp.constants
from pywhispercpp.model import Model
import importlib.metadata


__version__ = importlib.metadata.version('pywhispercpp')

__header__ = f"""
===================================================================
PyWhisperCpp
A simple example of transcribing a recording, based on whisper.cpp
Version: {__version__}               
===================================================================
"""


class Recording:
    """
    Recording class

    Example usage
    ```python
    from pywhispercpp.examples.recording import Recording

    myrec = Recording(5)
    myrec.start()
    ```
    """
    def __init__(self,
                 duration: int,
                 model: str = 'tiny.en',
                 **model_params):
        self.duration = duration
        self.sample_rate = pywhispercpp.constants.WHISPER_SAMPLE_RATE
        self.channels = 1
        self.pwcpp_model = Model(model, print_realtime=True, **model_params)

    def start(self):
        logging.info(f"Start recording for {self.duration}s ...")
        recording = sd.rec(int(self.duration * self.sample_rate), samplerate=self.sample_rate, channels=self.channels)
        sd.wait()
        logging.info('Duration finished')
        res = self.pwcpp_model.transcribe(recording)
        self.pwcpp_model.print_timings()


def _main():
    print(__header__)
    parser = argparse.ArgumentParser(description="", allow_abbrev=True)
    # Positional args
    parser.add_argument('duration', type=int, help=f"duration in seconds")
    parser.add_argument('-m', '--model', default='tiny.en', type=str, help="Whisper.cpp model, default to %(default)s")

    args = parser.parse_args()

    myrec = Recording(duration=args.duration, model=args.model)
    myrec.start()


if __name__ == '__main__':
    _main()



================================================
FILE: src/main.cpp
================================================
/**
 ********************************************************************************
 * @file    main.cpp
 * <AUTHOR>
 * @date    2023
 * @brief   Python bindings for [whisper.cpp](https://github.com/ggerganov/whisper.cpp) using Pybind11
 *
 * @par
 * COPYRIGHT NOTICE: (c) 2023.  All rights reserved.
 ********************************************************************************
 */

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/functional.h>
#include <pybind11/numpy.h>

#include "whisper.h"


#define STRINGIFY(x) #x
#define MACRO_STRINGIFY(x) STRINGIFY(x)


namespace py = pybind11;
using namespace pybind11::literals; // to bring in the `_a` literal


py::function py_new_segment_callback;
py::function py_encoder_begin_callback;
py::function py_logits_filter_callback;


// whisper context wrapper, to solve the incomplete type issue
// Thanks to https://github.com/pybind/pybind11/issues/2770
struct whisper_context_wrapper {
    whisper_context* ptr;

};


// struct inside params
struct greedy{
    int best_of;
};

struct beam_search{
    int beam_size;
    float patience;
};


struct whisper_model_loader_wrapper {
    whisper_model_loader* ptr;

};

struct whisper_context_wrapper whisper_init_from_file_wrapper(const char * path_model){
    struct whisper_context * ctx = whisper_init_from_file(path_model);
    struct whisper_context_wrapper ctw_w;
    ctw_w.ptr = ctx;
    return ctw_w;
}

struct whisper_context_wrapper whisper_init_from_buffer_wrapper(void * buffer, size_t buffer_size){
    struct whisper_context * ctx = whisper_init_from_buffer(buffer, buffer_size);
    struct whisper_context_wrapper ctw_w;
    ctw_w.ptr = ctx;
    return ctw_w;
}

struct whisper_context_wrapper whisper_init_wrapper(struct whisper_model_loader_wrapper * loader){
    struct whisper_context * ctx = whisper_init(loader->ptr);
    struct whisper_context_wrapper ctw_w;
    ctw_w.ptr = ctx;
    return ctw_w;
};

void whisper_free_wrapper(struct whisper_context_wrapper * ctx_w){
    whisper_free(ctx_w->ptr);
};

int whisper_pcm_to_mel_wrapper(
        struct whisper_context_wrapper * ctx,
        py::array_t<float> samples,
        int   n_samples,
        int   n_threads){
    py::buffer_info buf = samples.request();
    float *samples_ptr = static_cast<float *>(buf.ptr);
    return whisper_pcm_to_mel(ctx->ptr, samples_ptr, n_samples, n_threads);
};

int whisper_set_mel_wrapper(
        struct whisper_context_wrapper * ctx,
        py::array_t<float> data,
        int   n_len,
        int   n_mel){
    py::buffer_info buf = data.request();
    float *data_ptr = static_cast<float *>(buf.ptr);
    return whisper_set_mel(ctx->ptr, data_ptr, n_len, n_mel);

};

int whisper_n_len_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_n_len(ctx_w->ptr);
};

int whisper_n_vocab_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_n_vocab(ctx_w->ptr);
};

int whisper_n_text_ctx_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_n_text_ctx(ctx_w->ptr);
};

int whisper_n_audio_ctx_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_n_audio_ctx(ctx_w->ptr);
}

int whisper_is_multilingual_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_is_multilingual(ctx_w->ptr);
}


float * whisper_get_logits_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_get_logits(ctx_w->ptr);
};

const char * whisper_token_to_str_wrapper(struct whisper_context_wrapper * ctx_w, whisper_token token){
    return whisper_token_to_str(ctx_w->ptr, token);
};

whisper_token whisper_token_eot_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_eot(ctx_w->ptr);
}

whisper_token whisper_token_sot_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_sot(ctx_w->ptr);
}

whisper_token whisper_token_prev_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_prev(ctx_w->ptr);
}

whisper_token whisper_token_solm_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_solm(ctx_w->ptr);
}

whisper_token whisper_token_not_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_not(ctx_w->ptr);
}

whisper_token whisper_token_beg_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_beg(ctx_w->ptr);
}

whisper_token whisper_token_lang_wrapper(struct whisper_context_wrapper * ctx_w, int lang_id){
    return whisper_token_lang(ctx_w->ptr, lang_id);
}

whisper_token whisper_token_translate_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_translate(ctx_w->ptr);
}

whisper_token whisper_token_transcribe_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_token_transcribe(ctx_w->ptr);
}

void whisper_print_timings_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_print_timings(ctx_w->ptr);
}

void whisper_reset_timings_wrapper(struct whisper_context_wrapper * ctx_w){
    return whisper_reset_timings(ctx_w->ptr);
}

int whisper_encode_wrapper(
        struct whisper_context_wrapper * ctx,
        int   offset,
        int   n_threads){
    return whisper_encode(ctx->ptr, offset, n_threads);
}


int whisper_decode_wrapper(
        struct whisper_context_wrapper * ctx,
        const whisper_token * tokens,
        int   n_tokens,
        int   n_past,
        int   n_threads){
    return whisper_decode(ctx->ptr, tokens, n_tokens, n_past, n_threads);
};

int whisper_tokenize_wrapper(
        struct whisper_context_wrapper * ctx,
        const char * text,
        whisper_token * tokens,
        int   n_max_tokens){
    return whisper_tokenize(ctx->ptr, text, tokens, n_max_tokens);
};

int whisper_lang_auto_detect_wrapper(
        struct whisper_context_wrapper * ctx,
        int   offset_ms,
        int   n_threads,
        py::array_t<float> lang_probs){

    py::buffer_info buf = lang_probs.request();
    float *lang_probs_ptr = static_cast<float *>(buf.ptr);
    return whisper_lang_auto_detect(ctx->ptr, offset_ms, n_threads, lang_probs_ptr);

}

int whisper_full_wrapper(
        struct whisper_context_wrapper * ctx_w,
        struct whisper_full_params   params,
        py::array_t<float> samples,
        int   n_samples){
    py::buffer_info buf = samples.request();
    float *samples_ptr = static_cast<float *>(buf.ptr);
    return whisper_full(ctx_w->ptr, params, samples_ptr, n_samples);
}

int whisper_full_parallel_wrapper(
        struct whisper_context_wrapper * ctx_w,
        struct whisper_full_params   params,
        py::array_t<float> samples,
        int   n_samples,
        int n_processors){
    py::buffer_info buf = samples.request();
    float *samples_ptr = static_cast<float *>(buf.ptr);
    return whisper_full_parallel(ctx_w->ptr, params, samples_ptr, n_samples, n_processors);
}


int whisper_full_n_segments_wrapper(struct whisper_context_wrapper * ctx){
    return whisper_full_n_segments(ctx->ptr);
}

int whisper_full_lang_id_wrapper(struct whisper_context_wrapper * ctx){
    return whisper_full_lang_id(ctx->ptr);
}

int64_t whisper_full_get_segment_t0_wrapper(struct whisper_context_wrapper * ctx, int i_segment){
    return whisper_full_get_segment_t0(ctx->ptr, i_segment);
}

int64_t whisper_full_get_segment_t1_wrapper(struct whisper_context_wrapper * ctx, int i_segment){
    return whisper_full_get_segment_t1(ctx->ptr, i_segment);
}

// https://pybind11.readthedocs.io/en/stable/advanced/cast/strings.html
const py::bytes whisper_full_get_segment_text_wrapper(struct whisper_context_wrapper * ctx, int i_segment){
    const char * c_array = whisper_full_get_segment_text(ctx->ptr, i_segment);
    size_t length = strlen(c_array); // Determine the length of the array
    return py::bytes(c_array, length); // Return the data without transcoding
};

int whisper_full_n_tokens_wrapper(struct whisper_context_wrapper * ctx, int i_segment){
     return whisper_full_n_tokens(ctx->ptr, i_segment);
}

const char * whisper_full_get_token_text_wrapper(struct whisper_context_wrapper * ctx, int i_segment, int i_token){
    return whisper_full_get_token_text(ctx->ptr, i_segment, i_token);
}

whisper_token whisper_full_get_token_id_wrapper(struct whisper_context_wrapper * ctx, int i_segment, int i_token){
    return whisper_full_get_token_id(ctx->ptr, i_segment, i_token);
}

whisper_token_data whisper_full_get_token_data_wrapper(struct whisper_context_wrapper * ctx, int i_segment, int i_token){
    return whisper_full_get_token_data(ctx->ptr, i_segment, i_token);
}

float whisper_full_get_token_p_wrapper(struct whisper_context_wrapper * ctx, int i_segment, int i_token){
    return whisper_full_get_token_p(ctx->ptr, i_segment, i_token);
}

class WhisperFullParamsWrapper : public whisper_full_params {
    std::string initial_prompt_str;   
    std::string suppress_regex_str;      
public:
    py::function py_progress_callback;
    WhisperFullParamsWrapper(const whisper_full_params& params = whisper_full_params())
        : whisper_full_params(params),  
        initial_prompt_str(params.initial_prompt ? params.initial_prompt : ""),
        suppress_regex_str(params.suppress_regex ? params.suppress_regex : "") {
        initial_prompt = initial_prompt_str.empty() ? nullptr : initial_prompt_str.c_str();
        suppress_regex = suppress_regex_str.empty() ? nullptr : suppress_regex_str.c_str();
        // progress callback
        progress_callback_user_data = this;
        progress_callback = [](struct whisper_context* ctx, struct whisper_state* state, int progress, void* user_data) {
            auto* self = static_cast<WhisperFullParamsWrapper*>(user_data);
            if(self && self->print_progress){
                if (self->py_progress_callback) {
                    self->py_progress_callback(progress);  // Call Python callback
                }
                else {
                    fprintf(stderr, "Progress: %3d%%\n", progress);
                } // Default message
            }
        } ;
    }

    WhisperFullParamsWrapper(const WhisperFullParamsWrapper& other)
        : WhisperFullParamsWrapper(static_cast<const whisper_full_params&>(other)) {}
    
    void set_initial_prompt(const std::string& prompt) {
        initial_prompt_str = prompt;
        initial_prompt = initial_prompt_str.c_str();
    }

    void set_suppress_regex(const std::string& regex) {
        suppress_regex_str = regex;
        suppress_regex = suppress_regex_str.c_str();
    }
};

WhisperFullParamsWrapper  whisper_full_default_params_wrapper(enum whisper_sampling_strategy strategy) {
    return WhisperFullParamsWrapper(whisper_full_default_params(strategy));
}

// callbacks mechanism

void _new_segment_callback(struct whisper_context * ctx, struct whisper_state * state, int n_new, void * user_data){
    struct whisper_context_wrapper ctx_w;
    ctx_w.ptr = ctx;
    // call the python callback
//    py::gil_scoped_acquire gil;  // Acquire the GIL while in this scope.
    py_new_segment_callback(ctx_w, n_new, user_data);
};

void assign_new_segment_callback(struct whisper_full_params *params, py::function f){
    params->new_segment_callback = _new_segment_callback;
    py_new_segment_callback = f;
};

bool _encoder_begin_callback(struct whisper_context * ctx, struct whisper_state * state, void * user_data){
    struct whisper_context_wrapper ctx_w;
    ctx_w.ptr = ctx;
    // call the python callback
    py::object result_py = py_encoder_begin_callback(ctx_w, user_data);
    bool res = result_py.cast<bool>();
    return res;
}

void assign_encoder_begin_callback(struct whisper_full_params *params, py::function f){
    params->encoder_begin_callback = _encoder_begin_callback;
    py_encoder_begin_callback = f;
}

void _logits_filter_callback(
        struct whisper_context * ctx,
        struct whisper_state * state,
        const whisper_token_data * tokens,
        int   n_tokens,
        float * logits,
        void * user_data){
    struct whisper_context_wrapper ctx_w;
    ctx_w.ptr = ctx;
    // call the python callback
    py_logits_filter_callback(ctx_w, n_tokens, logits, user_data);
}

void assign_logits_filter_callback(struct whisper_full_params *params, py::function f){
    params->logits_filter_callback = _logits_filter_callback;
    py_logits_filter_callback = f;
}

py::dict get_greedy(whisper_full_params * params){
    py::dict d("best_of"_a=params->greedy.best_of);
    return d;
}

PYBIND11_MODULE(_pywhispercpp, m) {
    m.doc() = R"pbdoc(
        Pywhispercpp: Python binding to whisper.cpp
        -----------------------

        .. currentmodule:: _whispercpp

        .. autosummary::
           :toctree: _generate

    )pbdoc";

    m.attr("WHISPER_SAMPLE_RATE") = WHISPER_SAMPLE_RATE;
    m.attr("WHISPER_N_FFT") = WHISPER_N_FFT;
    m.attr("WHISPER_HOP_LENGTH") = WHISPER_HOP_LENGTH;
    m.attr("WHISPER_CHUNK_SIZE") = WHISPER_CHUNK_SIZE;

    py::class_<whisper_context_wrapper>(m, "whisper_context");
    py::class_<whisper_token>(m, "whisper_token")
            .def(py::init<>());
    py::class_<whisper_token_data>(m,"whisper_token_data")
            .def(py::init<>())
            .def_readwrite("id", &whisper_token_data::id)
            .def_readwrite("tid", &whisper_token_data::tid)
            .def_readwrite("p", &whisper_token_data::p)
            .def_readwrite("plog", &whisper_token_data::plog)
            .def_readwrite("pt", &whisper_token_data::pt)
            .def_readwrite("ptsum", &whisper_token_data::ptsum)
            .def_readwrite("t0", &whisper_token_data::t0)
            .def_readwrite("t1", &whisper_token_data::t1)
            .def_readwrite("vlen", &whisper_token_data::vlen);

    py::class_<whisper_model_loader_wrapper>(m,"whisper_model_loader")
            .def(py::init<>());

    m.def("whisper_init_from_file", &whisper_init_from_file_wrapper, "Various functions for loading a ggml whisper model.\n"
                                                                    "Allocate (almost) all memory needed for the model.\n"
                                                                    "Return NULL on failure");
    m.def("whisper_init_from_buffer", &whisper_init_from_buffer_wrapper, "Various functions for loading a ggml whisper model.\n"
                                                                        "Allocate (almost) all memory needed for the model.\n"
                                                                        "Return NULL on failure");
    m.def("whisper_init", &whisper_init_wrapper, "Various functions for loading a ggml whisper model.\n"
                                                "Allocate (almost) all memory needed for the model.\n"
                                                "Return NULL on failure");


    m.def("whisper_free", &whisper_free_wrapper, "Frees all memory allocated by the model.");

    m.def("whisper_pcm_to_mel", &whisper_pcm_to_mel_wrapper, "Convert RAW PCM audio to log mel spectrogram.\n"
                                                             "The resulting spectrogram is stored inside the provided whisper context.\n"
                                                             "Returns 0 on success");

    m.def("whisper_set_mel", &whisper_set_mel_wrapper, " This can be used to set a custom log mel spectrogram inside the provided whisper context.\n"
                                                        "Use this instead of whisper_pcm_to_mel() if you want to provide your own log mel spectrogram.\n"
                                                        "n_mel must be 80\n"
                                                        "Returns 0 on success");

    m.def("whisper_encode", &whisper_encode_wrapper, "Run the Whisper encoder on the log mel spectrogram stored inside the provided whisper context.\n"
                                                    "Make sure to call whisper_pcm_to_mel() or whisper_set_mel() first.\n"
                                                    "offset can be used to specify the offset of the first frame in the spectrogram.\n"
                                                    "Returns 0 on success");

    m.def("whisper_decode", &whisper_decode_wrapper, "Run the Whisper decoder to obtain the logits and probabilities for the next token.\n"
                                                    "Make sure to call whisper_encode() first.\n"
                                                    "tokens + n_tokens is the provided context for the decoder.\n"
                                                    "n_past is the number of tokens to use from previous decoder calls.\n"
                                                    "Returns 0 on success\n"
                                                    "TODO: add support for multiple decoders");

    m.def("whisper_tokenize", &whisper_tokenize_wrapper, "Convert the provided text into tokens.\n"
                                                        "The tokens pointer must be large enough to hold the resulting tokens.\n"
                                                        "Returns the number of tokens on success, no more than n_max_tokens\n"
                                                        "Returns -1 on failure\n"
                                                        "TODO: not sure if correct");

    m.def("whisper_lang_max_id", &whisper_lang_max_id, "Largest language id (i.e. number of available languages - 1)");
    m.def("whisper_lang_id", &whisper_lang_id, "Return the id of the specified language, returns -1 if not found\n"
                                                "Examples:\n"
                                                "\"de\" -> 2\n"
                                                "\"german\" -> 2");
    m.def("whisper_lang_str", &whisper_lang_str, "Return the short string of the specified language id (e.g. 2 -> \"de\"), returns nullptr if not found");







    m.def("whisper_lang_auto_detect", &whisper_lang_auto_detect_wrapper, "Use mel data at offset_ms to try and auto-detect the spoken language\n"
                                                                    "Make sure to call whisper_pcm_to_mel() or whisper_set_mel() first\n"
                                                                    "Returns the top language id or negative on failure\n"
                                                                    "If not null, fills the lang_probs array with the probabilities of all languages\n"
                                                                    "The array must be whispe_lang_max_id() + 1 in size\n"
                                                                    "ref: https://github.com/openai/whisper/blob/main/whisper/decoding.py#L18-L69\n");
    m.def("whisper_n_len", &whisper_n_len_wrapper, "whisper_n_len");
    m.def("whisper_n_vocab", &whisper_n_vocab_wrapper, "wrapper_whisper_n_vocab");
    m.def("whisper_n_text_ctx", &whisper_n_text_ctx_wrapper, "whisper_n_text_ctx");
    m.def("whisper_n_audio_ctx", &whisper_n_audio_ctx_wrapper, "whisper_n_audio_ctx");
    m.def("whisper_is_multilingual", &whisper_is_multilingual_wrapper, "whisper_is_multilingual");
    m.def("whisper_get_logits", &whisper_get_logits_wrapper, "Token logits obtained from the last call to whisper_decode()\n"
                                                            "The logits for the last token are stored in the last row\n"
                                                            "Rows: n_tokens\n"
                                                            "Cols: n_vocab");


    m.def("whisper_token_to_str", &whisper_token_to_str_wrapper, "whisper_token_to_str");
    m.def("whisper_token_eot", &whisper_token_eot_wrapper, "whisper_token_eot");
    m.def("whisper_token_sot", &whisper_token_sot_wrapper, "whisper_token_sot");
    m.def("whisper_token_prev", &whisper_token_prev_wrapper);
    m.def("whisper_token_solm", &whisper_token_solm_wrapper);
    m.def("whisper_token_not", &whisper_token_not_wrapper);
    m.def("whisper_token_beg", &whisper_token_beg_wrapper);
    m.def("whisper_token_lang", &whisper_token_lang_wrapper);

    m.def("whisper_token_translate", &whisper_token_translate_wrapper);
    m.def("whisper_token_transcribe", &whisper_token_transcribe_wrapper);

    m.def("whisper_print_timings", &whisper_print_timings_wrapper);
    m.def("whisper_reset_timings", &whisper_reset_timings_wrapper);

    m.def("whisper_print_system_info", &whisper_print_system_info);



    //////////////////////

    py::enum_<whisper_sampling_strategy>(m, "whisper_sampling_strategy")
        .value("WHISPER_SAMPLING_GREEDY", whisper_sampling_strategy::WHISPER_SAMPLING_GREEDY)
        .value("WHISPER_SAMPLING_BEAM_SEARCH", whisper_sampling_strategy::WHISPER_SAMPLING_BEAM_SEARCH)
        .export_values();

    py::class_<whisper_full_params>(m, "__whisper_full_params__internal")
        .def(py::init<>()) 
        .def("__repr__", [](const whisper_full_params& self) {
            std::ostringstream oss;
            oss << "whisper_full_params("
                << "strategy=" << self.strategy << ", "
                << "n_threads=" << self.n_threads << ", "
                << "n_max_text_ctx=" << self.n_max_text_ctx << ", "
                << "offset_ms=" << self.offset_ms << ", "
                << "duration_ms=" << self.duration_ms << ", "
                << "translate=" << (self.translate ? "True" : "False") << ", "
                << "no_context=" << (self.no_context ? "True" : "False") << ", "
                << "no_timestamps=" << (self.no_timestamps ? "True" : "False") << ", "
                << "single_segment=" << (self.single_segment ? "True" : "False") << ", "
                << "print_special=" << (self.print_special ? "True" : "False") << ", "
                << "print_progress=" << (self.print_progress ? "True" : "False") << ", "
                << "print_realtime=" << (self.print_realtime ? "True" : "False") << ", "
                << "print_timestamps=" << (self.print_timestamps ? "True" : "False") << ", "
                << "token_timestamps=" << (self.token_timestamps ? "True" : "False") << ", "
                << "thold_pt=" << self.thold_pt << ", "
                << "thold_ptsum=" << self.thold_ptsum << ", "
                << "max_len=" << self.max_len << ", "
                << "split_on_word=" << (self.split_on_word ? "True" : "False") << ", "
                << "max_tokens=" << self.max_tokens << ", "
                << "debug_mode=" << (self.debug_mode ? "True" : "False") << ", "
                << "audio_ctx=" << self.audio_ctx << ", "
                << "tdrz_enable=" << (self.tdrz_enable ? "True" : "False") << ", "
                << "suppress_regex=" << (self.suppress_regex ? self.suppress_regex : "None") << ", "
                << "initial_prompt=" << (self.initial_prompt ? self.initial_prompt : "None") << ", "
                << "prompt_tokens=" << (self.prompt_tokens ? "(whisper_token *)" : "None") << ", "
                << "prompt_n_tokens=" << self.prompt_n_tokens << ", "
                << "language=" << (self.language ? self.language : "None") << ", "
                << "detect_language=" << (self.detect_language ? "True" : "False") << ", "
                << "suppress_blank=" << (self.suppress_blank ? "True" : "False") << ", "
                << "temperature=" << self.temperature << ", "
                << "max_initial_ts=" << self.max_initial_ts << ", "
                << "length_penalty=" << self.length_penalty << ", "
                << "temperature_inc=" << self.temperature_inc << ", "
                << "entropy_thold=" << self.entropy_thold << ", "
                << "logprob_thold=" << self.logprob_thold << ", "
                << "no_speech_thold=" << self.no_speech_thold << ", "
                << "greedy={best_of=" << self.greedy.best_of << "}, "
                << "beam_search={beam_size=" << self.beam_search.beam_size << ", patience=" << self.beam_search.patience << "}, "
                << "new_segment_callback=" << (self.new_segment_callback ? "(function pointer)" : "None") << ", "
                << "progress_callback=" << (self.progress_callback ? "(function pointer)" : "None") << ", "
                << "encoder_begin_callback=" << (self.encoder_begin_callback ? "(function pointer)" : "None") << ", "
                << "abort_callback=" << (self.abort_callback ? "(function pointer)" : "None") << ", "
                << "logits_filter_callback=" << (self.logits_filter_callback ? "(function pointer)" : "None") << ", "
                << "grammar_rules=" << (self.grammar_rules ? "(whisper_grammar_element **)" : "None") << ", "
                << "n_grammar_rules=" << self.n_grammar_rules << ", "
                << "i_start_rule=" << self.i_start_rule << ", "
                << "grammar_penalty=" << self.grammar_penalty
                << ")";
            return oss.str();
        });

    py::class_<WhisperFullParamsWrapper, whisper_full_params>(m, "whisper_full_params")
        .def(py::init<>())
        .def_readwrite("strategy", &WhisperFullParamsWrapper::strategy)
        .def_readwrite("n_threads", &WhisperFullParamsWrapper::n_threads)
        .def_readwrite("n_max_text_ctx", &WhisperFullParamsWrapper::n_max_text_ctx)
        .def_readwrite("offset_ms", &WhisperFullParamsWrapper::offset_ms)
        .def_readwrite("duration_ms", &WhisperFullParamsWrapper::duration_ms)
        .def_readwrite("translate", &WhisperFullParamsWrapper::translate)
        .def_readwrite("no_context", &WhisperFullParamsWrapper::no_context)
        .def_readwrite("single_segment", &WhisperFullParamsWrapper::single_segment)
        .def_readwrite("print_special", &WhisperFullParamsWrapper::print_special)
        .def_readwrite("print_progress", &WhisperFullParamsWrapper::print_progress)
        .def_readwrite("progress_callback", &WhisperFullParamsWrapper::py_progress_callback)
        .def_readwrite("print_realtime", &WhisperFullParamsWrapper::print_realtime)
        .def_readwrite("print_timestamps", &WhisperFullParamsWrapper::print_timestamps)
        .def_readwrite("token_timestamps", &WhisperFullParamsWrapper::token_timestamps)
        .def_readwrite("thold_pt", &WhisperFullParamsWrapper::thold_pt)
        .def_readwrite("thold_ptsum", &WhisperFullParamsWrapper::thold_ptsum)
        .def_readwrite("max_len", &WhisperFullParamsWrapper::max_len)
        .def_readwrite("split_on_word", &WhisperFullParamsWrapper::split_on_word)
        .def_readwrite("max_tokens", &WhisperFullParamsWrapper::max_tokens)
        .def_readwrite("audio_ctx", &WhisperFullParamsWrapper::audio_ctx)
        .def_property("suppress_regex", 
            [](WhisperFullParamsWrapper &self) {
                return py::str(self.suppress_regex ? self.suppress_regex : "");
            },
            [](WhisperFullParamsWrapper &self, const std::string &new_c) {
                self.set_suppress_regex(new_c);
            })
        .def_property("initial_prompt",
        [](WhisperFullParamsWrapper &self) {
                return py::str(self.initial_prompt ? self.initial_prompt : "");
            },
            [](WhisperFullParamsWrapper &self, const std::string &initial_prompt) {
                self.set_initial_prompt(initial_prompt);
            }
        )
        .def_readwrite("prompt_tokens", &WhisperFullParamsWrapper::prompt_tokens)
        .def_readwrite("prompt_n_tokens", &WhisperFullParamsWrapper::prompt_n_tokens)
        .def_property("language", 
            [](WhisperFullParamsWrapper &self) { 
                return py::str(self.language); 
            },
            [](WhisperFullParamsWrapper &self, const char *new_c) {// using lang_id let us avoid issues with memory management
                const int lang_id = (new_c && strlen(new_c) > 0) ? whisper_lang_id(new_c) : -1;
                if (lang_id != -1) {
                    self.language = whisper_lang_str(lang_id);    
                } else {
                    self.language = ""; //defaults to auto-detect
                }
            })
        .def_readwrite("suppress_blank", &WhisperFullParamsWrapper::suppress_blank)
        .def_readwrite("temperature", &WhisperFullParamsWrapper::temperature)
        .def_readwrite("max_initial_ts", &WhisperFullParamsWrapper::max_initial_ts)
        .def_readwrite("length_penalty", &WhisperFullParamsWrapper::length_penalty)
        .def_readwrite("temperature_inc", &WhisperFullParamsWrapper::temperature_inc)
        .def_readwrite("entropy_thold", &WhisperFullParamsWrapper::entropy_thold)
        .def_readwrite("logprob_thold", &WhisperFullParamsWrapper::logprob_thold)
        .def_readwrite("no_speech_thold", &WhisperFullParamsWrapper::no_speech_thold)
        // little hack for the internal stuct <undefined type issue>
        .def_property("greedy", [](WhisperFullParamsWrapper &self) {return py::dict("best_of"_a=self.greedy.best_of);},
                                 [](WhisperFullParamsWrapper &self, py::dict dict) {self.greedy.best_of = dict["best_of"].cast<int>();})
        .def_property("beam_search", [](WhisperFullParamsWrapper &self) {return py::dict("beam_size"_a=self.beam_search.beam_size, "patience"_a=self.beam_search.patience);},
                                [](WhisperFullParamsWrapper &self, py::dict dict) {self.beam_search.beam_size = dict["beam_size"].cast<int>(); self.beam_search.patience = dict["patience"].cast<float>();})
        .def_readwrite("new_segment_callback_user_data", &WhisperFullParamsWrapper::new_segment_callback_user_data)
        .def_readwrite("encoder_begin_callback_user_data", &WhisperFullParamsWrapper::encoder_begin_callback_user_data)
        .def_readwrite("logits_filter_callback_user_data", &WhisperFullParamsWrapper::logits_filter_callback_user_data);


    py::implicitly_convertible<whisper_full_params, WhisperFullParamsWrapper>();
    
    m.def("whisper_full_default_params", &whisper_full_default_params_wrapper);

    m.def("whisper_full", &whisper_full_wrapper, "Run the entire model: PCM -> log mel spectrogram -> encoder -> decoder -> text\n"
                                                 "Uses the specified decoding strategy to obtain the text.\n");

    m.def("whisper_full_parallel", &whisper_full_parallel_wrapper, "Split the input audio in chunks and process each chunk separately using whisper_full()\n"
                                                                    "It seems this approach can offer some speedup in some cases.\n"
                                                                    "However, the transcription accuracy can be worse at the beginning and end of each chunk.");

    m.def("whisper_full_n_segments", &whisper_full_n_segments_wrapper, "Number of generated text segments.\n"
                                                                       "A segment can be a few words, a sentence, or even a paragraph.\n");

    m.def("whisper_full_lang_id", &whisper_full_lang_id_wrapper, "Language id associated with the current context");
    m.def("whisper_full_get_segment_t0", &whisper_full_get_segment_t0_wrapper, "Get the start time of the specified segment");
    m.def("whisper_full_get_segment_t1", &whisper_full_get_segment_t1_wrapper, "Get the end time of the specified segment");

    m.def("whisper_full_get_segment_text", &whisper_full_get_segment_text_wrapper, "Get the text of the specified segment");
    m.def("whisper_full_n_tokens", &whisper_full_n_tokens_wrapper, "Get number of tokens in the specified segment.");

    m.def("whisper_full_get_token_text", &whisper_full_get_token_text_wrapper, "Get the token text of the specified token in the specified segment.");
    m.def("whisper_full_get_token_id", &whisper_full_get_token_id_wrapper, "Get the token text of the specified token in the specified segment.");

    m.def("whisper_full_get_token_data", &whisper_full_get_token_data_wrapper, "Get token data for the specified token in the specified segment.\n"
                                                                                "This contains probabilities, timestamps, etc.");

    m.def("whisper_full_get_token_p", &whisper_full_get_token_p_wrapper, "Get the probability of the specified token in the specified segment.");

    ////////////////////////////////////////////////////////////////////////////

    m.def("whisper_bench_memcpy", &whisper_bench_memcpy, "Temporary helpers needed for exposing ggml interface");
    m.def("whisper_bench_ggml_mul_mat", &whisper_bench_ggml_mul_mat, "Temporary helpers needed for exposing ggml interface");

    ////////////////////////////////////////////////////////////////////////////
    // Helper mechanism to set callbacks from python
    // The only difference from the C-Style API

    m.def("assign_new_segment_callback", &assign_new_segment_callback, "Assigns a new_segment_callback, takes <whisper_full_params> instance and a callable function with the same parameters which are defined in the interface",
        py::arg("params"), py::arg("callback"));

    m.def("assign_encoder_begin_callback", &assign_encoder_begin_callback, "Assigns an encoder_begin_callback, takes <whisper_full_params> instance and a callable function with the same parameters which are defined in the interface",
            py::arg("params"), py::arg("callback"));

    m.def("assign_logits_filter_callback", &assign_logits_filter_callback, "Assigns a logits_filter_callback, takes <whisper_full_params> instance and a callable function with the same parameters which are defined in the interface",
            py::arg("params"), py::arg("callback"));


#ifdef VERSION_INFO
    m.attr("__version__") = MACRO_STRINGIFY(VERSION_INFO);
#else
    m.attr("__version__") = "dev";
#endif
}



================================================
FILE: tests/test_c_api.py
================================================


import _pywhispercpp as pw

import unittest
from unittest import TestCase


class TestCAPI(TestCase):

    model_file = './whisper.cpp/models/for-tests-ggml-tiny.en.bin'

    def test_whisper_init_from_file(self):
        ctx = pw.whisper_init_from_file(self.model_file)
        self.assertIsInstance(ctx, pw.whisper_context)

    def test_whisper_lang_str(self):
        return self.assertEqual(pw.whisper_lang_str(0), 'en')

    def test_whisper_lang_id(self):
        return self.assertEqual(pw.whisper_lang_id('en'), 0)

    def test_whisper_full_params_language_set_to_de(self):
        params = pw.whisper_full_params()
        params.language = 'de'
        return self.assertEqual(params.language, 'de')
    
    def test_whisper_full_params_language_set_to_german(self):
        params = pw.whisper_full_params()
        params.language = 'german'
        return self.assertEqual(params.language, 'de')
    
    def test_whisper_full_params_context(self):    
    
        params = pw.whisper_full_params()
        # to ensure that the string is not cached
        prompt = str(10120923) + "A" + " test"
        params.initial_prompt = prompt
        print("Params Prompt: ", params.initial_prompt)
        del prompt
        import gc
        gc.collect()
        return self.assertEqual(params.initial_prompt, str(10120923) + "A test")
    
    def test_whisper_full_params_regex(self):    
        params = pw.whisper_full_params()
        val = str(10120923) + "A" + " test"
        params.suppress_regex = val
        print("Params Prompt: ", params.suppress_regex)
        del val
        import gc
        gc.collect()
        return self.assertEqual(params.suppress_regex, str(10120923) + "A" + " test") 

    def test_whisper_full_params_default(self):
        params = pw.whisper_full_default_params(pw.whisper_sampling_strategy.WHISPER_SAMPLING_GREEDY)
        self.assertIsInstance(params, pw.whisper_full_params)
        self.assertEqual(params.suppress_regex, "")
    
    def test_whisper_lang_id(self):
        return self.assertEqual(pw.whisper_lang_id('en'), 0)
    
    def test_whisper_full_params(self):
        params = pw.whisper_full_params()
        return self.assertIsInstance(params.n_threads, int)


if __name__ == '__main__':
    
    unittest.main()



================================================
FILE: tests/test_model.py
================================================
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test model.py
"""
import unittest
from pathlib import Path
from unittest import TestCase

from pywhispercpp.model import Model, Segment

if __name__ == '__main__':
    pass

WHISPER_CPP_DIR = Path(__file__).parent.parent / 'whisper.cpp'

class TestModel(TestCase):
    audio_file = WHISPER_CPP_DIR/ 'samples/jfk.wav'
    model = Model("tiny", models_dir=str(WHISPER_CPP_DIR/'models'))

    def test_transcribe(self):
        segments = self.model.transcribe(str(self.audio_file))
        return self.assertIsInstance(segments, list) and \
               self.assertIsInstance(segments[0], Segment) if len(segments) > 0 else True

    def test_get_params(self):
        params = self.model.get_params()
        return self.assertIsInstance(params, dict)

    def test_lang_max_id(self):
        n = self.model.lang_max_id()
        return self.assertGreater(n, 0)

    def test_available_languages(self):
        av_langs = self.model.available_languages()
        return self.assertIsInstance(av_langs, list) and self.assertGreater(len(av_langs), 1)

    def test__load_audio(self):
        audio_arr = self.model._load_audio(str(self.audio_file))
        return self.assertIsNotNone(audio_arr)

    def test_auto_detect_language(self):
        detected_language, probs = self.model.auto_detect_language(str(self.audio_file))
        return self.assertIsInstance(detected_language, tuple) and self.assertEqual(detected_language[0], 'en')


if __name__ == '__main__':
    unittest.main()




================================================
FILE: .github/dependabot.yml
================================================
version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"



================================================
FILE: .github/workflows/docs.yml
================================================
name: docs
on:
  push:
    branches:
      - main
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - uses: actions/cache@v4
        with:
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          path: ~/.cache/pip
      - run: |
          pip install mkdocs-macros-plugin mkdocs-material mkdocstrings[python] black pywhispercpp
      - run: mkdocs gh-deploy --force



================================================
FILE: .github/workflows/pip.yml
================================================
name: Pip

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - main

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        platform: [windows-latest, macos-latest, ubuntu-latest]
        python-version: ["3.8", "3.11"]

    runs-on: ${{ matrix.platform }}

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true

    - uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Add requirements
      run: python -m pip install --upgrade wheel setuptools

    - name: Install requirements
      run: python -m pip install -r requirements.txt

    - name: Build and install
      run: pip install --verbose .[test]

#    - name: Test C-API
#      run: python -m unittest ./tests/test_c_api.py




================================================
FILE: .github/workflows/wheels.yml
================================================
name: Wheels

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
     - release
  release:
    types:
      - published

jobs:
  build_sdist:
    name: Build SDist
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true

    - name: Build SDist
      run: pipx run build --sdist

    - name: Check metadata
      run: pipx run twine check dist/*

    - uses: actions/upload-artifact@v4
      with:
        name: artifact-sdist
        path: dist/*.tar.gz


  build_wheels:
    name: Wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-24.04-arm, ubuntu-latest, windows-2019, macos-latest]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true

    # Used to host cibuildwheel
    - uses: actions/setup-python@v5

    - name: Install cibuildwheel
      run: python -m pip install cibuildwheel

    - name: Build wheels
      run: python -m cibuildwheel --output-dir wheelhouse
      env:
        CIBW_ARCHS: auto
         # for windows setup.py repairwheel step should solve it
        CIBW_SKIP: pp* cp38-*

    - name: Verify clean directory
      run: git diff --exit-code
      shell: bash

    - name: List files
      run: ls wheelhouse

    - name: Upload wheels
      uses: actions/upload-artifact@v4
      with:
        name: artifact-${{ matrix.os }}
        path: wheelhouse/*.whl

  test_wheels:
    name: Test wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    needs: build_wheels
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest, ubuntu-24.04-arm]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true

    - uses: actions/download-artifact@v4
      with:
        pattern: artifact-*
        merge-multiple: true
        path: wheelhouse

    - name: Verify artifact download
      run: |
        ls -l wheelhouse

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest

    - name: Install Wheel
      run: |
        pip install --no-index --find-links=./wheelhouse pywhispercpp

    - name: Run tests
      run: |
        pytest tests/
        

  upload_all:
    name: Upload if release
    needs: [build_wheels, build_sdist]
    runs-on: ubuntu-latest
    if: github.event_name == 'release' && github.event.action == 'published'

    steps:
    - uses: actions/setup-python@v5
      with:
        python-version: "3.x"

    - uses: actions/download-artifact@v4
      with:
        pattern: artifact-*
        merge-multiple: true
        path: dist

    - uses: pypa/gh-action-pypi-publish@release/v1
      with:
        verbose: true
        password: ${{ secrets.PYPI_API_TOKEN }}


