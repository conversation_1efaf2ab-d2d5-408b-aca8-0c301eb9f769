import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
from api.agent.agent_mysql import Mysql
from api.agent.agent_secret import Secret
from app_sl.geral.util import generate_unique_id

async def testar_conexao_loja247():
    secret = Secret()
    
    if not all([secret.LOJA247_MYSQL_HOST, secret.LOJA247_MYSQL_USER, 
                secret.LOJA247_MYSQL_PASSWORD, secret.LOJA247_MYSQL_DATABASE]):
        print("Erro: As variáveis de ambiente para LOJA247 não estão completamente definidas.")
        return None
    
    loja247_db = Mysql(
        host=secret.LOJA247_MYSQL_HOST,
        user=secret.LOJA247_MYSQL_USER,
        password=secret.LOJA247_MYSQL_PASSWORD,
        database=secret.LOJA247_MYSQL_DATABASE
    )
    
    try:
        conn = await loja247_db.conecta()
        print("Conexão com o banco LOJA247 estabelecida com sucesso!")
        return loja247_db
    except Exception as e:
        print(f"Erro ao conectar com o banco LOJA247: {e}")
        return None

async def testar_conexao_gptalk():
    gptalk_db = Mysql()  # Usa as configurações padrão do GPTALK_01
    
    try:
        conn = await gptalk_db.conecta()
        print("Conexão com o banco GPTALK_01 estabelecida com sucesso!")
        return gptalk_db
    except Exception as e:
        print(f"Erro ao conectar com o banco GPTALK_01: {e}")
        return None

async def importar_produtos(loja247_db, gptalk_db):
    LOJA_ID = 39
    negocio_idx = "9876543210"

    # 0. Deletar registros existentes
    delete_query = f"DELETE FROM PRODUTO WHERE NEGOCIO_IDX = '{negocio_idx}'"
    await gptalk_db.query(delete_query)

    # 1. Carregar produtos da LOJA247
    query = f"SELECT * FROM PRODUTO WHERE LOJA_ID = {LOJA_ID}"
    loja247_produtos = await loja247_db.query(query)

    # 2. Criar array gptalk_produtos
    gptalk_produtos = []

    # Carregar categorias do GPTALK
    categorias_query = f"SELECT IDX, ID_ANTIGO FROM PRODUTO_CATEGORIA WHERE NEGOCIO_IDX = '{negocio_idx}'"
    categorias = await gptalk_db.query(categorias_query)
    categorias_dict = {cat['ID_ANTIGO']: cat['IDX'] for cat in categorias}
    print("categorias_dict",    categorias_dict)

    # 3. Processar produtos
    for registro in loja247_produtos:
        #print("registro", registro)
        if registro['NOME'] is None or registro['NOME'] == "" or registro['NOME'] == None:
            continue
        
        # Converta a CATEGORIA para inteiro e depois para string
        categoria_key = int(registro['CATEGORIA']) if registro['CATEGORIA'] is not None else None
        #print("categoria_key", categoria_key)
        #print("categoria_id", categorias_dict.get(categoria_key, None))
        produto = {
            "IDX": generate_unique_id(),
            "NOME": registro['NOME'],
            "NEGOCIO_IDX": negocio_idx,
            "CATEGORIA_ID": categorias_dict.get(categoria_key, None),
            "CODIGO": registro['CODIGO'],
            "PRECO": registro['PRECO'],
            "DESCR": registro['DESCR'],
            "ESTOQUE": registro['ESTOQUE']
        }
        #print("produto", produto)
        #exit()
        #print(f"CATEGORIA original: {registro['CATEGORIA']}, tipo: {type(registro['CATEGORIA'])}")
        #print(f"Chave usada para buscar categoria: {categoria_key}")
        #print(f"CATEGORIA_ID resultante: {produto['CATEGORIA_ID']}")

        # 3.1. Adicionar ao array gptalk_produtos
        gptalk_produtos.append(produto)

    # 4. Criar e executar query de inserção
    if gptalk_produtos:
        for produto in gptalk_produtos:
            campos = ", ".join(produto.keys())
            valores = ", ".join([f"'{str(v)}'" if v is not None else "NULL" for v in produto.values()])
            insert_query = f"INSERT INTO PRODUTO ({campos}) VALUES ({valores})"
            
            print(f"Query de inserção: {insert_query}")
            
            try:
                await gptalk_db.query(insert_query)
            except Exception as e:
                print(f"Erro ao executar a query: {e}")
                print(f"Produto: {produto}")
                raise  # Re-lança a exceção para interromper a execução

    print(f"Importação de {len(gptalk_produtos)} produtos concluída com sucesso!")

async def main():
    loja247_db = await testar_conexao_loja247()
    gptalk_db = await testar_conexao_gptalk()

    if loja247_db and gptalk_db:
        print("\nIniciando importação de produtos...")
        await importar_produtos(loja247_db, gptalk_db)
    else:
        print("Não foi possível estabelecer conexão com um ou ambos os bancos de dados.")

if __name__ == "__main__":
    asyncio.run(main())
