from agents.tool import function_tool
from ...agent_neo4j import AgentNeo4j
from ...agent_evolutionzap import AgentEvolutionZap
from api.agent.agent import salva_mensagem
import json, re, requests
import platform
from ...agent_secret import Secret

# --- Configuração Inicial ---
neo4j = AgentNeo4j()
evolutionApi = AgentEvolutionZap()
secret = Secret()
is_local = platform.system() == "Windows"
instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID_LOCAL") if is_local else secret.get_secret("EVOLUTION_API_INSTANCE_ID")

# --- Funções Auxiliares para Envio de Mensagens ---

def formata_whatsapp_cor(item: dict) -> str:
    """Formata os dados de uma cor para exibição no WhatsApp."""
    nome = item.get('nome', 'Cor não informada')
    codigo = item.get('codigo', '')
    hexadecimal = item.get('hexadecimal', '')
    
    # Gera a URL da imagem da cor
    url_base = "https://app.assistenciamk.gptalk.com.br/imagens/cores/gera_cor.php"
    url = f"{url_base}?hex={hexadecimal.replace('#', '')}&codigo={codigo}" if hexadecimal else None
    
    url_imagem = None
    if url:
        try:
            # A URL de gera_cor.php redireciona para a imagem final.
            # allow_redirects=True (padrão) garante que sigamos o redirecionamento.
            # O timeout evita que a requisição fique presa indefinidamente.
            response = requests.get(url, allow_redirects=True, timeout=10)
            response.raise_for_status()  # Lança uma exceção para códigos de erro HTTP (4xx ou 5xx)
            url_imagem = response.url # A URL final após o redirecionamento
        except requests.exceptions.RequestException as e:
            print(f"[ERRO] Não foi possível obter a imagem da cor de {url}. Causa: {e}")
            url_imagem = None # Garante que url_imagem seja None em caso de falha

    msg = f"🎨 *{nome}*\n"
    if codigo:
        msg += f"🔢 *Código:* {codigo}\n"
    if hexadecimal:
        msg += f"🎨 *Hex:* {hexadecimal}\n"

    # Adiciona a URL da imagem para ser usada no envio
    item['url_imagem'] = url_imagem
    
    return msg.strip()

async def enviar_mensagens_zap_cor(whatsapp: str, dados: list, conversa_idx: str, usuario_idx: str, agente_idx: str, em_resposta_idx: str, canal_idx: str):
    """Envia os cards de cores para o WhatsApp com tratamento de erros e fallback."""
    print ("===== envia_mensagens_zap_cor() =====")
    print("dados", dados)
    if not dados:
        print("[AVISO] Nenhum dado de cor para enviar.")
        return

    for item in dados:
        print("item", item)
        mensagem_whatsapp = formata_whatsapp_cor(item)
        url_imagem = item.get('url_imagem')
        print("url_imagem", url_imagem)

        try:
            print(f"\n--- Enviando cor: {item.get('nome')} ---")
            # Salva a mensagem no histórico antes de tentar enviar
            await salva_mensagem(
                mensagem=mensagem_whatsapp,
                conversa_idx=conversa_idx,
                usuario_idx=usuario_idx,
                agente_idx=agente_idx,
                em_resposta_idx=em_resposta_idx,
                remetente="agente",
                destinatario="usuario",
                canal_idx=canal_idx
            )

            if url_imagem:
                print(f"Tentando enviar com imagem: {url_imagem}")
                await evolutionApi.send_evolution_media(
                    whatsapp,
                    url_imagem,
                    "image",
                    mensagem_whatsapp,
                    instancia_id
                )
                print("Envio com imagem bem-sucedido.")
            else:
                print("Enviando como mensagem de texto (sem imagem).")
                await evolutionApi.send_evolution_message(
                    whatsapp,
                    mensagem_whatsapp,
                    instancia_id
                )
                print("Envio de texto bem-sucedido.")

        except Exception as e:
            print(f"[ERRO] Falha ao enviar a cor '{item.get('nome')}' como mídia: {e}")
            print("Tentando enviar como mensagem de texto de fallback...")
            try:
                await evolutionApi.send_evolution_message(
                    whatsapp,
                    mensagem_whatsapp,
                    instancia_id
                )
                print("Envio de texto (fallback) bem-sucedido.")
            except Exception as fallback_e:
                print(f"[ERRO GRAVE] Falha ao enviar também como texto. Causa: {fallback_e}")

@function_tool
async def cor_lista_cartao(
    usuario_whatsapp: str, 
    agente_idx: str,
    usuario_idx: str,
    em_resposta_idx: str,
    conversa_idx: str,
    canal_idx: str,
    negocio_idx: str,
    mensagem_inicial: str,
    mensagem_final: str,
    termo_consulta: str) -> dict:
    """
    Consulta e lista uma ou mais cores, enviando o resultado em formato de cartão para o WhatsApp.

    A função executa a consulta no Neo4j usando uma QUERY PADRÃO FIXA e ela mesma envia para o usuário, não sendo necessário ao agente informar nada mais ao usuário.

    ===============================================================================
    🚨 PROCESSO PARA O AGENTE: COMO CRIAR O `termo_consulta` 🚨
    ===============================================================================
    O agente deve identificar a intenção do usuário e construir a string `termo_consulta` de acordo com as regras abaixo.

    1. **Identificar os termos** da solicitação do usuário.
       Exemplo: "procure a cor red velvet" → termos: "red" e "velvet"

    2. **Normalizar os termos**: Corrigir erros de digitação e, se necessário, passar para o singular.
       Exemplo: "vermelhos" → "vermelho"

    3. **Construir a string `termo_consulta`**:

       **PARA BUSCA POR NOME (BUSCA PARCIAL):**
       - Use o nome da cor. A busca encontrará correspondências parciais.
       - Exemplo: `termo_consulta="red"` (encontrará "red velvet", "dark red", etc.)

       **PARA BUSCA POR CÓDIGO (BUSCA EXATA):**
       - Use o código exato da cor.
       - Exemplo: `termo_consulta="COR007"`

       **PARA BUSCA POR HEXADECIMAL (BUSCA EXATA):**
       - Use o valor hexadecimal exato, incluindo o `#`.
       - Exemplo: `termo_consulta="#FF0000"`

       **REGRAS PARA TERMOS MÚLTIPLOS:**
       - Use `AND` para buscar cores que contenham TODOS os termos.
       - Use `OR` para buscar cores que contenham PELO MENOS um dos termos.
       - Exemplos:
         * `termo_consulta="red AND velvet"` (busca cores com ambos os termos no nome)
         * `termo_consulta="rosa OR pink"` (busca cores com um ou outro termo no nome)
         * `termo_consulta="COR001 OR red"` (busca mista: código OU nome)

       **PARA LISTAR TODAS AS CORES:**
       - Use um termo genérico como "todos", "tudo" ou simplesmente deixe a string vazia.
       - Exemplo: `termo_consulta="todos"`
       
        Retorno
        -------
        json com as chaves:
        status: success ou error
        message: string com dados do resultado ou mensagem de erro
       
    🚨🚨🚨 A RESPOSTA DEVE SER ENVIADA EXATAMENTE ASSIM, SEM ALTERAÇÕES. O USUÁRIO ESPERA RECEBER UM JSON COM RESPOSTA. ENTREGUE, PORTANTO, A STRING DO JSON RECEBIDO COMO RESPOSTA, SEM AJUSTES, SEM CONVERSÕES, SEM COMENTÁRIOS.
    """
    try:
        # Cria o dicionário de parâmetros para a query Neo4j
        p = {
            "negocio_idx": negocio_idx,
            "termo_consulta": termo_consulta
        }

        termo_normalizado = processar_termos_compostos(p.get("termo_consulta", ""))
        print("Termo consulta (original):", termo_consulta)
        print("Termo consulta (normalizado):", termo_normalizado)

        # Se termo_consulta está vazio ou é para listar tudo, faz busca tradicional
        termo_tudo = not termo_normalizado or termo_normalizado.strip() == "" or termo_normalizado.lower() in ["todos", "todo", "tudo", "all", "*"]
        if termo_tudo:
            query_final = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor)
            WHERE c.excluido <> 1
            RETURN c.idx AS idx, c.nome AS nome, c.codigo AS codigo, c.hexadecimal AS hexadecimal
            ORDER BY c.nome
            """
            p.pop("termo_consulta", None)
        else:
            # Busca por termo usando fulltext index (se existir)
            # O índice precisa existir: db.index.fulltext.queryNodes('corNomeCodigoFT', $termo_consulta)
            query_final = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor)
            CALL db.index.fulltext.queryNodes('corNomeCodigoFT', $termo_consulta) YIELD node AS cor
            WHERE cor.idx = c.idx AND c.excluido <> 1
            RETURN c.idx AS idx, c.nome AS nome, c.codigo AS codigo, c.hexadecimal AS hexadecimal
            ORDER BY c.nome
            """
            p["termo_consulta"] = termo_normalizado

        print("query_final:", query_final)
        print("params_final:", p)
        result = await neo4j.execute_read_query(query_final, p)
        print("result", result)

        # Se não encontrar resultados, envia uma mensagem e encerra.
        if not result:
            mensagem = "Nenhuma cor foi encontrada com os termos informados."
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
            return {
                "status": "success",
                "message": mensagem,
                "function": "cor_lista_cartao"
            }

        # Envia a mensagem inicial, se houver
        if mensagem_inicial:
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem_inicial, instancia_id)

        # Envia os cards de cores encontrados
        await enviar_mensagens_zap_cor(
            whatsapp=usuario_whatsapp,
            dados=result,
            conversa_idx=conversa_idx,
            usuario_idx=usuario_idx,
            agente_idx=agente_idx,
            em_resposta_idx=em_resposta_idx,
            canal_idx=canal_idx
        )

        # Envia a mensagem final, se houver
        if mensagem_final:
            await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem_final, instancia_id)

        # Converte o resultado para um formato serializável em JSON
        dados_serializaveis = json.loads(json.dumps(result, default=str))

        return {
            "status": "success",
            "message": dados_serializaveis,

        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Ocorreu um erro: {str(e)}"
        }


# Função para singularizar e normalizar termos compostos (adaptada de gera_cartao_produto)
def processar_termos_compostos(termos):
    if not termos or not isinstance(termos, str):
        return termos

    termos = termos.strip()

    if ' AND ' in termos.upper() or ' OR ' in termos.upper():
        return termos

    if termos.isdigit():
        return termos

    palavras_ignoradas = {
        'de', 'da', 'do', 'das', 'dos', 'a', 'o', 'as', 'os', 'um', 'uma', 'uns', 'umas',
        'para', 'por', 'per', 'com', 'sem', 'sob', 'sobre', 'em', 'no', 'na', 'nos', 'nas',
        'a', 'à', 'às', 'ao', 'aos', 'pelo', 'pela', 'pelos', 'pelas', 'até', 'entre',
        'durante', 'contra', 'desde', 'após', 'antes', 'depois', 'como', 'que',
        'se', 'e', 'ou', 'mas', 'mais', 'muito', 'pouco', 'muitos', 'poucos', 'este',
        'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas', 'aquele', 'aquela',
        'aqueles', 'aquelas', 'meu', 'minha', 'meus', 'minhas', 'seu', 'sua', 'seus', 'suas'
    }

    palavras_invariaveis = {'jeans'}
    preservar_plural_diante_de = {'sapatos'}
    preposicoes_de = {'de', 'do', 'da', 'dos', 'das'}

    regras_pt = [
        (r'(ões)$', 'ão'),
        (r'(ães)$', 'ão'),
        (r'(is)$', 'l'),
        (r'(éis)$', 'el'),
        (r'(eis)$', 'el'),
        (r'(óis)$', 'ol'),
        (r'(uis)$', 'ul'),
        (r'(ns)$', 'm'),
        (r'(rs)$', 'r'),
        (r'(is)$', 'il'),
        (r'(?<!e)s$', ''),
    ]

    regras_en = [
        (r'(ies)$', 'y'),
        (r'(ves)$', 'f'),
        (r'(ses)$', 's'),
        (r'(s)$', ''),
    ]

    def singularizar_palavra(palavra):
        palavra_lower = palavra.lower()
        if palavra_lower in palavras_invariaveis:
            return palavra_lower
        for plural, singular in regras_pt:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        for plural, singular in regras_en:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        return palavra_lower

    palavras = [p.strip() for p in termos.split()]
    palavras_processadas = []

    for i, palavra in enumerate(palavras):
        p_lower = palavra.lower()
        prox = palavras[i + 1].strip().lower() if i + 1 < len(palavras) else ''
        if p_lower in preservar_plural_diante_de and prox in preposicoes_de:
            palavra_final = p_lower
        else:
            palavra_final = singularizar_palavra(palavra)
        if palavra_final not in palavras_ignoradas:
            palavras_processadas.append(palavra_final)

    if not palavras_processadas:
        return ""
    if len(palavras_processadas) == 1:
        return palavras_processadas[0]
    return ' AND '.join(palavras_processadas)




if __name__ == "__main__":
    import asyncio

    async def run_test(test_name, termo_consulta):
        """Função auxiliar para executar um teste e imprimir os resultados."""
        print(f"\n--- INICIANDO TESTE: {test_name} ---")
        print(f"Termo de consulta: '{termo_consulta}'")
        
        resultado = await cor_lista_cartao(
            usuario_whatsapp="553184198720",
            agente_idx="agente_mk_01",
            usuario_idx="user_wpp_553184198720",
            em_resposta_idx="msg_12345",
            conversa_idx="conversa_67890",
            canal_idx="wpp_553184198720",
            negocio_idx="5544332211",
            mensagem_inicial=f"Resultado para '{termo_consulta}':",
            mensagem_final="",
            termo_consulta=termo_consulta
        )
        print(f"[RESULTADO DO TESTE '{test_name}']: {resultado}")
        print(f"--- TESTE '{test_name}' FINALIZADO ---")
        await asyncio.sleep(2) # Pausa para não sobrecarregar a API

    async def testa_cor_lista_cartao():
        # Cenário 1: Busca por nome simples
        await run_test("Busca por Nome Simples", "red")

        # Cenário 2: Busca por código exato
        #await run_test("Busca por Código", "bold-red-matte")

        # Cenário 3: Busca com múltiplos termos (AND)
        #await run_test("Busca com AND", "red AND matte")

        # Cenário 4: Listar todas as cores
        #await run_test("Listar Todas", "todos")

        # Cenário 5: Busca por hexadecimal
        #await run_test("Busca por Hexadecimal", "#cb1f40")
        
        # Cenário 6: Busca sem resultados
        #await run_test("Busca Sem Resultados", "cor_inexistente_xyz")

    asyncio.run(testa_cor_lista_cartao())
    #Execucao
    #python -m api.agent.assistenciamk.functions.cor_lista_cartao