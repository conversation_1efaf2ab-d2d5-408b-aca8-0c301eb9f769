from ..secrets import fetch_api_key_env
from openai import  OpenAI
import asyncio


async def deepseek_coder(messages, config=None):
    print("----- deepseek_coder()")
    print ("messages", messages)


    if config is None:
        config = {
           'model': "deepseek-coder",
            'temperature': 0.2,
            'top_p': 1,  
            'n': 1
        }

    #print("content",user_content)
    deepseek_api_key = fetch_api_key_env("DEEPSEEK_API_KEY")

    print("config", config)
    client = OpenAI(
    api_key=deepseek_api_key,
    base_url="https://api.deepseek.com/v1"
    model_chat_name = "deepseek-chat"
    )
    
    response = client.chat.completions.create(
    messages=messages,
    model=config["model"],
    temperature= config["temperature"],
    top_p= config["top_p"],  # Valor padrão
    n= config['n'],  # Valor padrão
  )


    #print(response.choices[0].message.content)
    return response.choices[0].message.content  




#=======================================
async def main():
    
    messages = [{
        "role":"user", "content": "ola, boa noite" 
    }]
    result = await deepseek_coder(messages)
    print("result",result)
#asyncio.run(main())