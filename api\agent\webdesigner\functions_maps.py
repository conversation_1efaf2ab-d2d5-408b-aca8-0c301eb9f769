site_update_images_map = {
     "name": "site_update_images",
     "description": "Adiciona imagens do pexels ao site, substituindo placeholders",
      "parameters": {
      "type": "object",
    "properties": {
      "site_id":{
          "type": "integer",
          "description": "ID do site a ser atualizado"
      },
      "model": {
        "type": "string",
        "description": "Nome de um modelo de Large Language Model. Exemplo: gemini-pro"
      },

    },
    "required": ["site_id,model"]
  }

}



site_update_ids_map = {
     "name": "site_update_ids",
     "description": "Adiciona ids para todos os elementos do siteq que ainda não possuem ids",
      "parameters": {
      "type": "object",
    "properties": {
      "site_id":{
          "type": "integer",
          "description": "ID do site a ser atualizado"
      },
      "model": {
        "type": "string",
        "description": "Nome de um modelo de Large Language Model. Exemplo: gemini-pro"
      },

    },
    "required": ["site_id,model"]
  }

}


#===================
site_update_map = {

  "name": "site_update",
  "description": "Use esta função para atualizar uma pagina html fazendo adições ou modificações em seu conteudo com base na solitiação do usuário, que irá detalhar o que deseja que seja feito. Opcionalmente ele poderá enviar o codigo html no qual as alerações serão",
  "parameters": {
    "type": "object",
    "properties": {
      "PAGINA":{
          "type": "string",
          "description": "Nomo do arquivo ou página. Exemplo: index.html , contato.html, produto.html, cabecalho.htm, rodape.htm . O sufico do nome da pagina só poderão ser .html ou .htm"
      },
      "REQUISITOS": {
        "type": "string",
        "description": "Instruções sobre o que deve ser acrescentado ou alterado na página"
      },
      "HTML": {
        "type": "string",
        "description": "Código html da pagina que será alterada. Opcional. Se não for fornecido, a função pegará um modelo padrão como base ou a página ja existente."
      },
      "SITE_ID": {
        "type": "integer",
        "description": " ID do site a ser atualizado"
      }


    },
    "required": ["PAGINA,REQUISITOS,SITE_ID"]
  }
}


#===================
site_project_create_map = {
    "name":"site_project_create",
    "description":
        "Use esta função para criar o projeto do site solicitado pelo usuário. Passe todas as informações informadas pelo usuário. Será retornado uma mensagem informando o inicio do projeto e solicitando que o usuário aguarde a conclusão.",
    "parameters" : {
        "type": "object",
        "properties": {
            "NOME": {
                "type": "string",
                "description" : "nome do site"
                 },
            "AREA": {
                "type": "string",
                "description" : "área de atuação da empresa ou profissional"
                 },
           "OBJETIVO": {
                "type": "string",
                "description" : "objetivo do site"
                 },
           "PUBLICO_ALVO": {
                "type": "string",
                "description" : "Público alvo do site"
                 },
            "DESIGN_ESTILO": {
                "type": "string",
                "description" : """preferências em termos de design, incluindo exemplos de sites que você gosta, paletas de cores desejadas, 
                e qualquer elemento visual específico que deseja incluir"""
                 },
            "FUNCIONALIDADES": {
                "type": "string",
                "description" : """
                Detalhes sobre qualquer funcionalidade especial que o site deve ter, como formulários de contato, galerias de imagens, integração com redes sociais, funcionalidades de e-commerce, chat ao vivo, entre outros.
                """
            },
            "SEO_MARKETING": {
                "type": "string",
                "description" : """
                Estratégias específicas de SEO ou marketing digital que gostaria de implementar, como palavras-chave alvo, integração com Google Analytics, campanhas de PPC, etc. Opcional
                """
            },    
            "OBSERVACAO": {
                "type": "string",
                "description" : "Observação adicional que o usuário queira acrescentar para o desenvolvimento do site. Opcional."
                 },
            },
          "required": ["NOME","AREA","OBJETIVO","PUBLICO_ALVO","DESIGN_ESTILO","FUNCIONALIDADES"],
       }
}   
