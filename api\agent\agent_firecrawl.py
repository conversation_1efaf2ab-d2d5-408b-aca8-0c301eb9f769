from fastapi import APIRouter
from .agent_mysql import Mysql
from fastapi import APIRouter
from .agent_secret import Secret
from firecrawl import FirecrawlApp
import time, json
from urllib.parse import urljoin

secret = Secret()
router = APIRouter()


class AgentFireCrawl:
    def __init__(self):
        self.api_key = secret.get_secret("FIRECRAWL_API_KEY")
        


@router.post("/agent/run")
async def agent_run(data: dict):
    pass






if __name__ == "__main__":
    import asyncio

    async def teste_importa_produtos():
        print ("==== importa_produtos() =====")
        firecrawl = AgentFireCrawl()
        base_domain = "https://loja.marykay.com.br" # Domínio base para URLs relativas
        base_url = f"{base_domain}/todos-os-produtos"

        # !!! IMPORTANTE: Descubra o número total de páginas no site !!!
        total_pages = 1 # Exemplo: Ajuste este valor! Comece com 1 ou 2 para testa
        
        output_filename = "mary_kay_products.json"
        api_key = secret.get_secret("FIRECRAWL_API_KEY")
        print("api_key", api_key)

        # --- Schema de Extração (REAJUSTADO com base no HTML completo) ---
        extraction_schema = {
            "type": "list",
            "attribute": "products", # Nome da chave onde a lista de produtos ficará no JSON
            # Seletor para o LINK (<a>) que envolve CADA produto
            "selector": "a.vtex-product-summary-2-x-clearLink",
            "schema": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        # Seletor para o NOME do produto (dentro do link/article)
                        "selector": "span.vtex-product-summary-2-x-productBrand"
                    },
                    "price": {
                        "type": "string",
                         # Seletor para o PREÇO formatado (dentro do link/article)
                        "selector": "span.vtex-store-components-3-x-sellingPriceValue"
                         # Pode precisar limpar o "R$ " depois
                    },
                    "relative_url": { # Nome alterado para clareza
                        "type": "string",
                        # Extrai o href do próprio elemento 'a' selecionado acima
                        "selector": "a.vtex-product-summary-2-x-clearLink", # Pode ser redundante, mas explícito
                        "extract": "href"
                    },
                    "image_url": {
                        "type": "string",
                        # Seletor para a IMAGEM do produto (dentro do link/article)
                        "selector": "img.vtex-product-summary-2-x-imageNormal",
                        "extract": "src" # Extrai o valor do atributo src da imagem
                    }
                }
            }
        }
        

        # --- Execução ---
        all_products = []
        app = None
        api_key = secret.get_secret("FIRECRAWL_API_KEY")

        print(f"Iniciando o Firecrawl para extrair produtos de {base_url}...")
        print(f"API Key encontrada: {'Sim' if api_key and api_key != 'SUA_API_KEY_AQUI' else 'Não (Verifique .env   ou    script!)'}")



        try:
            app = FirecrawlApp(api_key=api_key)

            for page_num in range(1, total_pages + 1):
                page_url = f"{base_url}?page={page_num}"
                print(f"Processando página {page_num}/{total_pages}: {page_url}")

                try:
                    scraped_data = app.scrape_url(
                        url=page_url,
                        pageOptions={'onlyMainContent': False, 'includeHtml': False},
                        extractorOptions={'mode': 'selector', 'extractionSchema': extraction_schema}
                    )

                    if scraped_data and 'data' in scraped_data and isinstance(scraped_data['data'], dict) and       'products' in scraped_data['data']:
                        extracted_products_page = scraped_data['data']['products']
                        if extracted_products_page:
                            print(f"  Encontrados {len(extracted_products_page)} produtos na página {page_num}.")
                            # Processa cada produto para montar URL absoluta e adicionar à lista
                            for product in extracted_products_page:
                                 if isinstance(product, dict) and 'relative_url' in product:
                                      # Monta a URL absoluta
                                      product['absolute_url'] = urljoin(base_domain, product['relative_url'])
                                      # Opcional: remover a chave relativa se não for mais necessária
                                      # del product['relative_url']
                                      all_products.append(product)
                                 else:
                                      print(f"  Item inválido ou sem URL relativa encontrado: {product}")

                        else:
                             print(f"  Nenhum produto encontrado na página {page_num} (lista vazia ou None).")

                    elif scraped_data and 'error' in scraped_data:
                         print(f"  Erro retornado pelo Firecrawl na página {page_num}: {scraped_data.get    ('error',   'Erro desconhecido')}")

                    else:
                        print(f"  Resposta inesperada ou falha na extração na página {page_num}.")

                except Exception as e:
                    print(f"  Erro ao processar a API call para a página {page_num}: {e}")

                time.sleep(1) # Pausa

            print(f"\nExtração concluída. Total de {len(all_products)} produtos coletados em {total_pages}  páginas  processadas.")

            if all_products:
                print(f"Salvando produtos em {output_filename}...")
                with open(output_filename, 'w', encoding='utf-8') as f:
                    json.dump(all_products, f, ensure_ascii=False, indent=4)
                print("Arquivo salvo com sucesso!")
            else:
                print("Nenhum produto foi coletado para salvar.")

        except Exception as e:
            print(f"\nErro geral ao inicializar ou executar o Firecrawl: {e}")

        finally:
            # Cleanup (se necessário)
            pass

    asyncio.run(teste_importa_produtos())
