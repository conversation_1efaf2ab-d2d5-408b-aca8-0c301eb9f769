from agents.tool import function_tool

@function_tool
async def test_function_1(param1: str, param2: int):
    """Função de teste 1 para verificar detecção de FunctionTool"""
    return f"Teste 1: {param1} - {param2}"

@function_tool
async def test_function_2(name: str):
    """Função de teste 2 para verificar detecção de FunctionTool"""
    return f"O<PERSON><PERSON>, {name}!"

def regular_function():
    """Função regular não decorada"""
    return "Esta é uma função regular"
    print(test_function_2("b"))
    print(regular_function())


if __name__ == "__main__":
    print(regular_function())
    print(test_function_1("a", 1))
    print(test_function_2("b"))
   
