from ...agents.agent00.gemini_vertex import GeminiVertex
from ...agents.agent00.gemini  import Gemini
from ...agents.agent00.openai import OpenAi


models_openai = ["gpt-4-0125-preview",
                "gpt-3.5-turbo-1106",
                "gpt-3.5-turbo-0613"]


models_gemini = ["gemini-pro",
                 "gemini-1.0-pro_generate", 
                 "gemini.pro_generate",
                 "gemini-1.5-pro-latest"
                 ]

async def llm_model_execute(
history=None,
tools=None,
functions=None,
llm = None,
message=None,
llmtype =None
):
     #print("*****llm_model_executa()*****",llm,message)
     
     result = ""
     
     if llm in models_openai:
          openai = OpenAi()
          #print(f"modelo da OPENAI: {llm}")
          result = await openai.run(
            llm=llm,
             llmtype = llmtype,
            tools= tools,
            functions=functions,
            message=message) 
          return result

    
     if llm in models_gemini:
          print(f"modelo gemini:{llm}")
          
          if llmtype == "image":
              gemini = GeminiVertex()
          else: 
              gemini = Gemini()
                 
          result = await gemini.run(
          llm=llm,
          llmtype = llmtype,
          tools= tools,
          functions=functions,
          message=message
           
          )

          print("result llm_execute", result)
          return result