from fastapi import APIRouter
from .agent_mysql import Mysql
from .agent_logger import AgentLogger

logger = AgentLogger()

router = APIRouter()


class AgentMaryKay:
    def __init__(self):
        self.nome = "<PERSON>"
        self.descricao = "<PERSON><PERSON>"
        self.tipo = "marykay"
        self.mysql = Mysql()

    def _format_query_with_params(self, sql: str, params: tuple) -> str:
        """Formata a query SQL substituindo os placeholders pelos valores reais"""
        try:
            # Substituir %s pelos valores reais
            formatted_sql = sql
            for param in params:
                if isinstance(param, str):
                    # Escapar aspas simples e adicionar aspas
                    escaped_param = param.replace("'", "''")
                    formatted_sql = formatted_sql.replace("%s", f"'{escaped_param}'", 1)
                else:
                    # Para números, booleanos, etc.
                    formatted_sql = formatted_sql.replace("%s", str(param), 1)
            
            return formatted_sql
        except Exception as e:
            logger.error(f"Erro ao formatar query: {e}")
            return f"ERRO AO FORMATAR: {sql} | PARAMS: {params}"

    def _construir_busca_inteligente(self, texto_busca: str, negocio_idx: str, usar_fulltext: bool = True):
        """
        Constrói busca inteligente por múltiplas palavras com FULLTEXT ou LIKE como fallback
        
        Args:
            texto_busca (str): Texto digitado pelo usuário (ex: "aroma labial") 
            negocio_idx (str): IDX do negócio
            usar_fulltext (bool): Se deve tentar usar FULLTEXT primeiro
            
        Returns:
            tuple: (where_clause, params_list)
            
        Exemplos:
            "mary kay" → FULLTEXT: +mary +kay (ambas obrigatórias)
            "aroma labial" → FULLTEXT: +aroma +labial (ambas obrigatórias)
            "very matte" → Encontra "Very Light Matte" (ambas palavras presentes)
        """
        logger.info(f"🔍 Construindo busca inteligente para: '{texto_busca}'")
        
        # Separar o texto em palavras individuais
        palavras = [palavra.strip() for palavra in texto_busca.split() if palavra.strip()]
        
        if not palavras:
            # Se não há palavras válidas, retornar busca básica
            return "WHERE NEGOCIO_IDX = %s AND EXCLUIDO = 0", [negocio_idx]
        
        if len(palavras) == 1:
            # Busca simples com uma palavra
            palavra = palavras[0]
            where_clause = "WHERE (NOME LIKE %s OR CODIGO LIKE %s OR COR_NOME LIKE %s OR DESCR LIKE %s) AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
            like_param = f'%{palavra}%'
            params = [like_param, like_param, like_param, like_param, negocio_idx]
            logger.info(f"🔍 Busca simples (1 palavra): '{palavra}'")
            return where_clause, params
        
        # Busca com múltiplas palavras
        logger.info(f"🔍 Busca inteligente com {len(palavras)} palavras: {palavras}")
        
        if usar_fulltext:
            # Como VISAO_PRODUTO_MARYKAY é uma VIEW, vamos usar LIKE otimizado
            # Os índices FULLTEXT nas tabelas base (PRODUTO e COR) já otimizam as consultas
            logger.info(f"🔍 VIEW detectada - usando LIKE otimizado (com índices FULLTEXT nas tabelas base)")
            # Continuar para o fallback LIKE que será mais eficiente agora
        
        # Fallback: Busca com LIKE para múltiplas palavras
        # Cada palavra deve estar presente em pelo menos uma das colunas
        conditions = []
        params = []
        
        for palavra in palavras:
            like_param = f'%{palavra}%'
            condition = "(NOME LIKE %s OR CODIGO LIKE %s OR COR_NOME LIKE %s OR DESCR LIKE %s)"
            conditions.append(condition)
            # Adicionar o mesmo parâmetro 4 vezes (uma para cada coluna)
            params.extend([like_param, like_param, like_param, like_param])
        
        # Juntar todas as condições com AND (todas as palavras devem estar presentes)
        where_clause = "WHERE " + " AND ".join(conditions) + " AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
        params.append(negocio_idx)
        
        logger.info(f"✅ Usando LIKE fallback com {len(palavras)} palavras")
        logger.info(f"🔍 Condições: {len(conditions)} grupos de condições")
        
        return where_clause, params

    async def get_produtos(self, data: dict):
        """Busca produtos no catálogo Mary Kay"""
        logger.info(f"AgentMaryKay.get_produtos chamado com: {data}")
        
        try:
            # Buscar tabela diretamente de data, com fallback
            tabela = data.get('table', 'VISAO_PRODUTO_MARYKAY')
            colunas = ["CODIGO", "NOME", "URL_IMAGEM", "PRECO", "COR_NOME", "COR_HEXADECIMAL", "ESTOQUE", "DESCR"]
            
            # Extrair parâmetros dos dados
            texto_busca = data.get('text', '')
            query = texto_busca
            offset = data.get('offset', 0)
            limit = data.get('limit', 10)
            
            
            logger.info(f"Parâmetros de busca: query='{texto_busca}', offset={offset}, limit={limit}")
            
            # Usar busca inteligente para construir WHERE clause
            negocio_idx = data.get('negocio_idx', '5544332211')
            
            if texto_busca and texto_busca.strip():
                # Usar busca inteligente para múltiplas palavras
                where_clause, base_params = self._construir_busca_inteligente(texto_busca, negocio_idx, usar_fulltext=True)
                logger.info(f"🔍 Busca INTELIGENTE COM filtro de texto: '{texto_busca}'")
            else:
                # Se não há texto, buscar todos os produtos
                where_clause = "WHERE NEGOCIO_IDX = %s AND EXCLUIDO = 0"
                base_params = [negocio_idx]
                logger.info(f"🔍 Busca SEM filtro de texto - retornando todos os produtos")

            # Nova lógica: se limit = 0, não aplica LIMIT
            if limit == 0:
                # Sem limitação - retorna todos os resultados
                sql = f"""
                        SELECT {', '.join(colunas)}
                        FROM {tabela}
                        {where_clause}
                        ORDER BY NOME
                        """
                params = tuple(base_params)
            else:
                # Com limitação normal
                sql = f"""
                        SELECT {', '.join(colunas)}
                        FROM {tabela}
                        {where_clause}
                        ORDER BY NOME
                        LIMIT %s, %s
                        """
                params = tuple(base_params + [offset, limit])
            
            # Mostrar query original com parâmetros separados
            logger.info(f"SQL original:\n{sql}")
            logger.info(f"Parâmetros: {params}")
            
            # Mostrar query formatada com valores substituídos
            query_formatada = self._format_query_with_params(sql, params)
            logger.info(f"🔍 SQL FINAL (com valores substituídos):\n{query_formatada}")
            
            # Executar consulta com tratamento de erro simplificado
            resultados = await self.mysql.query(sql, params=params)
            logger.info(f"***** QUERY *****: {query}")
            logger.info(f"✅ Query executada com sucesso")
            
            if resultados is None:
                logger.warning("⚠️ Query retornou None - possível problema na conexão")
                resultados = []
            elif not isinstance(resultados, list):
                logger.warning(f"⚠️ Query retornou tipo inesperado: {type(resultados)}")
                resultados = []
            
            logger.info(f"Resultados encontrados: {len(resultados)}")
            
            # Verificar se não encontrou resultados
            if len(resultados) == 0:
                logger.info(f"ℹ️ Nenhum produto encontrado para '{texto_busca}'")
            else:
                logger.info(f"🎯 Encontrados {len(resultados)} produtos:")
                for i, produto in enumerate(resultados[:3], 1):  # Mostrar primeiros 3
                    logger.info(f"   {i}. {produto.get('CODIGO', 'N/A')} - {produto.get('NOME', 'N/A')}")
                    # Log da URL da imagem para debug
                    url_imagem = produto.get('URL_IMAGEM', 'N/A')
                    logger.info(f"      URL_IMAGEM: {url_imagem}")
                    if url_imagem and len(url_imagem) > 100:
                        logger.info(f"      URL completa (len={len(url_imagem)}): {url_imagem}")
                if len(resultados) > 3:
                    logger.info(f"   ... e mais {len(resultados) - 3} produto(s)")
            
            # Retornar resposta estruturada
            resposta = {
                "success": True,
                "data": resultados,
                "total": len(resultados),
                "message": f"Encontrados {len(resultados)} produtos para '{texto_busca}'"
            }
            
            logger.info(f"Resposta construída com sucesso: {len(resultados)} produtos")
            return resposta
            
        except Exception as e:
            logger.error(f"❌ ERRO em get_produtos: {str(e)}")
            
            # Identificar tipos específicos de erro para melhor feedback
            error_msg = str(e).lower()
            if "doesn't exist" in error_msg or "não existe" in error_msg:
                logger.error(f"🔥 TABELA NÃO EXISTE: Verifique se '{tabela}' existe no banco")
                return {
                    "success": False,
                    "error": f"Tabela '{tabela}' não existe no banco de dados",
                    "message": "Erro de configuração - verifique o nome da tabela",
                    "data": [],
                    "total": 0
                }
            elif "unknown column" in error_msg or "coluna desconhecida" in error_msg:
                logger.error(f"🔥 COLUNA NÃO EXISTE: Verifique as colunas {colunas}")
                return {
                    "success": False,
                    "error": f"Uma ou mais colunas não existem: {colunas}",
                    "message": "Erro de estrutura do banco - verifique as colunas",
                    "data": [],
                    "total": 0
                }
            else:
                # Erro genérico
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {
                    "success": False,
                    "error": str(e),
                    "message": "Erro interno do sistema"
                }

    async def get_cores(self, data: dict):
        """Busca cores no catálogo Mary Kay"""
        logger.info(f"AgentMaryKay.get_cores chamado com: {data}")
        
        try:
            # Definir tabela e colunas para cores
            tabela = "COR"
            colunas = ["CODIGO", "NOME", "HEXADECIMAL"]
            
            # Extrair parâmetros dos dados
            query = data.get('text', '')
            offset = data.get('filters', {}).get('offset', 0)
            limit = data.get('filters', {}).get('limit', 50)  # Limite maior para cores
            
            # Obter o texto de busca
            texto_busca = data.get('filters', {}).get('text', query)
            
            logger.info(f"Parâmetros de busca cores: query='{texto_busca}', offset={offset}, limit={limit}")
            
            # Obter negocio_idx (sempre usar Mary Kay para cores)
            negocio_idx = '5544332211'  # Mary Kay
            
            # Construir WHERE clause
            if texto_busca and texto_busca.strip():
                # Busca por nome ou código da cor + filtro por negócio
                where_clause = "WHERE (NOME LIKE %s OR CODIGO LIKE %s) AND NEGOCIO_IDX = %s"
                like_param = f'%{texto_busca}%'
                base_params = [like_param, like_param, negocio_idx]
                logger.info(f"🔍 Busca de cores COM filtro de texto: '{texto_busca}' e NEGOCIO_IDX: {negocio_idx}")
            else:
                # Se não há texto, buscar todas as cores do negócio
                where_clause = "WHERE NEGOCIO_IDX = %s"
                base_params = [negocio_idx]
                logger.info(f"🔍 Busca de cores SEM filtro de texto - retornando todas as cores do NEGOCIO_IDX: {negocio_idx}")

            # Construir SQL com ou sem limite
            if limit == 0:
                # Sem limitação - retorna todas as cores
                sql = f"""
                        SELECT {', '.join(colunas)}
                        FROM {tabela}
                        {where_clause}
                        ORDER BY NOME
                        """
                params = tuple(base_params)
            else:
                # Com limitação normal
                sql = f"""
                        SELECT {', '.join(colunas)}
                        FROM {tabela}
                        {where_clause}
                        ORDER BY NOME
                        LIMIT %s, %s
                        """
                params = tuple(base_params + [offset, limit])
            
            # Mostrar query original com parâmetros separados
            logger.info(f"SQL original:\n{sql}")
            logger.info(f"Parâmetros: {params}")
            
            # Mostrar query formatada com valores substituídos
            query_formatada = self._format_query_with_params(sql, params)
            logger.info(f"🎨 SQL FINAL cores (com valores substituídos):\n{query_formatada}")
            
            # Executar consulta
            resultados = await self.mysql.query(sql, params=params)
            logger.info(f"✅ Query de cores executada com sucesso")
            
            if resultados is None:
                logger.warning("⚠️ Query de cores retornou None")
                resultados = []
            elif not isinstance(resultados, list):
                logger.warning(f"⚠️ Query de cores retornou tipo inesperado: {type(resultados)}")
                resultados = []
            
            logger.info(f"Cores encontradas: {len(resultados)}")
            
            # Verificar se não encontrou resultados
            if len(resultados) == 0:
                logger.info(f"ℹ️ Nenhuma cor encontrada para '{texto_busca}'")
            else:
                logger.info(f"🎨 Encontradas {len(resultados)} cores:")
                for i, cor in enumerate(resultados[:5], 1):  # Mostrar primeiras 5
                    logger.info(f"   {i}. {cor.get('CODIGO', 'N/A')} - {cor.get('NOME', 'N/A')} ({cor.get('HEXADECIMAL', 'N/A')})")
                if len(resultados) > 5:
                    logger.info(f"   ... e mais {len(resultados) - 5} cor(es)")
            
            # Retornar resposta estruturada
            resposta = {
                "success": True,
                "data": resultados,
                "total": len(resultados),
                "message": f"Encontradas {len(resultados)} cores para '{texto_busca}'"
            }
            
            logger.info(f"Resposta de cores construída com sucesso: {len(resultados)} cores")
            return resposta
            
        except Exception as e:
            logger.error(f"❌ ERRO em get_cores: {str(e)}")
            
            # Identificar tipos específicos de erro
            error_msg = str(e).lower()
            if "doesn't exist" in error_msg or "não existe" in error_msg:
                logger.error(f"🔥 TABELA COR NÃO EXISTE: Verifique se 'COR' existe no banco")
                return {
                    "success": False,
                    "error": f"Tabela 'COR' não existe no banco de dados",
                    "message": "Erro de configuração - verifique o nome da tabela",
                    "data": [],
                    "total": 0
                }
            elif "unknown column" in error_msg or "coluna desconhecida" in error_msg:
                logger.error(f"🔥 COLUNA NÃO EXISTE na tabela COR: Verifique as colunas {colunas}")
                return {
                    "success": False,
                    "error": f"Uma ou mais colunas não existem na tabela COR: {colunas}",
                    "message": "Erro de estrutura do banco - verifique as colunas",
                    "data": [],
                    "total": 0
                }
            else:
                # Erro genérico
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {
                    "success": False,
                    "error": str(e),
                    "message": "Erro interno do sistema",
                    "data": [],
                    "total": 0
                }



@router.post("/products/fetch")
async def fetch_products(data: dict):
    """Endpoint para busca de produtos Mary Kay"""
    logger.info(f"Endpoint /products/fetch chamado com: {data}")
    agent = AgentMaryKay()
    resultado = await agent.get_produtos(data)  # Corrigido com await
    logger.info(f"Retornando: {resultado}")
    return resultado


@router.post("/colors/fetch")
async def fetch_colors(data: dict):
    """Endpoint para busca de cores Mary Kay"""
    logger.info(f"Endpoint /colors/fetch chamado com: {data}")
    agent = AgentMaryKay()
    resultado = await agent.get_cores(data)
    logger.info(f"Retornando: {resultado}")
    return resultado


# ===== TESTES =====
if __name__ == "__main__":
    import asyncio

    async def teste_busca_produtos_mary_kay():
        """Teste da busca de produtos Mary Kay com texto 'Esfoliante Facial Mary Kay'"""
        logger.info("\n=== TESTE BUSCA PRODUTOS MARY KAY ===")
        
        # Mock da classe Mysql para teste
        class MockMysql:
            async def query(self, sql, params=None):
                # Simular retorno de produtos da busca
                if "VISAO_PRODUTO_MARYKAY" in sql and ("Esfoliante" in str(params) or "esfoliante" in str(params)):
                    return [
                        {
                            "CODIGO": "10123456",
                            "NOME": "Esfoliante Facial Mary Kay TimeWise",
                            "URL_IMAGEM": "https://marykay.com/produto1.jpg"
                        },
                        {
                            "CODIGO": "10234567", 
                            "NOME": "Esfoliante Corporal Mary Kay",
                            "URL_IMAGEM": "https://marykay.com/produto2.jpg"
                        }
                    ]
                return []
        
        # Versão simplificada do AgentMaryKay para teste
        class AgentMaryKayTeste:
            def __init__(self):
                self.nome = "Mary Kay"
                self.mysql = MockMysql()
                
            async def get_produtos(self, data: dict):
                logger.info(f"AgentMaryKay.get_produtos chamado com: {data}")
                
                try:
                    # Simular lógica de busca
                    query_text = data.get('filters', {}).get('text', '')
                    
                    if not query_text:
                        return {"success": False, "message": "Texto de busca é obrigatório"}
                    
                    # Simular SQL query
                    sql_query = f"""
                    SELECT CODIGO, NOME, URL_IMAGEM, PRECO, COR_NOME, COR_HEXADECIMAL
                    FROM VISAO_PRODUTO_MARYKAY 
                    WHERE (NOME LIKE '%{query_text}%' OR CODIGO LIKE '%{query_text}%')
                    AND NEGOCIO_IDX = '5544332211'
                    ORDER BY NOME
                    LIMIT 10
                    """
                    
                    logger.info(f"SQL Query simulada: {sql_query}")
                    
                    # Para o teste, vamos simular dados encontrados
                    if "Esfoliante" in query_text:
                        produtos_simulados = [
                            {
                                "CODIGO": "10123456",
                                "NOME": "Esfoliante Facial Mary Kay TimeWise",
                                "URL_IMAGEM": "https://marykay.com/produto1.jpg"
                            },
                            {
                                "CODIGO": "10234567", 
                                "NOME": "Esfoliante Corporal Mary Kay",
                                "URL_IMAGEM": "https://marykay.com/produto2.jpg"
                            }
                        ]
                        
                        return {
                            "success": True,
                            "data": produtos_simulados,
                            "total": len(produtos_simulados),
                            "query": sql_query,
                            "message": f"Encontrados {len(produtos_simulados)} produtos"
                        }
                    else:
                        return {
                            "success": True,
                            "data": [],
                            "total": 0,
                            "query": sql_query,
                            "message": "Nenhum produto encontrado"
                        }
                        
                except Exception as e:
                    return {"success": False, "error": str(e)}
        
        # Dados simulando a requisição do frontend
        data_teste = {
            "query": "Esfoliante Facial Mary Kay",
            "selectbox": "opcao1",  # Produtos Mary Kay
            "filters": {
                "offset": 0,
                "limit": 10,
                "text": "Esfoliante Facial Mary Kay"
            }
        }
        
        logger.info(f"📤 Testando busca com dados:")
        logger.info(f"   Query: '{data_teste['query']}'")
        logger.info(f"   Selectbox: {data_teste['selectbox']}")
        logger.info(f"   Texto para busca: '{data_teste['filters']['text']}'")
        
        try:
            logger.info(f"\n🔧 Criando agente Mary Kay (versão teste)...")
            agent = AgentMaryKayTeste()
            logger.info(f"✅ Agente criado: {agent.nome}")
            
            logger.info(f"\n🔍 Executando busca por produtos...")
            resultado = await agent.get_produtos(data_teste)
            
            logger.info(f"\n📊 RESULTADO:")
            logger.info(f"Tipo: {type(resultado)}")
            logger.info(f"Conteúdo: {resultado}")
            
            # Analisar resultado
            if isinstance(resultado, dict):
                success = resultado.get("success", False)
                data_produtos = resultado.get("data", [])
                query_usada = resultado.get("query", "")
                total = resultado.get("total", 0)
                
                logger.info(f"\n📋 ANÁLISE:")
                logger.info(f"✅ Success: {success}")
                logger.info(f"🔍 Query usada: '{query_usada}'")
                logger.info(f"📦 Total produtos: {total}")
                logger.info(f"📄 Dados produtos: {len(data_produtos) if isinstance(data_produtos, list) else 'N/A'}")
                
                if success and isinstance(data_produtos, list):
                    logger.info(f"\n🎯 PRODUTOS ENCONTRADOS:")
                    for i, produto in enumerate(data_produtos, 1):
                        logger.info(f"   {i}. Código: {produto.get('CODIGO', 'N/A')}")
                        logger.info(f"      Nome: {produto.get('NOME', 'N/A')}")
                        logger.info(f"      Imagem: {produto.get('URL_IMAGEM', 'N/A')}")
                        logger.info(f"      ---")
                        
                    logger.info(f"🎉 TESTE CONCLUÍDO COM SUCESSO!")
                    return True
                else:
                    if not success:
                        error_msg = resultado.get("message", resultado.get("error", "Erro desconhecido"))
                        logger.error(f"❌ ERRO: {error_msg}")
                    else:
                        logger.error(f"❌ DADOS INVÁLIDOS: Estrutura de dados inesperada")
                    return False
            else:
                logger.error(f"❌ FORMATO INESPERADO: {resultado}")
                return False
                
        except Exception as e:
            logger.error(f"❌ ERRO NO TESTE: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    async def teste_fluxo_completo():
        """Testa o fluxo completo: Frontend -> Backend -> Resposta"""
        logger.info("\n=== TESTE FLUXO COMPLETO ===")
        
        logger.info("📱 SIMULAÇÃO DO FLUXO:")
        logger.info("1. Usuário digita 'Esfoliante Facial Mary Kay' no input")
        logger.info("2. Após 2 segundos, executarBusca() é chamada")
        logger.info("3. get_url_filters() mapeia para '/agent/marykay/products/fetch'")
        logger.info("4. searchbar_03_execute() faz POST para o backend")
        logger.info("5. Backend processa e retorna dados")
        logger.info("6. exibirResultados() mostra na tela")
        
        # Simular dados do passo 4
        dados_post = {
            "query": "Esfoliante Facial Mary Kay",
            "selectbox": "opcao1",
            "filters": {
                "offset": 0,
                "limit": 10,
                "text": "Esfoliante Facial Mary Kay"
            }
        }
        
        logger.info(f"\n📤 Dados que seriam enviados via POST:")
        logger.info(f"URL: /agent/marykay/products/fetch")
        logger.info(f"Body: {dados_post}")
        
        # Simular resposta esperada do backend
        resposta_esperada = {
            "success": True,
            "data": [
                {
                    "CODIGO": "10123456",
                    "NOME": "Esfoliante Facial Mary Kay TimeWise",
                    "URL_IMAGEM": "https://marykay.com/produto1.jpg"
                }
            ],
            "total": 1,
            "message": "Produtos encontrados com sucesso"
        }
        
        logger.info(f"\n📨 Resposta esperada do backend:")
        logger.info(f"{resposta_esperada}")
        
        logger.info(f"\n💻 Frontend processaria a resposta e exibiria:")
        logger.info(f"- Total de {resposta_esperada['total']} produto(s) encontrado(s)")
        logger.info(f"- Lista de produtos no container searchbar_03-body")
        
        logger.info(f"🎉 FLUXO COMPLETO VALIDADO!")
        return True

    async def executar_todos_os_testes():
        logger.info("\n" + "="*60)
        logger.info("🧪 EXECUTANDO TESTES DA BUSCA MARY KAY")
        logger.info("="*60)
        
        resultados = []
        
        # Teste 1: Função básica
        logger.info("\n🔧 TESTE 1: Função get_produtos()")
        resultado1 = await teste_busca_produtos_mary_kay()
        resultados.append(("get_produtos()", resultado1))
        
        # Teste 2: Fluxo completo
        logger.info("\n🌐 TESTE 2: Fluxo completo Frontend->Backend")
        resultado2 = await teste_fluxo_completo()
        resultados.append(("fluxo_completo", resultado2))
        
        # Relatório final
        logger.info("\n" + "="*60)
        logger.info("📊 RELATÓRIO FINAL DOS TESTES")
        logger.info("="*60)
        
        for nome, sucesso in resultados:
            status = "✅ PASSOU" if sucesso else "❌ FALHOU"
            logger.info(f"{nome:20} | {status}")
        
        total_passou = sum(1 for _, sucesso in resultados if sucesso)
        total_testes = len(resultados)
        
        logger.info(f"\n🎯 RESULTADO: {total_passou}/{total_testes} testes passaram")
        
        if total_passou == total_testes:
            logger.info("🎉 TODOS OS TESTES PASSARAM! Sistema pronto para uso!")
            logger.info("\n💡 PRÓXIMOS PASSOS:")
            logger.info("1. Inicie o servidor backend")
            logger.info("2. Abra o frontend no navegador") 
            logger.info("3. Digite 'Esfoliante Facial Mary Kay' na barra de pesquisa")
            logger.info("4. Aguarde 2 segundos e veja os resultados!")
        else:
            logger.info("⚠️  Alguns testes falharam. Verifique os logs acima.")
        
        return total_passou == total_testes

    # Executar os testes
    logger.info("🚀 Iniciando testes...")
    asyncio.run(executar_todos_os_testes())