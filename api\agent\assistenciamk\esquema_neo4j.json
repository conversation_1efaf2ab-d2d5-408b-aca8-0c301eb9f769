{"database": "neo4j", "node_types": [{"label": "Negocio", "properties": [{"name": "bairro", "type": "STRING"}, {"name": "cep", "type": "STRING"}, {"name": "cidade", "type": "STRING"}, {"name": "cpf_cnpj", "type": "STRING"}, {"name": "complemento", "type": "STRING"}, {"name": "email", "type": "STRING"}, {"name": "excluido", "type": "INTEGER"}, {"name": "idx", "type": "STRING"}, {"name": "logradouro", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "numero", "type": "STRING"}, {"name": "razao_social", "type": "STRING"}, {"name": "telefone", "type": "STRING"}, {"name": "uf", "type": "STRING"}]}, {"label": ["<PERSON><PERSON><PERSON>"], "properties": [{"name": "NOME", "type": "STRING"}, {"name": "bairro", "type": "STRING"}, {"name": "cep", "type": "STRING"}, {"name": "cidade", "type": "STRING"}, {"name": "complemento", "type": "STRING"}, {"name": "cpf_cnpj", "type": "STRING"}, {"name": "dataCadastro", "type": "DATE_TIME|STRING"}, {"name": "email", "type": "STRING"}, {"name": "emailConfirmado", "type": "INTEGER"}, {"name": "excluido", "type": "INTEGER"}, {"name": "id", "type": "INTEGER"}, {"name": "idx", "type": "STRING"}, {"name": "interacoes", "type": "INTEGER"}, {"name": "logradouro", "type": "STRING"}, {"name": "negocio_idx", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "numero", "type": "STRING"}, {"name": "<PERSON><PERSON>a", "type": "STRING"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "INTEGER"}, {"name": "telefone", "type": "STRING"}, {"name": "telefoneConfirmado", "type": "INTEGER"}, {"name": "tksGastos", "type": "INTEGER"}, {"name": "tksTotal", "type": "INTEGER"}, {"name": "uf", "type": "STRING"}]}, {"label": "Produ<PERSON>", "properties": [{"name": "codigo", "type": "STRING"}, {"name": "descricao", "type": "STRING"}, {"name": "estoque", "type": "INTEGER"}, {"name": "excluido", "type": "INTEGER"}, {"name": "idx", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "preco", "type": "FLOAT|STRING"}]}, {"label": "Cor", "properties": [{"name": "idx", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "codigo", "type": "STRING"}, {"name": "hexadecimal", "type": "STRING"}]}], "relationship_types": [{"type": "POSSUI_PRODUTO", "properties": []}, {"type": "TEM_COR", "properties": []}, {"type": "DISPONIBILIZA_COR", "properties": []}, {"type": "ATENDIDO_POR", "properties": []}, {"type": "CLIENTE_DE", "properties": []}], "patterns": [["Negocio", "POSSUI_PRODUTO", "Produ<PERSON>"], ["Produ<PERSON>", "TEM_COR", "Cor"], ["<PERSON><PERSON><PERSON>", "ATENDIDO_POR", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "CLIENTE_DE", "Negocio"], ["Negocio", "DISPONIBILIZA_COR", "Cor"]]}