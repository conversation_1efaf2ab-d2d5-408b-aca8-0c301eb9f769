import asyncio
import aiohttp
from datetime import datetime
import json

async def send_to_endpoint(data: dict) -> str:
    """
    Envia os dados para o endpoint e retorna a resposta.
    """
    endpoint = "http://127.0.0.1:8000/api/agent/brain/send/text"
    
    # Garante que a mensagem está no formato correto
    if 'mensagem' not in data or not data['mensagem']:
        return "Erro: Mensagem não fornecida"
    
    print(f"\nEnviando para o endpoint: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, json=data) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    return f"Erro na requisição: {response.status}"
    except Exception as e:
        return f"Erro ao conectar ao servidor: {str(e)}"

async def chat_loop():
    """
    Loop principal do chat interativo.
    """
    print("=== Chat GPTalk ===")
    print("Digite sua mensagem (ou 'sair' para encerrar):\n")
    
    # Dados iniciais da conversa
    data = {
        "mensagem": "",
        "negocio_idx": "4015743441",
        "modelo": "1234567895",
        "usuario_idx": "1122334455",
        "agente": "@gptalkzap",
        "canal": "whatsapp",
        "modo_resposta_ia": "texto"
    }
    
    while True:
        # Solicita a mensagem do usuário
        user_input = input("Você: ")
        
        # Verifica se o usuário quer sair
        if user_input.lower() == 'sair':
            print("\nEncerrando o chat...")
            break
            
        if not user_input.strip():
            continue
            
        # Cria uma cópia dos dados para não modificar o dicionário original
        request_data = data.copy()
        
        # Atualiza a mensagem na cópia dos dados
        request_data["mensagem"] = user_input
        
        # Exibe a mensagem do usuário
        print(f"\nVocê: {user_input}")
        print("\nAguarde, processando...")
        
        try:
            # Envia para o endpoint e obtém a resposta
            response = await send_to_endpoint(request_data)
            
            # Tenta formatar a resposta como JSON, se possível
            try:
                response_data = json.loads(response)
                if 'resposta' in response_data:
                    print(f"\nAssistente: {response_data['resposta']}")
                else:
                    print(f"\nResposta recebida: {response}")
            except json.JSONDecodeError:
                print(f"\nResposta recebida: {response}")
                
        except Exception as e:
            print(f"\nOcorreu um erro: {str(e)}")
        
        print("\n" + "-"*50 + "\n")

if __name__ == "__main__":
    print("Iniciando o chat...")
    asyncio.run(chat_loop())