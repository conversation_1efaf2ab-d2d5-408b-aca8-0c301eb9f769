#!/usr/bin/env python3
import asyncio
import sys
import os
import json
from pathlib import Path

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from api.agent.agent_mysql import Mysql
    from api.agent.agent_logger import AgentLogger
except ImportError as e:
    print(f"Erro ao importar módulos: {e}")
    print("Verifique se está executando do diretório correto")
    sys.exit(1)

from neo4j import GraphDatabase

# Configurar logging
logger = AgentLogger()

class ServicoMigrator:
    def __init__(self):
        self.mysql = Mysql()
        
        # Configurações Neo4j do mcp.json
        self.neo4j_uri = "bolt://5.161.204.141:7687"
        self.neo4j_user = "neo4j"
        self.neo4j_password = "gcs@neo4j"
        self.neo4j_database = "neo4j"
        
        # Conectar ao Neo4j
        self.neo4j_driver = GraphDatabase.driver(
            self.neo4j_uri, 
            auth=(self.neo4j_user, self.neo4j_password)
        )
        
        logger.info("✅ ServicoMigrator inicializado")
    
    def convert_to_neo4j_properties(self, servico_mysql):
        """
        Converte um serviço do MySQL para propriedades do Neo4j
        - Converte nomes de campos para minúsculas
        - Trata valores None apropriadamente
        - Converte tipos de dados conforme necessário
        """
        neo4j_properties = {}
        
        for key, value in servico_mysql.items():
            # Converter nome da propriedade para minúscula
            neo4j_key = key.lower()
            
            # Tratar valores None e diferentes tipos
            if value is None or value == "None":
                neo4j_properties[neo4j_key] = None
            elif isinstance(value, (int, float)):
                neo4j_properties[neo4j_key] = value
            elif isinstance(value, str):
                # Manter strings vazias como string vazia, não None
                neo4j_properties[neo4j_key] = value
            else:
                # Para outros tipos, converter para string
                neo4j_properties[neo4j_key] = str(value)
        
        return neo4j_properties
    
    async def buscar_servicos_mysql(self, negocio_idx):
        """Busca serviços do MySQL usando agent_mysql.py"""
        logger.info(f"🔍 Buscando serviços do negócio {negocio_idx} no MySQL...")
        
        try:
            # Buscar serviços usando o método fetch do agent_mysql
            colunas = "*"
            tabela = "SERVICO"
            filtros = [f"NEGOCIO_IDX = '{negocio_idx}'", "EXCLUIDO = 0"]
            
            servicos = await self.mysql.fetch(colunas, tabela, filtros)
            
            logger.info(f"✅ Encontrados {len(servicos)} serviços no MySQL")
            return servicos
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar serviços do MySQL: {str(e)}")
            raise e
    
    def criar_servico_neo4j(self, servico_neo4j):
        """Cria um nó Servico no Neo4j"""
        with self.neo4j_driver.session() as session:
            # Query para criar/atualizar o nó Servico
            query = """
            MERGE (s:Servico {idx: $idx, negocio_idx: $negocio_idx})
            SET s += $properties
            RETURN s
            """
            
            # Executar query
            result = session.run(query, {
                "idx": servico_neo4j.get("idx"),
                "negocio_idx": servico_neo4j.get("negocio_idx"),
                "properties": servico_neo4j
            })
            
            record = result.single()
            if record:
                return record["s"]
            else:
                return None
    
    async def migrar_servicos(self, negocio_idx):
        """Migra todos os serviços de um negócio do MySQL para Neo4j"""
        logger.info(f"🚀 Iniciando migração dos serviços do negócio {negocio_idx}")
        
        try:
            # 1. Buscar serviços no MySQL
            servicos_mysql = await self.buscar_servicos_mysql(negocio_idx)
            
            if not servicos_mysql:
                logger.warning(f"⚠️ Nenhum serviço encontrado no MySQL para negócio {negocio_idx}")
                return
            
            # 2. Migrar cada serviço
            servicos_migrados = 0
            servicos_erro = 0
            
            for servico_mysql in servicos_mysql:
                try:
                    # Converter propriedades para Neo4j
                    servico_neo4j = self.convert_to_neo4j_properties(servico_mysql)
                    
                    # Criar nó no Neo4j
                    resultado = self.criar_servico_neo4j(servico_neo4j)
                    
                    if resultado:
                        servicos_migrados += 1
                        logger.info(f"✅ Serviço migrado: {servico_mysql.get('NOME', 'N/A')} (IDX: {servico_mysql.get('IDX', 'N/A')})")
                    else:
                        servicos_erro += 1
                        logger.error(f"❌ Erro ao criar nó para serviço IDX: {servico_mysql.get('IDX', 'N/A')}")
                        
                except Exception as e:
                    servicos_erro += 1
                    logger.error(f"❌ Erro ao migrar serviço {servico_mysql.get('IDX', 'N/A')}: {str(e)}")
            
            # 3. Relatório final
            logger.info(f"📊 Migração concluída:")
            logger.info(f"   ✅ Serviços migrados: {servicos_migrados}")
            logger.info(f"   ❌ Serviços com erro: {servicos_erro}")
            logger.info(f"   📋 Total processado: {len(servicos_mysql)}")
            
            return {
                "total_servicos": len(servicos_mysql),
                "servicos_migrados": servicos_migrados,
                "servicos_erro": servicos_erro,
                "sucesso": servicos_migrados > 0
            }
            
        except Exception as e:
            logger.error(f"❌ Erro geral durante a migração: {str(e)}")
            raise e
    
    def verificar_servicos_neo4j(self):
        """Verifica os serviços criados no Neo4j"""
        logger.info("🔍 Verificando serviços no Neo4j...")
        
        with self.neo4j_driver.session() as session:
            # Buscar serviços do negócio específico
            query = """
            MATCH (s:Servico) 
            WHERE s.negocio_idx = '4015743441'
            RETURN s.idx as idx, s.nome as nome, s.preco as preco, s.codigo as codigo
            ORDER BY s.nome
            """
            
            result = session.run(query)
            servicos = [record.data() for record in result]
            
            logger.info(f"📋 Serviços encontrados no Neo4j: {len(servicos)}")
            for servico in servicos:
                logger.info(f"   - {servico['nome']} (IDX: {servico['idx']}, Código: {servico['codigo']}, Preço: {servico['preco']})")
            
            return servicos
    
    def close(self):
        """Fecha conexões"""
        if hasattr(self, 'neo4j_driver'):
            self.neo4j_driver.close()
            logger.info("🔒 Conexão Neo4j fechada")

async def main():
    migrator = ServicoMigrator()
    
    try:
        # Migrar serviços do negócio específico
        resultado = await migrator.migrar_servicos("4015743441")
        
        if resultado and resultado["sucesso"]:
            print("\n" + "="*50)
            print("🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!")
            print("="*50)
            print(f"📊 Total de serviços: {resultado['total_servicos']}")
            print(f"✅ Serviços migrados: {resultado['servicos_migrados']}")
            print(f"❌ Serviços com erro: {resultado['servicos_erro']}")
            
            # Verificar resultado no Neo4j
            print("\n" + "="*50)
            print("🔍 VERIFICANDO SERVIÇOS NO NEO4J")
            print("="*50)
            servicos_neo4j = migrator.verificar_servicos_neo4j()
            
            if servicos_neo4j:
                print(f"✅ {len(servicos_neo4j)} serviços verificados no Neo4j")
            else:
                print("⚠️ Nenhum serviço encontrado no Neo4j")
        else:
            print("❌ Migração falhou ou não foi executada")
            
    except Exception as e:
        print(f"❌ Erro durante a migração: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        migrator.close()

if __name__ == "__main__":
    asyncio.run(main()) 