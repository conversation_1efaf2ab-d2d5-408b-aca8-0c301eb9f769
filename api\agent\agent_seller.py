from functions.util.period import _calculate_periods, get_period_type
import json
from .agent_mysql import Mysql
from fastapi import APIRouter
import re
from datetime import datetime
router = APIRouter()


class Seller:

    def __init__(self):
        self.mysql = Mysql()

    async def payment_methods_fetch_business(self, business_idx: str):
        print("===== payment_methods_fetch_business() =====", business_idx)
        result = await self.mysql.fetch("*", "VENDA_FORMA_PAGAMENTO", [f"NEGOCIO_ID = '{business_idx}'", "EXCLUIDO = 0"])
        print("result", result)
        return result

    async def payment_methods_fetch(self, business_id: int, colunas_nome: dict):
        result = await self.mysql.fetch(colunas_nome, "VENDA_FORMA_PAGAMENTO", [f"NEGOCIO_ID = '{business_id}'", "EXCLUIDO = 0"])
        return result

    async def payment_methods_add(self, data: dict):
        print("===== payment_methods_add() =====", data)
        inserts = []
        for record in data['adicionados']:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO VENDA_FORMA_PAGAMENTO ({columns}) VALUES ({values});"
            inserts.append(query)
        # Combine all the insert queries into a single statement
        query_insert = " ".join(inserts)
        result = await self.mysql.query(query_insert)
        return

    async def payment_methods_update(self, data: dict):
        #print("===== payment_methods_update() =====", data)
        updates = []
        # print("===== data =====",data)
        for record in data['atualizados']:
            idx = record['IDX']
            del record['IDX']
            sets = ", ".join(
                [f"{key} = '{value}'" for key, value in record.items()])
            query = f"UPDATE VENDA_FORMA_PAGAMENTO SET {sets} WHERE IDX = {idx};"
            updates.append(query)
        # Combine all the update queries into a single statement
        query_update = " ".join(updates)
        result = await self.mysql.query(query_update)
        return

    async def sale_add(self, data: dict):

        # print("salve_save()",data)
        try:
            # Inserir a venda
            venda_columns = ", ".join(data['venda'].keys())
            venda_values = ", ".join(
                [f"'{value}'" for value in data['venda'].values()])
            venda_query = f"INSERT INTO VENDA ({venda_columns}) VALUES ({venda_values});"
            venda_id = await self.mysql.query(venda_query)

            # Inserir os produtos da venda
            produto_inserts = []
            for produto in data['produtos']:
                if 'NOME' in produto:
                    del produto['NOME']
                produto_columns = ", ".join(produto.keys())
                produto_values = ", ".join(
                    [f"'{value}'" for value in produto.values()])
                produto_query = f"INSERT INTO VENDA_PRODUTO ({produto_columns}) VALUES ({produto_values});"
                produto_inserts.append(produto_query)

            produto_query = " ".join(produto_inserts)
            await self.mysql.query(produto_query)

            return {"status": "success", "venda_id": venda_id}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def fetch_sales(self, negocio_idx: str, filters: dict):
        # Primeiro, vamos obter a contagem total
        count_query = f"""
        SELECT COUNT(*) as QTDE
        FROM VISAO_VENDA_PAGAMENTO v
        WHERE v.NEGOCIO_IDX = '{negocio_idx}' AND v.EXCLUIDO = 0
        """

        # Adiciona os filtros na contagem
        if filters.get("data_inicio"):
            count_query += f" AND v.DT >= '{filters['data_inicio']}'"
        if filters.get("data_fim"):
            count_query += f" AND v.DT <= '{filters['data_fim']}'"
        if filters.get("numero"):
            count_query += f" AND v.IDX LIKE '%{filters['numero']}%'"
        if filters.get("cliente"):
            count_query += f" AND v.CLIENTE_NOME LIKE '%{filters['cliente']}%'"
        if filters.get("valor_de"):
            count_query += f" AND v.TOTAL_RS >= {filters['valor_de']}"
        if filters.get("valor_ate"):
            count_query += f" AND v.TOTAL_RS <= {filters['valor_ate']}"

        # Executa a query de contagem
        qtde = await self.mysql.query(count_query)

        base_query = f"""               
        SELECT *
        FROM (
            SELECT 
                v.IDX,
                v.DT,
                v.HORA_ENTRADA,
                v.HORA_SAIDA,
                v.SUBTOTAL_RS,
                v.ACRESCIMOS_RS,
                v.DECRESCIMOS_RS,
                v.TOTAL_RS,
                v.STATUS_PEDIDO,
                v.STATUS_PAGAMENTO,
                v.PAGTO1,
                v.PAGTO01_NOME,
                v.TOTALPAGO1_RS,
                v.PAGTO2,
                v.PAGTO02_NOME,
                v.TOTALPAGO2_RS,
                v.CLIENTE_NOME,
                (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'NOME', vp.NOME,
                            'IDX', vp.PRODUTO_IDX,
                            'PRODUTO_ID', vp.PRODUTO_IDX,
                            'PRECO', vp.PRECO,
                            'QTDE', vp.QTDE,
                            'TOTAL', vp.TOTAL
                        )
                    )
                    FROM VISAO_VENDA_PRODUTO_CAIXA vp
                    WHERE vp.VENDA_IDX = v.IDX AND vp.EXCLUIDO = 0
                ) as PRODUTOS
            FROM VISAO_VENDA_PAGAMENTO v
            WHERE v.NEGOCIO_IDX = '{negocio_idx}' AND v.EXCLUIDO = 0
        ) AS VendasDetalhadas
        WHERE 1=1
        """

        # Adiciona os filtros
        if filters.get("data_inicio"):
            base_query += f" AND VendasDetalhadas.DT >= '{filters['data_inicio']}'"
        if filters.get("data_fim"):
            base_query += f" AND VendasDetalhadas.DT <= '{filters['data_fim']}'"
        if filters.get("numero"):
            base_query += f" AND VendasDetalhadas.IDX LIKE '%{filters['numero']}%'"
        if filters.get("cliente"):
            base_query += f" AND VendasDetalhadas.CLIENTE_NOME LIKE '%{filters['cliente']}%'"
        if filters.get("valor_de"):
            base_query += f" AND VendasDetalhadas.TOTAL_RS >= {filters['valor_de']}"
        if filters.get("valor_ate"):
            base_query += f" AND VendasDetalhadas.TOTAL_RS <= {filters['valor_ate']}"

        # Ordem
        ordem = filters.get("ordem", "DATA_DESC")
        if ordem == "DATA_DESC":
            base_query += " ORDER BY VendasDetalhadas.DT DESC"
        elif ordem == "DATA_ASC":
            base_query += " ORDER BY VendasDetalhadas.DT ASC"

        base_query += f" LIMIT {filters['inicio']}, {filters['limite']}"
        print("base_query", base_query)
        data = await self.mysql.query(base_query)
        print("data", data)
        # Converter a string JSON em objeto Python para cada venda
        for venda in data:
            if venda['PRODUTOS']:
                venda['PRODUTOS'] = json.loads(venda['PRODUTOS'])

        return {"qtde": qtde[0]['QTDE'], "dados": data}

    async def sale_update(self, data: dict):
        try:
            # Atualizar a venda principal
            venda = data['venda']
            idx = venda['IDX']
            del venda['IDX']  # Remove o IDX para não incluir no SET

            # Criar a string SET para o UPDATE
            sets = ", ".join(
                [f"{key} = '{value}'" for key, value in venda.items()])
            venda_query = f"UPDATE VENDA SET {sets} WHERE IDX = '{idx}';"

            # Executar o UPDATE da venda
            await self.mysql.query(venda_query)

            # Primeiro, excluir os produtos antigos
            delete_query = f"DELETE FROM VENDA_PRODUTO WHERE VENDA_ID = '{idx}';"
            await self.mysql.query(delete_query)

            # Inserir os novos produtos
            produto_inserts = []
            for produto in data['produtos']:
                if 'NOME' in produto:
                    del produto['NOME']
                produto_columns = ", ".join(produto.keys())
                produto_values = ", ".join(
                    [f"'{value}'" for value in produto.values()])
                produto_query = f"INSERT INTO VENDA_PRODUTO ({produto_columns}) VALUES ({produto_values});"
                produto_inserts.append(produto_query)

            if produto_inserts:
                produto_query = " ".join(produto_inserts)
                await self.mysql.query(produto_query)

            return {"status": "success", "venda_id": idx}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def sale_delete(self, venda_idx: str):
        print("sale_delete()", venda_idx)
        try:
            # Marcar a venda como excluída
            venda_query = f"UPDATE VENDA SET EXCLUIDO = 1 WHERE IDX = '{venda_idx}'"
            print("venda_query", venda_query)
            await self.mysql.query(venda_query)

            # Marcar os produtos da venda como excluídos
            produtos_query = f"UPDATE VENDA_PRODUTO SET EXCLUIDO = 1 WHERE VENDA_ID = '{venda_idx}';"
            await self.mysql.query(produtos_query)

            return {"status": "success", "message": "Venda excluída com sucesso"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def dashboard_sales_periods(self, negocio_idx: str, data_inicio: str, data_termino: str, periodos: list, periodo_tipo: str):
        #print("====== dashboard_sales_periods() ======", negocio_idx, data_inicio, data_termino, periodos, periodo_tipo)
        # Construção das colunas de período dinamicamente com VALOR e QTDE
        periodo_columns = []
        for periodo in periodos:
            periodo_name = f"PERIODO_{periodo['periodo']}"
            inicio = datetime.strptime(periodo['inicio'], '%d/%m/%Y').strftime('%Y-%m-%d')
            termino = datetime.strptime(periodo['termino'], '%d/%m/%Y').strftime('%Y-%m-%d')
            periodo_columns.append(f"""
            COALESCE(SUM(
                CASE 
                    WHEN DT >= '{inicio}' AND DT <= '{termino}' THEN TOTAL_RS
                    ELSE 0
                END
            ), 0) as {periodo_name}_VALOR,
            COALESCE(COUNT(
                CASE 
                    WHEN DT >= '{inicio}' AND DT <= '{termino}' THEN 1
                END
            ), 0) as {periodo_name}_QTDE
            """)
        
        periodo_columns_str = ",\n".join(periodo_columns)
        
        # Query final com SALDO_ANTERIOR e SALDO_ATUAL subdivididos em VALOR e QTDE
        query = f"""
        SELECT 
            COALESCE(SUM(
                CASE 
                    WHEN DT < '{data_inicio}' THEN TOTAL_RS
                    ELSE 0
                END
            ), 0) as SALDO_ANTERIOR_VALOR,
            COALESCE(COUNT(
                CASE 
                    WHEN DT < '{data_inicio}' THEN 1
                END
            ), 0) as SALDO_ANTERIOR_QTDE,
            
            {periodo_columns_str},
            
            COALESCE(SUM(
                CASE 
                    WHEN DT <= '{data_termino}' THEN TOTAL_RS
                    ELSE 0
                END
            ), 0) as SALDO_ATUAL_VALOR,
            COALESCE(COUNT(
                CASE 
                    WHEN DT <= '{data_termino}' THEN 1
                END
            ), 0) as SALDO_ATUAL_QTDE
            
        FROM VENDA
        WHERE NEGOCIO_IDX = '{negocio_idx}'
        AND STATUS_PEDIDO != 'C'  -- Não incluir vendas canceladas
        AND STATUS_PAGAMENTO = 'R'  -- Apenas vendas recebidas
        """
        print("query", query)
        result = await self.mysql.query(query)
        return result[0] if result else None

    async def dashboard_sales_payments(self, negocio_idx: str, data_inicio: str, data_termino: str):
        query = f"""
        SELECT 
            vfp.IDX,
            vfp.NOME,
            SUM(CASE WHEN v.PAGTO1 = vfp.IDX THEN v.TOTALPAGO1_RS - v.TROCO ELSE 0 END) +
            SUM(CASE WHEN v.PAGTO2 = vfp.IDX THEN v.TOTALPAGO2_RS ELSE 0 END) AS TOTAL
        FROM 
            VENDA v
        JOIN 
            VENDA_FORMA_PAGAMENTO vfp
        ON 
            v.PAGTO1 = vfp.IDX OR v.PAGTO2 = vfp.IDX
        WHERE
            v.NEGOCIO_IDX = '{negocio_idx}'
            AND v.DT >= '{data_inicio}'
            AND v.DT <= '{data_termino}'
            AND v.STATUS_PAGAMENTO = 'R'
        GROUP BY 
            vfp.IDX, vfp.NOME;
        """
        result = await self.mysql.query(query)
        return result

    async def dashboard_sales_status(self, negocio_idx: str, data_inicio: str, data_termino: str):
        #print("===== dashboard_sales_status() =====", negocio_idx, data_inicio, data_termino)
        query = f"""
        SELECT 
            CASE 
                WHEN STATUS_PAGAMENTO = 'R' THEN 'RECEBIDO'
                WHEN STATUS_PAGAMENTO = 'P' THEN 'PENDENTE'
                WHEN STATUS_PAGAMENTO = 'C' THEN 'CANCELADO'
            END AS STATUS,
            COUNT(*) as QTDE,
            COALESCE(SUM(TOTAL_RS), 0) as VALOR
        FROM 
            VENDA
        WHERE 
            NEGOCIO_IDX = '{negocio_idx}'
            AND DT >= '{data_inicio}'
            AND DT <= '{data_termino}'
        GROUP BY 
            STATUS_PAGAMENTO
        """
        
        result = await self.mysql.query(query)
        
        # Initialize the response structure
        status_dict = {
            "RECEBIDO": {"VALOR": 0, "QTDE": 0},
            "PENDENTE": {"VALOR": 0, "QTDE": 0},
            "CANCELADO": {"VALOR": 0, "QTDE": 0}
        }
        
        # Fill in the actual values from the query result
        for row in result:
            status = row['STATUS']
            status_dict[status] = {
                "VALOR": float(row['VALOR']),
                "QTDE": row['QTDE']
            }
        
        return status_dict

    async def dashboard_sales_products(self, negocio_idx: str, data_inicio: str, data_termino: str):
        query = f"""
        SELECT 
            vp.NOME,
            SUM(vp.QTDE) as QTDE,
            COALESCE(SUM(vp.TOTAL), 0) as VALOR
        FROM 
            VISAO_VENDA_PRODUTO_CATEGORIA vp
        WHERE 
            vp.NEGOCIO_IDX = '{negocio_idx}'
            AND vp.DT >= '{data_inicio}'
            AND vp.DT <= '{data_termino}'
            AND vp.EXCLUIDO = 0
        GROUP BY 
            vp.NOME
        ORDER BY
            QTDE DESC
        LIMIT 10
        """
        
        result = await self.mysql.query(query)
        
        # Convert decimal values to float for JSON serialization
        for row in result:
            row['VALOR'] = float(row['VALOR'])
        
        return result

    async def dashboard_sales_sources(self, negocio_idx: str, data_inicio: str, data_termino: str):
        #print("===== dashboard_sales_source() =====", negocio_idx, data_inicio, data_termino)
        query = f"""
        SELECT 
            CASE 
                WHEN ORIGEM = 1 THEN 'BALCAO'
                WHEN ORIGEM = 2 THEN 'MESA'
                WHEN ORIGEM = 3 THEN 'TELEFONE'
                WHEN ORIGEM = 4 THEN 'WHATSAPP'
            END AS ORIGEM,
            COUNT(*) as QTDE,
            COALESCE(SUM(TOTAL_RS), 0) as VALOR
        FROM 
            VENDA
        WHERE 
            NEGOCIO_IDX = '{negocio_idx}'
            AND DT >= '{data_inicio}'
            AND DT <= '{data_termino}'
            AND STATUS_PAGAMENTO = 'R'
        GROUP BY 
            VENDA.ORIGEM
        """
        
        #print("query", query)
        result = await self.mysql.query(query)
        
        # Initialize the response structure
        origin_dict = {
            "BALCAO": {"VALOR": 0, "QTDE": 0},
            "MESA": {"VALOR": 0, "QTDE": 0},
            "TELEFONE": {"VALOR": 0, "QTDE": 0},
            "WHATSAPP": {"VALOR": 0, "QTDE": 0}
        }
        
        # Fill in the actual values from the query result
        for row in result:
            origin = row['ORIGEM']
            origin_dict[origin] = {
                "VALOR": float(row['VALOR']),
                "QTDE": row['QTDE']
            }
        
        return origin_dict

    async def dashboard_sales_hours(self, negocio_idx: str, data_inicio: str, data_termino: str):
        """
        Retorna o total e quantidade de vendas por hora do dia
        Args:
            negocio_idx (str): ID do negócio
            data_inicio (str): Data inicial no formato YYYY-MM-DD
            data_termino (str): Data final no formato YYYY-MM-DD
        Returns:
            dict: Dicionário com totais por hora
        """
        query = f"""
        SELECT 
            LEFT(HORA_ENTRADA, 2) as HORA,
            COUNT(*) as QTDE,
            COALESCE(SUM(TOTAL_RS), 0) as VALOR
        FROM 
            VENDA
        WHERE 
            NEGOCIO_IDX = '{negocio_idx}'
            AND DT >= '{data_inicio}'
            AND DT <= '{data_termino}'
            AND STATUS_PAGAMENTO = 'R'
        GROUP BY 
            LEFT(HORA_ENTRADA, 2)
        ORDER BY 
            HORA
        """
        
        result = await self.mysql.query(query)
        
        # Inicializa o dicionário de horas
        hours_dict = {}
        
        # Preenche o dicionário com os resultados
        for row in result:
            # Formata a hora como string com 2 dígitos
            hour = f"{row['HORA']:0>2}"
            hours_dict[hour] = {
                "VALOR": float(row['VALOR']),
                "QTDE": row['QTDE']
            }
        
        return hours_dict

    async def dashboard_sales_categories(self, negocio_idx: str, data_inicio: str, data_termino: str):
        query = f"""
        SELECT 
            CATEGORIA_NOME,
            SUM(QTDE) as QTDE,
            COALESCE(SUM(TOTAL), 0) as VALOR
        FROM 
            VISAO_VENDA_PRODUTO_CATEGORIA
        WHERE 
            NEGOCIO_IDX = '{negocio_idx}'
            AND DT >= '{data_inicio}'
            AND DT <= '{data_termino}'
            AND EXCLUIDO = 0
        GROUP BY 
            CATEGORIA_NOME
        ORDER BY
            QTDE DESC
        LIMIT 10
        """
        print("query categories", query)
        result = await self.mysql.query(query)
        for row in result:
            row['VALOR'] = float(row['VALOR'])
        return result


@router.post("/payment_methods/fetch")
async def payment_methods_fetch(business_id: int, colunas_nome: dict):
    colunas = ",".join(colunas_nome["colunas_nome"])
    # print("colunas",colunas)
    # print("business_id",business_id)
    seller = Seller()
    result = await seller.payment_methods_fetch(business_id, colunas)
    # print("result",result)

    return result


@router.get("/payment_methods/fetch/business/{business_idx}")
async def payment_methods_fetch_business(business_idx: str):

    #print("business_id", business_idx)
    seller = Seller()
    result = await seller.payment_methods_fetch_business(business_idx)
    #print("result", result)

    return result


@router.post("/payment_methods/add")
async def payment_methods_add(data: dict):
    seller = Seller()
    result = await seller.payment_methods_add(data)
    return result


@router.post("/payment_methods/update")
async def payment_methods_update(data: dict):
    seller = Seller()
    result = await seller.payment_methods_update(data)
    return result


@router.post("/sale/add")
async def sale_add(data: dict):
    # print("sale_add()",data)
    seller = Seller()
    result = await seller.sale_add(data)
    # print("result",result)
    return result


@router.post("/sales/fetch/{negocio_idx}")
async def fetch_sales(negocio_idx: str, filters: dict):
    seller = Seller()
    result = await seller.fetch_sales(negocio_idx, filters)
    return result



@router.post("/sale/update")
async def sale_update(data: dict):
    seller = Seller()
    result = await seller.sale_update(data)
    return result


@router.get("/sale/delete/{venda_idx}")
async def sale_delete(venda_idx: str):
    #print("/sale/delete/{venda_idx")
    #print("venda_idx", venda_idx)

    seller = Seller()
    result = await seller.sale_delete(venda_idx)
    return result



@router.post("/dashboard/sales/hours/")    
async def dashboard_sales_hours(filter: dict):
    seller = Seller()
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

        # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    horas = await seller.dashboard_sales_hours(negocio_idx, data_inicio, data_termino)
    print("horas", horas)
    return horas



@router.post("/dashboard/sales/products/")
async def dashboard_sales_products(filter: dict):  
    seller = Seller()
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

        # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    produtos = await seller.dashboard_sales_products(negocio_idx, data_inicio, data_termino)
    print("produtos", produtos)
    return produtos


@router.post("/dashboard/sales/payments/")    
async def dashboard_sales_payments(filter: dict):
    seller = Seller()
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

        # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    pagamentos = await seller.dashboard_sales_payments(negocio_idx, data_inicio, data_termino)
    print("pagamentos", pagamentos)
    return pagamentos

@router.post("/dashboard/sales/sources/")    
async def dashboard_sales_sources(filter: dict):
    seller = Seller()
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

        # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    origens = await seller.dashboard_sales_sources(negocio_idx, data_inicio, data_termino)
    print("origens", origens)
    return origens

@router.post("/dashboard/sales/categories/")
async def dashboard_sales_categories(filter: dict):
    seller = Seller()

    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

    # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    categorias = await seller.dashboard_sales_categories(negocio_idx, data_inicio, data_termino)
    print("categorias", categorias)
    return categorias
    
@router.post("/dashboard/sales/")
async def dashboard_sales(filter: dict):
    seller = Seller()

    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")

    # Converter datas para o formato correto
    if re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(
            data_inicio, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_inicio = datetime.strptime(
            data_inicio, '%d/%m/%Y').strftime('%Y-%m-%d')

    if re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(
            data_termino, '%Y-%m-%d').strftime('%Y-%m-%d')
    else:
        data_termino = datetime.strptime(
            data_termino, '%d/%m/%Y').strftime('%Y-%m-%d')

    periodo_tipo = get_period_type(datetime.strptime(data_inicio, '%Y-%m-%d'),
                                   datetime.strptime(data_termino, '%Y-%m-%d'))
    periodos = _calculate_periods(datetime.strptime(data_inicio, '%Y-%m-%d'),
                                  datetime.strptime(data_termino, '%Y-%m-%d'),
                                  periodo_tipo)

    vendas = {}
    vendas['periodos'] = periodos
    vendas['periodo_tipo'] = periodo_tipo
    vendas['dados'] = await seller.dashboard_sales_periods(negocio_idx,data_inicio,data_termino,periodos,periodo_tipo)


    #print("vendas", dashboard["vendas"]["dados"])
    #print("")
    return vendas


if __name__ == "__main__":
    import asyncio
    from fastapi.testclient import TestClient
    from api.main import app

    async def test_payment_methods_fetch_business():
        client = TestClient(app)

        # Teste com business_idx válido
        business_idx = "0068108573"  # Substitua por um business_idx válido
        business_idx = "1711256749"

        response = client.get(
            f"/api/agent/seller/payment_methods/fetch/business/{business_idx}")
        print("response", response)
        print("response", response.text)

    async def test_fetch_sales():
        client = TestClient(app)

        # Dados de teste para os filtros
        test_filters = {
            "data_inicio": "",
            "data_fim": "",
            "valor_de": 0,
            "valor_ate": 0,
            "numero": "037",
            "status_pedido": "",
            "status_pagamento": "",
            "inicio": 0,
            "limite": 5,
            "ordem": "DATA_DESC"
        }

        try:
            response = client.post(
                "/api/agent/seller/sales/fetch/1711256749",
                json=test_filters
            )

            # Verifica se a resposta foi bem-sucedida
            assert response.status_code == 200, "Erro na requisição: Status code não é 200"

            result = response.json()
            print("result", result)
            print("Resultado do teste de busca de vendas:")
            print("Total de registros:", result["qtde"])
            print("\nVendas encontradas:")
            for venda in result["dados"]:
                print("\n---------------------------------------------------")
                print(f"IDX: {venda['IDX']}")
                print(f"Data: {venda['DT']}")
                print(f"HORA_ENTRADA: {venda['HORA_ENTRADA']}")
                print(f"HORA_SAIDA: {venda['HORA_SAIDA']}")
                print(f"SUBTOTAL_RS: {venda['SUBTOTAL_RS']}")
                print(f"ACRESCIMOS_RS: {venda['ACRESCIMOS_RS']}")
                print(f"DECRESCIMOS_RS: {venda['DECRESCIMOS_RS']}")
                print("Pagamentos:")
                # print(f"Pagamento 01: {venda['PAGTO01_NOME']}")
                # print(f"Pagamento 02: {venda['PAGTO02_NOME']}")
                print(f"Status Pedido: {venda['STATUS_PEDIDO']}")
                print(f"Status Pagamento: {venda['STATUS_PAGAMENTO']}")
                print(f"Total: R$ {venda['TOTAL_RS']}")
                print("\nProdutos:")
                print("venda['PRODUTOS']", venda['PRODUTOS'])
                if venda['PRODUTOS']:

                    for produto in venda['PRODUTOS']:
                        print("produto vendido", produto)
                        print(f"  - Produto ID: {produto['PRODUTO_ID']}")
                        print(f"    Preço: R$ {produto['PRECO']}")
                        print(f"    Quantidade: {produto['QTDE']}")
                        print(f"    Total: R$ {produto['TOTAL']}")
                else:
                    print("  Nenhum produto encontrado")
                print("---------------------------------------------------")

            print("\n✓ Teste de busca de vendas executado com sucesso")

        except Exception as e:
            print("✕ Erro no teste de busca de vendas:", str(e))
            raise e

    async def test_query_dt():
        mysql = Mysql()

        query = f"""
WITH VendasDetalhadas AS (
            SELECT 
                v.*,
                (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'NOME', vp.NOME,
                            'IDX', vp.PRODUTO_IDX,
                            'PRODUTO_ID', vp.PRODUTO_IDX,
                            'PRECO', vp.PRECO,
                            'QTDE', vp.QTDE,
                            'TOTAL', vp.TOTAL
                        )
                    )
                    FROM VISAO_VENDA_PRODUTO_CAIXA vp
                    WHERE vp.VENDA_IDX = v.IDX AND vp.EXCLUIDO = 0
                ) as PRODUTOS
            FROM VISAO_VENDA_PAGAMENTO v
            WHERE v.NEGOCIO_IDX = '1711256749' AND v.EXCLUIDO = 0
        )
        SELECT
            IDX,
            DT,
            HORA_ENTRADA,
            HORA_SAIDA,
            SUBTOTAL_RS,
            ACRESCIMOS_RS,
            DECRESCIMOS_RS,
            TOTAL_RS,
            STATUS_PEDIDO,
            STATUS_PAGAMENTO,
            PAGTO1,
            PAGTO01_NOME,
            TOTALPAGO1_RS,
            PAGTO2,
            PAGTO02_NOME,
            TOTALPAGO2_RS,
            PRODUTOS
        FROM VendasDetalhadas
        WHERE 1=1
         AND DT >= '2024-11-18' ORDER BY DT DESC LIMIT 0, 5
"""

        result = await mysql.query(query)
        print("result", result)

    async def test_dashboard_sales():
        client = TestClient(app)

        filter = {
            "negocio_idx": "0068108573",
            "data_inicio": "01/01/2025",
            "data_termino": "31/01/2025"
        }

        response = client.post(
            "/api/agent/seller/dashboard/sales/",
            json=filter
        )

        if response.status_code == 200:
            result = response.json()
            #print("Resultado:", result)
        else:
            print(f"Erro {response.status_code}:", response.text)

    async def test_sale_delete():
        client = TestClient(app)

        # Teste com venda_idx válido
        venda_idx = "12345"  # Substitua por um venda_idx válido
        response = client.get(f"/api/agent/seller/sale/delete/{venda_idx}")
        print("response", response)


    async def test_dashboard_sales_sources():
        pass



    async def test_dashboard_sales_categories():
        client = TestClient(app)

        filter = {
            "negocio_idx": "0068108573",
            "data_inicio": "01/01/2025",
            "data_termino": "31/01/2025"
        }

        response = client.post(
            "/api/agent/seller/dashboard/sales/categories/",
            json=filter
        )

        if response.status_code == 200:
            result = response.json()
            print("Resultado dashboard_sales_categories:", result)
        else:
            print(f"Erro {response.status_code}:", response.text)

    # asyncio.run(test_sale_delete())
    asyncio.run(test_dashboard_sales())
    asyncio.run(test_dashboard_sales_categories())
