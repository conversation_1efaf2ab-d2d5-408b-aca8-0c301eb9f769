Directory structure:
└── dkundel-openai-aie-voice-agents-workshop/
    ├── README.md
    ├── package.json
    ├── 01-basic/
    │   ├── index.ts
    │   └── package.json
    └── 02-voice/
        ├── next.config.ts
        ├── package.json
        ├── postcss.config.mjs
        ├── tsconfig.json
        ├── public/
        │   └── .gitkeep
        └── src/
            └── app/
                ├── globals.css
                ├── layout.tsx
                ├── page.tsx
                └── server/
                    └── token.ts

================================================
FILE: README.md
================================================
# Building Voice Agents with OpenAI

Workshop at AI Engineer World's Fair 2025

## Requirements

- Node.js 22 or newer with npm installed.

  We will use Node.js for a consistent experience for all attendees but `bun` and `deno` are supported by the SDK.

- An OpenAI account with an OpenAI API key stored as `OPENAI_API_KEY` environment variable

## Setup

Clone this repository to have access to the boilerplate code for our workshop

```bash
<NAME_EMAIL>:dkundel-openai/aie-voice-agents-workshop.git
cd aie-voice-agents-workshop
npm install
```

This will automatically install the [OpenAI Agents SDK](https://openai.github.io/openai-agents-js), [`zod`](https://zod.dev) and TypeScript for you.

## Project structure

During the workshop we will use two projects.

We will start off in [01-basic](01-basic/) where we will create a basic text-based agent to familiarize yourself with the [OpenAI Agents SDK for TypeScript](https://openai.github.io/openai-agents-js/).

Afterwards we will use the Next.js app in [02-voice](02-voice/) to build our voice agent using the same SDK. We'll be using Next.js to have a framework where we can have both server-side code and client-code easily accessible but the SDK is compatible with any frontend framework.

## Running your code

### 01-basic

The following command will execute your `index.ts` file:

```bash
npm run start:01
```

### 02-voice

The following command will start up the development server for your Next.js app:

```bash
npm run start:02
```

## Resources

- [Agents SDK Quickstart](https://openai.github.io/openai-agents-js/guides/quickstart)
- [Agents SDK Voice Quickstart](https://openai.github.io/openai-agents-js/guides/voice-agents/quickstart/)
- [Agents SDK Examples](https://github.com/openai/openai-agents-js-internal/tree/main/examples)
- [Details about Voice Agent Features](https://openai.github.io/openai-gents-js/guides/voice-agents/build/)
- [Voice Agents Guide on the OpenAI Docs](https://platform.openai.com/docs/guides/voice-agents)



================================================
FILE: package.json
================================================
{
  "private": true,
  "scripts": {
    "start:01": "npm run start --prefix 01-basic",
    "start:02": "npm run dev --prefix 02-voice",
    "install": "npm install --prefix 01-basic && npm install --prefix 02-voice"
  }
}



================================================
FILE: 01-basic/index.ts
================================================
import { Agent, tool, run } from "@openai/agents";
import z from "zod";

const getWeather = tool({
  name: "getWeather",
  description: "Get the weather in a given location",
  parameters: z.object({
    location: z.string(),
  }),
  execute: async ({ location }) => {
    return `The weather in ${location} is sunny`;
  },
});

const agent = new Agent({
  name: "My Agent",
  instructions: "You are a helpful assistant.",
  model: "o4-mini",
  tools: [getWeather],
});

const result = await run(agent, "What is the weather in Tokyo?");

console.log(result.finalOutput);



================================================
FILE: 01-basic/package.json
================================================
{
  "private": true,
  "name": "01-basic",
  "type": "module",
  "scripts": {
    "start": "tsx index.ts"
  },
  "devDependencies": {
    "tsx": "^4.19.4"
  },
  "dependencies": {
    "@openai/agents": "latest",
    "zod": "^3.25.49"
  }
}



================================================
FILE: 02-voice/next.config.ts
================================================
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
};

export default nextConfig;



================================================
FILE: 02-voice/package.json
================================================
{
  "name": "02-voice",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@openai/agents": "*",
    "next": "15.3.3",
    "openai": "^5.0.2",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "server-only": "^0.0.1",
    "zod": "^3.25.49"
  },
  "devDependencies": {
    "@tailwindcss/postcss": "^4",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "tailwindcss": "^4",
    "typescript": "^5"
  }
}



================================================
FILE: 02-voice/postcss.config.mjs
================================================
const config = {
  plugins: ["@tailwindcss/postcss"],
};

export default config;



================================================
FILE: 02-voice/tsconfig.json
================================================
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}



================================================
FILE: 02-voice/public/.gitkeep
================================================
[Empty file]


================================================
FILE: 02-voice/src/app/globals.css
================================================
@import "tailwindcss";



================================================
FILE: 02-voice/src/app/layout.tsx
================================================
import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Voice Agent Demo",
  description: "Voice Agent Demo",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased`}>{children}</body>
    </html>
  );
}



================================================
FILE: 02-voice/src/app/page.tsx
================================================
"use client";

import { useRef, useState } from "react";
import {
  RealtimeAgent,
  RealtimeItem,
  RealtimeSession,
  tool,
} from "@openai/agents/realtime";
import { getSessionToken } from "./server/token";
import z from "zod";

const getWeather = tool({
  name: "getWeather",
  description: "Get the weather in a given location",
  parameters: z.object({
    location: z.string(),
  }),
  execute: async ({ location }) => {
    return `The weather in ${location} is sunny`;
  },
});

const weatherAgent = new RealtimeAgent({
  name: "Weather Agent",
  instructions: "Talk with a New York accent",
  handoffDescription: "This agent is an expert in weather",
  tools: [getWeather],
});

const agent = new RealtimeAgent({
  name: "Voice Agent",
  instructions:
    "You are a voice agent that can answer questions and help with tasks.",
  handoffs: [weatherAgent],
});

export default function Home() {
  const session = useRef<RealtimeSession | null>(null);
  const [connected, setConnected] = useState(false);
  const [history, setHistory] = useState<RealtimeItem[]>([]);

  async function onConnect() {
    if (connected) {
      setConnected(false);
      await session.current?.close();
    } else {
      const token = await getSessionToken();
      session.current = new RealtimeSession(agent, {
        model: "gpt-4o-realtime-preview-2025-06-03",
      });
      session.current.on("transport_event", (event) => {
        console.log(event);
      });
      session.current.on("history_updated", (history) => {
        setHistory(history);
      });
      session.current.on(
        "tool_approval_requested",
        async (context, agent, approvalRequest) => {
          const response = prompt("Approve or deny the tool call?");
          session.current?.approve(approvalRequest.approvalItem);
        }
      );
      await session.current.connect({
        apiKey: token,
      });
      setConnected(true);
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Voice Agent Demo</h1>
      <button
        onClick={onConnect}
        className="bg-black text-white p-2 rounded-md hover:bg-gray-800 cursor-pointer"
      >
        {connected ? "Disconnect" : "Connect"}
      </button>
      <ul>
        {history
          .filter((item) => item.type === "message")
          .map((item) => (
            <li key={item.itemId}>
              {item.role}: {JSON.stringify(item.content)}
            </li>
          ))}
      </ul>
    </div>
  );
}



================================================
FILE: 02-voice/src/app/server/token.ts
================================================
"use server";

import OpenAI from "openai";

export async function getSessionToken() {
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  const session = await openai.beta.realtime.sessions.create({
    model: "gpt-4o-realtime-preview",
  });

  return session.client_secret.value;
}


