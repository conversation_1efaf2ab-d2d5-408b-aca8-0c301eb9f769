import os
import json
from agents.tool import function_tool
from api.functions.util import generate_unique_id
from ...agent_logger import AgentLogger
from ...agent_neo4j import AgentNeo4j


logger = AgentLogger()
neo4j = AgentNeo4j()

schema_path = os.path.join(os.path.dirname(__file__), 'schemas', 'produto_catalogo_adicionar.json')
with open(schema_path, 'r', encoding='utf-8') as f:
    produto_catalogo_adicionar_neo4j = json.load(f)

@function_tool
async def produto_catalogo_adicionar(
    nome: str,
    codigo: str,
    preco_maior: float,
    preco: float,
    descricao: str, 
    cor_nome: str,
    url_imagem: str,
    negocio_idx: str
):
       
       """
       🚨 FUNÇÃO CRÍTICA: Adiciona produto ao catálogo da MaryKay no banco de dados neoj.

       Solicite os dados do produto ao usuario, um de cada vez e na squencia abaixo.
       Os seguintes dados deverão ser passados para a função:
       
       
       nome:
       - Nome do produto.
       - Exemplo: "Kit de Básico"
       - Obrigatório
    
       codigo:
       - Código do produto.
       - Exemplo: "10142673"
       - Obrigatório
       
       descricao:
       - Descrição do produto.
       - Exemplo: "Indicado para quem tem pele oleosa. Usar 3x ao dia."
       - Opcional
       
       preco_maior:
       - Preço de tabela do produto.
       - Exemplo: 120.00
       - Obrigatório



       preco:
       - Preço de venda do produto.
       - Exemplo: 100.00
       - Obrigatório

       
       cor_nome:
       - nome da cor do produto.
       - Exemplo: "red matte"
       - Opcional
       #Solicitar ao usuario o nome da cor. Caso ele informe, busque a cor na tabela COR usando a funcao consultar_cor(Caso não encontre, #informe ao usuario que a cor não foi encontrada e peça que ele verifique o nome correto.
       -opcional
       
       
       url_imagem:
       - url da imagem do produto
              
       ESQUEMA DE DADOS:
       {produto_catalogo_adicionar_neo4j}

        CYPHER DE INCLUSÃO DE DADOS

        criar uma query que adicione um no Produto no banco de dados
        Produto.nome = nome
        Produto.codigo = codigo
        Produto.preco_maior = preco_tabela
        Produto.preco = preco_desconto
        Produto.descricao = descricao
        Produto.url_imagem = url_imagem
            
        Criar os relacionamentos:
        Negocio(idx=negocio_idx-POSSUI-PRODUTO->Produto(idx=$idx)
        Produto(idx=$idx)-TEM_COR->Cor(nome=$cor_nome)
        Tanto a cor quanto o negócio já existem. Só o produto está sendo incluso.

        Após a inclusão , informe de forma conversacional ao usuario que o produto foi adicionado com sucesso.
       """
       logger.info("===== produto_catelogo_adicionar ======")
       logger.info(f"nome: {nome}")
       logger.info(f"codigo: {codigo}")
       logger.info(f"preco_tabela: {preco_maior}")
       logger.info(f"preco_desconto: {preco}")
       logger.info(f"descricao: {descricao}")
       logger.info(f"cor_nome: {cor_nome}")
       logger.info(f"negocio_idx: {negocio_idx}")

       try:
           # Gera um ID único para o novo produto
           idx = generate_unique_id()
           
           query = """
           // Cria o nó do Produto
           CREATE (p:Produto {
               idx: $idx,
               nome: $nome,
               codigo: $codigo,
               preco_maior: $preco_maior,
               preco: $preco,
               descricao: $descricao,
               url_imagem: $url_imagem,
               data_cadastro: datetime(),
               excluido: 0
           })
           
           // Adiciona a cláusula WITH para poder continuar a consulta
           WITH p
           
           // Cria relacionamento com o Negócio
           MATCH (n:Negocio {idx: $negocio_idx})
           CREATE (n)-[:POSSUI_PRODUTO]->(p)
           
           // Cria relacionamento com a Cor, se informada
           WITH p
           WHERE $cor_nome IS NOT NULL AND $cor_nome <> ''
           MATCH (c:Cor {nome: $cor_nome})
           CREATE (p)-[:TEM_COR]->(c)
           
           RETURN p
           """
           
           parametros = {
               'idx': idx,
               'nome': nome,
               'codigo': codigo,
               'preco_maior': float(preco_maior),
               'preco': float(preco),
               'descricao': descricao,
               'url_imagem': url_imagem,
               'cor_nome': cor_nome,
               'negocio_idx': negocio_idx
           }

           # Executar a query no Neo4j
           result = await neo4j.execute_write_query(query, parametros)
           print(f"Resultado da execução: {result}")
           
           # Verificar se a operação foi bem-sucedida
           if result and isinstance(result, list) and len(result) > 0:
               first_result = result[0]
               
               # Verificar se há erro no resultado
               if "erro" in first_result:
                   logger.error(f"❌ Erro do Neo4j: {first_result['erro']}")
                   return {
                       "success": False,
                       "message": f"Erro ao adicionar produto: {first_result['erro']}"
                   }
               
               # Verificar se a operação foi bem-sucedida
               if first_result.get("success") == True:
                      return  "Produto adicionado com sucesso ao catálogo!"
               else:
                       return  "Produto não foi criado. Verifique os dados fornecidos."
           else:
               logger.error(f"❌ Resultado inválido do Neo4j: {result}")
               return {
                   "success": False,
                   "message": "Resposta inválida do banco de dados"
               }
       except Exception as e:
           logger.error(f"===== ERRO EM produto_catalogo_adicionar() =====")
           logger.error(f"ERRO TIPO: {type(e).__name__}")
           logger.error(f"ERRO MENSAGEM: {str(e)}")
           logger.error(f"DADOS DO PRODUTO:")
           logger.error(f"  - NOME: {nome}")
           logger.error(f"  - CODIGO: {codigo}")
           logger.error(f"  - PRECO_MAIOR: {preco_maior}")
           logger.error(f"  - PRECO_DESCONTO: {preco}")
           logger.error(f"  - DESCRICAO: {descricao}")
           logger.error(f"  - COR_NOME: {cor_nome}")
           logger.error(f"  - URL_IMAGEM: {url_imagem}")
           logger.error(f"  - NEGOCIO_IDX: {negocio_idx}")
           logger.error("===== FIM ERRO produto_catalogo_adicionar() =====")
           return {"success": False, "error": f"Erro ao adicionar produto: {str(e)}"}


# Seção de testes
if __name__ == "__main__":
    import asyncio
    import uuid
    
    async def testa_adicionar_produto():
        # Dados de teste
        produto = {
            "nome": "batom teste 2" + str(uuid.uuid4())[:8],
            "codigo": str(uuid.uuid4())[:8],
            "preco_maior": 129.90,
            "preco": 89.90,
            "descricao": "Batom matte de longa duração - Cor de teste",
            "cor_nome": "Beige C140 (Matte)",  # Código de uma cor existente no banco
            "url_imagem": "https://exemplo.com/produto-teste.jpg",
            "negocio_idx": "5544332211"  # ID de negócio de teste
        }
        
        print("=== Teste de Inclusão de Produto ===")
        print(f"Dados do produto de teste:\n{produto}\n")
        
        try:
            resultado = await produto_catalogo_adicionar(
                nome=produto["nome"],
                codigo=produto["codigo"],
                preco_maior=produto["preco_maior"],
                preco=produto["preco"],
                descricao=produto["descricao"],
                cor_nome=produto["cor_nome"],
                url_imagem=produto["url_imagem"],
                negocio_idx=produto["negocio_idx"]
            )
            
            print("=== Resultado do Teste ===")
            print(resultado)
            
            
            return resultado
            
        except Exception as e:
            print(f"Erro durante o teste: {str(e)}")
            raise
    
    # Executar o teste
    asyncio.run(testa_adicionar_produto())
    #execucao
    #py -m api.agent.assistenciamk.functions.produto_catalogo_adicionar