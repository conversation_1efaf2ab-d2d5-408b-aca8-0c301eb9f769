import requests
import json
import base64
import os

# Configurações
API_KEY = "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"  # Substitua pela sua chave do OpenRouter
API_URL = "https://openrouter.ai/api/v1/chat/completions"

# Configuração dos headers
headers = {
    "Authorization": f"Bearer {API_KEY}",
    "HTTP-Referer": "http://localhost:5000",
    "Content-Type": "application/json"
}

modelo = "google/gemini-2.5-pro-exp-03-25:free"


def chat_simples(modelo):
    """
    Função simples para testar o modelo
    """
    payload = {
        "model": modelo,
        "messages": [
            {
                "role": "user",
                "content": "o que é agi?"
            }
        ]
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Erro na API: {response.status_code}", "message": response.text}
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}

def encode_image_to_base64(image_path):
    """
    Função para converter imagem em base64
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def vision_test(image_path, pergunta):
    """
    Função para testar o modelo de visão
    """
    # Obtém o diretório do script atual
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Constrói o caminho completo para a imagem
    image_full_path = os.path.join(script_dir, image_path)
    
    print(f"Tentando abrir imagem em: {image_full_path}")  # Debug
    
    try:
        # Codifica a imagem em base64
        image_base64 = encode_image_to_base64(image_full_path)
        
        payload = {
            "model": "google/gemini-pro-vision",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": pergunta
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
        }
        
        response = requests.post(API_URL, headers=headers, json=payload)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Erro na API: {response.status_code}", "message": response.text}
    except FileNotFoundError:
        return {"error": f"Arquivo não encontrado: {image_full_path}"}
    except Exception as e:
        return {"error": f"Erro ao processar imagem: {str(e)}"}

# Exemplo de uso para visão
if __name__ == "__main__":
    print("=== Teste de Visão Computacional ===")
    print("1. Chat simples")
    print("2. Análise de imagem")
    
    opcao = input("\nEscolha uma opção (1-2): ")
    
    if opcao == "1":
        resultado = chat_simples(modelo)
    elif opcao == "2":
        # Usa o nome exato do arquivo que você mencionou
        imagem_path = "document_image.jpg"
        pergunta = input("O que você quer saber sobre a imagem? ")
        
        resultado = vision_test(imagem_path, pergunta)
    
    print("\nResposta do modelo:")
    print(json.dumps(resultado, indent=2, ensure_ascii=False))