from fastapi import APIRouter
from .agent_logger import AgentLogger

logger = AgentLogger()
router = APIRouter()

import requests
from .agent_message import Message
from .agent_session import Session 
from .agent_validator import Validator
from .agent_gptalk import Gptalk
from .agent_converter import Converter
from .agent_user import User
from .agent_converter import Converter
from .agent_gptalk import Gptalk
from .agent_llm import LLM
from .agent_agent import Agent
from .agent_secret import Secret
from .agent_functioncall import FunctionCall
from .agent_mysql import Mysql


class AgentWhatsappWhapi:

    def __init__(self):
        secret = Secret()
        self.token = secret.WHATSAPP_WHAPI_TOKEN

    def message_send(self, message,  token, client_number ):
        logger.info("===== agent_whatsappwhapi.message_send() =====")
        logger.info(f"message: {message}")
        logger.info(f"client_number: {client_number}")
        logger.info(f"token: {token}")


        if token is None:
            token = self.token

        url = f"https://gate.whapi.cloud/messages/text"
        logger.info(f"url: {url}")
                 
        payload = {
            "typing_time": 0,
            "to": client_number,
            "body": message
        }
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}"
        }
        logger.info(f"payload: {payload}")
        logger.info(f"headers: {headers}")


        
        response = requests.post(url, json=payload, headers=headers)
        logger.info(f"response: {response.json()}")
        return response

    def send_media_image_message(self, client_number, caption, url_image, token=None):
        if token is None:
            token = self.token
            
        url = "https://gate.whapi.cloud/messages/image"
        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
            "content-type": "application/json"
        }
        payload = {
            "to": client_number,
            "media": url_image,
            "mime_type": "image/png",
            "caption": caption
        }
        
        response = requests.post(url, headers=headers, json=payload)
        return response

    def send_media_video_message(self, client_number, caption, url_video, token=None):
        if token is None:
            token = self.token
            
        url = "https://gate.whapi.cloud/messages/video"
        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
            "content-type": "application/json"
        }
        payload = {
            "to": client_number,
            "media": url_video,
            "mime_type": "video/mp4",
            "caption": caption
        }
        
        response = requests.post(url, headers=headers, json=payload)
        return response


# Manter a classe WhatsAppWhapi para compatibilidade com código existente
class WhatsAppWhapi(AgentWhatsappWhapi):
    pass


@router.post("/webhook")
async def webhook(data: dict):
    msg = Message()
    print(data)
    await msg.add_log(data)
    converter = Converter()
    llm = LLM()
    
    validator = Validator()
    gptalk = Gptalk()
    usuario = User()
    llm_modelo = llm.model_default
    #llm_modelo = llm.model_openai.gpt_4_o
    llm_modelo = llm.model_gemini.geminipro15
    agente = Agent()
    conversa = Session()
    whatsapp = AgentWhatsappWhapi()  # Usando a nova classe
    secret = Secret()
    functionCall = FunctionCall()

    #print("data", data)

    #========== PROCESSA MENSAGE RECEBIDA DO USUÁRIO ==========
    if data and 'messages' in data  and data['messages'][0]['from_me'] == False: #se tem mensagens e nao foi enviada pelo agente, mas pelo usuario
        #print("tem mensagens")
        agente_enviou = data['messages'][0]['from_me']
        telefone = data['messages'][0]['from']
        #print("telefone",telefone)
        nome =  data['messages'][0]['from_name']
        tipo =  data['messages'][0]['type']
        chat_id = data['messages'][0]['chat_id']
        canal_id = data['channel_id']
        timestamp = data['messages'][0]['timestamp']  


        #print("tipo", tipo)  
        if tipo == "text" and not agente_enviou: #enviada pelo cliente
            #print("é texto e nao foi o agente que enviou")
            recebido = data['messages'][0]['text']['body']
            
            # tenta carregar o usuario para verificar se já existe
            await usuario.fetch("*",[f"TELEFONE='{telefone}'"])
           
            if not usuario.ID: #se não encontrou o usuario, adiciona
                data_hora  = converter.time_now_to_date_hour()
                await usuario.add({"TELEFONE":telefone,"NOME":nome,"ORIGEM":canal_id,"DATA_CAD": data_hora  })

            #print("usuario.ID",usuario.ID)


            #verifica se o texto é um nick de agente para ativação
            nick = validator.validate_agent_nick(recebido)
            #print("nick",nick)
    
            #========== ATIVAÇÃO DE AGENTE POR NICK
            if nick: #se é um nick de agente, verifica se o nick é de um agente existente

                if not await agente.fetch_agent_nick("*",nick): #se não foi encontrado agente com o nick informado retornar mensagem de erro
                    print("agente  não encontrado", nick)
                    msg = f"{recebido} não foi encontrado. Verifique se o nome esta correto e tente novamente."
                    whatsapp.message_send(msg,client_number=telefone)
                    #interrompe o fluxo e aguarda nova mensagem 
                    return  
                
                #se encontrou o agente, envia a imagem do agente
                #print("agente",agente.NOME, agente.ID, nick)
                url_imagem = f"https://www.gptalk.com.br/imagens/agentes/capas/whatsapp/{agente.ARQUIVO}.png"
                #url_imagem = f"https://sandbox.api.pagseguro.com/qrcode/QRCO_7D3AEFD7-2B92-40FC-8FF9-F60946BDB2E4/png"
                #print("url_imagem",url_imagem)
                caption = agente.FUNCAO_NOME + " / " + agente.NOME
                whatsapp.send_media_image_message(telefone,caption,url_imagem)
               
                #verifica se o agente selecionado já está com uma conversa aberta
                await conversa.fetch("*",[f"AGENTE_ID={agente.ID}","ENCERRADA=0"])
                if(conversa.ID): #se tem conversa aberta com o agente....
                    #atuliza a ultima interação
                    data = {}
                    data['ID'] = conversa.ID
                    data['ULTIMA_INTERACAO'] = converter.time_now_to_date_hour()
                    await conversa.update(data)
                    #encerra o fluxo e aguarda nova mensagem
                    return #retona aguardando nova mensagem
                else: #se não tem conversa aberta com o agente...
                    #gera uma mensagem de abertura para o usuário
                    recebido = "olá" 
                    #inicia uma nova conversa
                    conversa = Session()
                    conversa.USUARIO_ID = usuario.ID
                    conversa.AGENTE_ID = agente.ID
                    conversa.USUARIO_NOME = nome
                    conversa.INICIO = converter.time_now_to_date_hour()
                    conversa.ULTIMA_INTERACAO  = converter.time_now_to_date_hour()
                    conversa.CANAL_ID = canal_id
                    conversa.CHAT_ID = chat_id
                    await conversa.add()
            
            #==========ATIVAÇÃO DE AGENTE POR CONVERSA
            if not agente.ID: #se o agente não foi informado, buscca ultima conversa aberta do usuario para carregar o agente
                #carrega a ultima conversa do usuario
                await conversa.fetch("*",[f"USUARIO_ID={usuario.ID}","ENCERRADA=0"])
                #print("ida da ultima conversa",conversa.ID)
                if conversa.ID:  #tem conversa anterior aberta
                    #print("USUARIO TEM CONVERSA ABERTA:",conversa.ID,"Agente:", conversa.AGENTE_ID)
                    #carrega o agente da ultima conversa
                    await agente.fetch_agent_id("*",conversa.AGENTE_ID)
                    #atualiza ultima interação
                    conversa.ULTIMA_INTERACAO  = converter.time_now_to_date_hour()
                    await conversa.update({"ID":conversa.ID,"ULTIMA_INTERACAO":conversa.ULTIMA_INTERACAO })
                else:   #não tem conversa anterior aberta...
                    
                    #carrega o agente padrão e inicia uma nova conversa
                    await agente.fetch_agent_id("*",1)

                    print("inicia uma nova conversa com agente ",agente.ID)
                    conversa = Session()
                    conversa.USUARIO_ID = usuario.ID
                    conversa.AGENTE_ID = agente.ID 
                    conversa.USUARIO_NOME = nome
                    conversa.INICIO = converter.time_now_to_date_hour()
                    conversa.ULTIMA_INTERACAO  = converter.time_now_to_date_hour()
                    conversa.CANAL_ID = canal_id
                    conversa.CHAT_ID = chat_id
                    await conversa.add()

                
            #========== HISTÓRICO DE MENSAGENS
            #carrega o historico de mensagens da conversa
            mensagens= []
            #print("agente.HISTORICO",agente.HISTORICO)
            if agente.HISTORICO == 1: #mensagens de uma conversa
                 mensagens =   await msg.fetch_messages(columns="*",filters=[f"CONVERSA_ID={conversa.ID}",f"AGENTE_ID={agente.ID}"])
                
            #print ("quantidade de mensagens",len(mensagens))
            if len(mensagens) > 0 :
                #print("mensagens",mensagens)
                historico = await converter.message_chat(mensagens)
            else:
                historico = []
            #print("historico inicial",historico)




#========== MENSAGEM: ENVIO / RECEBIMENTO

            #formata a mensagem recebida para envio ao modelo
            mensagem = {'role':'user','content':recebido}
       
            #define o modelo de linguagem a ser utilizado para responder
            print("----------")
            print("AGENTE INICIAL",agente.ID,agente.NOME)
            print("----------")
            if agente.ID ==1: #se for o agente principal ou padrão avaliar a intenção
                llm_modelo = llm.model_gemini.geminipro15
                resposta = await gptalk.intention_detect(
                historico=historico,
                 mensagemRecebida=mensagem,
                 llm_modelo=llm_modelo,
                llm=llm,
                agente=agente,
                usuario=usuario, 
                conversa_id = conversa.ID, 
                canal_id=canal_id
                )

            else: #se for um agente secundário, responder diretamente
                llm_modelo = llm.model_openai.gpt_4_o
                #chama o modelo de linguagem para processar a mensagem e agurda a resposta
                resposta = await functionCall.chat(
                historico=historico,
                mensagemRecebida=mensagem,
                llm_modelo=llm_modelo,
                llm=llm,
                agente=agente,
                usuario=usuario,
                conversa_id = conversa.ID, 
                canal_id=canal_id)
                  #print("##### recebido ##### \n", recebido)  

            print("##### recebido ##### \n", recebido)
            print("##### resposta##### \n",resposta)

            whatsapp.message_send(resposta,telefone)


@router.post("/webhook/agent")
async def webhook_agent(data: dict):
    msg = Message()
    print(data)
    await msg.add_log(data)
    converter = Converter()
    llm = LLM()
    
    validator = Validator()
    gptalk = Gptalk()
    usuario = User()
    llm_modelo = llm.model_default
    #llm_modelo = llm.model_openai.gpt_4_o
    llm_modelo = llm.model_gemini.geminipro15
    agente = Agent()
    conversa = Session()
    whatsapp = AgentWhatsappWhapi()  # Usando a nova classe
    secret = Secret()
    functionCall = FunctionCall()

    #print("data", data)

    #========== PROCESSA MENSAGE RECEBIDA DO USUÁRIO ==========
    if data and 'messages' in data  and data['messages'][0]['from_me'] == False: #se tem mensagens e nao foi enviada pelo agente, mas pelo usuario
        #print("tem mensagens")
        agente_enviou = data['messages'][0]['from_me']
        telefone = data['messages'][0]['from']
        #print("telefone",telefone)
        nome =  data['messages'][0]['from_name']
        tipo =  data['messages'][0]['type']
        chat_id = data['messages'][0]['chat_id']
        canal_id = data['channel_id']
        timestamp = data['messages'][0]['timestamp']  


        #print("tipo", tipo)  
        if tipo == "text" and not agente_enviou: #enviada pelo cliente
            #print("é texto e nao foi o agente que enviou")
            recebido = data['messages'][0]['text']['body']
            
            # tenta carregar o usuario para verificar se já existe
            await usuario.fetch("*",[f"TELEFONE='{telefone}'"])
           
            if not usuario.ID: #se não encontrou o usuario, adiciona
                data_hora  = converter.time_now_to_date_hour()
                await usuario.add({"TELEFONE":telefone,"NOME":nome,"ORIGEM":canal_id,"DATA_CAD": data_hora  })

            #print("usuario.ID",usuario.ID)

            await agente.fetch_agent_id("*",1)

            #verifica se o agente selecionado já está com uma conversa aberta
            await conversa.fetch("*",[f"AGENTE_ID={agente.ID}","ENCERRADA=0"])
            if(conversa.ID): #se tem conversa aberta com o agente....
                #atuliza a ultima interação
                data = {}
                data['ID'] = conversa.ID
                data['ULTIMA_INTERACAO'] = converter.time_now_to_date_hour()
                await conversa.update(data)
                #encerra o fluxo e aguarda nova mensagem
                #return #retona aguardando nova mensagem
            else: #se não tem conversa aberta com o agente...
                #gera uma mensagem de abertura para o usuário
                #recebido = "olá" 
                #inicia uma nova conversa
                conversa = Session()
                conversa.USUARIO_ID = usuario.ID
                conversa.AGENTE_ID = agente.ID
                conversa.USUARIO_NOME = nome
                conversa.INICIO = converter.time_now_to_date_hour()
                conversa.ULTIMA_INTERACAO  = converter.time_now_to_date_hour()
                conversa.CANAL_ID = canal_id
                conversa.CHAT_ID = chat_id
                await conversa.add()

            ###############################################################################################
            #========== PROCESSA PROMPT PARA GENERAR RESPOSTA DO AGENTE ==========
            #carrega historico da conversa
            mysql = Mysql()
            # Carrega o histórico da conversa atual
            if conversa.ID: #se tem conversa atual, carrega mensagens anterores
                sql = f"SELECT ID, USUARIO_NOME AS 'USER', USUARIO_MENSAGEM AS 'MESSAGE', 'user' AS 'ROLE' FROM CONVERSATIONS_MESSAGES WHERE CONVERSATION_ID = {conversa.ID} AND USUARIO_MENSAGEM != '' UNION ALL SELECT ID, AGENTE_NOME AS 'USER', AGENTE_MENSAGEM AS 'MESSAGE', 'assistant' AS 'ROLE' FROM CONVERSATIONS_MESSAGES WHERE CONVERSATION_ID = {conversa.ID} AND AGENTE_MENSAGEM != '' ORDER BY ID;"
                historico = await mysql.query(sql)
            else:
                historico = [] #sem hsitorico para nova conversa

            # Cria contexto do agente
            context = agente.PROMPT_SISTEMA

            #adiciona o contexto e o histórico ao llm
            await llm.add_context(context)
            
            #adiciona o historico da conversa
            if historico:
                for item in historico:
                    await llm.add_message(item['MESSAGE'], item['ROLE'])

            #adiciona a pergunta do usuario 
            await llm.add_message(recebido, 'user')

            #print(llm.messages)
            
            #########################################################################################################
            #chamar a funcao de function calling
            try:
                await functionCall.call_function(llm,agente)
            except:
                print("Erro no function calling")


            #gera a resposta do agente com llm
            resposta = await llm.generate(llm_modelo)
            # retira caracteres especiais que podem dar erro no whatsapp
            resposta = resposta.replace("*","")
            resposta = resposta.replace("_","")
            resposta = resposta.replace("#","")
            resposta = resposta.replace("~","")
            resposta = resposta.replace("`","")

            print(f"whapi agente resposta: {resposta}")
           

            #salva a mensagem na tabela conversations_messages
            data = {
                "CONVERSATION_ID":conversa.ID,
                "USUARIO_NOME":nome,
                "USUARIO_TELEFONE": telefone,
                "USUARIO_MENSAGEM":recebido,
                "AGENTE_NOME":agente.NOME,
                "AGENTE_MENSAGEM":resposta,
                "DATA_HORA": converter.time_now_to_date_hour()
            }
            mysql = Mysql()
            result = await mysql.insert("CONVERSATIONS_MESSAGES",data)

            # envia a resposta para o usuario
            whatsapp.message_send(resposta,client_number=telefone)
    return {"status": "ok"}


@router.post("/maintenance")
async def maintenance(data:dict=None):
    data_retorno = {}
    data_retorno['response'] = data
    return data_retorno


if __name__ == "__main__":
    
    async def mainY():
        pass
                
    import asyncio
    asyncio.run(mainY())

    async def main():
        token="qIJozyA1DHyYzgaHcU8DxOejxGMb23ur"
        client_number="553184198720"
        message = "Fazendo frio aí?"
        wz = AgentWhatsappWhapi()
        wz.message_send(message,token,client_number)
        pass

    asyncio.run(main())