
import importlib
import inspect

async def carrega_ferramentas(codigo: str) -> list:
    ferramentas = []
    print("carrega_ferramentas")
    try:
        if codigo and codigo.startswith("agent_"):
            # Verificar se o código tem o formato esperado (agent_subpasta)
            partes = codigo.split("_")
            if len(partes) >= 2:
                # Segunda parte do código (ex: 'atmzap' de 'agent_atmzap')
                subpasta = partes[1]
                print("subpasta", subpasta)

                # Construir o caminho do módulo
                module_path = f"api.agent.{subpasta}.function"
                print("module_path", module_path)           

                try:
                    pass
                    # Importar o módulo functions.py da subpasta
                    module = importlib.import_module(module_path)
                    
                    print(f"Módulo {module_path} importado com sucesso")

                    
                    # Obter todas as funções do módulo que têm o decorator @function_tool
                    for name, obj in inspect.getmembers(module):
                        print("name", name)

                        # Verificar se é uma função normal ou um objeto FunctionTool
                        is_function = inspect.isfunction(obj)
                        print("is_function", is_function)
                        is_function_tool = hasattr(obj, '__class__') and 'FunctionTool' in str(type(obj))
                        print("is_function_tool", is_function_tool)
                        
                        if (is_function or is_function_tool) and not name.startswith('_') and name != 'function_tool':
                            if is_function_tool:
                                # É um objeto FunctionTool (função decorada)
                                ferramentas.append(obj)
                                #print(f"✅ FunctionTool carregada: {name}")
                            elif is_function:
                                #print(f"Analisando função: {name}")
                                try:
                                    # Verificar se a função foi decorada com @function_tool
                                    # através do código fonte
                                    source = inspect.getsource(obj)
                                    #print(f"Código fonte da função {name} obtido")

                                    
                                    if '@function_tool' in source:
                                        ferramentas.append(obj)
                                        print(f"✅ Função carregada: {name}")
                                    else:
                                        pass
                                        #print(f"❌ Função {name} não tem decorator @function_tool")
                                except (OSError, TypeError) as e:
                                    pass
                                    # Se não conseguir obter o código fonte, pular
                                    #print(f"❌ Não foi possível obter código fonte da função {name}: {e}")

                        else:
                            if not name.startswith('_') and name != 'function_tool':
                                pass
                                #print(f"Objeto {name} ignorado (tipo: {type(obj)})")
                    
                    print(f"Total de {len(ferramentas)} funções carregadas do módulo {subpasta}")
                    
                except ImportError as e:
                    print(f"Erro ao importar módulo {module_path}: {e}")
                except Exception as e:
                    print(f"Erro ao carregar funções do módulo {module_path}: {e}")
            else:
                logger.warning(f"Código do agente '{codigo}' não tem formato esperado (agent_subpasta)")
        else:
            print(f"Código do agente '{codigo}' não começa com 'agent_', nenhuma função carregada")
            
    except Exception as e:
            print(f"Erro geral ao carregar funções para código '{codigo}': {e}")
        
    print(ferramentas)
    return ferramentas


if __name__ == "__main__":
    import asyncio
    
    
    
    asyncio.run(carrega_ferramentas("agent_assistenciamk"))