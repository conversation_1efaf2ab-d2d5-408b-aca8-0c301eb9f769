from fastapi import APIRouter
from fastapi.testclient import TestClient
from .agent_mysql import Mysql
import asyncio
from ..app import app  # Importa o app do novo arquivo

router = APIRouter()



class Designer:
    def __init__(self):
        self.mysql = Mysql()
        

    async def get_user_images(self, idx: str, offset: int = 0, limit: int = 10):
        print(f"get_user_images: {idx}, {offset}, {limit}")
        query = f"SELECT * FROM DESIGN_IMAGEM WHERE USUARIO_IDX = '{idx}' LIMIT {limit} OFFSET {offset}"
        result = await self.mysql.query(query)
        print("result", result)
        return result

@router.post("/style/create")
async def create_style(data: dict):
    designer = Designer()
    result = await designer.create_style(data)
    return result

@router.post("/component/create")
async def create_component(data: dict):
    designer = Designer()
    result = await designer.create_component(data)
    return result

@router.post("/images/user/{idx}")
async def get_images(idx: str, offset: int = 0, limit: int = 10):
    designer = Designer()
    result = await designer.get_user_images(idx, offset,limit)
    return result









