#!/usr/bin/env python3
"""
Teste standalone para o AgentWhisper
Usa diretamente a classe AgentWhisper sem servidor FastAPI
"""

import os
import sys
from pathlib import Path

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_agent_whisper_direct():
    """Testa AgentWhisper diretamente (sem FastAPI)"""
    print("🎯 Testando AgentWhisper diretamente...")
    
    # Caminho do arquivo de áudio
    audio_file_path = r"C:\Users\<USER>\Audio\_temp\olabdia.mp3"
    
    # Verificar se arquivo existe
    if not os.path.exists(audio_file_path):
        print(f"❌ Arquivo não encontrado: {audio_file_path}")
        print("💡 Dica: Ajuste o caminho do arquivo no código")
        return False
    
    try:
        # Importar e usar a classe diretamente
        from api.agent.agent_whisper import AgentWhisper
        
        print("🔄 Inicializando AgentWhisper...")
        agent = AgentWhisper()
        
        print(f"📤 Transcrevendo arquivo: {os.path.basename(audio_file_path)}")
        result = agent.transcribe(audio_file_path)
        
        print("✅ Transcrição realizada com sucesso!")
        print(f"📝 Texto: {result['transcription']}")
        print(f"🌍 Idioma: {result['language']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro ao importar: {str(e)}")
        print("💡 Dica: Verifique se o agent_logger.py existe")
        return False
    except Exception as e:
        print(f"❌ Erro durante transcrição: {str(e)}")
        return False

def main():
    """Função principal de teste"""
    print("🎙️  TESTE STANDALONE - AGENTWHISPER 🎙️")
    print("=" * 50)
    
    # Executar teste
    success = test_agent_whisper_direct()
    
    # Relatório final
    print("\n" + "=" * 50)
    if success:
        print("🎉 Teste passou com sucesso!")
        return 0
    else:
        print("⚠️  Teste falhou")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 