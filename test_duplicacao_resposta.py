import asyncio
import json
from api.agent.agent import resposta_texto, carrega_ferramentas
from api.agent.agent_openai import OpenAi

oai = OpenAi()

async def test_entrada_estoque():
    # Carrega as ferramentas do agente
    ferramentas = carrega_ferramentas("assistenciamk")
    
    # Cria um agente de teste
    agente = {
        "name": "assistente_teste",
        "instructions": "Você é um assistente que ajuda com entrada de estoque.",
        "model": "gpt-4-turbo-preview",
        "tools": ferramentas
    }
    
    # Histórico de mensagens de exemplo
    historico = [
        {"role": "user", "content": "Quero adicionar produtos ao estoque"}
    ]
    
    # Testa a função resposta_texto
    resultado = await resposta_texto(
        agente_openai=agente,
        historico_mensagens=historico,
        usuario_idx="teste_usuario",
        agente_idx="teste_agente",
        conversa_idx="teste_conversa",
        modo_resposta_ia="texto_zap"
    )
    
    print("Resultado:", json.dumps(resultado, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(test_entrada_estoque())
