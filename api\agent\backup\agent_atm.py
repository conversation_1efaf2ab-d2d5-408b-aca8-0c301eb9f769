from .agent_mysql import Mysql
from fastapi import APIRouter

from typing import List, Optional

router = APIRouter()


class Atm:
    def __init__(self):
        self.mysql = Mysql()

    async def automation_fetch(self, folder_idx: str, conta_idx: str):
        pastas = await self.mysql.query(f"""
            SELECT * FROM AUTOMACAO_PASTA 
            WHERE MAE_IDX = {folder_idx} AND CONTA_IDX = '{conta_idx}'
        """)

        automacoes = await self.mysql.query(f"""
            SELECT IDX, NOME FROM AUTOMACAO 
            WHERE PASTA_IDX = {folder_idx} AND CONTA_IDX = '{conta_idx}'
        """)

        result = {}
        result['pastas'] = pastas
        result['automacoes'] = automacoes

        return result

    async def automation_save(self, automacao: dict):
        print("===== self.automation_save()")
        if(automacao['ID'] == 0):
            #inserir
            del automacao['ID']
            result = await self.mysql.add('AUTOMACAO',automacao)
        else:
            #atualizar
            result = await self.mysql.update('AUTOMACAO',automacao)
            result = {"ID": automacao['ID']}
        return result

#Carrega a lista de pastas e de automações
@router.get("/automations/fetch")
async def automations_fetch(folder_idx: str, conta_idx: str):
    print("automation_fetch")
    atm = Atm()
    result = await atm.automation_fetch(folder_idx, conta_idx)
    print("result", result)
    return result

#Salva a automação
@router.post("/automation/save")
async def save_automation(data: dict):
    print("===== save_automation()")
    atm = Atm()

    automacao = data.get("AUTOMACAO")
    result = await atm.automation_save(automacao)
    print("result", result)
    return result


if __name__ == "__main__":
    import asyncio

    async def test_automations_fetch():
        print("\nTestando função automation_fetch")
        atm = Atm()
        
        # Dados de teste
        folder_idx = "0"
        conta_idx = "3231234562"
        
        try:
            result = await atm.automation_fetch(folder_idx, conta_idx)
            print("Resultado do automation_fetch:")
            print(result)
            print("✓ Teste de automation_fetch executado com sucesso")
        except Exception as e:
            print("✕ Erro no teste de automation_fetch:", str(e))
            raise e

    async def test_automation_save():
        print("\nTestando função automation_save")
        atm = Atm()
        
        # Dados de teste
        automacao = {
            "IDX": "9699055762",
            "NOME": "TESTE2 - Minha segunda automação salva",
            "PASTA_IDX": "0",
            "STATUS": 1,
            "ID": 0,
            "CONTA_IDX": "3231234562"
        }
        
        try:
            result = await atm.automation_save(automacao)
            print("Resultado do automation_save:")
            print(result)
            print("✓ Teste de automation_save executado com sucesso")
        except Exception as e:
            print("✕ Erro no teste de automation_save:", str(e))
            raise e

    # Executa os testes
    asyncio.run(test_automations_fetch())
    asyncio.run(test_automation_save())
