from dotenv import load_dotenv
import os
import sys

# Carrega as variáveis de ambiente do arquivo .env
# Tenta carregar de múltiplos locais para garantir que encontre o arquivo correto
# Carregar arquivo .env do diretório raiz do projeto (server)
env_file = os.path.join(os.path.dirname(__file__), '../../.env')
try:
    if os.path.exists(env_file):
        load_dotenv(env_file, override=True)
    else:
        raise FileNotFoundError(f"Arquivo .env não encontrado em: {env_file}")
except Exception as e:
    print(f"⚠️ Erro ao carregar .env: {e}")



class Secret:
    def __init__(self):
        self.DEEPL_API_KEY = os.getenv("DEEPL_API_KEY")
        self.GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.GOOGLE_CLOUD_REGION = os.getenv('GOOGLE_CLOUD_REGION')
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
        self.DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
        self.GPTALK_01_MYSQL_HOST = os.getenv('GPTALK_01_MYSQL_HOST')
        self.GPTALK_01_MYSQL_PORT = os.getenv('GPTALK_01_MYSQL_PORT')
        self.GPTALK_01_MYSQL_DATABASE = os.getenv('GPTALK_01_MYSQL_DATABASE')
        self.GPTALK_01_MYSQL_USER = os.getenv('GPTALK_01_MYSQL_USER')
        self.GPTALK_01_MYSQL_PASSWORD = os.getenv('GPTALK_01_MYSQL_PASSWORD')
        self.GPTALK_01_NEO4J_URI= os.getenv('GPTALK_01_NEO4J_URI')
        self.GPTALK_01_NEO4J_USER=os.getenv('GPTALK_01_NEO4J_USER')
        self.GPTALK_01_NEO4J_PASSWORD=os.getenv('GPTALK_01_NEO4J_PASSWORD')
        self.GPTALK_01_NEO4J_DATABASE=os.getenv('GPTALK_01_NEO4J_DATABASE')
        self.GPTALK_01_AURA_INSTANCE=os.getenv('GPTALK_01_AURA_INSTANCE')
        self.GPTALK_DOMAIN = os.getenv('GPTALK_DOMAIN')
        self.GPTALK_SITE_SUBDOMAIN = os.getenv('GPTALK_SITE_SUBDOMAIN')
        self.GPTALK_SITE_SUBDOMAIN_DIRECTORY = os.getenv('GPTALK_SITE_SUBDOMAIN_DIRECTORY')
        self.GPTALK_CPANEL_USER = os.getenv('GPTALK_CPANEL_USER')
        self.GPTALK_CPANEL_PASSWORD = os.getenv('GPTALK_CPANEL_PASSWORD')
        self.DOMAIN = os.getenv('DOMAIN')
        self.GPTALK_CPANEL_TOKEN = os.getenv('GPTALK_CPANEL_TOKEN')
        self.YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')
        self.GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
        self.MY_AWS_ACCESS_KEY = os.getenv('MY_AWS_ACCESS_KEY')
        self.MY_AWS_SECRET_KEY = os.getenv('MY_AWS_SECRET_KEY')
        self.PEXELS_API_KEY = os.getenv('PEXELS_API_KEY')
        self.GPTALK_EMAIL_INCOMING_IMAP_PORT=os.getenv('GPTALK_EMAIL_INCOMING_IMAP_PORT')
        self.GPTALK_EMAIL_INCOMING_POP3_PORT=os.getenv('GPTALK_EMAIL_INCOMING_POP3_PORT')
        self.GPTALK_EMAIL_OUTGOING_SMTP_PORT=os.getenv('GPTALK_OUTGOING_EMAIL_SMTP_PORT')
        self.GPTALK_EMAIL_DEFAULT_USER=os.getenv('GPTALK_EMAIL_DEFAULT_USER')
        self.GPTALK_EMAIL_DEFAULT_PASSWORD=os.getenv('GPTALK_EMAIL_DEFAULT_PASSWORD')
        self.GPTALK_EMAIL_INCOMING_SERVER=os.getenv('GPTALK_EMAIL_INCOMING_SERVER')
        self.GPTALK_EMAIL_OUTGOING_SERVER=os.getenv('GPTALK_EMAIL_INCOMING_SERVER')
        self.IDEOGRAM_API_KEY=os.getenv('IDEOGRAM_API_KEY')
        self.WHM_USER = os.getenv("WHM_USER")
        self.WHM_TOKEN = os.getenv("WHM_TOKEN")
        self.WHM_URL = os.getenv("WHM_URL")
        self.WHATSAPP_WHAPI_TOKEN= os.getenv("WHATSAPP_WHAPI_TOKEN")
        self.WHATSAPP_WHAPI_CLIENT_NUMBER= os.getenv("WHATSAPP_WHAPI_CLIENT_NUMBER")

        # Adicione estas novas linhas
        self.LOJA247_MYSQL_HOST = os.getenv('LOJA247_MYSQL_HOST')
        self.LOJA247_MYSQL_USER = os.getenv('LOJA247_MYSQL_USER')
        self.LOJA247_MYSQL_PASSWORD = os.getenv('LOJA247_MYSQL_PASSWORD')
        self.LOJA247_MYSQL_DATABASE = os.getenv('LOJA247_MYSQL_DATABASE')
        self.GEMINI_25_OPENROUTER_API_KEY = os.getenv('GEMINI_25_OPENROUTER_API_KEY')
        
        # Variáveis da Mega API
        self.MEGA_API_BASE_URL = os.getenv('MEGA_API_BASE_URL')
        self.MEGA_API_TOKEN = os.getenv('MEGA_API_TOKEN')
        self.MEGA_API_INSTANCE_KEY = os.getenv('MEGA_API_INSTANCE_KEY')

    def get_secret(self, name):
        # Primeiro tenta obter do cache da instância
        if hasattr(self, name):
            return getattr(self, name)
        # Se não encontrar, tenta do os.getenv
        return os.getenv(name)
    
    def debug_env_vars(self):
        """Método para debug das variáveis de ambiente"""
        print(f"=== DEBUG VARIÁVEIS DE AMBIENTE ===")
        print(f"MEGA_API_BASE_URL: {self.MEGA_API_BASE_URL}")
        print(f"MEGA_API_TOKEN: {self.MEGA_API_TOKEN}")
        print(f"MEGA_API_INSTANCE_KEY: {self.MEGA_API_INSTANCE_KEY}")
        
        # Verifica diretamente no os.getenv
        print(f"=== VERIFICAÇÃO DIRETA os.getenv ===")
        print(f"MEGA_API_BASE_URL (direct): {os.getenv('MEGA_API_BASE_URL')}")
        print(f"MEGA_API_TOKEN (direct): {os.getenv('MEGA_API_TOKEN')}")
        print(f"MEGA_API_INSTANCE_KEY (direct): {os.getenv('MEGA_API_INSTANCE_KEY')}")
        
        # Verifica se há diferenças
        print(f"=== VERIFICAÇÃO DE DISCREPÂNCIAS ===")
        if self.MEGA_API_TOKEN != os.getenv('MEGA_API_TOKEN'):
            print(f"⚠️  DISCREPÂNCIA ENCONTRADA!")
            print(f"self.MEGA_API_TOKEN: {self.MEGA_API_TOKEN}")
            print(f"os.getenv('MEGA_API_TOKEN'): {os.getenv('MEGA_API_TOKEN')}")
        else:
            print(f"✓ MEGA_API_TOKEN está consistente")
        
        # Lista todas as variáveis de ambiente que começam com MEGA_API
        print(f"=== TODAS AS VARIÁVEIS MEGA_API ===")
        for key, value in os.environ.items():
            if key.startswith('MEGA_API'):
                print(f"{key}: {value}")
        print(f"==================================")
    
    def validate_mega_api_vars(self):
        """Valida se as variáveis da Mega API estão corretas"""
        required_vars = ['MEGA_API_BASE_URL', 'MEGA_API_TOKEN', 'MEGA_API_INSTANCE_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not self.get_secret(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️  VARIÁVEIS AUSENTES: {missing_vars}")
            return False
        else:
            print(f"✓ Todas as variáveis da Mega API estão presentes")
            return True


