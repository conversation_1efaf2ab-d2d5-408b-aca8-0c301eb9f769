"""
Decoradores personalizados para controle de acesso às funções baseado na função do usuário.
"""

def usuario(func):
    """
    Decorator para marcar funções que devem ser carregadas apenas para usuários com função 'Usuario'
    """
    func._usuario_funcao_required = 'Usuario'
    return func

def admin(func):
    """
    Decorator para marcar funções que devem ser carregadas apenas para usuários com função 'Admin'
    """
    func._usuario_funcao_required = 'Admin'
    return func

def gerente(func):
    """
    Decorator para marcar funções que devem ser carregadas apenas para usuários com função 'Gerente'
    """
    func._usuario_funcao_required = 'Gerente'
    return func

def vendedor(func):
    """
    Decorator para marcar funções que devem ser carregadas apenas para usuários com função 'Vendedor'
    """
    func._usuario_funcao_required = 'Vendedor'
    return func

def publico(func):
    """
    Decorator para marcar funções que devem ser carregadas para qualquer usuário (sem restrição)
    """
    func._usuario_funcao_required = 'publico'
    return func

def get_required_function(func):
    """
    Função auxiliar para obter a função requerida de uma função decorada
    """
    return getattr(func, '_usuario_funcao_required', None)

def has_access(func, usuario_funcao):
    """
    Verifica se o usuário tem acesso à função baseado na sua função
    """
    required_function = get_required_function(func)
    
    # Se não tem decorator de função específica, permite acesso
    if required_function is None:
        return True
    
    # Se é público, permite acesso
    if required_function == 'publico':
        return True
    
    # Verifica se a função do usuário corresponde à requerida
    return usuario_funcao == required_function
