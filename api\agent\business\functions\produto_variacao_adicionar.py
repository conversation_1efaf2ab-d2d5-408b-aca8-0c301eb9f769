import os
import json
from agents.tool import function_tool
from api.functions.util import generate_unique_id
from ...agent_logger import AgentLogger
from ...agent_neo4j import AgentNeo4j

logger = AgentLogger()
neo4j = AgentNeo4j()

#@function_tool
async def produto_variacao_adicionar(
    produto_original_codigo: str,
    cor_nome: str,
    codigo: str,
    negocio_idx: str
):
    """
    🚨 FUNÇÃO CRÍTICA: Adiciona produto que é uma variação de outro ja existente.

    Solicite os dados do produto ao usuario, um de cada vez e na squencia abaixo.
    Os seguintes dados deverão ser passados para a função:
    
    
    PRODUTO_ORIGINAL_CODIGO:
    - Codigo do produto que sera 'clonado'.
    - Exemplo: "2234123512"
    - Obrigatório
   🚨🚨🚨IMPORTANTE 
    Solicite ao usuario o codigo do produto. Caso ele informar, busque o produto usando a funcao consultar_produto_negocio(). Caso não encontre, informe ao usuario que o produto não foi encontrado e peça que ele verifique o codigo correto.



    CODIGO:
    - Código do novo produto.
    - Exemplo: "10142673"
    - Obrigatório
    
    COR_NOME:
    - Nome da cor do produto.
    - Exemplo: "Red Matte"
    - Opcional
   🚨🚨🚨IMPORTANTE 
    Solicite ao usuario o nome da cor. Caso ele informe, busque a cor na tabela COR usando a funcao cor_consultar(). Caso não encontre, informe ao usuario que a cor não foi encontrada e peça que ele verifique o nome correto.

    """
    logger.info("===== produto_variacao_adicionar ======")
    logger.info(f"negocio_idx: {negocio_idx}")
    logger.info(f"produto_original_codigo: {produto_original_codigo}")
    logger.info(f"cor_nome: {cor_nome}")
    logger.info(f"codigo: {codigo}")

    try:
        # 1. Primeiro, buscar o produto original para clonar seus dados
        query_buscar_original = """
        MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_original_codigo})
        WHERE p.excluido = 0
        RETURN p.nome AS nome,
               p.preco AS preco,
               p.preco_maior AS preco_maior,
               p.descricao AS descricao,
               p.url_imagem AS url_imagem,
               p.idx AS produto_original_idx
        """
        
        parametros_busca = {
            'negocio_idx': negocio_idx,
            'produto_original_codigo': produto_original_codigo
        }
        
        produto_original = await neo4j.execute_read_query(query_buscar_original, parametros_busca)
        
        if not produto_original or len(produto_original) == 0:
            logger.error(f"❌ ERRO: Produto original com código {produto_original_codigo} não encontrado")
            return {
                "success": False, 
                "message": f"Produto original com código {produto_original_codigo} não foi encontrado no negócio. Verifique o código informado."
            }
        
        # Pegar os dados do primeiro (e único) produto encontrado
        # Após a linha 67, adicione:
        dados_original = produto_original[0]
        logger.info(f"✅ Produto original encontrado: {dados_original.get('nome', 'NOME_NAO_ENCONTRADO')}")
        logger.info(f"🔍 Debug - Dados originais completos: {dados_original}")
        
        # 2. Verificar se a cor existe (se informada)
        if cor_nome:
            query_verificar_cor = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor)
            WHERE toLower(c.nome) CONTAINS toLower($cor_nome)
            RETURN c.nome AS nome, c.idx AS cor_idx
            LIMIT 1
            """
            
            parametros_cor = {
                'negocio_idx': negocio_idx,
                'cor_nome': cor_nome
            }
            
            cor_encontrada = await neo4j.execute_read_query(query_verificar_cor, parametros_cor)
            
            if not cor_encontrada or len(cor_encontrada) == 0:
                logger.error(f"❌ ERRO: Cor {cor_nome} não encontrada")
                return {
                    "success": False,
                    "message": f"Cor '{cor_nome}' não foi encontrada no negócio. Verifique o nome da cor informado."
                }
            
            cor_nome_validado = cor_encontrada[0]['nome']
            logger.info(f"✅ Cor encontrada: {cor_nome_validado}")
        else:
            cor_nome_validado = None
        
        # 3. Verificar se já existe um produto com o novo código
        query_verificar_codigo = """
        MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $codigo})
        WHERE p.excluido = 0
        RETURN p.codigo
        """
        
        parametros_codigo = {
            'negocio_idx': negocio_idx,
            'codigo': codigo
        }
        
        codigo_existente = await neo4j.execute_read_query(query_verificar_codigo, parametros_codigo)
        
        if codigo_existente and len(codigo_existente) > 0:
            logger.error(f"❌ ERRO: Código {codigo} já existe")
            return {
                "success": False,
                "message": f"Já existe um produto com o código '{codigo}' neste negócio. Escolha um código diferente."
            }
        
        # 4. Criar o novo produto (variação)
        produto_idx = generate_unique_id()
        
        query_criar_produto = """
        // Cria o nó do novo Produto com dados clonados
        CREATE (p:Produto {
            idx: $produto_idx,
            nome: $nome,
            codigo: $codigo,
            preco_maior: $preco_maior,
            preco: $preco,
            descricao: $descricao,
            url_imagem: $url_imagem,
            excluido: 0,
            data_cadastro: datetime()
        })
        
        // Adiciona a cláusula WITH para poder continuar a consulta
        WITH p
        
        // Cria relacionamento com o Negócio
        MATCH (n:Negocio {idx: $negocio_idx})
        CREATE (n)-[:POSSUI_PRODUTO]->(p)
        
        // Cria relacionamento com a Cor, se informada
        WITH p
        WHERE $cor_nome IS NOT NULL AND $cor_nome <> ''
        MATCH (c:Cor {nome: $cor_nome})
        CREATE (p)-[:TEM_COR]->(c)
        
        RETURN p.idx AS produto_idx, p.nome AS nome, p.codigo AS codigo
        """
        
        parametros_criar = {
            'produto_idx': produto_idx,
            'nome': dados_original['nome'],
            'codigo': codigo,
            'preco_maior': dados_original['preco_maior'],
            'preco': dados_original['preco'],
            'descricao': dados_original['descricao'],
            'url_imagem': dados_original['url_imagem'],
            'cor_nome': cor_nome_validado,
            'negocio_idx': negocio_idx
        }
        
        logger.info(f"📦 Criando produto variação com dados: {parametros_criar}")
        
        resultado = await neo4j.execute_write_query(query_criar_produto, parametros_criar)
        
        # Verifica se o resultado é válido
        if not resultado or len(resultado) == 0:
            logger.error("❌ ERRO: Falha na inserção do produto - neo4j.execute_write_query() retornou resultado vazio")
            return {
                "success": False, 
                "message": "Erro ao inserir produto no banco de dados. Verifique os logs para mais detalhes."
            }
        
        produto_criado = resultado[0]
        logger.info("===== produto_variacao_adicionar SUCESSO =====")
        
        mensagem_sucesso = f"✅ Produto variação adicionado com sucesso!\n"
        mensagem_sucesso += f"📦 Nome: {produto_criado['nome']}\n"
        mensagem_sucesso += f"🔢 Código: {produto_criado['codigo']}\n"
        if cor_nome_validado:
            mensagem_sucesso += f"🎨 Cor: {cor_nome_validado}\n"
        mensagem_sucesso += f"🆔 ID: {produto_criado['produto_idx']}"
        
        return {
            "success": True, 
            "message": mensagem_sucesso,
            "data": {
                "produto_idx": produto_criado['produto_idx'],
                "nome": produto_criado['nome'],
                "codigo": produto_criado['codigo'],
                "cor": cor_nome_validado
            }
        }
        
    except Exception as e:
        logger.error(f"===== ERRO EM produto_variacao_adicionar() =====")
        logger.error(f"ERRO TIPO: {type(e).__name__}")
        logger.error(f"ERRO MENSAGEM: {str(e)}")
        logger.error(f"DADOS DO PRODUTO:")
        logger.error(f"  - PRODUTO_ORIGINAL_CODIGO: {produto_original_codigo}")
        logger.error(f"  - CODIGO: {codigo}")
        logger.error(f"  - COR_NOME: {cor_nome}")
        logger.error(f"  - NEGOCIO_IDX: {negocio_idx}")
        logger.error("===== FIM ERRO produto_variacao_adicionar() =====")
        return {
            "success": False, 
            "error": f"Erro ao adicionar produto variação: {str(e)}"
        }


# Seção de testes
if __name__ == "__main__":
    import asyncio
    import uuid
    
    async def testa_adicionar_produto_variacao():
        # Dados de teste
        dados_teste = {
            "produto_original_codigo": "10102679",  # Código de um produto existente no banco
            "codigo": str(uuid.uuid4())[:8],  # Novo código único
            "cor_nome": "Beige C120 (Matte)",  # Nome de uma cor existente no banco
            "negocio_idx": "5544332211"  # ID de negócio de teste
        }
        
        print("=== Teste de Inclusão de Produto Variação ===")
        print(f"Dados do teste:\n{dados_teste}\n")
        
        try:
            resultado = await produto_variacao_adicionar(
                produto_original_codigo=dados_teste["produto_original_codigo"],
                cor_nome=dados_teste["cor_nome"],
                codigo=dados_teste["codigo"],
                negocio_idx=dados_teste["negocio_idx"]
            )
            
            print("=== Resultado do Teste ===")
            print(f"Sucesso: {resultado.get('success', False)}")
            print(f"Mensagem: {resultado.get('message', 'Nenhuma mensagem retornada')}")
            
            if resultado.get('success', False):
                print(f"Dados do produto criado: {resultado.get('data', {})}")
            else:
                print(f"Erro: {resultado.get('error', 'Erro desconhecido')}")
            
            return resultado
            
        except Exception as e:
            print(f"Erro durante o teste: {str(e)}")
            raise
    
    # Executar o teste
    asyncio.run(testa_adicionar_produto_variacao())
    # Execução:
    # py -m api.agent.business.functions.produto_variacao_adicionar
