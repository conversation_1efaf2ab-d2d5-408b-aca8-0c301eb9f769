{"generated_at": "2025-08-20T13:54:00Z", "database": "neo4j", "node_types": [{"label": "Produ<PERSON>", "properties": [{"name": "codigo", "type": "STRING"}, {"name": "descricao", "type": "STRING"}, {"name": "estoque", "type": "INTEGER"}, {"name": "excluido", "type": "INTEGER"}, {"name": "idx", "type": "STRING"}, {"name": "nome", "type": "STRING"}, {"name": "preco", "type": "FLOAT"}, {"name": "preco_maior", "type": "FLOAT"}]}, {}, {"label": "Cor", "properties": [{"name": "nome", "type": "STRING"}, {"name": "codigo", "type": "STRING"}, {"name": "hexadecimal", "type": "STRING"}]}], "relationship_types": [{"type": "DISPONIBILIZA_COR", "properties": []}, {"type": "POSSUI_PRODUTO", "properties": []}, {"type": "TEM_COR", "properties": []}], "patterns": [["Produ<PERSON>", "TEM_COR", "Cor"], ["Negocio", "DISPONIBILIZA_COR", "Cor"], ["Negocio", "POSSUI_PRODUTO", "Produ<PERSON>"]]}