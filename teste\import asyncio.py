import asyncio
import os
from typing import Any, List
from unittest.mock import MagicMock, patch

import neo4j
from neo4j_graphrag.embeddings import OpenAIEmbeddings
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import LLMInterface, OpenAILLM, LLMResponse

# --- Configurações da Aplicação ---
# Em um ambiente real, você usaria as credenciais reais.
# Para este exemplo, usaremos mocks para que o código rode sem chaves de API.
NEO4J_URI = "neo4j://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "sua_senha_super_secreta"
NEO4J_DATABASE = "neo4j"
# OPENAI_API_KEY = "sk-..." # Necessário em produção

# --- Texto de Exemplo para Ingestão ---
# Usaremos um texto simples para focar no funcionamento da biblioteca.
# A lógica para extrair texto de um vídeo do YouTube (Passo 1) permanece a mesma.
TEXTO_PARA_PROCESSAR = """
A Acme Corporation, fundada em 1985 por <PERSON> em Nova York, é líder em widgets.
<PERSON>, que entrou em 2010, agora é a Diretora de Engenharia.
O produto principal, SuperWidget X1, foi desenvolvido pela equipe de Sarah.
"""

# --- Definição do Nosso Esquema (Nossa "Planta da Casa") ---
# Este é o esquema que desenhamos juntos, agora formatado para a biblioteca.
# A biblioteca usará isso para guiar o LLM na extração.
NOSSO_ESQUEMA = {
    "node_types": [
        {"label": "Person", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Organization", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Product", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Location", "properties": [{"name": "name", "type": "STRING"}]}
    ],
    "relationship_types": [
        "WORKS_FOR",
        "FOUNDED_BY",
        "LOCATED_IN",
        "DEVELOPED"
    ],
    "patterns": [
        ("Person", "WORKS_FOR", "Organization"),
        ("Organization", "FOUNDED_BY", "Person"),
        ("Organization", "LOCATED_IN", "Location"),
        ("Product", "DEVELOPED", "Person")
    ]
}

# --- Mock (Simulação) das Chamadas de API para Teste ---
# Este bloco simula as respostas da OpenAI para que o código possa ser executado
# sem uma chave de API real. Em produção, você removeria este bloco.

def mock_llm_responses() -> List[LLMResponse]:
    """Simula a resposta do LLM para o nosso texto de exemplo."""
    return [
        LLMResponse(
            content="""
            {
                "nodes": [
                    {"id": "0", "label": "Organization", "properties": {"name": "Acme Corporation"}},
                    {"id": "1", "label": "Person", "properties": {"name": "John Smith"}},
                    {"id": "2", "label": "Location", "properties": {"name": "Nova York"}},
                    {"id": "3", "label": "Person", "properties": {"name": "Sarah Johnson"}},
                    {"id": "4", "label": "Product", "properties": {"name": "SuperWidget X1"}}
                ],
                "relationships": [
                    {"type": "FOUNDED_BY", "start_node_id": "0", "end_node_id": "1"},
                    {"type": "LOCATED_IN", "start_node_id": "0", "end_node_id": "2"},
                    {"type": "WORKS_FOR", "start_node_id": "3", "end_node_id": "0"},
                    {"type": "DEVELOPED", "start_node_id": "4", "end_node_id": "3"}
                ]
            }
            """
        )
    ]

async def main():
    """
    Função principal que configura e executa o pipeline de ingestão
    usando a biblioteca neo4j-graphrag.
    """
    print("--- Iniciando Pipeline de Ingestão com a Biblioteca Neo4j-GraphRAG ---")

    # --- Configuração dos Componentes ---
    # Em produção, você inicializaria os componentes com suas chaves de API.
    # llm = OpenAILLM(model_name="gpt-4o", api_key=OPENAI_API_KEY)
    # embedder = OpenAIEmbeddings(api_key=OPENAI_API_KEY)
    
    # Para este exemplo, usamos Mocks (objetos simulados)
    llm_mock = MagicMock(spec=LLMInterface)
    llm_mock.ainvoke.side_effect = mock_llm_responses()
    
    embedder_mock = MagicMock(spec=OpenAIEmbeddings)
    embedder_mock.embed_query.return_value = [random.random() for _ in range(1536)]

    # Conexão real com o banco de dados Neo4j
    driver = neo4j.GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    # Limpa o banco de dados para um novo teste
    with driver.session(database=NEO4J_DATABASE) as session:
        session.run("MATCH (n) DETACH DELETE n")
    print("-> Banco de dados limpo para o teste.")

    # --- Criação do Pipeline ---
    # A classe `SimpleKGPipeline` é o nosso "mestre de obras".
    # Ela já contém toda a lógica para orquestrar os passos que discutimos.
    kg_builder = SimpleKGPipeline(
        llm=llm_mock,
        driver=driver,
        embedder=embedder_mock,
        schema=NOSSO_ESQUEMA,
        from_pdf=False,  # Estamos usando texto, não um arquivo PDF
        on_error="RAISE", # Se o LLM falhar, o processo para.
        neo4j_database=NEO4J_DATABASE
    )
    
    print("\n-> Pipeline configurado. Executando a ingestão do texto...")

    # --- Execução do Pipeline ---
    # Com uma única chamada, a biblioteca executa todo o fluxo:
    # 1. Quebra o texto em pedaços (chunks).
    # 2. Gera embeddings para os chunks.
    # 3. Chama o LLM para extrair entidades e relações com base no nosso esquema.
    # 4. Escreve o grafo léxico (Documento, Chunks) e o grafo de conhecimento no Neo4j.
    # 5. Executa a resolução de entidades (MERGE) para evitar duplicatas.
    result = await kg_builder.run_async(text=TEXTO_PARA_PROCESSAR)

    print("\n--- Pipeline Concluído ---")
    print(f"ID da Execução: {result.run_id}")
    print(f"Resultado Final: {result.result}")

    # --- Verificação dos Resultados no Banco de Dados ---
    print("\n--- Verificando o Grafo Criado no Neo4j ---")
    with driver.session(database=NEO4J_DATABASE) as session:
        nodes_result = session.run("MATCH (n:__Entity__) RETURN n.label AS label, n.properties.name AS name")
        print("Nós de Entidade Criados:")
        for record in nodes_result:
            print(f"- Nó: {record['label']}, Nome: {record['name']}")
        
        rels_result = session.run("MATCH (n)-[r]->(m) WHERE NOT r:FROM_CHUNK AND NOT r:NEXT_CHUNK RETURN n.properties.name AS start, type(r) AS type, m.properties.name AS end")
        print("\nRelações Criadas:")
        for record in rels_result:
            print(f"- Relação: ({record['start']})-[:{record['type']}]->({record['end']})")

    driver.close()

if __name__ == "__main__":
    # Para rodar este script, você precisa ter o Neo4j rodando localmente
    # e ter instalado as bibliotecas:
    # pip install "neo4j-graphrag[openai]"
    asyncio.run(main())