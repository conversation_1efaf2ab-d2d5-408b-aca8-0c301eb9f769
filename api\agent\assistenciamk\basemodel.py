# Modelos para cliente
class Cliente(BaseModel):
    ID: int # Ou o tipo correto do ID do seu cliente
    IDX: str    
    NOME: Optional[str] = None
    TELEFONE: Optional[str] = None
    EMAIL: Optional[str] = None
    CEP: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None
    # Adicione todos os campos relevantes do seu dicionário 'cliente'
    from pydantic import BaseModel, Field

# Modelo para um item de serviço individual
class Produto(BaseModel):
    codigo: str
    nome: str
    valor: float
    quantidade: int


    # Modelo para um item de forma de pagamento individual
class FormaPagamento(BaseModel):
    nr: int = Field(alias="nr") # Use Field(alias) se o nome do campo Python for diferente do JSON
    id: int
    valor: float
    vencimento: str # Ou datetime.date, se preferir lidar com objetos de data
    pago: Literal[0, 1] # 0 ou 1   


# Classes Pydantic para estruturar respostas de produtos
class CorProduto(BaseModel):
    nome: str = Field(description="Nome da cor")
    hexadecimal: Optional[str] = Field(description="Código hexadecimal da cor")
    codigo: str = Field(description="Código da variação")
    estoque: Optional[float] = Field(description="Quantidade em estoque")

class ProdutoIndividual(BaseModel):
    nome: str = Field(description="Nome do produto")
    preco: float = Field(description="Preço do produto")
    preco_maior: Optional[float] = Field(description="Preço original para cálculo de desconto")
    desconto: Optional[float] = Field(description="Valor do desconto")
    cor: Optional[CorProduto] = Field(description="Cor do produto se aplicável")
    codigo: Optional[str] = Field(description="Código do produto")
    url_imagem: Optional[str] = Field(description="URL da imagem do produto")
    estoque: Optional[float] = Field(description="Quantidade em estoque")

class GrupoProdutos(BaseModel):
    nome: str = Field(description="Nome do grupo de produtos")
    preco: float = Field(description="Preço do grupo")
    preco_maior: Optional[float] = Field(description="Preço original para cálculo de desconto")
    desconto: Optional[float] = Field(description="Valor do desconto")
    cores_disponiveis: List[CorProduto] = Field(description="Lista de cores disponíveis")
    url_imagem: Optional[str] = Field(description="URL da imagem do produto")


    class RespostaProdutos(BaseModel):
        tipo: str = Field(description="Tipo de resposta: 'individual' ou 'grupo'")
        mensagem: str = Field(description="Mensagem explicativa")
        produtos_individuais: Optional[List[ProdutoIndividual]] = Field(description="Lista de produtos individuais")
        grupos_produtos: Optional[List[GrupoProdutos]] = Field(description="Lista de grupos de produtos")
