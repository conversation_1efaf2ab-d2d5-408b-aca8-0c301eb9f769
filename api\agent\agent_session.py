from datetime import datetime, timezone
from .agent_mysql import Mysql
import asyncio, json,  pytz


class Session:
    def __init__(self):
        self.mysql = Mysql()
        self.ID = 0

    async def fetch(self,columns:str,filters:str):
       
       conversa = await self.mysql.fetch(columns,"CONVERSA",filters,order="ULTIMA_INTERACAO DESC")

       if conversa:
            conversa = conversa[0]
            for column in conversa.keys():
                setattr(self, column, conversa[column])
       
       return conversa


    async def update(self,data):
        return await self.mysql.update("CONVERSA",data)

    async def add (self):
        conversa = {attr: value for attr, value in vars(self).items() if attr.isupper()}

        id = await self.mysql.add('CONVERSA',conversa)
        if id:
             self.ID = id
             return id
        else:
            return 0
       
async def main():

     agente_id = 1

     data = {
         'messages': [
             {
                 'id': 'e1gaaLj2hoo2Islnb3rLvg-gMWAzGHxuQ',
                 'from_me': False,
                 'type': 'text',
                 'chat_id': '<EMAIL>',
                 'timestamp': 1716878086,
                 'source': 'mobile',
                 'text': {'body': 'Sim , e por ai?'},
                 'from': '553184784825',
                 'from_name': 'Carlos Silva'
             }
         ],
         'event': {
             'type': 'messages',
             'event': 'post'
         },
         'channel_id': 'AQUAMN-AA6GQ'
     }
     
     telefone = data['messages'][0]['from']
     nome = data['messages'][0]['from_name']
     tipo = data['messages'][0]['type']
     chat_id = data['messages'][0]['chat_id']
     canal_id = data['channel_id']
 
     timestamp = data['messages'][0]['timestamp']
     
     #print("telefone", telefone)
     #print("nome", nome)
     #print("tipo", tipo)
     #print("chat_id", chat_id)
    # print("timestamp", timestamp)

     session = Session()
     #conversa_id = await session.save(usuario_nome=nome,usuario_telefone=telefone,chat_id=chat_id,agente_id=agente_id,canal_id=canal_id)
     #print("conversa_id", conversa_id)

     conversa = await session.fetch("*",["USUARIO_TELEFONE='5531984784825'"])
     #print('conversa',conversa)
     



#asyncio.run(main())
        
