# Sistema de Decoradores Personalizados para Controle de Acesso

## Visão Geral

Este sistema permite criar anotações personalizadas para controlar quais funções são carregadas baseado na `usuario_funcao`. As funções mantêm a compatibilidade com `@function_tool` e são carregadas condicionalmente.

## Decoradores Disponíveis

### Decoradores por Função
- `@usuario` - Carrega apenas se `usuario_funcao == "Usuario"`
- `@admin` - Carrega apenas se `usuario_funcao == "Admin"`
- `@gerente` - Carrega apenas se `usuario_funcao == "Gerente"`
- `@vendedor` - Carrega apenas se `usuario_funcao == "Vendedor"`
- `@publico` - Carrega para qualquer `usuario_funcao`

### Comportamento Restritivo
- Funções **DEVEM ter** `@function_tool` **E** decorator personalizado para serem carregadas
- Funções **apenas com** `@function_tool` (sem decorator personalizado) **NÃO** são carregadas
- Funções são carregadas **APENAS** se `usuario_funcao` corresponder ao decorator

## Como Usar

### 1. Importar os Decoradores
```python
from ...decorators import usuario, admin, gerente, vendedor, publico
```

### 2. Aplicar aos Decoradores
```python
# Função apenas para usuários
@function_tool
@usuario
async def minha_funcao_usuario(parametro: str) -> Dict:
    """Esta função só carrega para usuario_funcao = 'Usuario'"""
    return {"resultado": "sucesso"}

# Função apenas para administradores
@function_tool
@admin
async def minha_funcao_admin(parametro: str) -> Dict:
    """Esta função só carrega para usuario_funcao = 'Admin'"""
    return {"resultado": "sucesso"}

# Função pública (todos os usuários)
@function_tool
@publico
async def minha_funcao_publica(parametro: str) -> Dict:
    """Esta função carrega para qualquer usuario_funcao"""
    return {"resultado": "sucesso"}

# ATENÇÃO: Função apenas com @function_tool NÃO será carregada
@function_tool
async def minha_funcao_nao_carrega(parametro: str) -> Dict:
    """Esta função NÃO será carregada (falta decorator personalizado)"""
    return {"resultado": "sucesso"}
```

## Exemplo Prático

### Arquivo: `consultar_produto_entrada.py`
```python
from agents.tool import function_tool
from ...decorators import usuario

@function_tool
@usuario  # Só carrega se usuario_funcao = "Usuario"
async def consultar_produto_entrada(...):
    # Implementação da função
    pass
```

## Como Funciona Internamente

### 1. Modificação em `carrega_ferramentas()` (linhas 1955-2040)
- Importa `has_access` de `decorators.py`
- Verifica se função tem `@function_tool`
- Se tem, verifica acesso com `has_access(obj, usuario_funcao)`
- Só carrega se acesso for permitido

### 2. Logs de Diagnóstico
```
🔍 INICIANDO carrega_ferramentas
👤 Usuario função: Usuario
📁 pasta identificada: assistenciamk
✅ STATUS: Função carregada: consultar_produto_entrada ✓ (@function_tool + @Usuario + acesso para Usuario)
🚫 STATUS: Função rejeitada: funcao_admin (acesso negado - requer @Admin, usuário é Usuario)
⚠️ STATUS: Função rejeitada: funcao_sem_decorator (tem @function_tool mas falta decorator personalizado)
📈 Total de funções verificadas: 10
🎯 Total de funções carregadas: 2
🚫 Total de funções rejeitadas por acesso: 8
```

## Vantagens

1. **Compatibilidade Total**: Mantém `@function_tool` funcionando
2. **Flexibilidade**: Pode criar novos decoradores facilmente
3. **Segurança**: Controle granular de acesso
4. **Transparência**: Logs detalhados do processo
5. **Segurança Restritiva**: Apenas funções com ambos decoradores são carregadas

## Adicionando Novos Decoradores

Para criar um novo decorator (ex: `@supervisor`):

```python
# Em decorators.py
def supervisor(func):
    func._usuario_funcao_required = 'Supervisor'
    return func
```

## Testando

Para testar, modifique a `usuario_funcao` e observe os logs:
- `usuario_funcao = "Usuario"` → Carrega funções com `@usuario` e `@publico`
- `usuario_funcao = "Admin"` → Carrega funções com `@admin` e `@publico`
- `usuario_funcao = "Gerente"` → Carrega funções com `@gerente` e `@publico`
