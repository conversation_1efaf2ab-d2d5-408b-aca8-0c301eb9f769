import requests
import sys
import base64

with open('image.jpg',"rb") as arquivo:
    arq_binary = arquivo.read()
    arq_b64 = base64.b64encode(arq_binary).decode('utf-8')
    #print(arq_b64)


# Configurações
instance = "gptalk-zaplocal"
urlbase = "http://127.0.0.1:8081"
api_key = "429683C4C977415CAAFCCE10F7D57E11"


# Headers padrão
headers = {
    "apikey": api_key,
    "Content-Type": "application/json"
}

# 1. Teste de envio de texto
print("=== TESTE ENVIO TEXTO ===")
url_text = f"{urlbase}/message/sendText/{instance}"

payload_text = {
    "number": "************",
    "text": "Olá, bom dia! Teste de envio de texto"
}

response_text = requests.post(url_text, json=payload_text, headers=headers)
print(f"Status: {response_text.status_code}")
print(f"Resposta: {response_text.text}")
print()

print("===== Teste de mensagem concluido ======")

#===========================================================


url_media = f"{urlbase}/message/sendMedia/{instance}"
print("url_media", url_media)


payload = {
    "number": "************",
    "mediatype": "image",
    "fileName": "image.jpg",
    "caption": "teste",
    "media": "https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png"
}

response_media = requests.post(url_media, json=payload, headers=headers)
print(f"Status: {response_media.status_code}")
print(f"Resposta: {response_media.text}")
#print()
