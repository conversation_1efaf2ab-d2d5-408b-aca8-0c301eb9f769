import requests
import asyncio
from .agent_llm import LLM
from .agent_image import Image
from .agent_secret import Secret
from .agent_translator import  Translator 

class Pexels:
    def __init__(self, api_key=None,model=None):
        secret = Secret()
        
        self.llm = LLM()
        self.api_key = secret.PEXELS_API_KEY
        self.url = "https://api.pexels.com/v1/search"
        self.model = model if model else self.llm.model_default 
        self.image = Image()
        self.translator = Translator()

    async def pexels_request(self,query=None,orientation='',per_page=10,page=1,size='small',color='',locale=''):

        # Parâmetros da consulta
        params = {
        'query': query,  
        'orientation': orientation,
        'per_page': per_page,
        'page':page,
        'size':size,
        'color':color,
        'locale':locale,
        }

        # Cabeçalho com a chave de autorização
        headers = {
        'Authorization': self.api_key
        }

        # Faz a requisição GET à API
        response = requests.get(self.url, headers=headers, params=params)

        # Verifica se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Extrai o conteúdo da resposta
            data = response.json()
            return data
        else:
            print(f"Erro ao fazer a requisição: {response.status_code}")

   
    async def image_search(self, id=None, text=None, width=None,height=None, model=None,size=None):
        #print("##########image_search",id,text,width,height)
        orientacao = self.image.classify_dimension(width, height)
        #print("")
        #print("alt em portugues",text)
        text = await self.translator.deepl_translate_text(text,"EN-US")
        #print("alt em ingles",text)
        #print("")
        result = await self.pexels_request(query=text,orientation=orientacao,size=size)
        #print("result",result )
        image_link = result['photos'][0]['src']['medium']
        #print("XXXXXXX IMAGE LINK OBITO")
        #print(image_link)
        #print('')
        

        return image_link






async def main():

    pxs = Pexels(api_key='amQP8ljF6yrfBCX2xMe0jM156dKwa9DYkDj7u8pOC705a7oNR8SgZlq2')
    result = await pxs.pexels_request(
         query="dog in a pool",
         orientation='landscape',
         per_page=1,
        size='small',
        color='black',
        page=1
        )

    print("result",result)
#asyncio.run(main())

















