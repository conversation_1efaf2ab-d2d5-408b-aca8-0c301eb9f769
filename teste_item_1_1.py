#!/usr/bin/env python3
"""
TESTE 1.1: Verificar status usando ID ÚNICO DE CONTROLE como token
"""
import requests

# Configurações com ID ÚNICO DE CONTROLE
url = 'https://apinocode01.megaapi.com.br/rest/instance/megacode-MqgHxQV7790'
headers = {'Authorization': 'Bearer 4649a86d-1e20-4dd1-a6ad-e41f1b92c495'}

print('🔍 TESTE 1.1: Status da instância com ID ÚNICO DE CONTROLE')
print(f'URL: {url}')
print(f'Token: 4649a86d-1e20-4dd1-a6ad-e41f1b92c495')
print('-' * 50)

try:
    response = requests.get(url, headers=headers)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response completa: {data}')
        
        if data.get('error') == False:
            print('✅ SUCESSO: Instância conectada!')
            print('🎯 SOLUÇÃO ENCONTRADA: ID ÚNICO DE CONTROLE é o token correto!')
        else:
            print('❌ FALHA: Ainda não autorizado')
            print(f'Erro: {data.get("message", "Erro desconhecido")}')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 1.1:') 