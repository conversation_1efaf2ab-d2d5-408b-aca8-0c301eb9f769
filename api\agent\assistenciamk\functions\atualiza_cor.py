from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()
@function_tool
async def atualiza_cor(query: str, params: str):
    """
    Atualização GENÉRICA na base de CORES (Neo4j).

    NÓS DISPONÍVEIS:
    - Cor      {idx, nome, codigo, hexadecimal, excluido}

    RELAÇÕES:
    - (n:Negocio)-[:DISPONIBILIZA_COR]->(c:Cor)

    Regras obrigatórias na query:
    - MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor)
    - WHERE c.excluido <> 1
    - SET ...

    Exemplos prontos:
    1. Atualizar nome de uma cor:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {codigo: $codigo})
               WHERE c.excluido <> 1
               SET c.nome = $novo_nome
               RETURN c"
       params: '{"negocio_idx": "5544332211", "codigo": "COR001", "novo_nome": "Vermelho Escuro"}'

    2. Atualizar código hexadecimal de uma cor:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {idx: $cor_idx})
               WHERE c.excluido <> 1
               SET c.hexadecimal = $novo_hexadecimal
               RETURN c"
       params: '{"negocio_idx": "5544332211", "cor_idx": "abc123", "novo_hexadecimal": "#8B0000"}'

    3. Atualizar código de uma cor:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {nome: $nome_atual})
               WHERE c.excluido <> 1
               SET c.codigo = $novo_codigo
               RETURN c"
       params: '{"negocio_idx": "5544332211", "nome_atual": "Vermelho", "novo_codigo": "COR-VERMELHO"}'

    4. Marcar cor como excluída:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {codigo: $codigo})
               SET c.excluido = 1
               RETURN c"
       params: '{"negocio_idx": "5544332211", "codigo": "COR001"}'
    """
    try:
        p = json.loads(params)
        if p.get("negocio_idx") is None:
            return {"status": "error", "message": "negocio_idx obrigatório"}

        q = query.upper()
        if "MATCH" not in q or "SET" not in q:
            return {"status": "error", "message": "Query precisa de MATCH e SET"}
        if "$NEGOCIO_IDX" not in q:
            return {"status": "error", "message": "Faltam filtros obrigatórios ($negocio_idx)"}

        result = await neo4j.execute_write_query(query, p)
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
   import asyncio
   
   async def testa_atualiza_cor():
      # Query específica para atualizar o nome de uma cor
      query = """MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {codigo: $codigo})
               WHERE c.excluido <> 1
               SET c.nome = $novo_nome
               RETURN c.nome, c.codigo, c.hexadecimal"""
      params = '{"negocio_idx": "5544332211", "codigo": "COR001", "novo_nome": "Vermelho Intenso"}'
      result = await atualiza_cor(query, params)
      print(result)

   asyncio.run(testa_atualiza_cor())
   #Execucao
   #python -m api.agent.assistenciamk.functions.atualiza_cor