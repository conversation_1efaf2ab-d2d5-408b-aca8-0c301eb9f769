from http import client
from math import log
from pydantic import BaseModel, <PERSON> 
from typing import Optional, List, Literal 
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from .agent_llm import LLM
from .agent_customer import AgentCustomer
from ..cache import cache
from ..functions.util import generate_unique_id
from ..functions.util import cep_data
from .agent_openai import OpenAi
from agents import function_tool
from .agent_neo4j import AgentNeo4j
from ..functions.validation.cpf_cnpj import cpf_cnpj_valido
import requests
import json
import re
from datetime import datetime
import pytz
from .agent_logger import AgentLogger
from .agent_message import Message
from .agent_ttsEdge import AgentTTSEdge
from .agent_whisper import AgentWhisper
from threading import Lock
import tempfile
import os
import base64
from .agent_product import Product
from .agent_workOrder import AgentWorkOrder

logger = AgentLogger()
modo_resposta_ia = "texto"
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
agentProduct = Product()
agentCustomer = AgentCustomer()

router = APIRouter()
oai = OpenAi()
neo4j = AgentNeo4j()




os_acesso_template_appweb = """
    "✅ **Ordem de serviço gerada com sucesso!**

    📋 **Dados da OS:**
    - Cliente: [NOME_DO_CLIENTE]
    - Veículo: [VEICULO_CATEGORIA] [VEICULO_MARCA] [VEICULO_NOME]
    - Total: R$ [VALOR_TOTAL]

    📄 **Acesse o PDF da ordem de serviço:**

    <div style='margin: 15px 0; text-align: center;'>
        <button onclick='window.open("{PDF_LINK}", "_blank");' 
                style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer; font-size: 14px;'>
            👁️ VER PDF
        </button>
        <button onclick='navigator.clipboard.writeText("{PDF_LINK}"); alert("Link copiado para a área de transferência!");' 
                style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;'>
            📋 COPIAR LINK
        </button>
    </div>"

"""

os_acesso_template_zap = """

    "✅ **Ordem de serviço gerada com sucesso!**

    📋 **Dados da OS:**
    - Cliente: [NOME_DO_CLIENTE]
    - Veículo: [VEICULO_CATEGORIA] [VEICULO_MARCA] [VEICULO_NOME]
    - Total: R$ [VALOR_TOTAL]

    📄 **Acesse o PDF da ordem de serviço:**

    👁️ VER PDF
    {PDF_LINK}
    
"""

def limpar_tags_agente(texto: str) -> str:
    """Remove tags incompletas ou malformadas que podem aparecer nas respostas do agente"""
    if not texto:
        return texto
    
    texto = re.sub(r'^/toolcall>', '', texto.strip())
    texto = re.sub(r'^/tool_call>', '', texto.strip())
    texto = re.sub(r'^<toolcall[^>]*>', '', texto.strip())
    texto = re.sub(r'^</toolcall>', '', texto.strip())
    texto = re.sub(r'^<tool_call[^>]*>', '', texto.strip())
    texto = re.sub(r'^</tool_call>', '', texto.strip())
    texto = re.sub(r'^</?tool[^>]*>', '', texto.strip())
    texto = re.sub(r'^/function>', '', texto.strip())
    texto = re.sub(r'^<function[^>]*>', '', texto.strip())
    texto = texto.strip()
    
    return texto



def extrair_texto_puro(conteudo: str) -> str:
    """Extrai o texto puro de um conteúdo, removendo formatação HTML e Markdown"""
    texto_sem_tags = limpar_tags_agente(conteudo)
    texto_sem_html = re.sub(r'<[^>]+>', '', texto_sem_tags)
    texto_sem_markdown = re.sub(r'(\*\*|__|~~|`|\*|_)+', '', texto_sem_html)
    linhas_limpas = [linha.strip() for linha in texto_sem_markdown.splitlines() if linha.strip()]
    texto_puro_final = "\n".join(linhas_limpas)
    return texto_puro_final

def limpar_html(conteudo: str) -> str:
    """
    Remove apenas tags HTML preservando formatação Markdown para WhatsApp.
    WhatsApp suporta formatação básica em Markdown, então mantemos essa formatação.
    """
    if not conteudo:
        return conteudo

    # Primeiro, limpar tags malformadas do agente
    texto_sem_tags = limpar_tags_agente(conteudo)

    # Remover apenas tags HTML, preservando Markdown
    texto_sem_html = re.sub(r'<[^>]+>', '', texto_sem_tags)

    # Processar linhas mantendo quebras de linha
    linhas_limpas = [linha.strip() for linha in texto_sem_html.splitlines() if linha.strip()]
    texto_final = "\n".join(linhas_limpas)

    return texto_final



def limpar_emojis_para_tts(texto: str) -> str:
    """Remove emojis do texto para evitar problemas na síntese de voz"""
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002500-\U00002BEF"  # chinese char
        u"\U00002702-\U000027B0"
        u"\U00002702-\U000027B0"
        u"\U000024C2-\U0001F251"
        u"\U0001f926-\U0001f937"
        u"\U00010000-\U0010ffff"
        u"\u2640-\u2642" 
        u"\u2600-\u2B55"
        u"\u200d"
        u"\u23cf"
        u"\u23e9"
        u"\u231a"
        u"\ufe0f"  # dingbats
        u"\u3030"
                      "]+", flags=re.UNICODE)
    return emoji_pattern.sub(r'', texto)


async def text_to_speech(text: str, voice: str = "pt-BR-FranciscaNeural") -> str:
    """Converte texto para áudio usando AgentTTSEdge"""
    try:
        import base64
        
        texto_limpo = limpar_emojis_para_tts(text)
        
        tts_agent = AgentTTSEdge()
        result = await tts_agent.synthesize_to_bytes(texto_limpo, voice)
        
        if result and result.get("success") and result.get("audio_data"):
            audio_bytes = result["audio_data"]
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            logger.info(f"🎙️ TTS: Texto convertido para áudio usando voz {voice} - {len(audio_bytes)} bytes")
            return audio_base64
        
        logger.warning("🎙️ TTS: Nenhum áudio gerado pelo AgentTTSEdge")
        return ""
        
    except Exception as e:
        logger.error(f"🎙️ TTS: Erro na síntese de voz com AgentTTSEdge: {str(e)}")
        return ""


async def transcribe_audio(audio_base64: str, audio_format: str = "mp3") -> str:
    """Converte áudio em base64 para texto usando AgentWhisper"""
    try:
        format_mapping = {
            "mp3": "mp3", "wav": "wav", "webm": "webm", "m4a": "m4a",
            "flac": "flac", "ogg": "ogg", "mpeg": "mp3", "mpga": "mp3"
        }
        
        file_extension = format_mapping.get(audio_format.lower(), "mp3")
        audio_bytes = base64.b64decode(audio_base64)
        logger.info(f"Áudio decodificado: {len(audio_bytes)} bytes")
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            whisper_agent = AgentWhisper()
            result = whisper_agent.transcribe(temp_file_path)
            result_text = result.get("transcription", "") if isinstance(result, dict) else str(result)
            return result_text
            
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"Erro na transcrição com AgentWhisper: {str(e)}")
        return ""


# Modelos Pydantic
class ClienteData(BaseModel):
    IDX: str
    NOME: str
    EMAIL: str
    TELEFONE: str
    CPF_CNPJ: Optional[str] = None
    CEP: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None


class ProdutoItem(BaseModel):
    idx: str
    nome: str
    valor: float
    quantidade: int


class ServicoItem(BaseModel):
    idx: str
    nome: str
    valor: float
    quantidade: int


class Item(BaseModel):
    idx: str
    nome: str
    valor: float
    quantidade: int    


class FormaPagamentoItem(BaseModel):
    nr: int = Field(alias="nr")
    id: int
    nome: str 
    valor: float
    vencimento: str
    pago: Literal[0, 1]


class NegocioData(BaseModel):
    IDX: Optional[str] = None
    NOME: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    RESPONSAVEL: Optional[str] = None
    TELEFONE: Optional[str] = None
    EMAIL: Optional[str] = None


class AgentOficinatech:
    def __init__(
        self,
        name="oficinatech",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        negocio=None,
        plataforma_url=None):
        logger.info("===== AgentOficinatech() =====")
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.instructions = f"""




        
        Seu nome é Tech. Você é uma atendente do aplicativo oficinatech, aplicativo de gerenciamento de oficina de veiculos. Estas são as suas atribuições:
        -responder as perguntas do usuário sobre o uso do aplicativo de forma clara e objetiva.
        -responder perguntas sobre os dados da oficina, como faturamento, agentamentos feitos, etc. 
        -Realizar operações cadastrais quando for sollicitada, como cadastrar bicicletas, clientes, etc. Para isto, você deverá usar as funções especiais disponiveis (tools).
        -Sempre que receber alguma imagem, sem que esteja claro o contexto ou o que fazer com ela, ou o usuário não tenha ainda informado, pergunte a ele o que  deseja fazer com a imagem. 
        -Para atualizar os dados de um cliente, use a função cliente_atualizar().
        -Para excluir um cliente, use a função cliente_excluir(). A exclusão é lógica, marcando o cliente como excluído.
        -Para consultar produtos, use a função produto_consultar().
        -Para atualizar os dados de um produto, use a função produto_atualizar().
        -Para excluir um produto, use a função produto_excluir(). A exclusão é lógica.
        -Para consultar serviços, use a função servico_consultar().
        -Para atualizar os dados de um serviço, use a função servico_atualizar().
        -Para excluir um serviço, use a função servico_excluir(). A exclusão é lógica.
        Alguns dados adicionais que você precisará para algumas tarefas e funções:
        -Nome do usuário: {usuario_nome}
        -Nome da oficina: {negocio_nome}
        -URL da plataforma: {plataforma_url}  
        -IDX do usuário: {usuario_idx}
        -IDX do negócio: {negocio_idx}
        -Data e hora atual: {data_hora}
        -Negocio: {negocio}

        Você executa tarefas especiais de atualização de dados da oficina, usando suas funções especiais.
        -Atualizar logo: logo_atualizar()
        -Adicionar modelo de veiculo: produto_adicionar()
        -Iniciar ordem de serviço: ordem_servico_abrir() - IMPORTANTE: sempre passe o parâmetro plataforma_url = {plataforma_url}

        Não use emojis em todas as frases. Use apenas quando for necessário ou o contexto exigir.
        Consulta de dados deverão ser feitas usando a função consulta_atualiza_dados(). Nunca use em 2 frases seguidas

        Ao dar informações ao usuário, seja listas, resumos, tabelsa etc, leve em consideração que a maioria dos usuários acessa o app por celular. Então que o sdados sejam exibidos de forma responsiva e mobile-first (layou mobile como prioridade), evitando assim o uso de multiplas colunas e muitos dados na horizontal.

        Quando o usuario solicitar informaões ou você precisar de buscar dados de cliente, serviços, produtos, etc, sempre use a função consulta_atualiza_dados() ou outra função que seja adequada para o caso. Nunca responda baseado em seu proprio conhecimento ou de fontes externas. Só confie em dados obtidos através das suas funções de consulta.

        Qualquer referencia a código do cliente, na verdade se refere ao IDX no cliente no banco de dados. Então se houver uma busca ou solicitação de dados e for mencionado 'código do cliente' , o que se ser na verdade buscado, adicionado ou referenciado é o IDX do cliente.

        ORDEM DE SERVIÇO:

        STATUS (status_id):
        0 - 🟡 A Iniciar (padrão)
        1 - 🔵 Em Andamento
        2 - 🟠 Aguardando Peças
        3 - 🟣 Aguardando Cliente
        4 - 🟢 Concluída
        5 - 🔴 Cancelada
        6 - ✅ Entregue
        
        PRIORIDADE (prioridade_id):
        0 - 📄 Normal (padrão)
        1 - ⬇️ Baixa
        2 - ➡️ Média
        3 - ⬆️ Alta
        4 - 🚨 Urgente
        5 - ⚡ Crítica

        IMPORTANTE:
        Não informe ao clente que irá fazer uma consulta, nem informe como fará. Jamais mostre as querys de consulta. Apenas realize as consultas necessarias e no final informe o resultado com os dados encontrados.
        """

    def get_router(self):
        return self.router

    def get_instructions(self):
        return self.instructions

    def get_agent(self):
        return self.agent


class AgentOficinatechRealTime:
    def __init__(
        self,
        name="oficinatech_realtime",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        negocio=None,
        plataforma_url=None):
        logger.info("===== AgentOficinatechRealTime() =====")

        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx
        self.negocio_nome = negocio_nome
        self.negocio_area = negocio_area
        self.negocio = negocio
        self.plataforma_url = plataforma_url
        
        self.connection = None
        self.session = None
        self.is_connected = False
        
        self.instructions = f"""
        Seu nome é Tech. Você é uma atendente do aplicativo oficinatech, aplicativo de gerenciamento de oficina de veiculos.
        
        ⚡ MODO TEMPO REAL ATIVADO ⚡
        Você está operando em modo de voz tempo real, respondendo diretamente em áudio sem conversões de texto.
        
        Suas atribuições são:
        - Responder as perguntas do usuário sobre o uso do aplicativo de forma clara e objetiva
        - Responder perguntas sobre os dados da oficina, como faturamento, agendamentos feitos, etc
        - Realizar operações cadastrais quando solicitadas, usando as funções especiais disponíveis (tools)
        - Sempre que receber alguma imagem, pergunte ao usuário o que deseja fazer com ela
        
        Dados importantes para suas tarefas:
        - Nome do usuário: {usuario_nome}
        - Nome da oficina: {negocio_nome}
        - URL da plataforma: {plataforma_url}  
        - IDX do usuário: {usuario_idx}
        - IDX do negócio: {negocio_idx}
        - Data e hora atual: {data_hora}
        - Negocio: {negocio}

        INSTRUÇÕES ESPECÍFICAS PARA TEMPO REAL:
        - Seja conversacional e natural, como se fosse uma conversa ao vivo
        - Evite respostas muito longas - mantenha-se direto e objetivo
        - Use linguagem falada, não escrita (ex: "vou verificar isso pra você" ao invés de "verificarei")
        - Não cite códigos ou números técnicos em excesso
        - Seja empático e prestativo como um atendente real
        
        STATUS DA ORDEM DE SERVIÇO:
        0 - A Iniciar (padrão)
        1 - Em Andamento
        2 - Aguardando Peças
        3 - Aguardando Cliente
        4 - Concluída
        5 - Cancelada
        6 - Entregue
        
        PRIORIDADE:
        0 - Normal (padrão)
        1 - Baixa
        2 - Média
        3 - Alta
        4 - Urgente
        5 - Crítica

        IMPORTANTE:
        Não informe que irá fazer consultas - apenas faça e informe o resultado naturalmente.
        Quando solicitado algum procedimento, como cadastramento, alteração, exclusão, etc. não diga que irá fazer , apenas faça e informe o resultado naturalmente.

        Para fazer cadastros, utilize as seguintes funções:
        - Cadastrar cliente: cliente_adicionar()
        - Cadastrar produto: produto_adicionar()
        - Cadastrar serviço: servico_adicionar()
        - Para ATUALIZAR dados de um cliente, use a função cliente_atualizar().
        - Para EXCLUIR um cliente, use a função cliente_excluir().
        - Para CONSULTAR produtos, use a função produto_consultar().
        - Para ATUALIZAR dados de um produto, use a função produto_atualizar().
        - Para EXCLUIR um produto, use a função produto_excluir().
        - Para CONSULTAR serviços, use a função servico_consultar().
        - Para ATUALIZAR dados de um serviço, use a função servico_atualizar().
        - Para EXCLUIR um serviço, use a função servico_excluir().

        Jamais cadastre produtos usando a funçao servico_adicionar e jamais cadastre serviços usando a funçao produto_adicionar. São funções diferentes, com dados procesados de forma diferente.

    ## Esquema de dados (parcial) - GTtalk (Neo4j)

    Os dados do negócio estão armazenados num banco de dados Neo4j.  Desta forma, todas as operações de consula ou escrita será feita através de consultas Cypher. Abaixo esta o esquema de dados do banco de dados com os nós, propriedades e relacioanmentos utilizados no negócio e que deve consultar para realizar as operações.

    ### Pessoa
    - labels: [Recurso, Pessoa]
    - Propriedades: nome, email, telefone
    - Relacionamentos:
        - POSSUI_NEGOCIO (OUTGOING) → Negocio
        - CLIENTE_DE (OUTGOING) → Negocio
        - SOLICITADA_POR (OUTGOING) → OrdemServico
        - ENVIADA_POR (OUTGOING) → Interacao
        - INICIOU (OUTGOING) → Conversa

    ### Negocio
    - labels: [Negocio]
    - Propriedades: nome, razao_social, cpf_cnpj, cep, logradouro, numero, complemento, bairro, cidade, uf
    - Relacionamentos:
        - POSSUI_NEGOCIO (INCOMING) ← Pessoa
        - VENDE_PRODUTO (OUTGOING) → Produto
        - PRESTA_SERVICO (OUTGOING) → Servico
        - EMITIDA_POR (OUTGOING) → OrdemServico
        - CLIENTE_DE (INCOMING) ← Pessoa

    ### Produto
    - labels: [Negocio, Produto]
    - Propriedades: nome, preco, descr
    - Relacionamentos:
        - VENDE_PRODUTO (INCOMING) ← Negocio
        - UTILIZA_PRODUTO (INCOMING) ← OrdemServico
        - SUBPRODUTO_DE (OUTGOING) → Produto

    ### Servico
    - labels: [Negocio, Servico]
    - Propriedades: nome, preco, descr
    - Relacionamentos:
        - PRESTA_SERVICO (INCOMING) ← Negocio
        - INCLUI_SERVICO (INCOMING) ← OrdemServico

    ### OrdemServico
    - labels: [Comercio, OrdemServico]
    - Propriedades: numero, qtde_parcelas_pagamento, total_servicos, total_produtos, total, entrada, saida, item_nome, item_categoria, item_marca, item_placa, item_ano, item_cor, item_serie, item_km
    - Relacionamentos:
        - UTILIZA_PRODUTO (OUTGOING) → Produto
        - INCLUI_SERVICO (OUTGOING) → Servico
        - SOLICITADA_POR (INCOMING) ← Pessoa
        - EMITIDA_POR (INCOMING) ← Negocio
        - TEM_PAGAMENTO (OUTGOING) → Pagamento

    ### Pagamento
    - labels: [Comercio, Pagamento]
    - Propriedades: vencimento, parcela
    - Relacionamentos:
        - TEM_PAGAMENTO (INCOMING) ← OrdemServico
        - TEM_STATUS (OUTGOING) → Status





        """

    def get_instructions(self):
        return self.instructions



    async def initialize_connection(self):
        logger.info("🔄 Inicializando conexão WebSocket para tempo real...")
        pass

    async def close_connection(self):
        logger.info("🔌 Fechando conexão WebSocket...")
        pass


def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{conversa_idx}"
    

def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


def limitar_tamanho_mensagem(mensagem: str, imagem: str = None, max_tokens: int = 60000) -> str:
    """Limita o tamanho total da mensagem para evitar exceder o limite de tokens"""
    max_chars = max_tokens * 4
    
    if not imagem:
        return mensagem[:max_chars] if len(mensagem) > max_chars else mensagem
    
    max_msg_chars = int(max_chars * 0.2)
    max_img_chars = int(max_chars * 0.8)
    
    if len(mensagem) > max_msg_chars:
        mensagem = mensagem[:max_msg_chars] + "..."
    
    if len(imagem) > max_img_chars:
        imagem = imagem[:max_img_chars] + "..."
    
    return f"Imagem em base64 (truncada se necessário):\n{imagem}\n\nMensagem do usuário:\n{mensagem}"


async def detectar_intencao_usuario(mensagem: str):
    return ""


async def process_with_agent(
    mensagem: str,
    negocio_idx: str,
    modelo: str,
    usuario_funcao: int,
    usuario_nome: str = "Carlos",
    usuario_idx: str = "",
    plataforma_url: str = "",
    imagem: str = "",
    is_audio_response: bool = False,
    negocio: dict = None,
):
    """Função comum para processar mensagens com o agente OficinatTech"""
    
    conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "oficinatech")
    
    if not conversa_idx:
        conversa_idx = generate_unique_id()
        historico_mensagens = []
    
    if imagem:
        mensagem = "imagem_link :" + imagem + ";mensagem:" + mensagem
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)

    if usuario_funcao == 0:
        usuario_funcao = "administrador"
    else:
        usuario_funcao = "funcionario"
    
    agentOfTech = AgentOficinatech(
        negocio_idx=negocio_idx,
        usuario_nome=usuario_nome,
        plataforma_url=plataforma_url,
        usuario_idx=usuario_idx,
        negocio=negocio
    )
    
    llm = LLM()
    model = llm.get_model_idx(modelo)
    # Linha 246-246
    # Linha 246-247
    logger.info(f"Modelo carregado: {model} (modelo original: {modelo})")
    
    instructions = agentOfTech.get_instructions()
    
    intencao = await detectar_intencao_usuario(mensagem)
    
    if is_audio_response:
        logger.info("Resposta para áudio detectada - forçando formato de texto simples")
        instructions += """
        
        INSTRUÇÃO ESPECIAL PARA RESPOSTA DE ÁUDIO:
        Esta resposta será convertida para áudio (text-to-speech), portanto:
        - NUNCA use JSON estruturado ou HTML
        - SEMPRE responda em texto simples e natural
        - Converta valores em texto por extenso e informe no final a moeda utilizda. Exemplo: R$ 100,00 = cem reais. 
        - Para produtos, descreva-os de forma conversacional
        - Use frases completas e bem estruturadas
        - Evite citar códigos, símbolos especiais ou formatação complexa
        - Seja claro e direto na comunicação
        """
    
    output_type = None

    tools_list = [
        cliente_consultar,
        cliente_excluir,
        cliente_atualizar,
        produto_consultar,
        produto_atualizar,
        produto_excluir,
        servico_consultar,
        servico_atualizar,
        servico_excluir,
        cliente_adicionar,
        produto_adicionar,
        servico_adicionar,
        ordem_servico_abrir,
        ordem_servico_consultar,
        ordem_servico_atualizar,
        ordem_servico_excluir,
        ordem_servico_resposta_template,        
    ]
    
    agenteTech = {
        "name": "MCP+",
        "instructions": instructions,
        "model": model,
        "tools": tools_list,
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": [],
    }
    
    agenteTech_obj = await oai.agent_create(**agenteTech)
    
    return agenteTech_obj, historico_mensagens, conversa_idx, intencao


async def process_agent_stream(
    agenteTech_obj,
    historico_mensagens: list,
    negocio_idx: str,
    conversa_idx: str
):
    """Função comum para processar o streaming do agente OficinatTech"""
    global messageChat
    
    logger.info("=== INICIANDO PROCESS_AGENT_STREAM OFICINATECH ===")
    resposta_completa = ""
    chunk_count = 0
    
    try:
        async for response in oai.agent_run(agenteTech_obj, historico_mensagens):
            chunk_count += 1
            
            if isinstance(response, bytes):
                chunk_str = response.decode('utf-8')
            else:
                chunk_str = str(response)
            
            resposta_completa += chunk_str
            
            if chunk_count == 1 and ('/tool_call>' in chunk_str or '/toolcall>' in chunk_str or '<tool' in chunk_str):
                chunk_limpo = limpar_tags_agente(chunk_str)
                yield chunk_limpo
            else:
                yield chunk_str
            
        resposta_limpa = limpar_tags_agente(resposta_completa)
        texto_para_historico = extrair_texto_puro(resposta_limpa)
        historico_mensagens = add_message_to_history(historico_mensagens, texto_para_historico, False)

        
        cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
        cache[cache_key] = historico_mensagens
        
        with messageChat_lock:
            
            messageChat["SAIDA"] = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
            messageChat["RECEBIDO"] = resposta_limpa
            messageChat["RECEBIDO_TKS"] = len(texto_para_historico.split())
            messageChat["TOTAL_TKS"] = messageChat.get("ENVIADO_TKS", 0) + messageChat["RECEBIDO_TKS"]
            
            
            messageId = await agentMessage.add(messageChat.copy())
            
            if messageId:
                pass
            else:
                logger.error("❌ ERRO: Mensagem não foi salva no banco de dados!")
                logger.error(f"Dados enviados: {messageChat}")

    except Exception as stream_error:
        logger.error(f"Erro durante o streaming: {str(stream_error)}")
        if resposta_completa:
            texto_puro = extrair_texto_puro(resposta_completa)
            historico_mensagens = add_message_to_history(historico_mensagens, texto_puro, False)
            cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
            cache[cache_key] = historico_mensagens
        raise stream_error


# FUNÇÕES DE FERRAMENTAS (TOOLS) MIGRADAS PARA NEO4J



@function_tool
async def cliente_adicionar(
    nome: str,
    telefone: str = "",
    email: str = "",
    cpf_cnpj: str = "",
    cep: str = "",
    logradouro: str = "",
    bairro: str = "",
    cidade: str = "",
    uf: str = "",
    numero: str = "",
    complemento: str = "",
    negocio_idx: str = ""
 ):
    """
    Adiciona um novo cliente no banco de dados Neo4j.
    
    Args:
        nome: Nome do cliente (obrigatório)
        telefone: Telefone do cliente
        email: Email do cliente
        cpf_cnpj: CPF ou CNPJ do cliente
        cep: CEP do cliente
        logradouro: Endereço do cliente
        bairro: Bairro do cliente
        cidade: Cidade do cliente
        uf: Estado do cliente
        numero: Número do endereço
        complemento: Complemento do endereço
        negocio_idx: IDX do negócio (obrigatório)
    """
    logger.info(f"🆕 Adicionando cliente: {nome}")
    
    try:
        # Gerar IDX único para o cliente
        cliente_idx = generate_unique_id()
        
        # Validar CPF/CNPJ se fornecido
        if cpf_cnpj and not cpf_cnpj_valido(cpf_cnpj):
            return {"erro": "CPF/CNPJ inválido"}
        
        # Buscar dados do CEP se fornecido
        if cep:
            dados_cep = await cep_data(cep)
            if dados_cep and dados_cep.get("success"):
                logradouro = logradouro or dados_cep.get("logradouro", "")
                bairro = bairro or dados_cep.get("bairro", "")
                cidade = cidade or dados_cep.get("cidade", "")
                uf = uf or dados_cep.get("uf", "")
        
        # Query para criar o cliente e relacionamento
        query = """
        MATCH (n:Negocio {idx: $negocio_idx})
        CREATE (c:Pessoa {
            idx: $cliente_idx,
            nome: $nome,
            telefone: $telefone,
            email: $email,
            cpfCnpj: $cpf_cnpj,
            cep: $cep,
            logradouro: $logradouro,
            bairro: $bairro,
            cidade: $cidade,
            uf: $uf,
            numero: $numero,
            complemento: $complemento,
            excluido: 0,
            dataCadastro: datetime()
        })
        CREATE (c)-[:CLIENTE_DE]->(n)
        RETURN c.idx as cliente_idx, c.nome as nome
        """
        
        params = {
            "negocio_idx": negocio_idx,
            "cliente_idx": cliente_idx,
            "nome": nome,
            "telefone": telefone,
            "email": email,
            "cpf_cnpj": cpf_cnpj,
            "cep": cep,
            "logradouro": logradouro,
            "bairro": bairro,
            "cidade": cidade,
            "uf": uf,
            "numero": numero,
            "complemento": complemento
        }
        
        result = await neo4j.execute_write_query(query, params)
        
        if result:
            logger.info(f"✅ Cliente {nome} adicionado com sucesso - IDX: {cliente_idx}")
            return {
                "sucesso": True,
                "cliente_idx": cliente_idx,
                "nome": nome,
                "mensagem": f"Cliente {nome} cadastrado com sucesso!"
            }
        else:
            return {"erro": "Falha ao adicionar cliente"}
            
    except Exception as e:
        logger.error(f"❌ Erro ao adicionar cliente: {str(e)}")
        return {"erro": f"Erro ao adicionar cliente: {str(e)}"}


@function_tool
async def servico_adicionar(
    nome: str,
    preco: float,
    codigo: str = "",
    descricao: str = "",
    negocio_idx: str = ""
):
    """
    Adiciona um novo servico da oficina no banco de dados Neo4j.
    
    Args:
        nome: Nome do serviço (obrigatório)
        preco: Preço do serviço (obrigatório)
        codigo: Código do serviço
        descricao: Descrição do serviço
        negocio_idx: IDX do negócio (obrigatório)
    """
    logger.info("==========servico_adicionar()==========")
    logger.info(f"🆕 Adicionando serviço: {nome}")
    
    try:
        # Gerar IDX único para o serviço
        idx = generate_unique_id()
        
        # Query para criar o produto e relacionamento
        query = """
        MATCH (n:Negocio {idx: $negocio_idx})
        CREATE (p:Servico {
            idx: $idx,
            nome: $nome,
            codigo: $codigo,
            preco: $preco,
            descricao: $descricao,
            excluido: 0,
            negocioIdx: $negocio_idx
        })
        CREATE (n)-[:PRESTA_SERVICO]->(p)
        RETURN p.idx as idx, p.nome as nome
        """
        
        params = {
            "negocio_idx": negocio_idx,
            "idx": idx,
            "nome": nome,
            "codigo": codigo,
            "preco": preco,
            "descricao": descricao
        }
        
        result = await neo4j.execute_write_query(query, params)
        logger.info(f"result: {result}")
        
        if result:
            logger.info(f"✅ Serviço {nome} adicionado com sucesso")
            return {
                "sucesso": True,
                "idx": idx,
                "nome": nome,
                "preco": preco,
                "mensagem": f"Serviço {nome} cadastrado com sucesso!"
            }
        else:
            return {"erro": "Falha ao adicionar serviço"}
            
    except Exception as e:
        logger.error(f"❌ Erro ao adicionar serviço: {str(e)}")
        return {"erro": f"Erro ao adicionar serviço: {str(e)}"}



@function_tool
async def produto_adicionar(
    nome: str,
    preco: float,
    codigo: str = "",
    estoque: int = 0,
    descricao: str = "",
    negocio_idx: str = ""
):
    """
    Adiciona um novo produto no banco de dados Neo4j.
    
    Args:
        nome: Nome do produto (obrigatório)
        preco: Preço do produto (obrigatório)
        codigo: Código do produto
        estoque: Quantidade em estoque
        descricao: Descrição do produto
        negocio_idx: IDX do negócio (obrigatório)
    """
    logger.info(f"🆕 Adicionando produto: {nome}")
    
    try:
        # Gerar IDX único para o produto
        idx = generate_unique_id()
        
        # Query para criar o produto e relacionamento
        query = """
        MATCH (n:Negocio {idx: $negocio_idx})
        CREATE (p:Produto {
            idx: $idx,
            nome: $nome,
            codigo: $codigo,
            preco: $preco,
            estoque: $estoque,
            excluido: 0,
            negocioIdx: $negocio_idx
        })
        CREATE (n)-[:VENDE_PRODUTO]->(p)
        RETURN p.idx as produto_idx, p.nome as nome
        """
        
        params = {
            "negocio_idx": negocio_idx,
            "idx": idx,
            "nome": nome,
            "codigo": codigo,
            "preco": preco,
            "estoque": estoque,
            "descricao": descricao
        }
        
        result = await neo4j.execute_write_query(query, params)
        logger.info(f"result: {result}")
        
        if result:
            logger.info(f"✅ Produto {nome} adicionado com sucesso")
            return {
                "sucesso": True,
                "nome": nome,
                "preco": preco,
                "mensagem": f"Produto {nome} cadastrado com sucesso!"
            }
        else:
            return {"erro": "Falha ao adicionar produto"}
            
    except Exception as e:
        logger.error(f"❌ Erro ao adicionar produto: {str(e)}")
        return {"erro": f"Erro ao adicionar produto: {str(e)}"}


@function_tool
async def ordem_servico_abrir(
    #cliente_idx: str,
    cliente: ClienteData,  # Alterado de dict
    negocio: NegocioData,
    formas_pagamento: List[FormaPagamentoItem]=[], 
    item_categoria: str = "",
    item_nome: str = "",
    item_marca: str = "",
    item_serie: str = "",
    item_km: str = "",
    item_ano: str = "",
    item_cor: str = "",
    item_placa: str = "",
    observacao: str = "",
    prioridade: int = 0,
    negocio_idx: str = "",
    plataforma_url: str = "",
    produtos: List[ProdutoItem] = [],
    servicos: List[ServicoItem] = [],
    
):
    """
    Não solicite todas as informações de uma vez. Solicite uma por vez.
    Abre uma nova ordem de serviço;
    
    CLIENTE:
    -Objeto ClienteData com os dados completos do cliente
    -CAMPOS OBRIGATÓRIOS DO CLIENTE:
        • NOME: string - Nome completo do cliente (obrigatório)
        • EMAIL: string - Email do cliente (obrigatório)
        • TELEFONE: string - Telefone do cliente (obrigatório)
    -CAMPOS OPCIONAIS DO CLIENTE:
        • CPF_CNPJ, CEP, LOGRADOURO, BAIRRO, CIDADE, UF, NUMERO, COMPLEMENTO
    -Obrigatório
    -Solicite que o usuário informe um destes dados: nome do cliente, cpf, cnpj, email ou telefone. Use a informação fornecida para localizar o cliente, e em seguida passe-o para a função com TODOS os campos obrigatórios preenchidos. Caso o cliente não seja encontrado, informe ao usuario que o cliente não foi encontrado e solicite que informe outra informação do cliente para uma nova busca.
    
    VEICULO_CATEGORIA:
    -categoria do veiculo. 
    -Exemplos: Bicicleta, Moto, Carro, Caminhão, etc.
    -default = "Bicicleta".
    -Opcional
    VEICULO_NOME:
    -nome ou modelo do  veiculo.
    -Obrigatório
    -Exemplo: Sense21, Gol, etc.
    VEICULO_MARCA:
    -marca do veiculo.
    -default = "".
    -Opcional
    -Exemplo: Sense, Honda, Yamaha, Shimano, etc.
    VEICULO_ANO:
    -ano do veiculo.
    -Opcional
    VEICULO_SERIE:
    -serie do veiculo.
    -default = "".
    -Opcional
    VEICULO_COR:
    -cor do veiculo.
    -default = "".
    -Opcional
    -Exemplo: Vermelho, Preto, etc.
    VEICULO_PLACA:
    -placa do veiculo. Solicitar somente se o veiculo for um veiculo.
    -default = "".
    -Opcional
    -Exemplo: ABC1234
    VEICULO_KM:
    -quilometragem do veiculo.
    -default = "".
    -Opcional

    
    SERVICOS:
    -Array de dicionarios com os serviços a serem feitos. cada serviço tera os seguintes dados:
    -idx - identificador unico do serviço
    -nome - nome completo
    -valor - valor do serviço
    -quantidade - quantidade desejada
    -Exemplo:
    [
        {"idx": "1234567890", nome: "revisão geral","valor": 100, "quantidade": 1},
    ]
    -Opcional (pode não ter serviços)
    -Solicite o nome ou código do serviço e faça uma busca na tabela SERVICO para encontrar o ID do serviço. Caso não encontre, informe ao usuario que o serviço não foi encontrado e solicite que informe o nome ou código do serviço ou pergunte se ele deseja ver os serviços disponíveis. Caso ele queira, carregue e liste os serviços do negócio dele.
    Após enconrar o serviço, solicite ao usuário a quantidade, caso ele ainda não tenha informado. 
    Encontrado o serviço, adicine a lista de serviços com o id, valor e quantidade.
    Pergunte se ele deseja adicionar mais serviços.
    IMPORTANTE: Ao fazer buscas por serviços, verifque  se o texto informado é encontrado total ou parcialmente na coluna NOME ou CODIGO do serviço. Em geral os serviços também possuem códigos, e o usuario pode optar em informar o codigo em vez do nome.

    PRODUTOS:
    -Array de dicionarios com os produtos a serem utilizados. cada produto tera os seguintes dados:
    -idx - indentificador unico do produto
    -nome - nome completo
    -valor - preço do serviço
    -quantidade - quantidade desejada pelo cliente
    -Exemplo:
    [
        {"idx": "9835421345", nome: "Pneu aro 26","valor": 80, "quantidade": 2},
    ]
    -Opcional (pode não ter produtos)
    -Solicite o nome ou código do produto e faça uma busca na tabela PRODUTO para encontrar o ID do produto. Caso não encontre, informe ao usuario que o produto não foi encontrado e solicite que informe o nome ou código do produto ou pergunte se ele deseja ver os produtos disponíveis. Caso ele queira, carregue e liste os produtos do negócio dele.
    Após enconrar o produto, solicite ao usuário a quantidade, caso ele ainda não tenha informado. 
    Encontrado o produto, adicine a lista de produtos com o id, valor e quantidade.
    Pergunte se ele deseja adicionar mais produtos.
    IMPORTANTE: Ao fazer buscas por produtos, verifque  se o texto informado é encontrado total ou parcialmente na coluna NOME ou CODIGO do produto. Em geral os produtos também possuem códigos, e o usuario pode optar em informar o codigo em vez do nome.

    TOTAL:
    -Total da ordem de serviço.
    -Obrigatório
    -Exemplo: 100, 200, 300
    -Neste momento calcule o total a ser pago pelos serviços (soma de (valor * quantidade) de todos os serviços) + produtos (soma de (valor * quantidade) de todos os produtos) e informe ao usuario.  


    QTDE_PARCELAS_PAGAMENTO:
    -Quantidade de parcelas de pagamento.
    -inteiro(default 1)
    -Obrigatório
    -Exemplo: 1, 2, 3, etc.
    -Total a ser pago pelos serviços + produtos (soma de ((valor * quantidade) de todos os serviços) + ((valor * quantidade) de todos os produtos)).  
    

    FORMAS_PAGAMENTO:
    -Array de dicionarios com as formas de pagamento. Cada forma de pagamento tera os seguintes dados:
    -nr .  Número sequencial da parcela. O número máximo sera o total de parcelas.
    -id
    -valor 
    -vencimento . Data no formato YYYY-MM-DD . Mas caso for necessário exibir ao usuario, exiba no formato DD/MM/YYYY.
    pago: e se esta pago ou não. Caso esteja pago, o valor será 1, caso não esteja pago, o valor será 0.
    -Exemplo: 
    {[n        {{"nr": 1, "id": 1, "valor": 100, "vencimento": "2024-01-01", "pago": 1}},
        {{"nr": 2, "id": 2, "valor": 100, "vencimento": "2024-01-01", "pago": 0}}
    ]}

            Estes são os ids das formas de pagamento:
            1 - Pix
            2 - Cartão de crédito
            3 - Cartão de débito
            4 - Dinheiro
            5 - Cheque
            6 - Transferência
            7 - Boleto

    OBSERVACAO:
    -Observação da ordem de serviço.
    -default = ""
    -Opcional

    STATUS_ID:
    -ID do status da ordem de serviço.
    -Inteiro (default = 0)
    -Opcional
    -Deve ser solicitado APÓS as formas de pagamento
    -Apresentar as opções ao usuário:
        0 - 🟡 A Iniciar (padrão)
        1 - 🔵 Em Andamento  
        2 - 🟠 Aguardando Peças
        3 - 🟣 Aguardando Cliente
        4 - 🟢 Concluída
        5 - 🔴 Cancelada
        6 - ✅ Entregue
    -Usuário pode informar o número (ID) ou a descrição
    -Exemplo: "1" ou "Em Andamento"

    PRIORIDADE_ID:
    -ID da prioridade da ordem de serviço.
    -Inteiro (default = 0)
    -Opcional
    -Deve ser solicitado APÓS as formas de pagamento
    -Apresentar as opções ao usuário:
        0 - 📄 Normal (padrão)
        1 - ⬇️ Baixa
        2 - ➡️ Média
        3 - ⬆️ Alta
        4 - 🚨 Urgente
        5 - ⚡ Crítica
    -Usuário pode informar o número (ID) ou a descrição
    -Exemplo: "3" ou "Alta"

    PLATAFORMA_URL:
    -URL da plataforma para geração do link do PDF
    -Obrigatório
    -Exemplo: http://localhost/gptalk ou https://app.oficinatech.gptalk.com.br
            

    OBSERVAÇÕES IMPORTANTES:
    1-Após o usuário identificar a categoria do veiculo, passe a se referir ao veiculo pela sua categoria.
    Por exemplo, caso ele informe que a categoria é bicicleta, passe a se referir ao veiculo como bicicleta. Exemplos: qual a marca da bicileta? qual a cor da bicileta?
    2-Se a categoria for bicicleta, não solicitar placa nem kilometragem
    3-A ordem de serviço pode ter apenas serviços, apenas produtos, ou ambos. Sempre pergunte se o usuário deseja adicionar serviços e produtos.
    4-APÓS confirmar as formas de pagamento, solicite o STATUS e a PRIORIDADE da ordem de serviço, apresentando as opções numeradas para o usuário escolher.

    TEMPLATE DE RESPOSTA FINAL:
    Após gerar a ordem de serviço com sucesso, você DEVE usar EXATAMENTE este template de resposta:

    "✅ **Ordem de serviço gerada com sucesso!**

    📋 **Dados da OS:**
    - Cliente: [NOME_DO_CLIENTE]
    - Veículo: [VEICULO_CATEGORIA] [VEICULO_MARCA] [VEICULO_NOME]
    - Total: R$ [VALOR_TOTAL]

    📄 **Acesse o PDF da ordem de serviço:**

    <div style='margin: 15px 0; text-align: center;'>
        <button onclick='window.open("{PDF_LINK}", "_blank");' 
                style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer; font-size: 14px;'>
            👁️ VER PDF
        </button>
        <button onclick='navigator.clipboard.writeText("{PDF_LINK}"); alert("Link copiado para a área de transferência!");' 
                style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;'>
            📋 COPIAR LINK
        </button>
    </div>"

    IMPORTANTE: Substitua {PDF_LINK} pelo link real gerado pela função.


    """
    
            # Esta função serve como uma "ferramenta" para o agente e chama a lógica principal.
    # Linha 1-8
    fuso_brasilia = pytz.timezone('America/Sao_Paulo')
    entrada= datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")

    return await core_ordem_servico_abrir(
        #cliente_idx=cliente_idx,
        cliente =cliente,
        item_categoria=item_categoria,
        item_nome=item_nome,
        item_marca=item_marca,
        item_ano=item_ano,
        item_cor=item_cor,
        item_placa=item_placa,
        item_serie=item_serie,
        item_km=item_km,
        observacao=observacao,
        prioridade=prioridade,
        negocio_idx=negocio_idx,
        plataforma_url=plataforma_url,
        formas_pagamento=formas_pagamento,
        produtos=produtos,
        servicos=servicos,
        
        negocio=negocio)
    


async def core_ordem_servico_abrir(
    cliente: ClienteData,  # Alterado de dict
    negocio: NegocioData,
    formas_pagamento: List[FormaPagamentoItem],
    item_categoria: str = "",
    item_nome: str = "",
    item_marca: str = "",
    item_serie: str = "",
    item_km: str = "",
    item_ano: str = "",
    item_cor: str = "",
    item_placa: str = "",
    observacao: str = "",
    prioridade: int = 0,
    negocio_idx: str = "",
    plataforma_url: str = "",
    produtos: List[ProdutoItem] = [],
    servicos: List[ServicoItem] = [],
    
):
    logger.info("🚀 Iniciando core_ordem_servico_abrir()")
    logger.info(f"Cliente: {cliente}")
    cliente_idx = cliente.IDX 
    logger.info(f"Cliente IDX: {cliente_idx}")
    
    # Definir entrada no início da função
    fuso_brasilia = pytz.timezone('America/Sao_Paulo')
    entrada = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
    
    # ----------------------------------------------------------
    # 1.  VALIDA / CRIA nós obrigatórios
    # ----------------------------------------------------------
    logger.info("🔍 Verificando nós obrigatórios...")

    # Mapeia tipo_idx a partir do ID da forma de pagamento
    tipo_pagamento_map = {
        1: "1234567890",  # Pix
        2: "1234567891",  # Cartão crédito
        3: "1234567892",  # Débito
        4: "1234567893",  # Dinheiro
        5: "1234567894",  # Cheque
        6: "1234567895",  # Transferência
        7: "1234567896",  # Boleto
    }

    # Coleta todos os tipos de pagamento que serão usados
    tipos_a_criar = set()
    for fp in formas_pagamento:
        if fp.id in tipo_pagamento_map:
            tipos_a_criar.add(tipo_pagamento_map[fp.id])
        else:
            logger.error(f"❌ ID de forma de pagamento desconhecido: {fp.id}")
            return {"erro": f"ID de forma de pagamento desconhecido: {fp.id}"}

    # Prioridade e Status
    prioridade_idx = "3000000001" if prioridade == 0 else \
                     "3000000002" if prioridade == 1 else \
                     "3000000003" if prioridade == 2 else \
                     "3000000004" if prioridade == 3 else \
                     "3000000005" if prioridade == 4 else \
                     "3000000006"  # crítica
    status_idx = "4000000001"

    # ----------------------------------------------------------
    # 2.  CRIA ou GARANTE existência dos nós
    # ----------------------------------------------------------
    logger.info("📦 Garantindo existência de Negocio, Cliente, Prioridade, Status...")
    await neo4j.execute_write_query(
        """
        MERGE (n:Negocio {idx: $negocio_idx})
        ON CREATE SET n.nome = "Negócio Teste"

        MERGE (c:Pessoa:Cliente {idx: $cliente_idx})
        ON CREATE SET c.nome = "Cliente Teste"

        MERGE (p:Prioridade {idx: $prioridade_idx})
        ON CREATE SET p.nome = CASE $prioridade_idx
                                  WHEN "3000000001" THEN "Normal"
                                  WHEN "3000000002" THEN "Baixa"
                                  WHEN "3000000003" THEN "Média"
                                  WHEN "3000000004" THEN "Alta"
                                  WHEN "3000000005" THEN "Urgente"
                                  WHEN "3000000006" THEN "Crítica"
                               END

        MERGE (s:Status {idx: $status_idx})
        ON CREATE SET s.nome = "Aberta"
        """,
        {
            "negocio_idx": negocio_idx,
            "cliente_idx": cliente_idx,
            "prioridade_idx": prioridade_idx,
            "status_idx": status_idx,
        },
    )

    logger.info("💳 Garantindo existência dos tipos de pagamento...")
    for tipo_idx in tipos_a_criar:
        await neo4j.execute_write_query(
            """
            MERGE (pt:PagamentoTipo {idx: $tipo_idx})
            ON CREATE SET pt.nome = CASE $tipo_idx
                                      WHEN "1234567890" THEN "Pix"
                                      WHEN "1234567891" THEN "Cartão de Crédito"
                                      WHEN "1234567892" THEN "Cartão de Débito"
                                      WHEN "1234567893" THEN "Dinheiro"
                                      WHEN "1234567894" THEN "Cheque"
                                      WHEN "1234567895" THEN "Transferência"
                                      WHEN "1234567896" THEN "Boleto"
                                    END
            """,
            {"tipo_idx": tipo_idx},
        )

    logger.info("🛒 Garantindo existência dos produtos...")
    for prod in produtos:
        await neo4j.execute_write_query(
            """
            MERGE (pr:Produto {idx: $idx})
            ON CREATE SET pr.nome = $nome
            """,
            {"idx": prod.idx, "nome": prod.nome},
        )

    logger.info("🛠️ Garantindo existência dos serviços...")
    for serv in servicos:
        await neo4j.execute_write_query(
            """
            MERGE (s:Servico {idx: $idx})
            ON CREATE SET s.nome = $nome
            """,
            {"idx": serv.idx, "nome": serv.nome},
        )

    # ----------------------------------------------------------
    # 3.  PREPARA dados de pagamento
    # ----------------------------------------------------------
    pagamentos_param = []
    for fp in formas_pagamento:
        pag_dict = fp.model_dump()
        pag_dict["idx"] = generate_unique_id()
        pag_dict["tipo_idx"] = tipo_pagamento_map[fp.id]
        pagamentos_param.append(pag_dict)

    # ----------------------------------------------------------
    # 4.  CALCULA totais
    # ----------------------------------------------------------
    produtos_param = [p.model_dump() for p in produtos]
    servicos_param = [s.model_dump() for s in servicos]
    total_produtos = sum(p.valor * p.quantidade for p in produtos)
    total_servicos = sum(s.valor * s.quantidade for s in servicos)
    total_os = total_produtos + total_servicos

    if total_os != sum(fp.valor for fp in formas_pagamento):
        logger.warning(
            f"⚠️ Total OS ({total_os}) ≠ soma pagamentos ({sum(fp.valor for fp in formas_pagamento)})"
        )

    # ----------------------------------------------------------
    # 5.  GERA número e idx da OS
    # ----------------------------------------------------------
    proximo_numero = await gera_ultimo_numero_os(negocio_idx)
    if proximo_numero is None:
        return {"erro": "Falha ao gerar o número da ordem de serviço."}

    os_idx = generate_unique_id()
    logger.info(f"🆕 Nova OS: idx={os_idx}, número={proximo_numero}")

    # ----------------------------------------------------------
    # 6.  CRIA a OS com todos os relacionamentos
    # ----------------------------------------------------------
    query_produtos = ""
    if produtos:
        query_produtos = """
        WITH os
        UNWIND $produtos AS produto_data
        MERGE (prod:Produto {idx: produto_data.idx})
        CREATE (os)-[:UTILIZA_PRODUTO {
            quantidade: toInteger(produto_data.quantidade),
            preco: toFloat(produto_data.valor)
        }]->(prod)
        """

    query_servicos = ""
    if servicos:
        query_servicos = """
        WITH os
        UNWIND $servicos AS servico_data
        MERGE (serv:Servico {idx: servico_data.idx})
        CREATE (os)-[:INCLUI_SERVICO {
            quantidade: toInteger(servico_data.quantidade),
            preco: toFloat(servico_data.valor)
        }]->(serv)
        """

    query = f"""
    // Entidades já existem (garantidos acima)
    MATCH (n:Negocio {{idx: $negocio_idx}})
    MATCH (c:Pessoa {{idx: $cliente_idx}})
    MATCH (p:Prioridade {{idx: $prioridade_idx}})
    MATCH (s:Status {{idx: $status_idx}})

    CREATE (os:OrdemServico:Comercio {{
        idx: $os_idx,
        numero: $numero,
        item_categoria: $item_categoria,
        item_nome: $item_nome,
        item_marca: $item_marca,
        item_ano: $item_ano,
        item_cor: $item_cor,
        item_placa: $item_placa,
        observacao: $observacao,
        total: $total_os,
        total_servicos: $total_servicos,
        total_produtos: $total_produtos,
        qtde_parcelas_pagamento: size($pagamentos),
        entrada: datetime(),
        excluido: 0
    }})

    CREATE (os)-[:EMITIDA_POR]->(n),
           (os)-[:SOLICITADA_POR]->(c),
           (os)-[:TEM_PRIORIDADE {{entrada: datetime(), ativo: 1}}]->(p),
           (os)-[:TEM_STATUS {{entrada: datetime(), ativo: 1}}]->(s)

    // Pagamentos
    WITH os
    UNWIND $pagamentos AS pagamento_data
    MATCH (pt:PagamentoTipo {{idx: pagamento_data.tipo_idx}})
    CREATE (pag:Pagamento:Comercio {{
        idx: pagamento_data.idx,
        valor: toFloat(pagamento_data.valor),
        vencimento: date(pagamento_data.vencimento),
        parcela: toInteger(pagamento_data.nr),
        pago: toInteger(pagamento_data.pago),
        excluido: 0
    }})
    CREATE (os)-[:TEM_PAGAMENTO]->(pag)
    CREATE (pag)-[:TEM_TIPO]->(pt)

    {query_produtos}

    {query_servicos}

    // Retorno
    WITH DISTINCT os
    MATCH (c:Pessoa)-[:SOLICITADA_POR]->(os)
    RETURN os, c.nome AS cliente_nome
    """

    fuso_brasilia = pytz.timezone('America/Sao_Paulo')
    entrada= datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")

    ordem_servico = {
        "IDX": os_idx,
        "NUMERO": proximo_numero,
        "ITEM_CATEGORIA": item_categoria,
        "ITEM_NOME": item_nome,
        "ITEM_MARCA": item_marca,
        "ITEM_SERIE": item_serie,
        "ITEM_KM": item_km,
        "ITEM_ANO": item_ano,
        "ITEM_COR": item_cor,
        "ITEM_PLACA": item_placa,
        "OBSERVACAO": observacao,
        "PRIORIDADE_IDX": prioridade_idx,
        "STATUS_IDX": status_idx,
        "TOTAL_OS": total_os,
        "TOTAL_PRODUTOS": total_produtos,
        "TOTAL_SERVIÇOS": total_servicos,
        "ENTRADA": entrada,
    }

    params = {
        "negocio_idx": negocio_idx,
        "cliente_idx": cliente_idx,
        "prioridade_idx": prioridade_idx,
        "status_idx": status_idx,
        "os_idx": os_idx,
        "numero": proximo_numero,
        "item_categoria": item_categoria,
        "item_nome": item_nome,
        "item_marca": item_marca,
        "item_ano": item_ano,
        "item_cor": item_cor,
        "item_placa": item_placa,
        "observacao": observacao,
        "total_os": total_os,
        "total_servicos": total_servicos,
        "total_produtos": total_produtos,
        "pagamentos": pagamentos_param,  # ou ajuste conforme o nome correto no seu código
        "produtos": produtos_param,
        "servicos": servicos_param,
    }

    logger.info(f"@@@@@ Executando query para criar OS: {query}")
    logger.info(f"@@@@@ Parâmetros da query: {json.dumps(params, indent=2)}")
    logger.info(f"@@@@@ servicos_param enviado ao Neo4j: {json.dumps(servicos_param, indent=2)}")


    try:
        write_result = await neo4j.execute_write_query(query, params)

        # ---------- LIMPEZA DE RELACIONAMENTOS DUPLICADOS ----------
        if servicos:
            logger.info("🧹 Removendo relacionamentos INCLUI_SERVICO duplicados...")
            await neo4j.execute_write_query(
                """
                MATCH (os:OrdemServico {idx: $os_idx})-[r:INCLUI_SERVICO]->(s:Servico)
                WITH os, s, COLLECT(r) AS rels
                WHERE SIZE(rels) > 1
                WITH os, s, rels[1..] AS duplicatas
                UNWIND duplicatas AS r
                DELETE r
                """,
                {"os_idx": os_idx}
            )
            logger.info("✅ Relacionamentos INCLUI_SERVICO duplicados removidos.")

        if produtos:
            logger.info("🧹 Removendo relacionamentos UTILIZA_PRODUTO duplicados...")
            await neo4j.execute_write_query(
                """
                MATCH (os:OrdemServico {idx: $os_idx})-[r:UTILIZA_PRODUTO]->(p:Produto)
                WITH os, p, COLLECT(r) AS rels
                WHERE SIZE(rels) > 1
                WITH os, p, rels[1..] AS duplicatas
                UNWIND duplicatas AS r
                DELETE r
                """,
                {"os_idx": os_idx}
            )
            logger.info("✅ Relacionamentos UTILIZA_PRODUTO duplicados removidos.")

        logger.info(f"Resultado final da escrita no Neo4j: {write_result}")
    except Exception as e:
        logger.error(f"Erro ao executar escrita no Neo4j: {str(e)}", exc_info=True)
        return {"erro": f"Falha ao gravar OS no banco: {str(e)}"}

    if not write_result:
        logger.error("❌ A criação da OS não retornou nenhum nó.")
        return {"erro": "Falha ao criar a OS – nenhum nó retornado."}


    #os_node = write_result[0].get("os")
    cliente_nome = cliente.NOME

    url_os = f"{plataforma_url}/ordem-servico/{os_idx}" if plataforma_url else ""

    logger.info(f"✅ OS #proximo_numero#{proximo_numero} criada.")

    result = await gera_ordem_servico_pdf(ordem_servico=ordem_servico,cliente=cliente,negocio=negocio, servicos=servicos_param, produtos=produtos_param,formas_pagamento=pagamentos_param, plataforma_url=plataforma_url)
        
        
    return {
        "sucesso": True,
        "os_idx": os_idx,
        "osnumero": proximo_numero,
        "cliente_nome": cliente_nome or "Nome do cliente não encontrado",
        "pdf_link":   f"{plataforma_url}/negocios/{negocio_idx}/pdf/{result['pdf_nome']}",
        "mensagem": f"Ordem de serviço #{proximo_numero} criada com sucesso !",
    }


@function_tool
async def ordem_servico_atualizar(
    os_idx: str,
    negocio_idx: str,
    status_id: Optional[int] = None,
    prioridade_id: Optional[int] = None
):
    """
    Atualiza o status e/ou a prioridade de uma Ordem de Serviço existente.
    Esta função mantém um histórico das alterações, desativando o relacionamento antigo
    e criando um novo com as datas de entrada e saída.

    Args:
        os_idx (str): O IDX da Ordem de Serviço a ser atualizada. É obrigatório.
        negocio_idx (str): O IDX do negócio para verificação de permissão. É obrigatório.
        status_id (Optional[int]): O novo ID do status para a OS.
            - 0: A Iniciar, 1: Em Andamento, 2: Aguardando Peças, 3: Aguardando Cliente,
            - 4: Concluída, 5: Cancelada, 6: Entregue
        prioridade_id (Optional[int]): O novo ID da prioridade para a OS.
            - 0: Normal, 1: Baixa, 2: Média, 3: Alta, 4: Urgente, 5: Crítica
    """
    logger.info(f"🔄 Atualizando Ordem de Serviço: {os_idx}")

    if status_id is None and prioridade_id is None:
        return {"erro": "Nenhum dado (status ou prioridade) foi fornecido para atualização."}

    # Mapeamento de IDs para IDX e Nomes
    status_map = {
        0: {"idx": "4000000001", "nome": "A Iniciar"},
        1: {"idx": "4000000002", "nome": "Em Andamento"},
        2: {"idx": "4000000003", "nome": "Aguardando Peças"},
        3: {"idx": "4000000004", "nome": "Aguardando Cliente"},
        4: {"idx": "4000000005", "nome": "Concluída"},
        5: {"idx": "4000000006", "nome": "Cancelada"},
        6: {"idx": "4000000007", "nome": "Entregue"},
    }
    prioridade_map = {
        0: {"idx": "3000000001", "nome": "Normal"},
        1: {"idx": "3000000002", "nome": "Baixa"},
        2: {"idx": "3000000003", "nome": "Média"},
        3: {"idx": "3000000004", "nome": "Alta"},
        4: {"idx": "3000000005", "nome": "Urgente"},
        5: {"idx": "3000000006", "nome": "Crítica"},
    }

    query_parts = []
    params = {"os_idx": os_idx, "negocio_idx": negocio_idx}
    
    base_query = "MATCH (os:OrdemServico {idx: $os_idx})-[:EMITIDA_POR]->(:Negocio {idx: $negocio_idx})"
    
    # --- Lógica para Status ---
    if status_id is not None:
        if status_id not in status_map:
            return {"erro": f"ID de status inválido: {status_id}"}
        
        novo_status_idx = status_map[status_id]["idx"]
        novo_status_nome = status_map[status_id]["nome"]
        params["novo_status_idx"] = novo_status_idx
        params["novo_status_nome"] = novo_status_nome
        
        status_part = """
        WITH os
        OPTIONAL MATCH (os)-[r_st_antigo:TEM_STATUS]->()
        WHERE r_st_antigo.ativo = 1 OR r_st_antigo.ativo IS NULL
        SET r_st_antigo.ativo = 0, r_st_antigo.saida = datetime()
        WITH os
        MERGE (s_novo:Status {idx: $novo_status_idx})
        ON CREATE SET s_novo.nome = $novo_status_nome
        CREATE (os)-[:TEM_STATUS {entrada: datetime(), ativo: 1}]->(s_novo)
        """
        query_parts.append(status_part)

    # --- Lógica para Prioridade ---
    if prioridade_id is not None:
        if prioridade_id not in prioridade_map:
            return {"erro": f"ID de prioridade inválido: {prioridade_id}"}

        nova_prioridade_idx = prioridade_map[prioridade_id]["idx"]
        nova_prioridade_nome = prioridade_map[prioridade_id]["nome"]
        params["nova_prioridade_idx"] = nova_prioridade_idx
        params["nova_prioridade_nome"] = nova_prioridade_nome

        prioridade_part = """
        WITH os
        OPTIONAL MATCH (os)-[r_pr_antigo:TEM_PRIORIDADE]->()
        WHERE r_pr_antigo.ativo = 1 OR r_pr_antigo.ativo IS NULL
        SET r_pr_antigo.ativo = 0, r_pr_antigo.saida = datetime()
        WITH os
        MERGE (p_nova:Prioridade {idx: $nova_prioridade_idx})
        ON CREATE SET p_nova.nome = $nova_prioridade_nome
        CREATE (os)-[:TEM_PRIORIDADE {entrada: datetime(), ativo: 1}]->(p_nova)
        """
        query_parts.append(prioridade_part)

    # --- Construção da Query Final ---
    final_query = base_query + "\n" + "\n".join(query_parts) + "\nRETURN os.idx as os_idx"

    logger.info(f"Query de atualização de OS construída: {final_query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(final_query, params)
        if result:
            logger.info(f"✅ OS {os_idx} atualizada com sucesso.")
            return {"sucesso": True, "os_idx": os_idx, "mensagem": "Ordem de Serviço atualizada com sucesso!"}
        else:
            logger.warning(f"⚠️ A atualização da OS {os_idx} não retornou resultados. A OS existe e pertence ao negócio?")
            return {"erro": "Ordem de Serviço não encontrada ou falha ao atualizar."}
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar Ordem de Serviço: {str(e)}")
        return {"erro": f"Erro ao atualizar Ordem de Serviço: {str(e)}"}


async def gera_ordem_servico_pdf(
    ordem_servico,
    cliente,
    negocio: NegocioData,
    servicos: List[ServicoItem] = [],
    produtos: List[ProdutoItem] = [],
    formas_pagamento: List[FormaPagamentoItem] = [],
    plataforma_url: str = "",
    ):
    """
    Gera os PDF da ordem de serviço.
    Esta função deve ser implementada para gerar o PDF com os dados da OS.
    """
    logger.info("@@@@@@@@@@@@ 🔄 Gerando PDF da ordem de serviço...")
    
    # Prepara os dados para o payload
    negocio_para_pdf = negocio.model_dump() if isinstance(negocio, BaseModel) else negocio
    cliente_para_pdf = cliente.model_dump() if isinstance(cliente, BaseModel) else cliente
    
    payload = {
        "cliente": cliente_para_pdf,
        "negocio": negocio_para_pdf,
        "ordem_servico": ordem_servico,
        "produtos": produtos,
        "servicos": servicos,
        "formas_pagamento": formas_pagamento,
    }

    logger.info(f"Payload para gerar PDF: {json.dumps(payload, indent=2)}")

    url = f"{plataforma_url}/app_phj/app/oficinatech/modulo/ordem_servico/ordem_servico_pdf.php"
    logger.info(f"URL para geração do PDF: {url}")
    
    headers = {'Content-Type': 'application/json'}

    try:
        logger.info(f"Tentando gerar PDF com URL: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        logger.info(f"Status da resposta HTTP: {response.status_code}")
        logger.info(f"Headers da resposta: {response.headers}")
        logger.info(f"Conteúdo da resposta: {response.text}")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get("success"):
                    logger.info(f"✅ PDF gerado com sucesso: {data}")
                    return data
                else:
                    logger.error(f"❌ PHP retornou erro: {data.get('message', 'Erro desconhecido no PHP')}")
                    raise Exception(f"Erro retornado pelo PHP: {data.get('message', 'Erro desconhecido')}")
            except ValueError:
                logger.error("Erro ao decodificar JSON da resposta do PHP.")
                logger.error(f"Resposta recebida: {response.text}")
                raise Exception("Resposta inválida do servidor de PDF (não é um JSON válido).")
        else:
            logger.error(f"Erro HTTP {response.status_code} ao tentar gerar PDF.")
            raise Exception(f"Erro HTTP {response.status_code} ao contatar o servidor de PDF.")
            
    except requests.exceptions.Timeout:
        logger.error(f"Timeout ao tentar conectar com {url}")
        raise Exception("O servidor de geração de PDF demorou muito para responder.")
    except requests.exceptions.RequestException as e:
        logger.error(f"Erro de conexão ao tentar gerar PDF: {e}")
        raise Exception(f"Não foi possível conectar ao servidor de geração de PDF: {e}")
    except Exception as e:
        logger.error(f"Erro inesperado ao gerar PDF: {e}")
        raise


@router.post("/send/text")
async def send_text(data: dict):
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL
    print("===== send_text() =====")
    print(f"data: {data}")

    # Extrair dados necessários
    mensagem = data.get("mensagem") or ""
    negocio_idx = data.get("negocio_idx") or ""
    modelo = data.get("modelo") or "1234567890"
    modelo = "1234567897"
    logger.info(f"modelo: {modelo}")
    imagem = data.get("imagem") or ""
    plataforma_url = data.get("plataforma_url") or ""
    usuario_nome = data.get("usuario_nome") or "Carlos"
    usuario_funcao = data.get("usuario_funcao") or 1
    usuario_idx = data.get("usuario_idx") or ""
    negocio = data.get("negocio") or {}
    canal = data.get("canal") or "web_app"  
    modo_resposta_ia = data.get("modo_resposta_ia") or "texto"
    if not modelo:
        return {"status": "fail", "message": "Modelo é obrigatório"}

    # ✅ MAPEAR CORRETAMENTE PARA OS CAMPOS DA TABELA MENSAGEM
    with messageChat_lock:  # 🔒 PROTEÇÃO CONTRA CONCORRÊNCIA
        messageChat.clear()  # ✅ LIMPAR DADOS ANTERIORES
        messageChat.update({
            "ENVIADO": mensagem,
            "LLM_IDX": modelo,
            "CANAL_ID": "WEB_APP",
            "ENTRADA": datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S"),
            "AGENTE_ID": 13,
            "CONVERSA_IDX": 0,
            "ENVIADO_TKS": 0,
            "RECEBIDO_TKS": 0,
            "TOTAL_TKS": 0,
            "FUNCAO_CHAMADA": 0,
            "NEGOCIO_IDX": negocio_idx,
            "USUARIO_IDX": negocio_idx,
            "IMAGEM": imagem if imagem else None,
        })

    try:
        # Usar a função comum para processar com o agente
        agentOficinatech_obj, historico_mensagens, conversa_idx, intencao = await process_with_agent(
            mensagem=mensagem,
            negocio_idx=negocio_idx,
            modelo=modelo,
            usuario_nome=usuario_nome,
            plataforma_url=plataforma_url,
            imagem=imagem,
            usuario_idx=usuario_idx,
            usuario_funcao=usuario_funcao,
            negocio=negocio
        )

        # Função para extrair texto de respostas aninhadas
        def extrair_texto_resposta(msg):
            if isinstance(msg, dict):
                if "messages" in msg and isinstance(msg["messages"], list) and len(msg["messages"]) > 0:
                    msg = msg["messages"][0]
                return msg.get("message") or msg.get("resposta") or msg.get("resposta_texto") or str(msg)
            return str(msg)

        # Atualizar CONVERSA_IDX antes do streaming
        with messageChat_lock:
            messageChat["CONVERSA_IDX"] = conversa_idx

        # Processar a resposta com base no modo
        resposta_completa = ""
        async for chunk in process_agent_stream(
            agenteTech_obj=agentOficinatech_obj,
            historico_mensagens=historico_mensagens,
            negocio_idx=negocio_idx,
            conversa_idx=conversa_idx
        ):
            if isinstance(chunk, bytes):
                chunk_str = chunk.decode('utf-8')
            else:
                chunk_str = str(chunk)
            if chunk_str.startswith('data: '):
                chunk_str = chunk_str[6:]
            resposta_completa += chunk_str

        print ("canal atual: " + canal)
        print(f"resposta completa:")
        print(resposta_completa)
        # Aplicar limpeza condicional baseada no canal
        if canal.lower() in ["web_app", "app_web"]:
            # Mantém a formatação original para web/app
            texto_resposta = resposta_completa
        else:
            # Remove apenas HTML para WhatsApp, preservando Markdown
            logger.info("vou tirar as tags HTML do texto")
            texto_resposta = limpar_html(resposta_completa)
        
        logger.info(f"Resposta processada: {texto_resposta}")

        # Formatar a resposta no padrão do agent_neo4j
        response_body = {
            "success": True,
            "message": texto_resposta
        }

        # Lógica para modos de áudio
        if modo_resposta_ia in ["audio", "voz_ao_vivo", "texto_voz_ao_vivo"]:
            audio_resposta = await text_to_speech(texto_resposta)
            if audio_resposta:
                response_body["resposta_audio"] = audio_resposta
                response_body["audio_format"] = "mp3"
                if modo_resposta_ia == "texto_voz_ao_vivo":
                    response_body["resposta_texto"] = texto_resposta
            else:
                response_body["message"] = f"{texto_resposta} (Falha na conversão para áudio)"

        return JSONResponse(content=response_body, headers={"Content-Type": "application/json"})

    except Exception as e:
        logger.error(f"Erro durante execução do agente: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return JSONResponse(content={
            "status": "fail",
            "message": f"Erro durante execução: {str(e)}"
        }, headers={"Content-Type": "application/json"})


@function_tool
async def cliente_consultar(negocio_idx: str, busca: Optional[str] = ""):
    """
    Consulta clientes. Usa fulltext quando há busca, Cypher simples quando busca vazia.
    A busca fulltext considera nome, telefone ou email com poder de busca avançada.

    Args:
        negocio_idx (str): O IDX do negócio. É obrigatório.
        busca (Optional[str]): Texto para buscar em nome, telefone ou email. Parcial.

    Returns:
        dict: Resultado da consulta contendo os dados encontrados ou erro.
    """
    logger.info("========== cliente_consultar() [híbrido] ==========")
    logger.info(f"🔍 DEBUG: Iniciando consulta de clientes")
    logger.info(f"🔍 DEBUG: negocio_idx = {negocio_idx}")
    logger.info(f"🔍 DEBUG: busca = '{busca}'")

    # Verificar se há busca para decidir qual estratégia usar
    if not busca or busca.strip() == "":
        # Sem busca: usar Cypher simples para listar todos
        logger.info("🔍 Busca vazia - usando Cypher simples para listar todos")
        query = """
        MATCH (c:Pessoa)-[:CLIENTE_DE]->(n:Negocio {idx: $negocio_idx})
        WHERE (c.excluido = 0 OR c.excluido IS NULL)
        RETURN c.nome      AS nome,
               c.telefone  AS telefone,
               c.email     AS email,
               c.codigo    AS codigo,
               c.idx       AS idx
        ORDER BY c.nome
        """
        params = {"negocio_idx": negocio_idx}
    else:
        # Com busca: usar fulltext para busca avançada
        logger.info(f"🔍 Busca com termo '{busca.strip()}' - usando fulltext")
        query = """
        CALL db.index.fulltext.queryNodes('clienteBuscaIndex', $busca) YIELD node, score
        MATCH (node)-[:CLIENTE_DE]->(n:Negocio {idx: $negocio_idx})
        WHERE (node.excluido = 0 OR node.excluido IS NULL)
        RETURN node.nome      AS nome,
               node.telefone  AS telefone,
               node.email     AS email,
               node.codigo    AS codigo,
               node.idx       AS idx
        ORDER BY score DESC, node.nome
        """
        params = {
            "busca": busca.strip(),
            "negocio_idx": negocio_idx
        }

    logger.info(f"Query construída: {query}")
    logger.info(f"Parâmetros: {params}")
    logger.info(f"🔍 DEBUG: Chamando neo4j.execute_read_query()")

    try:
        logger.info(f"🔍 DEBUG: Antes da chamada execute_read_query")
        result = await neo4j.execute_read_query(query, params)
        logger.info(f"🔍 DEBUG: Depois da chamada execute_read_query")
        logger.info(f"🔍 DEBUG: Tipo do resultado: {type(result)}")
        logger.info(f"🔍 DEBUG: Resultado bruto: {result}")
        
        if isinstance(result, list) and len(result) > 0 and "erro" in str(result[0]):
            logger.error(f"❌ DEBUG: Erro detectado no resultado: {result}")
            return {"success": False, "error": f"Erro na query: {result}"}
        
        logger.info(f"✅ Resultado da consulta de cliente: {len(result) if result else 0} registro(s) encontrado(s).")
        logger.info(f"🔍 DEBUG: Retornando resultado com sucesso")
        return {"success": True, "data": result}
        
    except Exception as e:
        error_msg = f"Erro inesperado em cliente_consultar [híbrido]: {e}"
        logger.error(f"❌ {error_msg}")
        logger.error(f"❌ DEBUG: Tipo da exceção: {type(e)}")
        logger.error(f"❌ DEBUG: Args da exceção: {e.args}")
        import traceback
        logger.error(f"❌ DEBUG: Traceback completo: {traceback.format_exc()}")
        return {"success": False, "error": error_msg}



@function_tool
async def cliente_atualizar(
    cliente_idx: str,
    negocio_idx: str,
    nome: Optional[str] = None,
    telefone: Optional[str] = None,
    email: Optional[str] = None,
    cpf_cnpj: Optional[str] = None,
    cep: Optional[str] = None,
    logradouro: Optional[str] = None,
    bairro: Optional[str] = None,
    cidade: Optional[str] = None,
    uf: Optional[str] = None,
    numero: Optional[str] = None,
    complemento: Optional[str] = None
):
    """
    Atualiza os dados de um cliente existente. O cliente é identificado pelo seu IDX.

    Args:
        cliente_idx (str): O IDX do cliente a ser atualizado. É obrigatório.
        negocio_idx (str): O IDX do negócio. É obrigatório.
        nome (Optional[str]): O novo nome do cliente.
        telefone (Optional[str]): O novo telefone do cliente.
        email (Optional[str]): O novo email do cliente.
        cpf_cnpj (Optional[str]): O novo CPF ou CNPJ do cliente.
        cep (Optional[str]): O novo CEP do cliente.
        logradouro (Optional[str]): O novo logradouro do cliente.
        bairro (Optional[str]): O novo bairro do cliente.
        cidade (Optional[str]): A nova cidade do cliente.
        uf (Optional[str]): A nova UF do cliente.
        numero (Optional[str]): O novo número do endereço.
        complemento (Optional[str]): O novo complemento do endereço.
    """
    logger.info(f"🔄 Atualizando cliente: {cliente_idx}")

    set_clauses = []
    params = {"cliente_idx": cliente_idx, "negocio_idx": negocio_idx}

    # Mapeia os argumentos para as cláusulas SET e parâmetros da query
    arg_map = {
        "nome": nome, "telefone": telefone, "email": email, "cpfCnpj": cpf_cnpj,
        "cep": cep, "logradouro": logradouro, "bairro": bairro, "cidade": cidade,
        "uf": uf, "numero": numero, "complemento": complemento
    }

    for key, value in arg_map.items():
        if value is not None:
            set_clauses.append(f"c.{key} = ${key}")
            params[key] = value

    if not set_clauses:
        return {"erro": "Nenhum dado foi fornecido para atualização."}

    set_query_part = ", ".join(set_clauses)
    query = f"""
    MATCH (c:Pessoa {{idx: $cliente_idx}})-[:CLIENTE_DE]->(n:Negocio {{idx: $negocio_idx}})
    SET {set_query_part}
    RETURN c.idx as cliente_idx, c.nome as nome
    """

    logger.info(f"Query de atualização construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Cliente {cliente_idx} atualizado com sucesso.")
            return {"sucesso": True, "cliente_idx": cliente_idx, "mensagem": "Dados do cliente atualizados com sucesso!"}
        else:
            return {"erro": "Cliente não encontrado ou falha ao atualizar."}
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar cliente: {str(e)}")
        return {"erro": f"Erro ao atualizar cliente: {str(e)}"}


@function_tool
async def cliente_excluir(cliente_idx: str, negocio_idx: str):
    """
    Realiza a exclusão lógica de um cliente (soft delete) definindo a propriedade 'excluido' como 1.
    O cliente não será mais exibido nas consultas padrão.

    Args:
        cliente_idx (str): O IDX do cliente a ser excluído. É obrigatório.
        negocio_idx (str): O IDX do negócio para garantir a permissão. É obrigatório.
    """
    logger.info(f"🗑️ Excluindo logicamente o cliente: {cliente_idx}")

    query = """
    MATCH (c:Pessoa {idx: $cliente_idx})-[:CLIENTE_DE]->(n:Negocio {idx: $negocio_idx})
    SET c.excluido = 1
    RETURN c.idx as cliente_idx, c.nome as nome
    """
    params = {"cliente_idx": cliente_idx, "negocio_idx": negocio_idx}

    logger.info(f"Query de exclusão lógica construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Cliente {cliente_idx} marcado como excluído com sucesso.")
            return {"sucesso": True, "cliente_idx": cliente_idx, "mensagem": "Cliente excluído com sucesso!"}
        else:
            return {"erro": "Cliente não encontrado ou falha ao excluir."}
    except Exception as e:
        logger.error(f"❌ Erro ao excluir cliente: {str(e)}")
        return {"erro": f"Erro ao excluir cliente: {str(e)}"}


@function_tool
async def produto_consultar(negocio_idx: str, termo: Optional[str] = None):
    """
    Consulta produtos da oficina via índice full-text.
    O termo pode ser parte do nome ou o código do produto.
    Se nenhum termo for fornecido, lista todos os produtos do negócio.

    Args:
        negocio_idx (str): O IDX do negócio. É obrigatório.
        termo (Optional[str]): Termo para buscar por nome ou código.
    """
    logger.info("========== produto_consultar() ==========")

    try:
        if termo:
            query = """
            CALL db.index.fulltext.queryNodes('produtoBuscaIndex', $termo) YIELD node, score
            WHERE node.excluido = 0 AND node.negocio_idx = $negocio_idx
            RETURN node.nome AS nome, node.preco AS preco, node.codigo AS codigo, node.idx AS idx
            ORDER BY score DESC
            """
            params = {"termo": termo, "negocio_idx": negocio_idx}
            logger.info(f"Query (full-text): {query}")
        else:
            query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:VENDE_PRODUTO]->(p:Produto)
            WHERE p.excluido = 0
            RETURN p.nome AS nome, p.preco AS preco, p.codigo AS codigo, p.idx AS idx
            ORDER BY p.nome
            """
            params = {"negocio_idx": negocio_idx}
            logger.info(f"Query (listagem completa): {query}")

        logger.info(f"Parâmetros: {params}")
        result = await neo4j.execute_read_query(query, params)
        logger.info(f"✅ Resultado da consulta de produto: {len(result)} registro(s) encontrado(s).")
        return {"success": True, "data": result}

    except Exception as e:
        error_msg = f"Erro inesperado em produto_consultar: {e}"
        logger.error(f"❌ {error_msg}")
        return {"success": False, "error": error_msg}


@function_tool
async def produto_atualizar(
    produto_idx: str,
    negocio_idx: str,
    nome: Optional[str] = None,
    preco: Optional[float] = None,
    codigo: Optional[str] = None,
    estoque: Optional[int] = None,
    descricao: Optional[str] = None
):
    """
    Atualiza os dados de um produto existente. O produto é identificado pelo seu IDX.

    Args:
        produto_idx (str): O IDX do produto a ser atualizado. É obrigatório.
        negocio_idx (str): O IDX do negócio. É obrigatório.
        nome (Optional[str]): O novo nome do produto.
        preco (Optional[float]): O novo preço do produto.
        codigo (Optional[str]): O novo código do produto.
        estoque (Optional[int]): A nova quantidade em estoque.
        descricao (Optional[str]): A nova descrição do produto.
    """
    logger.info(f"🔄 Atualizando produto: {produto_idx}")

    set_clauses = []
    params = {"produto_idx": produto_idx, "negocio_idx": negocio_idx}

    arg_map = { "nome": nome, "preco": preco, "codigo": codigo, "estoque": estoque, "descricao": descricao }

    for key, value in arg_map.items():
        if value is not None:
            set_clauses.append(f"p.{key} = ${key}")
            params[key] = value

    if not set_clauses:
        return {"erro": "Nenhum dado foi fornecido para atualização."}

    set_query_part = ", ".join(set_clauses)
    query = f"""
    MATCH (n:Negocio {{idx: $negocio_idx}})-[:VENDE_PRODUTO]->(p:Produto {{idx: $produto_idx}})
    SET {set_query_part}
    RETURN p.idx as produto_idx, p.nome as nome
    """

    logger.info(f"Query de atualização construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Produto {produto_idx} atualizado com sucesso.")
            return {"sucesso": True, "produto_idx": produto_idx, "mensagem": "Dados do produto atualizados com sucesso!"}
        else:
            return {"erro": "Produto não encontrado ou falha ao atualizar."}
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar produto: {str(e)}")
        return {"erro": f"Erro ao atualizar produto: {str(e)}"}


@function_tool
async def produto_excluir(produto_idx: str, negocio_idx: str):
    """
    Realiza a exclusão lógica de um produto (soft delete) definindo a propriedade 'excluido' como 1.
    O produto não será mais exibido nas consultas padrão.

    Args:
        produto_idx (str): O IDX do produto a ser excluído. É obrigatório.
        negocio_idx (str): O IDX do negócio para garantir a permissão. É obrigatório.
    """
    logger.info(f"🗑️ Excluindo logicamente o produto: {produto_idx}")

    query = """
    MATCH (n:Negocio {idx: $negocio_idx})-[:VENDE_PRODUTO]->(p:Produto {idx: $produto_idx})
    SET p.excluido = 1
    RETURN p.idx as produto_idx, p.nome as nome
    """
    params = {"produto_idx": produto_idx, "negocio_idx": negocio_idx}

    logger.info(f"Query de exclusão lógica construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Produto {produto_idx} marcado como excluído com sucesso.")
            return {"sucesso": True, "produto_idx": produto_idx, "mensagem": "Produto excluído com sucesso!"}
        else:
            return {"erro": "Produto não encontrado ou falha ao excluir."}
    except Exception as e:
        logger.error(f"❌ Erro ao excluir produto: {str(e)}")
        return {"erro": f"Erro ao excluir produto: {str(e)}"}

@function_tool
async def servico_consultar(negocio_idx: str, termo: Optional[str] = None):
    """
    Consulta serviços da oficina via índice full-text.
    O termo pode ser parte do nome ou o código do serviço.
    Se nenhum termo for fornecido, lista todos os serviços do negócio.

    Args:
        negocio_idx (str): O IDX do negócio. É obrigatório.
        termo (Optional[str]): Termo para buscar por nome ou código.
    """
    logger.info("========== servico_consultar() ==========")

    try:
        if termo:
            query = """
            CALL db.index.fulltext.queryNodes('servicoBuscaIndex', $termo) YIELD node, score
            WHERE node.excluido = 0 AND node.negocio_idx = $negocio_idx
            RETURN node.nome AS nome, node.preco AS preco, node.codigo AS codigo, node.idx AS idx
            ORDER BY score DESC
            """
            params = {"termo": termo, "negocio_idx": negocio_idx}
            logger.info(f"Query (full-text): {query}")
        else:
            query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:PRESTA_SERVICO]->(s:Servico)
            WHERE s.excluido = 0
            RETURN s.nome AS nome, s.preco AS preco, s.codigo AS codigo, s.idx AS idx
            ORDER BY s.nome
            """
            params = {"negocio_idx": negocio_idx}
            logger.info(f"Query (listagem completa): {query}")

        logger.info(f"Parâmetros: {params}")
        result = await neo4j.execute_read_query(query, params)
        logger.info(f"✅ Resultado da consulta de serviço: {len(result)} registro(s) encontrado(s).")
        return {"success": True, "data": result}

    except Exception as e:
        error_msg = f"Erro inesperado em servico_consultar: {e}"
        logger.error(f"❌ {error_msg}")
        return {"success": False, "error": error_msg}


@function_tool
async def servico_atualizar(
    servico_idx: str,
    negocio_idx: str,
    nome: Optional[str] = None,
    preco: Optional[float] = None,
    codigo: Optional[str] = None,
    descricao: Optional[str] = None
):
    """
    Atualiza os dados de um serviço existente. O serviço é identificado pelo seu IDX.

    Args:
        servico_idx (str): O IDX do serviço a ser atualizado. É obrigatório.
        negocio_idx (str): O IDX do negócio. É obrigatório.
        nome (Optional[str]): O novo nome do serviço.
        preco (Optional[float]): O novo preço do serviço.
        codigo (Optional[str]): O novo código do serviço.
        descricao (Optional[str]): A nova descrição do serviço.
    """
    logger.info(f"🔄 Atualizando serviço: {servico_idx}")

    set_clauses = []
    params = {"servico_idx": servico_idx, "negocio_idx": negocio_idx}

    arg_map = { "nome": nome, "preco": preco, "codigo": codigo, "descricao": descricao }

    for key, value in arg_map.items():
        if value is not None:
            set_clauses.append(f"s.{key} = ${key}")
            params[key] = value

    if not set_clauses:
        return {"erro": "Nenhum dado foi fornecido para atualização."}

    set_query_part = ", ".join(set_clauses)
    query = f"""
    MATCH (n:Negocio {{idx: $negocio_idx}})-[:PRESTA_SERVICO]->(s:Servico {{idx: $servico_idx}})
    SET {set_query_part}
    RETURN s.idx as servico_idx, s.nome as nome
    """

    logger.info(f"Query de atualização construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Serviço {servico_idx} atualizado com sucesso.")
            return {"sucesso": True, "servico_idx": servico_idx, "mensagem": "Dados do serviço atualizados com sucesso!"}
        else:
            return {"erro": "Serviço não encontrado ou falha ao atualizar."}
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar serviço: {str(e)}")
        return {"erro": f"Erro ao atualizar serviço: {str(e)}"}

        logger.error(f"❌ Erro crítico durante a geração do número da OS: {str(e)}")


@function_tool
async def servico_excluir(servico_idx: str, negocio_idx: str):
    """
    Realiza a exclusão lógica de um serviço (soft delete) definindo a propriedade 'excluido' como 1.
    O serviço não será mais exibido nas consultas padrão.


        servico_idx (str): O IDX do serviço a ser excluído. É obrigatório.
        negocio_idx (str): O IDX do negócio para garantir a permissão. É obrigatório.
    """
    logger.info(f"🗑️ Excluindo logicamente o serviço: {servico_idx}")

    query = """
    MATCH (n:Negocio {idx: $negocio_idx})-[:PRESTA_SERVICO]->(s:Servico {idx: $servico_idx})
    SET s.excluido = 1
    RETURN s.idx as servico_idx, s.nome as nome
    """
    params = {"servico_idx": servico_idx, "negocio_idx": negocio_idx}

    logger.info(f"Query de exclusão lógica construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        if result:
            logger.info(f"✅ Serviço {servico_idx} marcado como excluído com sucesso.")
            return {"sucesso": True, "servico_idx": servico_idx, "mensagem": "Serviço excluído com sucesso!"}
        else:
            return {"erro": "Serviço não encontrado ou falha ao excluir."}
    except Exception as e:
        logger.error(f"❌ Erro ao excluir serviço: {str(e)}")
        return {"erro": f"Erro ao excluir serviço: {str(e)}"}


@function_tool
async def ordem_servico_consultar(
    negocio_idx: str,
    numero: Optional[int] = None,
    cliente_nome: Optional[str] = None,
    cliente_telefone: Optional[str] = None,
    cliente_email: Optional[str] = None,
    status: Optional[str] = None,
    prioridade: Optional[str] = None,
    data_inicio: Optional[str] = None,
    data_fim: Optional[str] = None,
    item_nome: Optional[str] = None
):
    """
    Consulta ordens de serviço da oficina permitindo busca por múltiplos critérios.
    
    Args:
        negocio_idx (str): O IDX do negócio. É obrigatório.
        numero (Optional[int]): Número específico da OS para busca exata.
        cliente_nome (Optional[str]): Nome do cliente para busca parcial.
        cliente_telefone (Optional[str]): Telefone do cliente para busca parcial.
        cliente_email (Optional[str]): Email do cliente para busca parcial.
        status (Optional[str]): Status da OS (ex: "Aberta", "Em Andamento", "Concluída").
        prioridade (Optional[str]): Prioridade da OS (ex: "Normal", "Alta", "Urgente").
        data_inicio (Optional[str]): Data inicial para filtro por período (formato YYYY-MM-DD).
        data_fim (Optional[str]): Data final para filtro por período (formato YYYY-MM-DD).
        item_nome (Optional[str]): Nome/modelo do item/veículo para busca parcial.
    
    Returns:
        dict: Resultado da consulta contendo os dados encontrados ou erro.
    """
    logger.info("========== ordem_servico_consultar() ==========")
    logger.info(f"Parâmetros recebidos: negocio_idx={negocio_idx}, numero={numero}, cliente_nome={cliente_nome}")
    
    try:
        # Construir a query base
        query_parts = [
            "MATCH (os:OrdemServico)-[:EMITIDA_POR]->(n:Negocio {idx: $negocio_idx})",
            "MATCH (os)-[:SOLICITADA_POR]->(c:Pessoa)",
            "MATCH (os)-[:TEM_STATUS]->(st:Status)",
            "MATCH (os)-[:TEM_PRIORIDADE]->(pr:Prioridade)",
            "WHERE (os.excluido = 0 OR os.excluido IS NULL)"
        ]
        
        params = {"negocio_idx": negocio_idx}
        
        # Adicionar filtros condicionais
        if numero is not None:
            query_parts.append("AND os.numero = $numero")
            params["numero"] = numero
            
        if cliente_nome:
            query_parts.append("AND toLower(c.nome) CONTAINS toLower($cliente_nome)")
            params["cliente_nome"] = cliente_nome
            
        if cliente_telefone:
            query_parts.append("AND c.telefone CONTAINS $cliente_telefone")
            params["cliente_telefone"] = cliente_telefone
            
        if cliente_email:
            query_parts.append("AND toLower(c.email) CONTAINS toLower($cliente_email)")
            params["cliente_email"] = cliente_email
            
        if status:
            query_parts.append("AND toLower(st.nome) CONTAINS toLower($status)")
            params["status"] = status
            
        if prioridade:
            query_parts.append("AND toLower(pr.nome) CONTAINS toLower($prioridade)")
            params["prioridade"] = prioridade
            
        if data_inicio:
            query_parts.append("AND date(os.entrada) >= date($data_inicio)")
            params["data_inicio"] = data_inicio
            
        if data_fim:
            query_parts.append("AND date(os.entrada) <= date($data_fim)")
            params["data_fim"] = data_fim
            
        if item_nome:
            query_parts.append("AND toLower(os.item_nome) CONTAINS toLower($item_nome)")
            params["item_nome"] = item_nome
        
        # Adicionar o RETURN e ORDER BY
        query_parts.extend([
            "RETURN os.numero AS numero,",
            "       os.idx AS os_idx,",
            "       c.nome AS cliente_nome,",
            "       c.telefone AS cliente_telefone,",
            "       c.email AS cliente_email,",
            "       c.idx AS cliente_idx,",
            "       st.nome AS status,",
            "       pr.nome AS prioridade,",
            "       os.item_categoria AS item_categoria,",
            "       os.item_nome AS item_nome,",
            "       os.item_marca AS item_marca,",
            "       os.item_ano AS item_ano,",
            "       os.item_cor AS item_cor,",
            "       os.item_placa AS item_placa,",
            "       os.total AS total,",
            "       os.total_servicos AS total_servicos,",
            "       os.total_produtos AS total_produtos,",
            "       os.observacao AS observacao,",
            "       toString(os.entrada) AS data_entrada,",
            "       toString(os.saida) AS data_saida",
            "ORDER BY os.numero DESC"
        ])
        
        # Montar a query final
        query = "\n".join(query_parts)
        
        logger.info(f"Query construída: {query}")
        logger.info(f"Parâmetros: {params}")
        
        # Executar a consulta
        result = await neo4j.execute_read_query(query, params)
        
        if result:
            logger.info(f"✅ Consulta de ordens de serviço realizada com sucesso: {len(result)} registro(s) encontrado(s).")
            
            # Formatar os resultados para melhor apresentação
            ordens_formatadas = []
            for ordem in result:
                ordem_formatada = {
                    "numero": ordem.get("numero"),
                    "os_idx": ordem.get("os_idx"),
                    "cliente": {
                        "nome": ordem.get("cliente_nome"),
                        "telefone": ordem.get("cliente_telefone"),
                        "email": ordem.get("cliente_email"),
                        "idx": ordem.get("cliente_idx")
                    },
                    "status": ordem.get("status"),
                    "prioridade": ordem.get("prioridade"),
                    "item": {
                        "categoria": ordem.get("item_categoria"),
                        "nome": ordem.get("item_nome"),
                        "marca": ordem.get("item_marca"),
                        "ano": ordem.get("item_ano"),
                        "cor": ordem.get("item_cor"),
                        "placa": ordem.get("item_placa")
                    },
                    "valores": {
                        "total": ordem.get("total"),
                        "total_servicos": ordem.get("total_servicos"),
                        "total_produtos": ordem.get("total_produtos")
                    },
                    "observacao": ordem.get("observacao"),
                    "data_entrada": ordem.get("data_entrada"),
                    "data_saida": ordem.get("data_saida")
                }
                ordens_formatadas.append(ordem_formatada)
            
            return {
                "success": True,
                "data": ordens_formatadas,
                "total_encontrado": len(result),
                "mensagem": f"Encontradas {len(result)} ordem(ns) de serviço."
            }
        else:
            logger.info("ℹ️ Nenhuma ordem de serviço encontrada com os critérios especificados.")
            return {
                "success": True,
                "data": [],
                "total_encontrado": 0,
                "mensagem": "Nenhuma ordem de serviço encontrada com os critérios especificados."
            }
            
    except Exception as e:
        error_msg = f"Erro inesperado em ordem_servico_consultar: {str(e)}"
        logger.error(f"❌ {error_msg}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "error": error_msg}

async def gera_ultimo_numero_os(negocio_idx: str):
    """
    Gera ou atualiza o número sequencial para uma nova Ordem de Serviço.
    Desmembrado em múltiplas etapas para clareza e para contornar limitações do driver Neo4j:
    1. Verifica se o negócio existe (leitura).
    2. Verifica se o nó numerador da OS existe (leitura).
    3. Cria o nó numerador se ele não existir (escrita pura).
    4. Incrementa o número (escrita pura).
    5. Lê o valor incrementado (leitura pura).
    """
    logger.info(f"🔢 Iniciando geração de número de OS para o negócio: {negocio_idx}")

    try:
        # --- ETAPA 1: Verificar se o negócio existe ---
        check_negocio_query = "MATCH (n:Negocio {idx: $negocio_idx}) RETURN n.idx as found_idx"
        negocio_encontrado = await neo4j.execute_read_query(check_negocio_query, {"negocio_idx": negocio_idx})
        if not negocio_encontrado:
            logger.error(f"❌ Negócio com idx '{negocio_idx}' não foi encontrado no banco de dados.")
            return None

        # --- ETAPA 2: Verificar se o nó OsNumerador já existe ---
        check_numerador_query = "MATCH (:Negocio {idx: $negocio_idx})-[:TEM_OS_NUMERADOR]->(num:OsNumerador) RETURN num.idx"
        numerador_existente = await neo4j.execute_read_query(check_numerador_query, {"negocio_idx": negocio_idx})

        # --- ETAPA 3: Criar o nó OsNumerador se não existir ---
        if not numerador_existente:
            logger.info(f"🔢 ETAPA 3: OsNumerador não encontrado. Criando um novo...")
            numerador_idx = generate_unique_id()
            create_numerador_query = """
            MATCH (n:Negocio {idx: $negocio_idx})
            CREATE (n)-[:TEM_OS_NUMERADOR]->(:OsNumerador {
                idx: $numerador_idx,
                ultimo_numero: 0,
                excluido: 0
            })
            """
            create_params = {"negocio_idx": negocio_idx, "numerador_idx": numerador_idx}
            await neo4j.execute_write_query(create_numerador_query, create_params)
            logger.info(f"🔢 ETAPA 2: OsNumerador já existe.")
        # --- ETAPA 4: Incrementar o número (escrita pura) ---
        increment_query = """
        MATCH (n:Negocio {idx: $negocio_idx})-[:TEM_OS_NUMERADOR]->(num:OsNumerador)
        SET num.ultimo_numero = num.ultimo_numero + 1
        """
        await neo4j.execute_write_query(increment_query, {"negocio_idx": negocio_idx})

        # --- ETAPA 5: Ler o valor final (leitura pura) ---
        read_final_value_query = """
        MATCH (n:Negocio {idx: $negocio_idx})-[:TEM_OS_NUMERADOR]->(num:OsNumerador)
        RETURN num.ultimo_numero as proximo_numero
        """
        result = await neo4j.execute_read_query(read_final_value_query, {"negocio_idx": negocio_idx})
        
        if result and result[0].get("proximo_numero") is not None:
            proximo_numero = result[0]["proximo_numero"]
            logger.info(f"✅ ETAPA 5: Próximo número de OS obtido: {proximo_numero}")
            return proximo_numero
        else:
            logger.error(f"❌ Falha ao ler o valor final do numerador (ETAPA 5). Resultado: {result}")
            return None

    except Exception as e:
        logger.error(f"❌ Erro crítico durante a geração do número da OS: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None


async def testa_abrir_os():
    """
    Função de teste para simular a abertura de uma Ordem de Serviço.
    """
    logger.info("🧪 Iniciando teste de abertura de Ordem de Serviço...")
    
    try:
        # --- DADOS DE TESTE ---
        # IMPORTANTE: O cliente com o cliente_idx abaixo DEVE existir no banco de dados.
        # Você pode criar um com a função cliente_adicionar ou usar um IDX existente.
        # Exemplo de como criar um cliente para o teste:
        # await cliente_adicionar(nome="Cliente de Teste OS", negocio_idx="4015743441", telefone="999998888")
        
        # Criar um objeto ClienteData para o teste.
        # Preencha com dados válidos de um cliente existente.
        cliente_teste = ClienteData(
            IDX="1122334455", # <--- SUBSTITUA PELO IDX DE UM CLIENTE REAL
            NOME="Cliente Teste para OS",
            EMAIL="<EMAIL>",
            TELEFONE="11999998877",
            CPF_CNPJ="50518251548",
            CEP="01000-000",
            LOGRADOURO="Rua Teste",
            BAIRRO="Bairro Teste",
            CIDADE="Cidade Teste",
            UF="SP",
            NUMERO="123",
            COMPLEMENTO="Apto 1",
        )

        dados_os = {
            "cliente": cliente_teste,
            "formas_pagamento": [
                FormaPagamentoItem(nr=1, id=1, nome="Pix",valor=150.0, vencimento="2024-07-25", pago=0),
                FormaPagamentoItem(nr=2, id=2, nome = "Cartão de Crédito",  valor=150.0, vencimento="2024-08-25", pago=0)
            ],
            "servicos": [
                ServicoItem(idx="1928374650", nome="Regulagem de Marchas", valor=20.0, quantidade=1)
            ],
            "item_nome": "Veículo de Teste",
            "item_marca": "Marca Teste",
            "item_ano": "2024",
            "item_cor": "Preto",
            "item_placa": "TST-1234",
            "observacao": "Item para teste de abertura de OS.",
            "prioridade": 2, # Média
            "negocio_idx": "4015743441",
            "plataforma_url":"http://127.0.0.1/gptalk",
            "negocio": {
                "IDX": "4015743441",
                "NOME": "Oficina Tech",
                "CNPJ": "12345678000195",
                "TELEFONE": "11999998888",
                "LOGRADOURO": "RUA ABC",
                "NUMERO": "123",
                "COMPLEMENTO": "Sala 1",
                "BAIRRO": "Centro",
                "CIDADE": "São Paulo",
                "UF": "SP",
                "CEP": "01000-000",
                "EMAIL": "<EMAIL>",
                "RESPONSAVEL":"João da Silva",
            }
        }    
        
        resultado = await core_ordem_servico_abrir(**dados_os)
        
        

        
        
        
        
        logger.info("📄 Resultado da abertura da OS:")
        # Usar json.dumps para formatar bem o dicionário no log
        import json
        logger.info(json.dumps(resultado, indent=2, ensure_ascii=False))

    except Exception as e:
        logger.error("❌ Ocorreu um erro ao executar testa_abrir_os:")
        import traceback
        logger.error(f"Tipo de Erro: {type(e).__name__}")
        logger.error(f"Mensagem: {e}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")
        raise

@function_tool
async def ordem_servico_atualizar(
    os_idx: str,
    negocio_idx: str,
    status_id: Optional[int] = None,
    prioridade_id: Optional[int] = None
):
    """
    Atualiza o status e/ou a prioridade de uma Ordem de Serviço existente.
    Esta função mantém um histórico das alterações, desativando o relacionamento antigo
    e criando um novo com as datas de entrada e saída.

    Args:
        os_idx (str): O IDX da Ordem de Serviço a ser atualizada. É obrigatório.
        negocio_idx (str): O IDX do negócio para verificação de permissão. É obrigatório.
        status_id (Optional[int]): O novo ID do status para a OS.
            - 0: A Iniciar, 1: Em Andamento, 2: Aguardando Peças, 3: Aguardando Cliente,
            - 4: Concluída, 5: Cancelada, 6: Entregue
        prioridade_id (Optional[int]): O novo ID da prioridade para a OS.
            - 0: Normal, 1: Baixa, 2: Média, 3: Alta, 4: Urgente, 5: Crítica
    """
    logger.info(f"🔄 Atualizando Ordem de Serviço: {os_idx}")

    if status_id is None and prioridade_id is None:
        return {"erro": "Nenhum dado (status ou prioridade) foi fornecido para atualização."}

    # Mapeamento de IDs para IDX e Nomes
    status_map = {
        0: {"idx": "4000000001", "nome": "A Iniciar"},
        1: {"idx": "4000000002", "nome": "Em Andamento"},
        2: {"idx": "4000000003", "nome": "Aguardando Peças"},
        3: {"idx": "4000000004", "nome": "Aguardando Cliente"},
        4: {"idx": "4000000005", "nome": "Concluída"},
        5: {"idx": "4000000006", "nome": "Cancelada"},
        6: {"idx": "4000000007", "nome": "Entregue"},
    }
    prioridade_map = {
        0: {"idx": "3000000001", "nome": "Normal"},
        1: {"idx": "3000000002", "nome": "Baixa"},
        2: {"idx": "3000000003", "nome": "Média"},
        3: {"idx": "3000000004", "nome": "Alta"},
        4: {"idx": "3000000005", "nome": "Urgente"},
        5: {"idx": "3000000006", "nome": "Crítica"},
    }

    query_parts = []
    params = {"os_idx": os_idx, "negocio_idx": negocio_idx}
    
    base_query = "MATCH (os:OrdemServico {idx: $os_idx})-[:EMITIDA_POR]->(:Negocio {idx: $negocio_idx})"
    
    # --- Lógica para Status ---
    if status_id is not None:
        if status_id not in status_map:
            return {"erro": f"ID de status inválido: {status_id}"}
        
        novo_status_idx = status_map[status_id]["idx"]
        novo_status_nome = status_map[status_id]["nome"]
        params["novo_status_idx"] = novo_status_idx
        params["novo_status_nome"] = novo_status_nome
        
        status_part = """
        WITH os
        OPTIONAL MATCH (os)-[r_st_antigo:TEM_STATUS]->()
        WHERE r_st_antigo.ativo = 1 OR r_st_antigo.ativo IS NULL
        SET r_st_antigo.ativo = 0, r_st_antigo.saida = datetime()
        WITH os
        MERGE (s_novo:Status {idx: $novo_status_idx})
        ON CREATE SET s_novo.nome = $novo_status_nome
        CREATE (os)-[:TEM_STATUS {entrada: datetime(), ativo: 1}]->(s_novo)
        """
        query_parts.append(status_part)

    # --- Lógica para Prioridade ---
    if prioridade_id is not None:
        if prioridade_id not in prioridade_map:
            return {"erro": f"ID de prioridade inválido: {prioridade_id}"}

        nova_prioridade_idx = prioridade_map[prioridade_id]["idx"]
        nova_prioridade_nome = prioridade_map[prioridade_id]["nome"]
        params["nova_prioridade_idx"] = nova_prioridade_idx
        params["nova_prioridade_nome"] = nova_prioridade_nome

        prioridade_part = """
        WITH os
        OPTIONAL MATCH (os)-[r_pr_antigo:TEM_PRIORIDADE]->()
        WHERE r_pr_antigo.ativo = 1 OR r_pr_antigo.ativo IS NULL
        SET r_pr_antigo.ativo = 0, r_pr_antigo.saida = datetime()
        WITH os
        MERGE (p_nova:Prioridade {idx: $nova_prioridade_idx})
        ON CREATE SET p_nova.nome = $nova_prioridade_nome
        CREATE (os)-[:TEM_PRIORIDADE {entrada: datetime(), ativo: 1}]->(p_nova)
        """
        query_parts.append(prioridade_part)

    # --- Construção da Query Final ---
    final_query = base_query + "\n" + "\n".join(query_parts) + "\nRETURN os.idx as os_idx"

    logger.info(f"Query de atualização de OS construída: {final_query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(final_query, params)
        if result:
            logger.info(f"✅ OS {os_idx} atualizada com sucesso.")
            return {"sucesso": True, "os_idx": os_idx, "mensagem": "Ordem de Serviço atualizada com sucesso!"}
        else:
            logger.warning(f"⚠️ A atualização da OS {os_idx} não retornou resultados. A OS existe e pertence ao negócio?")
            return {"erro": "Ordem de Serviço não encontrada ou falha ao atualizar."}
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar Ordem de Serviço: {str(e)}")
        return {"erro": f"Erro ao atualizar Ordem de Serviço: {str(e)}"}


@function_tool
async def ordem_servico_resposta_template():
   """
    Retorna o template de resposta para a ordem de serviço.
     você DEVE usar EXATAMENTE este template de resposta:

    "✅ **Ordem de serviço gerada com sucesso!**

    📋 **Dados da OS:**
    - Cliente: [NOME_DO_CLIENTE]
    - Veículo: [VEICULO_CATEGORIA] [VEICULO_MARCA] [VEICULO_NOME]
    - Total: R$ [VALOR_TOTAL]

    📄 **Acesse o PDF da ordem de serviço:**

    <div style='margin: 15px 0; text-align: center;'>
        <button onclick='window.open("{PDF_LINK}", "_blank");' 
                style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer; font-size: 14px;'>
            👁️ VER PDF
        </button>
        <button onclick='navigator.clipboard.writeText("{PDF_LINK}"); alert("Link copiado para a área de transferência!");' 
                style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;'>
            📋 COPIAR LINK
        </button>
    </div>"

    IMPORTANTE: Substitua {PDF_LINK} pelo link real gerado pela função.

    """
   return {
        "sucesso": True,
        "os_idx": "1234567890",
        "osnumero": 45,
        "cliente_nome": "Carlos Silva",
        "pdf_link": f"http://127.0.0.1/gptalk/negocios/4015743441/pdf/4185022270.pdf",
        "mensagem": f"Ordem de serviço #4 criada com sucesso !"
    }




@function_tool
async def ordem_servico_excluir(ordem_servico_numero: int, negocio_idx: str):
    """
    Realiza a exclusão lógica de uma ordem de serviço (soft delete) definindo a propriedade 'excluido' como 1.
    A ordem de serviço não será mais exibida nas consultas padrão.

    Args:
        ordem_servico_numero (int): O numero da ordem de serviço a ser excluída. É obrigatório.
        negocio_idx (str): O IDX do negócio para garantir a permissão. É obrigatório.
    """
    logger.info(f"🗑️ Excluindo logicamente a ordem de serviço: {ordem_servico_numero}")

    # Corrigido: A direção do relacionamento estava invertida e a verificação foi melhorada.
    # A OS é emitida pelo Negócio, então a seta vai de Negocio para OrdemServico.
    query = """
    MATCH (os:OrdemServico {numero: $ordem_servico_numero})-[:EMITIDA_POR]->(n:Negocio {idx: $negocio_idx})
    SET os.excluido = 1
    RETURN os.numero as numero
    """
    
    params = {"ordem_servico_numero": ordem_servico_numero, "negocio_idx": negocio_idx}

    logger.info(f"Query de exclusão lógica construída: {query}")
    logger.info(f"Parâmetros: {params}")

    try:
        result = await neo4j.execute_write_query(query, params)
        print(result)
        if result and result[0].get("success") :
            numero_excluido = ordem_servico_numero
            logger.info(f"✅ Ordem de serviço {numero_excluido} marcada como excluída com sucesso.")
            return {
                "sucesso": True,
                "numero": numero_excluido,
                "mensagem": f"Ordem de serviço {numero_excluido} foi excluída com sucesso."
            }
        else:
            logger.warning(f"⚠️ Nenhuma ordem de serviço encontrada com o número {ordem_servico_numero} e relacionamento [:EMITIDA_POR] para o negócio {negocio_idx}.")
            return {"erro": f"Ordem de serviço número {ordem_servico_numero} não encontrada ou não pertence a este negócio."}
    except Exception as e:
        logger.error(f"❌ Erro ao excluir ordem de serviço: {str(e)}")
        return {"erro": f"Erro ao tentar excluir a ordem de serviço: {str(e)}"}


if __name__ == "__main__":
    import asyncio

    async def teste_escrita_neo4j():
        """
        Testa a função execute_write_query do AgentNeo4j.
        Cria um nó, verifica se a operação ocorreu sem erros e depois o remove.
        """
        logger.info("🧪 Iniciando teste de ESCRITA no Neo4j...")
        teste_idx = generate_unique_id()
        
        # 1. Query de Escrita
        write_query = "CREATE (t:TesteEscrita {idx: $idx, mensagem: 'teste de escrita'})"
        write_params = {"idx": teste_idx}
        
        logger.info(f"📝 Executando query de ESCRITA: {write_query}")
        write_result = await neo4j.execute_write_query(write_query, write_params)
        
        # 2. A escrita bem-sucedida (sem RETURN) deve retornar uma lista vazia
        if isinstance(write_result, list) and len(write_result) == 0:
            logger.info("✅ Teste de ESCRITA bem-sucedido.")
        else:
            logger.error(f"❌ Falha no teste de ESCRITA. Resultado inesperado: {write_result}")
            raise AssertionError("Teste de escrita falhou.")
        
        # 3. Limpeza específica do teste de escrita
        logger.info("🧹 Limpando o nó do teste de escrita...")
        cleanup_query = "MATCH (t:TesteEscrita {idx: $idx}) DELETE t"
        await neo4j.execute_write_query(cleanup_query, {"idx": teste_idx})
        logger.info("✅ Nó do teste de escrita removido.")

    async def teste_leitura_neo4j():
        """
        Testa a função execute_read_query do AgentNeo4j.
        Cria um nó com dados conhecidos, lê e verifica se os dados estão corretos.
        """
        logger.info("🧪 Iniciando teste de LEITURA no Neo4j...")
        teste_idx = generate_unique_id()
        teste_mensagem = f"Teste de leitura - {teste_idx}"
        
        # 1. Setup: Criar um nó de teste para poder ler
        setup_query = "CREATE (t:TesteLeitura {idx: $idx, mensagem: $mensagem})"
        setup_params = {"idx": teste_idx, "mensagem": teste_mensagem}
        await neo4j.execute_write_query(setup_query, setup_params)
        logger.info("🔧 Setup: Nó de teste para leitura criado.")

        try:
            # 2. Executar a query de leitura
            read_query = "MATCH (t:TesteLeitura {idx: $idx}) RETURN t.mensagem as mensagem"
            read_params = {"idx": teste_idx}
            
            logger.info(f"📖 Executando query de LEITURA: {read_query}")
            read_result = await neo4j.execute_read_query(read_query, read_params)
            
            # 3. Verificar o resultado
            if read_result and read_result[0].get("mensagem") == teste_mensagem:
                logger.info(f"✅ Teste de LEITURA bem-sucedido. Mensagem lida: '{read_result[0]['mensagem']}'")
            else:
                logger.error(f"❌ Falha no teste de LEITURA. Resultado inesperado: {read_result}")
                raise AssertionError("Teste de leitura falhou.")
        finally:
            # 4. Teardown: Limpar o nó de teste criado, garantindo que seja executado
            logger.info("🧹 Limpando o nó do teste de leitura...")
            cleanup_query = "MATCH (t:TesteLeitura {idx: $idx}) DELETE t"
            await neo4j.execute_write_query(cleanup_query, {"idx": teste_idx})
            logger.info("✅ Nó do teste de leitura removido.")

    async def main():
        """Função principal para rodar todos os testes em sequência."""
        print("Iniciando testes assíncronos...")
        await teste_leitura_neo4j()
        await teste_escrita_neo4j()
       
        
       
        print("Testes concluídos.")


    async def  teste_chat():
        data = {
            "mensagem": "Olá, bom dia",
            "negocio_idx": "4015743441",
            "modelo": "1234567890",
            "usuario_idx": "4344140157",
            "agente": "@gptalkzap",
            "usuario_idx": "1122334455",
            "modo_resposta_ia": "texto"
            
        }

        result = await send_text(data)
        print("result:", result)

    #asyncio.run(main())
    #asyncio.run( teste_leitura_neo4j())
    #asyncio.run( teste_escrita_neo4j())
    asyncio.run(teste_chat())
    #asyncio.run(testa_abrir_os())
    
    async def teste_cliente_consultar_isolado():
        """Teste isolado da função cliente_consultar"""
        logger.info("🧪 Iniciando teste isolado de cliente_consultar...")
        
        try:
            # Teste com negócio real
            negocio_idx = "4015743441"
            busca = "carlos"  # Busca vazia para listar todos
            
            logger.info(f"Testando com negocio_idx: {negocio_idx}")
            logger.info(f"Testando com busca: '{busca}'")
            
            resultado = await cliente_consultar(negocio_idx=negocio_idx, busca=busca)
            
            logger.info(f"🎯 RESULTADO FINAL: {resultado}")
            
            return resultado
            
        except Exception as e:
            logger.error(f"❌ Erro no teste isolado: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"erro": str(e)}
    
   # asyncio.run(teste_cliente_consultar_isolado())
