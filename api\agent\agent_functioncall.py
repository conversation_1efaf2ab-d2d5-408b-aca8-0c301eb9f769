from .agent_filemanager import FileManager
from .agent_llm import LLM
from .agent_converter import Converter
from .agent_message import Message
from .agent_user import User
import importlib, inspect
import os,json
import os

class FunctionCall:
    def __init__(self,user:User=None,model:str=None):
        self.fileManager = FileManager()
        self.llm = LLM()
        self.conv = Converter()
        self.user  = user
        self.model = model

    async def call_chat_function(self,funcao_nome,funcao_mapa,funcao_parametros):
        #print("########## call_chat_function()")
        #print("funcao_nome", funcao_nome)
        #print("funcao_mapa", funcao_mapa)
        #print("funcao_parametros", funcao_parametros)
        #print("agent", funcao_mapa["agent"])
        #print("class", funcao_mapa["class"])
        
        agent = funcao_mapa["agent"]
        class_name = funcao_mapa["class"]

        agent = importlib.import_module(f".{agent}", package="api.agent")
        #print("agent", agent)

        # Obtém a classe a partir do nome
        agent_class = getattr(agent, class_name)

        #print("agent_class", agent_class)

        #cria uma instancia do agente
        agent_instance = agent_class()

        #print("vou obter a refencia da funcao")

        func_to_call = getattr(agent_instance, funcao_nome)
        #print("funcao_nome", funcao_nome)
        
        #print("parametros", funcao_parametros)
        #print("Tipo dos parâmetros:", type(funcao_parametros))
        
        mapa = funcao_mapa
        #print("mapa",mapa)

        # Chama a função usando a referência e passando os parâmetros
        response = await func_to_call(**funcao_parametros)
        #print("response(funcao):", response)
        return response

    async def call_task_function(self,tarefa=None):     
        #print("========== call_function()==========")
        #print(tarefa)
        #print("===== call_function =====")
        #print("agente",agente)
        agente = tarefa['AGENTE_NOME']
        funcao_nome = tarefa['AGENTE_FUNCAO']
        #entrada = tarefa['ENTRADA'] if tarefa['ENTRADA'] else tarefa['ANTERIOR_SAIDA']
        anterior_saida = tarefa.get('ANTERIOR_SAIDA') or ''
        entrada = tarefa.get('ENTRADA') or ''
        entrada = f"{anterior_saida} {entrada}"

        #entrada = json.dumps(entrada)
        #print("entrada",entrada) 
        #print("XXXXXXXXXXXXXXXXXXXXDiretório de trabalho atual:", os.getcwd())
        base_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(base_dir, agente)
        #print("XXXXXXXXXXXXXXXXXXXXXXfile_path",file_path)

        funcoes_mapas = await self.fileManager.load_text_file(file_path,'functions_maps.py')
        if not funcoes_mapas:
            return {"STATUS":"F","SAIDA":f"O mapa de funções do agente {agente} não foi encontrado."}
   
        #verifica se a função existe no mapa de funções
        match = funcao_nome in funcoes_mapas
        if match: #função existe
            try:
                
                #carrega o mapa da função
                funcao_mapa = await self.load_function_map(agente, funcao_nome)

                if not isinstance(funcao_mapa, dict) or not funcao_mapa:
                    return False,"T",  "O mapa da função {funcao_nome} está num formato inválido" 

                #print("mapa da funcao:")
                #print(funcao_mapa)
                #print("entrada", entrada)
                #print("mapa", funcao_mapa)

                funcao_parametros =await self.load_function_params(funcao_mapa,entrada)
              
                #print("parametros mapeados", funcao_parametros)
                #print('parametros data type antes da extração', type(funcao_parametros))
                #print("vou extrair o objeto parametro")
            
                funcao_parametros =  await self.conv.extract_object(funcao_parametros)
                #print("parametros extraidos")
                #print("funcao_parametros",funcao_parametros)
                
                if not isinstance(funcao_parametros, dict):
                    return {'STATUS':"T","SAIDA":  "Erro na definição de parametros"}
                

                
                print('funcao_parametros é um objeto valido',funcao_parametros)

                if 'incompleto' in funcao_parametros:
            
                    funcao_parametros = funcao_parametros['incompleto']
                    return {"STATUS":"T","SAIDA":f"Faltam parametros para executar a função {funcao_nome}{funcao_parametros}"}
                else:
                    funcao_parametros = funcao_parametros['completo']

                #print('funcao_parametros final:')
                #print(funcao_parametros)
                
               
                
                #print(f"vou carregar o módulo agent_{agente}")
                module = importlib.import_module(f".agent_{agente}", package="api.agent")
                #print(module)
                
                #print("vou carregar a classe")
                classes = [member for name, member in inspect.getmembers(module, inspect.isclass) if member.__module__ == module.__name__]
                
                if len(classes) != 1:
                    raise Exception("O módulo deve conter exatamente uma classe.")
          
                agent_class = classes[0]
                #print(agent_class)
                
                #print("vou criar a instancia da classe")
                agent_instance = agent_class()

                #print("vou obter a refencia da funcao")
                func_to_call = getattr(agent_instance, funcao_nome)
        
                #print(f"Vou executar a função {funcao_nome} do agente {agente}")
                # EXECUTA A FUNÇÃO
                
                if self.has_parameter(func_to_call, 'model'):
                    funcao_parametros['model'] = self.model
                
                #if self.has_parameter(func_to_call, 'tarefa'):
                #    funcao_parametros['tarefa'] = tarefa

                response = await getattr(agent_instance, funcao_nome)(**funcao_parametros)
                print("response(funcao)", response)
                response['SAIDA'] = response.pop('RESULT')
                return response 
            
                
                
            except SyntaxError as e:
                #print(f"Erro de sintaxe ao processar o dicionário: {e}")
                return {"STATUS":"F","SAIDA":"Erro de sintaxe"}
        else: #função não existe
            
            #print(f'Função {funcao_nome} não encontrada')
            return {"STATUS":"T","SAIDA":f"Função {funcao_nome} não foi encontrada."}

    def has_parameter(self,func, param_name):
        # Função que verifica se uma função tem um determinado parâmetro
        params = inspect.signature(func).parameters
        return param_name in params
         
    def extract_text_from_json(self,json_string):
            
            # Finding the index of the first { and the last }
            first_brace_index = json_string.find('{')
            last_brace_index = json_string.rfind('}') + 1

            # Extracting the substring including both the first and last braces
            extracted_text = json_string[first_brace_index:last_brace_index]

            return extracted_text

    
    async def load_function_params(self,funcao_mapa,entrada):
    
        #print("==========load_function_param()==========",funcao_mapa,entrada,modelo)
    
        tarefa = f"""
            Este é o mapa representativo/explicativo de uma função:
            {funcao_mapa}
            Preciso que seja criado e retornado um objeto json que será usado para executar a função. Para isto, extraia os valores dos parametros do             seguinte texto:
            {entrada}
            se todos os parametros obrigatórios (requireds) forem encontrados, será retornado um json com a chave principal sendo 'completo',  com todos os             parametros como filhos. Exemplo:
                {{
            "completo": 
            {{"param1": "valor do param1"}}, 
            {{"param2": "valor do param2"}},
            {{"param3": "valor do param3"}},
            {{"param4": "valor do param4"}}
    
            }}
              Retorne o objeto json desta forma somente se todos os parametros constantes no mapa da função forem preenchidos com dados correspondentes             encontrados no texto. Se você entender que um algo mencionado no texto corresponde a uma propriedade da função, utilize esta informação.
            
            Caso um ou mais parametros obrigatórios (requeridos) não sejam encontrados no texto, retorno um json com a chave principal sendo 'incompleto': e dentro dela  os parametros que não tem dados fornecidos no texto de entrada. Exemplo:

            {{
            "incompleto": 
              {{"param3": ""}},
              {{"param4": ""}}
            }}
            
            Nenhum parametro obrigatório (required) pode retonar vazio. Se o dado que ele requer não existe no texto de entrada , então a o objeto deve ser considerado incompleto. Valores vazios devem ser preenchidos com None.

            Caso todos os parametros obrigatórios (requeridos) sejam  encontrados, será retornado completo, mesmo que tenha algum incompleto, mas que não seja obrigatório , ou seja, opcional.

            Caso os parametros opcionais não tenham dados (em branco, nulo, ou None) , não devem ser retornados , ou seja, não devem ser incluidos no objeto de retorno.

            se qualquer chave estiver sem valor (vazia, em branco ou None) ela não deve ser incluida no objeto final.
            
retorne somente o objeto, sem nenhum comentário adicional antes ou depois. Certifique-se que  o formato do json seja valido , fazendo qualqur formatacao necessaria para que não de erro de codificacao quando o objeto for utilizado.
"""

        result = await self.llm.run_model(model=self.model,message=[{'role':'user','content':tarefa}])
        return result

    async  def load_function_map(self,agente,funcao_nome):
        #print("load_function_maps",agente,funcao_nome)



        # Importa o módulo dinamicamente
        module = importlib.import_module(f".functions_maps",package=f"api.agent.{agente}")

        # Acessa o mapa de funções diretamente pelo seu nome
        function_map = getattr(module, f"{funcao_nome}_map")
        #print("module", function_map['name'])
        return function_map


    async def chat(self, historico:list,mensagemRecebida:dict,llm_modelo:str,llm:LLM,agente, usuario:User,conversa_id,canal_id):
       print("==================chat", "Functioncall - chat")
       print("agente",agente.NOME,agente.ARQUIVO)
       from .agent_business import Business
       negocio = Business()

       #print("historico", historico)

       funcao_nome = "chat"

       agent_instance = await agente.get_instance(usuario)
       
       #print("agent_instance", agent_instance)
       agente.instancia = agent_instance
       #print("usuario", agent_instance.usuario)
       
       #print("funcao_nome",funcao_nome)
       
       # Debugging the signature
    
       mensagens = []

       if historico:
            mensagens.extend(historico)

       #print("\n\n historico2", historico)
       # Adiciona mensagem de configuração do agente à lista de mensagens
       system = await agent_instance.get_system() if inspect.iscoroutinefunction(agent_instance.get_system) else await agent_instance.get_system()
       msgSistema = {"role": "system", "content": system}

       mensagens.append(msgSistema)

       #adicionam mensagem do usuario a lista de menagens
       mensagens.append(mensagemRecebida)

       
       #print("mensagens", mensagens)
       # Verifica se o atributo 'function_map' existe em 'agent_instance' antes de acessar seu valor
       function_map = getattr(agent_instance, 'function_map', None)
       print("function_map", function_map)
       # Envia mensagem para o modelo LLM e recebe resposta
       resposta = await llm.run_model(
       message=mensagens,
       model=llm_modelo,
       functions=list(function_map.values()) if function_map else None
       )
       
       #print("resposta inicial do modelo", resposta)

       msg = Message()
       msg.ID = 0
       msg.AGENTE_ID = agente.ID
       msg.CONVERSA_ID = conversa_id
       msg.DATA_HORA = self.conv.time_now_to_date_hour()
       msg.CANAL_ID = canal_id
       msg.USUARIO_ID = usuario.ID
       msg.RECEBIDO = mensagemRecebida['content']
       msg.RECEBIDO_TKS = llm.TOKEN_PROMPT
       msg.ENVIADO = json.dumps(llm.FUNCAO_PARAMETROS) if llm.FUNCAO_CHAMADA else llm.RESPOSTA
       msg.ENVIADO_TKS = llm.TOKEN_RESPOSTA
       await msg.add()


       if llm.FUNCAO_CHAMADA:
           funcao_nome = resposta
           #print("funcao_nome", funcao_nome)
           funcao_mapa = agent_instance.function_map.get(funcao_nome)
           funcao_agente = funcao_mapa['agent']
           resposta_funcao = await self.call_chat_function(
               funcao_nome=funcao_nome,
               funcao_mapa= funcao_mapa,
               funcao_parametros=llm.FUNCAO_PARAMETROS
               )
           #print("")
           #print("resposta da função", resposta_funcao,type(resposta_funcao))
           #print("")
           llm.FUNCAO_NOME = funcao_nome
           llm.FUNCAO_RESPOSTA = resposta_funcao
           llm.AGENTE_RESPOSTA_DIRETA = funcao_mapa.get('agent_direct_response', False)
           resposta_funcao_str = json.dumps(resposta_funcao)
           
           mensagem = {
             "role": "assistant",
             "content": "",
             "function_call": {
               "name": funcao_nome,
                 "arguments": json.dumps(llm.FUNCAO_PARAMETROS)
             }
            }
           mensagens.append(mensagem)


           mensagemFuncao ={"role":"function","name":funcao_nome,"content":json.dumps(resposta_funcao)}
           mensagens.append(mensagemFuncao)
           #print("mensagemFuncao", mensagemFuncao)
           resposta =  await llm.run_model(
           message=mensagens,
           model=llm_modelo
           )
           

           msg = Message()
           msg.ID = 0
           msg.AGENTE_ID = agente.ID
           msg.FUNCAO_NOME = funcao_nome
           msg.FUNCAO_AGENTE = funcao_agente
           msg.CONVERSA_ID = conversa_id
           msg.DATA_HORA = self.conv.time_now_to_date_hour()
           msg.CANAL_ID = canal_id
           msg.USUARIO_ID = usuario.ID
           msg.RECEBIDO = json.dumps(resposta_funcao)
           msg.RECEBIDO_TKS = llm.TOKEN_PROMPT
           msg.ENVIADO = llm.RESPOSTA
           msg.ENVIADO_TKS = llm.TOKEN_RESPOSTA
           await msg.add()

       #print ("resposta final", resposta)
       return resposta






       #response = await getattr(agent_instance, funcao_nome)(**funcao_parametros)
       #print("response(funcao):", response)
       #return response 

    @staticmethod
    async def call_agent_direct_response(agente,funcao_nome,resposta, usuario=None):
        print("========== call_agent_direct_response ==========")
        print("agente.NOME",agente.NOME)
        print("agente.instancia",agente.instancia)
        print("funcao_nome", funcao_nome)
        print("resposta", resposta)
        print("resposta type", type(resposta))
        print("resposta nome do prato", resposta[0]['NOME'])    

        agent_instance = await agente.get_instance(usuario)
        print("agent_instance", agent_instance)
       

        func_to_call = getattr(agent_instance, f"direct_response_whatsappwhapi_{funcao_nome}")
        #print("funcao_nome", funcao_nome)
        
        
        response = await func_to_call(resposta)
        #print("response(funcao):", response)
        return response

        
        
        
        
        
        
        
        
        
        
        
        return "O agente deu  a resposta diretamente.";
        

if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        fc = FunctionCall()
        usuario = User()
        await usuario.fetch("NOME,TELEFONE",["ID=1"])
        from .agent_agent import Agent
        agente = Agent()
        #ag = await agente.fetch_agent_id("*", 10)
        await agente.fetch_agent_nick("*", "tropeirodadani")
        funcao_nome = "get_meal_day"
        funcao_resposta = [{'NOME': 'Carne cozida com batata', 'DESCR': 'Arroz, carne cozida com batata,e salada', 'PRECO': None, 'VARIAVEL': 0, 'STATUS': 'Principal'}, {'NOME': 'Tropeiro', 'DESCR': 'Arroz,tropeiro, bife de pernil acebolado ,couve', 'PRECO': None, 'VARIAVEL': 0, 'STATUS': 'Opção'}]

        result = await fc.call_agent_direct_response(agente,funcao_nome,funcao_resposta, usuario)
        print("result", result)

    asyncio.run(main())