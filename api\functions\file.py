import os
import asyncio
import shutil
#=========================
async def save_file(data):
    print ("===== save_file()=====")
    url = data["path"]
    file =  data["arquivo"]
    content = data["conteudo"]
    print(content)
    print("========================")
    
    
    
    # Verificar se o diretório existe, se não, criar
    if not os.path.exists(url):
        os.makedirs(url)

    # Combinar o diretório com o nome do arquivo
    fullpath = os.path.join(url, file)
    
    


    # Pré-processamento: assegura que todas as quebras de linha são do tipo Unix ('\n')
    #content = content.replace('\r\n', '\n')


    dados_binarios = content.encode('utf-8')
    print("dados binarios",dados_binarios)

    # Salvar o arquivo
    with open(fullpath, 'wb') as f:
      f.write(dados_binarios)
      print(fullpath , "salvo com sucesso" )
    
    return "Arquivo salvo com sucesso" 
   
      
#=========================
async def load_file(data):
    print("load_file()",data)
    data = verifica_nome_arquivo(data)
    
    fullpath = data["path"] + "\\" + data["arquivo"]
    
    print("fullpath", fullpath)
    try:
        with open(fullpath, 'r', encoding='utf-8') as file:
            contents = file.read()
            print("contents",contents)
            return contents
    except FileNotFoundError:
        print(f"File '{fullpath}' not found.")
        return None

#==========
def verifica_nome_arquivo(data):
    if "arquivo" not in data:
      # Limpa espaços em branco e determina o separador
      caminho_limpado = data["path"].strip()
   
      caminho = data["path"]
      separador = ""
      if '\\' in caminho:
        separador = '\\'
      else:
        separador =  '/'
    else:
      return data

    # Extrai o nome do arquivo do caminho
    partes_do_path = caminho_limpado.split(separador)
    nome_do_arquivo = partes_do_path[-1]
    
    # Remove o nome do arquivo do 'path'
    novo_path = separador.join(partes_do_path[:-1])
    
    # Atualiza o dicionário 'data'
    data["path"] = novo_path
    data["arquivo"] = nome_do_arquivo
    return data


#=========================
async def delete_dir(dir):
    print ("----- delete_dir()")
    dir = dir["path"]
    print("dir",dir)
    try:
        shutil.rmtree(dir)  # Remove the directory
        return f"Diretorio '{dir}' apagado com sucesso."
    except OSError as e:
        return f"Erro: {dir} : {e.strerror}"
  

async def copy_files_and_subfolders(source, destination):
    # Check if the destination path exists. If not, create the directory.
    if not os.path.exists(destination):
        os.makedirs(destination)
    
    for root, subfolders, files in os.walk(source):
        # Calculate the current destination path based on the directory structure
        destination_path = os.path.join(destination, root[len(source):])
        if not os.path.exists(destination_path):
            os.makedirs(destination_path)
        for file in files:
            # Full path of the source file
            source_file = os.path.join(root, file)
            # Full path of the destination file
            destination_file = os.path.join(destination_path, file)
            # Copy the file from source to destination
            shutil.copy2(source_file, destination_file)
    
    return "Files copied successfully!"

async def list_files_folders(base_path):
    result = []
    for root, dirs, files in os.walk(base_path):
        for dir_name in dirs:
            result.append({"type": "folder", "path": os.path.join(root, dir_name)})
        for file_name in files:
            ext = os.path.splitext(file_name)[1][1:]  # Remove o ponto da extensão
            if ext == "":  # Se não tiver extensão, considera como tipo 'file'
                ext = "file"
            result.append({"type": ext, "path": os.path.join(root, file_name)})
    return result

async def main():
    source = r"C:\Users\<USER>\Documents\GitHub\gptalk-iapn\src\app\components\menus\Menu006"
    destination = r"C:\Users\<USER>\Documents\GitHub\iapn-next-react-components\app\componentes\menus\Menu006"
    result = await copy_files_and_subfolders(source, destination)
    print(result)

# Running the main function
#if __name__ == "__main__":
#    asyncio.run(main())