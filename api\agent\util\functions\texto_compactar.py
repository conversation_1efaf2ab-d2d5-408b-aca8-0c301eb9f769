# Importação correta
from api.agent.util.functions.tokens_contador import tokens_contador
import re
# Remover a importação duplicada do tiktoken se já existe no tokens_contador
# import tiktoken  # Esta linha pode ser removida se não for mais necessária



# --------------------------------------------------
# 2. STOPWORDS + PROTEGIDAS
# --------------------------------------------------
STOPWORDS = {
       "o", "a", "os", "as", "um", "uma", "foi", "é", "este", "com", "de",
       "do", "da", "dos", "das", "ao", "no", "na", "nos", "nas", "solicitado",
       "sucesso", "que", "para", "em", "bom", "dia", "boa", "tarde", "noite",
       "ola", "oi", "tudo", "bem", "por", "favor", "sem", "s", "tambem", "entao",
       "assim", "deste", "desta", "nesse", "nessa", "sobre", "ate", "apos", "preciso","olá","ola"
        }

# Frases stopwords a serem removidas
STOPWORDS_FRASES = [
    "como esta", "como está", "como vai", "tudo bem", "e ai", "e aí"
]

PROTEGIDAS = {"nome", "codigo", "numero", "id", "item", "produto", "cliente", "cor", "preco"}
STOPWORDS = STOPWORDS - PROTEGIDAS


async def texto_compactar(texto: str, max_tokens: int = 0) -> str:
    """
    Limpa stopwords e corta **exatamente** no limite de tokens.
    Só texto, sem JSON.
    """
    texto_compacto = limpar_texto(texto)
    
    if max_tokens > 0:
        texto_compacto = cortar_no_limite(texto_compacto, max_tokens)
    
    tks_normal = tokens_contador(texto)
    tks_comp = tokens_contador(texto_compacto)   
    return tks_normal, tks_comp, texto_compacto


# ---------- LIMPADOR PURO TEXTO ----------
def limpar_texto(texto: str) -> str:
    """Remove stopwords, frases stopwords, espaços múltiplos e mantém números/códigos."""
    texto = re.sub(r"\s+", " ", texto.strip().lower())
    
    # Remover frases stopwords primeiro (case insensitive)
    for frase in STOPWORDS_FRASES:
        pattern = re.compile(re.escape(frase), re.IGNORECASE)
        texto = pattern.sub("", texto)
    
    # Remover pontuação mantendo apenas letras e números
    texto = re.sub(r'[^\w\s]', '', texto)
    
    # Remover espaços duplicados que podem ter sido criados
    texto = re.sub(r'\s+', ' ', texto.strip())
    
    codigos = re.findall(r"\b\d+\b", texto)
    palavras = [p for p in texto.split() if p not in STOPWORDS or p in codigos]
    
    return " ".join(palavras)

def cortar_no_limite(texto: str, max_tokens: int) -> str:
    """Corta palavra a palavra até caber no limite de tokens."""
    palavras = texto.split()
    while tokens(" ".join(palavras)) > max_tokens and palavras:
        palavras.pop()
    return " ".join(palavras)


if __name__ == "__main__":
    import asyncio 
    import os
    os.system('cls')

    async def testa_texto_compactar():
        texto = "Olá, como está? Eu sou um texto longo que precisa ser compactado."
        print("texto original: ", texto)
        texto_compacto = await texto_compactar(texto)
        print("texto compactado: ", texto_compacto)

    asyncio.run(testa_texto_compactar())
