from agents import function_tool, OpenAIChatCompletionsModel
from .agent_openai import OpenAi  # Changed from OpenAI to your custom OpenAi class
from .agent_llm import LLM
# agent_accountant.py
from ..cache import cache  # Ajuste o caminho conforme a estrutura do seu projeto
from ..functions.database.mysql import executa_query_mysql
from pydantic import BaseModel

from fastapi import APIRouter
from .agent_mysql import Mysql
from pydantic import BaseModel
from ..functions.util import generate_unique_id
from ..functions.util import generate_idx
from tabulate import tabulate
from datetime import datetime, timedelta
from pprint import pprint
import calendar
from typing import Union, List, Dict, Any
import json
import asyncio
import re




router = APIRouter()

class Lancamento(BaseModel):
    pass
class Accountant:
    def __init__(self):
        self.mysql = Mysql()

    async def create_account(self, data: dict):
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CONTABIL_CONTA ({columns}) VALUES ({values});"
            inserts.append(query)

        query_insert = " ".join(inserts)
        result = await self.mysql.query(query_insert)
        return result

    async def create_account_type(self, data: dict):
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CONTABIL_CONTA_TIPO ({columns}) VALUES ({values});"
            inserts.append(query)

        query_insert = " ".join(inserts)
        result = await self.mysql.query(query_insert)
        return result

    async def fetch_account(self, columns: str, negocio_idx: str, area_id: str, formato: str = None):

        #Carregamento das contas do negócio
        table = "CONTABIL_CONTA"
        query = f"SELECT {columns} FROM {table} WHERE NEGOCIO_IDX = '{negocio_idx}' AND EXCLUIDO = 0 ORDER BY CODIGO"

        
        result = await self.mysql.query(query)  

        #Caso o negócio ainda não tenha contas, será carregado as contas modelo de acordo com o tipo de negócio
        if not result:     
            result = await self.fetch_accountant_model(area_id, negocio_idx)


        #Formatação em forma de tupla para uso na definição (system) agente_dre
        if formato == "tupla":
            result = await self.format_tuple(result, columns)


        return result



    async def format_tuple(self, dados_json, colunas):

         """
         Converte uma lista de dicionários em uma lista de tuplas com base nas colunas especificadas.
     
         Args:
             dados_json (list): Lista de dicionários contendo os dados.
             colunas (str): String com os nomes das colunas separados por vírgula.
     
         Returns:
             list: Lista de tuplas contendo os valores das colunas especificadas.
         """
         # Verifica se o argumento dados_json é uma lista
         if not isinstance(dados_json, list):
             raise ValueError("O argumento 'dados_json' deve ser uma lista de dicionários.")
         
         # Transforma a string de colunas em uma lista
         colunas_lista = [col.strip() for col in colunas.split(",")]
         
         # Verifica se todas as colunas especificadas existem nos dicionários
         for item in dados_json:
             if not all(col in item for col in colunas_lista):
                 raise KeyError(f"Uma ou mais colunas especificadas não existem nos dados: {colunas_lista}")
         
         # Cria a lista de tuplas
         tuplas = [tuple(item[col] for col in colunas_lista) for item in dados_json]
         
         return tuplas





    async def fetch_accounts_dre(self, negocio_idx: str, columns: str):
        print("===== fetch_accounts_dre() =====")
        print("negocio_idx", negocio_idx)
        print("columns", columns)
        # Remove vírgula extra no final se existir
        columns = columns.rstrip(',')
        table = "CONTABIL_CONTA"
        query = f"""
        SELECT {columns}
        FROM {table}
        WHERE 
        NEGOCIO_IDX = '{negocio_idx}'
        AND CODIGO LIKE '3%'
        AND EXCLUIDO = 0
        """
        #print("query", query)
        result = await self.mysql.query(query)
        return result


    async def fetch_account_types(self, columns: str):
        query = f"""
        SELECT {columns}
        FROM CONTABIL_CONTA_TIPO
        """
        result = await self.mysql.query(query)
        return result

    async def fetch_account_analytics(self, columns: str, negocio_idx: str):
        query = f"""
        SELECT {columns}
        FROM CONTABIL_CONTA c1
        WHERE 
            NEGOCIO_IDX = '{negocio_idx}'
            AND EXCLUIDO = 0
            AND NOT EXISTS (
                SELECT 1 
                FROM CONTABIL_CONTA c2 
                WHERE c2.IDX_PAI = c1.IDX
                AND c2.EXCLUIDO = 0
            )
        ORDER BY CODIGOp
        """
        result = await self.mysql.query(query)
        return result

    async def update_account(self, data: dict):
        updates = []
        for record in data["atualizados"]:
            set_clause = ", ".join(
                [f"{key} = '{value}'" for key, value in record.items() if key != "IDX"])
            query = f"UPDATE CONTABIL_CONTA SET {set_clause} WHERE IDX = '{record['IDX']}';"
            updates.append(query)
        query_update = " ".join(updates)
        result = await self.mysql.query(query_update)
        return result

    async def fetch_accountant_model(self, negocio_area: str, negocio_idx: str):
        # Consulta para buscar as contas modelo baseadas no tipo de negócio
        print("##### fetch_accountant_model() #####")
        print("negocio_area",negocio_area)
        print("negocio_idx",negocio_idx)

        def find_parent_idx(codigo, plano_contas_dict):
            """
            Encontra o IDX do pai baseado no código da conta
            """
            # Se código for raiz (1, 2 ou 3), retorna '0'
            if len(codigo.split('.')) == 1:
                return '0'

            # Remove o último segmento do código para encontrar o pai
            parent_code = '.'.join(codigo.split('.')[:-1])
            # print("parent_code",parent_code)
            idx_pai = plano_contas_dict.get(parent_code, '0')
            # print("idx_pai",idx_pai)
            return idx_pai


        #verifica se o tipo de negócio tem um plano de contas próprio. caso não tenha, será carregado o plano de contas geral
        total_contas = await self.mysql.query(f"SELECT COUNT(*) FROM CONTABIL_CONTA_NEGOCIO_TIPO WHERE NEGOCIO_AREA_ID = {negocio_area} AND EXCLUIDO = 0")
        print("total_contas",total_contas)
        total_contas = total_contas[0]['COUNT(*)']
        print("total_contas",total_contas)

        if total_contas == 0:
            print("não tem plano de contas modelo para a area " + str(negocio_area) )
            negocio_area = 0

        query = f"""
        SELECT 
            ID,
            NOME,
            CODIGO ,
            NIVEL,
            IDX_PAI,
            NATUREZA_ID,
            SISTEMA_ID 
        FROM CONTABIL_CONTA_NEGOCIO_TIPO 
        WHERE NEGOCIO_AREA_ID = {negocio_area}  
        AND EXCLUIDO = 0
        ORDER BY CODIGO
        """     




        # print("query",query)
        result = await self.mysql.query(query)

        # Vericar se tem dados no result e caso sim, gerar uma query insrt com todas as contas
        # devera ser incluido nos daods de cada linha a coluan NEGOCIO_IDX com a variavel negocio_idx como valor
        # Os dados deverão ser inseridos na tabela CONTABIL_CONTA

        # Verifica se existem dados no resultado
        if result:
            inserts = []
            plano_contas_dict = {}
            for record in result:

                # Prepara os dados para inserção incluindo o NEGOCIO_IDX
                insert_data = {
                    'IDX_PAI': find_parent_idx(record['CODIGO'], plano_contas_dict),
                    'IDX': generate_unique_id(),  # Gerando ID único
                    'CODIGO': record['CODIGO'],
                    'NIVEL': record['NIVEL'],
                    'NOME': record['NOME'],
                    'NEGOCIO_IDX': negocio_idx,
                    'NATUREZA_ID': record['NATUREZA_ID'],
                    'SISTEMA_ID': record['SISTEMA_ID']
                }
                plano_contas_dict[insert_data['CODIGO']] = insert_data['IDX']

                # Monta a query de inserção
                columns = ", ".join(insert_data.keys())
                values = ", ".join(
                    [f"'{str(value)}'" for value in insert_data.values()])
                query_insert = f"INSERT INTO CONTABIL_CONTA ({columns}) VALUES ({values});"
                inserts.append(query_insert)

            # Executa todas as inserções em uma única query
            if inserts:
                query_final = " ".join(inserts)
                await self.mysql.query(query_final)
                # print("plano_contas_dict",plano_contas_dict)

        columns = "IDX,IDX_PAI,TIPO_ID,NATUREZA_ID,CODIGO,NOME"
        result = await self.fetch_account(columns, negocio_idx, negocio_area)

        return result

    async def accounts_search(self, business_id: str, keywords: str, columns: str):
        # Remove pontos da palavra-chave de busca
        normalized_keywords = keywords.replace(".", "")

        # Modificada a query para incluir a verificação de contas sem filhas (analíticas)
        query = f"""
        SELECT {columns} 
        FROM CONTABIL_CONTA c1
        WHERE NEGOCIO_IDX = '{business_id}' 
        AND (
            NOME LIKE '%{keywords}%' 
            OR CODIGO LIKE '%{keywords}%'
            OR REPLACE(CODIGO, '.', '') LIKE '%{normalized_keywords}%'
            OR CODIGO LIKE '%.{keywords}.%'
            OR CODIGO LIKE '%.{keywords}'
            OR CODIGO LIKE '{keywords}.%'
        ) 
        AND EXCLUIDO = 0
        AND NOT EXISTS (
            SELECT 1 
            FROM CONTABIL_CONTA c2 
            WHERE c2.IDX_PAI = c1.IDX
            AND c2.EXCLUIDO = 0
        )
        ORDER BY CODIGO;
        """
        #print("query", query)
        result = await self.mysql.query(query)
        return result

    async def update_transaction_query(self,lancamento):
        #print("##### update_transaction_query() #####")
        #print("lancamento", lancamento)
        """
        Gera um script SQL para atualizar a tabela CONTABIL_CONTA com base   nos lançamentos contábeis.
        
        Args:
            lancamentos (list): Lista de dicionários representando   lançamentos contábeis.
            
        Returns:
            str: Script SQL completo para atualização das contas.
        """
        #query = "START TRANSACTION;\n"
        query = ""
        
        
        idx = lancamento.get("IDX", "")
        conta_debito = lancamento.get("CONTA_DEBITO", 0)
        conta_credito = lancamento.get("CONTA_CREDITO", 0)
        valor = lancamento.get("VALOR", 0)
        lancamento_idx = lancamento.get("LANCAMENTO_IDX", "")
        #print("conta_debito", conta_debito)
        #print("conta_credito", conta_credito)
        #print("valor", valor)
        #print("lancamento_idx", lancamento_idx)
        
        # Processamento da conta de débito (se existir)
        if conta_debito != 0:
            query = f"""
    -- Atualizando conta de débito {conta_debito} para lançamento {idx}
    UPDATE CONTABIL_CONTA 
    SET 
        DEBITO = CASE 
            WHEN NATUREZA_ID = 'D' THEN DEBITO + {valor}  -- Caso 1.1
            WHEN NATUREZA_ID = 'C' THEN DEBITO - {valor}  -- Caso 2.1
            ELSE DEBITO
        END,
        SALDO_ATUAL = CASE 
            WHEN NATUREZA_ID = 'D' THEN SALDO_ATUAL + {valor}  -- Caso 1.2
            WHEN NATUREZA_ID = 'C' THEN SALDO_ATUAL - {valor}  -- Caso 2.2
            ELSE SALDO_ATUAL
        END
    WHERE ID = {conta_debito};
    """
            
            # Processamento da conta de crédito (se existir)
        if conta_credito != 0:
                query += f"""
    -- Atualizando conta de crédito {conta_credito} para lançamento {idx}
    UPDATE CONTABIL_CONTA 
    SET 
        CREDITO = CASE 
            WHEN NATUREZA_ID = 'D' THEN CREDITO - {valor}  -- Caso 3.1
            WHEN NATUREZA_ID = 'C' THEN CREDITO + {valor}  -- Caso 4.1
            ELSE CREDITO
        END,
        SALDO_ATUAL = CASE 
            WHEN NATUREZA_ID = 'D' THEN SALDO_ATUAL - {valor}  -- Caso 3.2
            WHEN NATUREZA_ID = 'C' THEN SALDO_ATUAL + {valor}  -- Caso 4.2
            ELSE SALDO_ATUAL
        END
    WHERE ID = {conta_credito};
    """
        
        #query += "COMMIT;"
        #print(query)
        #result = await mysql.query(query)
        #print(result)
        #print("yyyy query apos processamento  yyyyy", query)
        #print("yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy")
        return query

    async def create_transaction(self, negocio_idx: str, data: dict):
        #print("##### create_transaction() #####")
        #print("data", data);
        saldo_query = "";
        try:
            # Prepara a query para inserir o lançamento principal
            lancamento = data['contabil_lancamento']
            if 'OPERACAO' in lancamento:
                del lancamento['OPERACAO']
            lancamento_query = f"""
            INSERT INTO CONTABIL_LANCAMENTO 
            (IDX, DATA, DOCUMENTO, HISTORICO, NEGOCIO_IDX, USUARIO_IDX)
            VALUES 
            (
                '{lancamento['IDX']}',
                '{lancamento['DATA']}',
                '{lancamento['DOCUMENTO']}',
                '{lancamento['HISTORICO']}',
                '{lancamento['NEGOCIO_IDX']}',
                '{lancamento['USUARIO_IDX']}'
            );
            """

            # Primeiro exclui todas as contas existentes (por segurança)
            delete_query = f"""
            DELETE FROM CONTABIL_LANCAMENTO_CONTA 
            WHERE LANCAMENTO_IDX = '{lancamento['IDX']}';
            """

            # Prepara as queries para inserir os lançamentos das contas
            conta_queries = []
            for conta in data['contabil_lancamento_conta']:
                query = f"""
                INSERT INTO CONTABIL_LANCAMENTO_CONTA 
                (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX)
                VALUES 
                (
                    '{conta['IDX']}',
                    {conta['CONTA_DEBITO']},
                    {conta['CONTA_CREDITO']},
                    {conta['VALOR']},
                    '{conta['LANCAMENTO_IDX']}'
                );
                """
                conta_queries.append(query)
                #print("conta", conta)
                #saldo_query =  saldo_query + await self.update_transaction_query(conta)    
                #print("saldo_query", saldo_query)

            # Executa todas as queries em uma única transação
            queries = [lancamento_query, delete_query] + conta_queries
            query_final = " ".join(queries)
            #print("query_final", query_final)
            result = await self.mysql.query(query_final)
            return {"status": "success", "message": "Lançamento criado com sucesso"}

        except Exception as e:
            print("Erro ao criar lançamento:", str(e))
            return {"status": "error", "message": str(e)}

    async def fetch_transactions(self, negocio_idx: str, filters: dict):
        offset = filters.get("inicio")
        limit = filters.get("limite")

        count_query = f"""
        WITH LancamentosTotal AS (
            SELECT 
                l.*,
                (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'CONTA_DEBITO', lc.CONTA_DEBITO,
                            'CONTA_CREDITO', lc.CONTA_CREDITO
                        )
                    )
                    FROM CONTABIL_LANCAMENTO_CONTA lc
                    WHERE lc.LANCAMENTO_IDX = l.IDX
                ) as CONTAS,
                (SELECT SUM(VALOR) / 2 
                 FROM CONTABIL_LANCAMENTO_CONTA 
                 WHERE LANCAMENTO_IDX = l.IDX) as TOTAL
            FROM CONTABIL_LANCAMENTO l
            WHERE l.NEGOCIO_IDX = '{negocio_idx}'
            AND l.EXCLUIDO = 0
        )
        SELECT COUNT(*) as QTDE
        FROM LancamentosTotal l
        WHERE 1=1
        """

        filters_query = ""

        # Adiciona os filtros usando a CTE
        if filters.get("data_inicio"):
            filters_query += f" AND l.DATA >= '{filters['data_inicio']}'"
        if filters.get("data_fim"):
            filters_query += f" AND l.DATA <= '{filters['data_fim']}'"
        if filters.get("documento"):
            filters_query += f" AND l.DOCUMENTO LIKE '%{filters['documento']}%'"
        if filters.get("historico"):
            filters_query += f" AND l.HISTORICO LIKE '%{filters['historico']}%'"
        if filters.get("valor_de"):
            filters_query += f" AND l.TOTAL >= {filters['valor_de']}"
        if filters.get("conta_debito"):
            filters_query += f""" AND JSON_CONTAINS(l.CONTAS, '{{"CONTA_DEBITO": {filters['conta_debito']}}}')"""
        if filters.get("conta_credito"):
            filters_query += f""" AND JSON_CONTAINS(l.CONTAS, '{{"CONTA_CREDITO": {filters['conta_credito']}}}')"""

        count_query += filters_query

        qtde = await self.mysql.query(count_query)

        base_query = f"""               
        WITH LancamentosDetalhados AS (
            SELECT 
                l.*,
                (
                    SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'IDX', lc.IDX,
                            'CONTA_DEBITO', lc.CONTA_DEBITO,
                            'CONTA_CREDITO', lc.CONTA_CREDITO,
                            'VALOR', lc.VALOR,
                            'CONTA_DEBITO_NOME', cd.NOME,
                            'CONTA_DEBITO_CODIGO', cd.CODIGO,
                            'CONTA_CREDITO_NOME', cc.NOME,
                            'CONTA_CREDITO_CODIGO', cc.CODIGO
                        )
                    )
                    FROM CONTABIL_LANCAMENTO_CONTA lc
                    LEFT JOIN CONTABIL_CONTA cd ON cd.ID = lc.CONTA_DEBITO
                        AND cd.NEGOCIO_IDX = '{negocio_idx}'
                        AND cd.EXCLUIDO = 0
                    LEFT JOIN CONTABIL_CONTA cc ON cc.ID = lc.CONTA_CREDITO
                        AND cc.NEGOCIO_IDX = '{negocio_idx}'
                        AND cc.EXCLUIDO = 0
                    WHERE lc.LANCAMENTO_IDX = l.IDX
                ) as CONTAS,
                (
                    SELECT SUM(VALOR) / 2
                    FROM CONTABIL_LANCAMENTO_CONTA
                    WHERE LANCAMENTO_IDX = l.IDX
                ) as TOTAL
            FROM CONTABIL_LANCAMENTO l
            WHERE l.NEGOCIO_IDX = '{negocio_idx}'
            AND l.EXCLUIDO = 0
        )
        SELECT 
            IDX,
            DATA,
            DOCUMENTO,
            HISTORICO,
            USUARIO_IDX,
            CONTAS,
            TOTAL
        FROM LancamentosDetalhados l
        WHERE 1=1 

        """

        base_query += filters_query

        # Adiciona ORDER BY baseado no filtro 'ordem'
        ordem = filters.get("ordem", "DATA_DESC")
        if ordem == "DATA_DESC":
            base_query += " ORDER BY CRIADO DESC"
        elif ordem == "DATA_ASC":
            base_query += " ORDER BY CRIADO ASC"

        base_query += f" LIMIT {filters['inicio']}, {filters['limite']}"
        data = await self.mysql.query(base_query)
        print ("base_query:")
        print(base_query)
        print("data")
        print(data)
        return {"qtde": qtde[0]['QTDE'], "dados": data}

    async def fetch_transaction(self, lancamento_idx: str):
        query = f"""
        SELECT 
            l.*,
            (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'IDX', lc.IDX,
                        'CONTA_DEBITO', lc.CONTA_DEBITO,
                        'CONTA_CREDITO', lc.CONTA_CREDITO,
                        'VALOR', lc.VALOR,
                        'CONTA_DEBITO_NOME', cd.NOME,
                        'CONTA_DEBITO_CODIGO', cd.CODIGO,
                        'CONTA_CREDITO_NOME', cc.NOME,
                        'CONTA_CREDITO_CODIGO', cc.CODIGO
                    )
                )
                FROM CONTABIL_LANCAMENTO_CONTA lc
                LEFT JOIN CONTABIL_CONTA cd ON cd.ID = lc.CONTA_DEBITO
                LEFT JOIN CONTABIL_CONTA cc ON cc.ID = lc.CONTA_CREDITO
                WHERE lc.LANCAMENTO_IDX = l.IDX
            ) as CONTAS
        FROM CONTABIL_LANCAMENTO l
        WHERE l.IDX = '{lancamento_idx}'
        AND l.EXCLUIDO = 0
        """
        # print("query",query)
        result = await self.mysql.query(query)
        # print("result",result)

        if not result:
            return {"ERRO": "Lançamento não encontrado"}

        # Formata o resultado para garantir que CONTAS seja um array
        lancamento = result[0]
        if lancamento['CONTAS'] is None:
            lancamento['CONTAS'] = []
        # else:
            # Converte os valores decimais para float para serialização JSON
            # for conta in lancamento['CONTAS']:
            # conta['VALOR'] = float(conta['VALOR'])

        return lancamento

    async def update_transaction(self, negocio_idx: str, data: dict):
        print("##### update_transaction() #####")
        print("data", data)
        try:
            # Atualiza o lançamento principal
            lancamento = data['contabil_lancamento']
            if 'OPERACAO' in lancamento:
                del lancamento['OPERACAO']
            lancamento_query = f"""
            UPDATE CONTABIL_LANCAMENTO 
            SET DATA='{lancamento['DATA']}', 
                DOCUMENTO='{lancamento['DOCUMENTO']}', 
                HISTORICO='{lancamento['HISTORICO']}' 
            WHERE IDX='{lancamento['IDX']}' 
            AND NEGOCIO_IDX='{negocio_idx}';
            """

            # Exclui todas as contas existentes do lançamento
            delete_query = f"""
            DELETE FROM CONTABIL_LANCAMENTO_CONTA 
            WHERE LANCAMENTO_IDX = '{lancamento['IDX']}';
            """

            # Prepara as queries para inserir as novas contas
            conta_queries = []
            for conta in data['contabil_lancamento_conta']:

                query = f"""
                INSERT INTO CONTABIL_LANCAMENTO_CONTA 
                (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX)
                VALUES 
                (
                    '{conta['IDX']}',
                    {conta['CONTA_DEBITO']},
                    {conta['CONTA_CREDITO']},
                    {conta['VALOR']},
                    '{lancamento['IDX']}'
                );
                """
                conta_queries.append(query)

            # Executa todas as queries em uma única transação
            queries = [lancamento_query, delete_query] + conta_queries
            query_final = " ".join(queries)

            result = await self.mysql.query(query_final)
            return {"status": "success", "message": "Lançamento atualizado com sucesso"}

        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def delete_transaction(self, idx: str):
        print("@@@@@ delete_transaction", idx)
        try:
            # Marca o lançamento principal como excluído
            lancamento_query = f"""
            UPDATE CONTABIL_LANCAMENTO 
            SET EXCLUIDO = 1 
            WHERE IDX = '{idx}';
            """

            # Marca todas as contas do lançamento como excluídas
            contas_query = f"""
            UPDATE CONTABIL_LANCAMENTO_CONTA 
            SET EXCLUIDO = 1 
            WHERE LANCAMENTO_IDX = '{idx}';
            """

            # Executa as queries em uma única transação
            query_final = " ".join([lancamento_query, contas_query])
            print("query_final", query_final)
            await self.mysql.query(query_final)

            return {"status": "success", "message": "Lançamento excluído com sucesso"}

        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def totaliza_saldo_anterior(self, data_inicio: str, conta_id: str, conta_natureza: str):
        print("##### totaliza_saldo_anterior() #####")
        print("data_inicio", data_inicio)
        print("conta_id", conta_id)
        print("conta_natureza", conta_natureza)

        # Query para calcular o saldo anterior da conta
        query = f"""
        SELECT 
            COALESCE(SUM(
                CASE 
                    WHEN '{conta_natureza}' = 'D' THEN
                        CASE
                            WHEN lc.CONTA_DEBITO = {conta_id} THEN lc.VALOR
                            WHEN lc.CONTA_CREDITO = {conta_id} THEN -lc.VALOR
                            ELSE 0
                        END
                    ELSE
                        CASE
                            WHEN lc.CONTA_DEBITO = {conta_id} THEN -lc.VALOR
                            WHEN lc.CONTA_CREDITO = {conta_id} THEN lc.VALOR
                            ELSE 0
                        END
                END
            ), 0) as SALDO_ANTERIOR
        FROM CONTABIL_LANCAMENTO_CONTA lc
        JOIN CONTABIL_LANCAMENTO l ON l.IDX = lc.LANCAMENTO_IDX
        WHERE l.EXCLUIDO = 0
        AND l.DATA < '{data_inicio}'
        """
        print("query completa: ", query)

        result = await self.mysql.query(query)
        result = result[0]['SALDO_ANTERIOR']
        return result

    async def totaliza_movimento(self, data_inicio: str, data_termino: str, conta_id: str, conta_natureza: str):
        print("##### totaliza_movimento() #####")
        print("data_inicio", data_inicio)
        print("data_termino", data_termino)
        print("conta_id", conta_id)
        print("conta_natureza", conta_natureza)

        # Query para calcular o movimento da conta no período
        query = f"""
        SELECT 
            COALESCE(SUM(
                CASE 
                    WHEN '{conta_natureza}' = 'D' THEN
                        CASE
                            WHEN lc.CONTA_DEBITO = {conta_id} THEN lc.VALOR
                            WHEN lc.CONTA_CREDITO = {conta_id} THEN -lc.VALOR
                            ELSE 0
                        END
                    ELSE
                        CASE
                            WHEN lc.CONTA_DEBITO = {conta_id} THEN -lc.VALOR
                            WHEN lc.CONTA_CREDITO = {conta_id} THEN lc.VALOR
                            ELSE 0
                        END
                END
            ), 0) as MOVIMENTO
        FROM CONTABIL_LANCAMENTO_CONTA lc
        JOIN CONTABIL_LANCAMENTO l ON l.IDX = lc.LANCAMENTO_IDX
        WHERE l.EXCLUIDO = 0
        AND l.DATA >= '{data_inicio}'
        AND l.DATA <= '{data_termino}'
        """
        print("query completa: ", query)

        result = await self.mysql.query(query)
        result = result[0]['MOVIMENTO']
        return result


    def _determinar_periodo(self, data_inicio, data_termino):
        # Converter strings para objetos datetime
        data_inicio = datetime.strptime(data_inicio, '%Y-%m-%d')
        data_termino = datetime.strptime(data_termino, '%Y-%m-%d')
        
        delta = data_termino - data_inicio
        if delta.days <= 14:
            return 'D'
        elif 15 <= delta.days <= 90:
            return 'S'
        elif 91 <= delta.days <= 365:
            return 'M'
        else:
            return 'A'

    # Fun o para formatar o per odo com base no tipo
    def formatar_periodo(self, data: datetime, tipo_periodo: str, data_inicio: datetime = None) -> str:
        """Formata o período de acordo com o tipo
        D: DD/MM/YYYY
        S: NN (número sequencial da semana no período)
        M: mmm/YY (3 primeiras letras do mês em português)
        A: YYYY
        """
        if isinstance(data, str):
            try:
                data = datetime.strptime(data, '%Y-%m-%d')
            except ValueError:
                try:
                    data = datetime.strptime(data, '%d/%m/%Y')
                except ValueError:
                    return None

        if tipo_periodo == 'D':
            return data.strftime('%d_%m_%Y')
        elif tipo_periodo == 'S':
            # Se tiver data_inicio, calcula o número da semana a partir dela
            if data_inicio:
                dias_desde_inicio = (data - data_inicio).days
                numero_semana = (dias_desde_inicio // 7) + 1
            else:
                numero_semana = data.isocalendar()[1]
            return f"{numero_semana:02d}"
        elif tipo_periodo == 'M':
            meses = ['jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set', 'out', 'nov', 'dez']
            return f"{meses[data.month-1]}/{str(data.year)[2:]}"
        elif tipo_periodo == 'A':
            return str(data.year)
        return None

    def get_period_type(self, data_inicio: str, data_termino: str) -> str:
        """Determina o tipo de período baseado no intervalo de datas"""
        inicio = datetime.strptime(data_inicio, '%Y-%m-%d')
        termino = datetime.strptime(data_termino, '%Y-%m-%d')
        dias = (termino - inicio).days + 1

        if dias <= 14:
            return 'D'  # Diário
        elif dias <= 90:
            return 'S'  # Semanal
        elif dias <= 365:
            return 'M'  # Mensal
        else:
            return 'A'  # Anualc
    
 
    def get_period_type(self, data_inicio: Union[str, datetime], data_termino: Union[str, datetime]):
        """Determina o tipo de período baseado no intervalo de datas"""
        # Converte para datetime se for string
        if isinstance(data_inicio, str):
            inicio = datetime.strptime(data_inicio, '%Y-%m-%d')
        else:
            inicio = data_inicio

        if isinstance(data_termino, str):
            termino = datetime.strptime(data_termino, '%Y-%m-%d')
        else:
            termino = data_termino

        dias = (termino - inicio).days + 1

        if dias <= 14:
            return 'D'  # Diário
        elif dias <= 90:
            return 'S'  # Semanal
        elif dias <= 365:
            return 'M'  # Mensal
        else:
            return 'A'  # Anual

    def get_period_start_end(self, data: datetime, period_type: str, data_inicio: datetime = None, data_termino: datetime = None) -> tuple:
        """Retorna início e fim do período
        Args:
            data: Data do lançamento
            period_type: Tipo do período (D, S, M, A)
            data_inicio: Data inicial do filtro, usada para garantir que o primeiro período comece nesta data
            data_termino: Data final do filtro, usada para garantir que o último período não ultrapasse esta data
        """
        if period_type == 'D':
            return data, data
        elif period_type == 'S':
            # Calcula o início da semana (segunda-feira)
            inicio = data - timedelta(days=data.weekday())
            
            # Garante que o início não seja anterior à data_inicio
            if data_inicio and inicio < data_inicio:
                inicio = data_inicio
            
            fim = inicio + timedelta(days=6)
            # Se tiver data_termino e o fim ultrapassar, ajusta para a data final
            if data_termino and fim > data_termino:
                fim = data_termino
            return inicio, fim
        elif period_type == 'M':
            if data_inicio and data.month == data_inicio.month and data.year == data_inicio.year:
                inicio = data_inicio
            else:
                inicio = data.replace(day=1)
            
            ultimo_dia = calendar.monthrange(data.year, data.month)[1]
            fim = data.replace(day=ultimo_dia)
            if data_termino and fim > data_termino:
                fim = data_termino
            return inicio, fim
        else:  # A
            if data_inicio and data.year == data_inicio.year:
                inicio = data_inicio
            else:
                inicio = data.replace(month=1, day=1)
            fim = data.replace(month=12, day=31)
            if data_termino and fim > data_termino:
                fim = data_termino
            return inicio, fim

   
    async def dre_movimento_grupos(self, movimento):
        """
        Recebe o movimento['contas'] e totaliza por grupo/periodo.
        Os grupos são os mesmos da DRE: RECEITA, DEDUCAO, CUSTO, DESPESA, FINANCEIRO
        """
    
        print("movimento_grupos()",movimento)
    
        # Mapeamento de TIPO_ID para grupos do DRE
        grupo_map = {
            3: 'RECEITA',
            4: 'DEDUCAO',
            5: 'CUSTOS VARIÁVEIS',
            6: 'CUSTOS FIXOS',
            7: 'DESPESAS',
            8: 'FINANCEIRO'
        }

        # Estrutura para armazenar os totais por grupo e período
        grupos = {
            "totais": {
                "receitaBruta": {"total": 0, "contas": [], "periodos": {}},
                "deducoes": {"total": 0, "contas": [], "periodos": {}},
                "receitaLiquida": {"total": 0, "periodos": {}},
                "custos variaveis": {"total": 0, "contas": [], "periodos": {}},
                "custos fixos": {"total": 0, "contas": [], "periodos": {}},
                "custos": {"total": 0, "contas": [], "periodos": {}},    
                "resultadoBruto": {"total": 0, "contas": [], "periodos": {}},            
                "despesas": {"total": 0, "contas": [], "periodos": {}},
                "financeiro": {"total": 0, "contas": [], "periodos": {}},
                "resultadoLiquido": {"total": 0, "periodos": {}}
            }
        }

        # Processa cada conta do movimento['contas']
        for movimento_conta in movimento['contas']:
            grupo = grupo_map.get(movimento_conta['conta_grupo'])
            if not grupo:
                continue

            conta = {
                'idx': movimento_conta['conta_id'],
                'codigo': movimento_conta['conta_codigo'],
                'saldo': movimento_conta['total']
            }

            # Atualiza os totais e período baseado no grupo
            grupo_key = None
            if grupo == 'RECEITA':
                grupo_key = 'receitaBruta'
            elif grupo == 'DEDUCAO':
                grupo_key = 'deducoes'
            elif grupo == 'CUSTOS VARIÁVEIS':
                grupo_key = 'custos variaveis'
            elif grupo == 'CUSTOS FIXOS':
                grupo_key = 'custos fixos'
            elif grupo == 'DESPESAS':
                grupo_key = 'despesas'
            elif grupo == 'FINANCEIRO':
                grupo_key = 'financeiro'

            if grupo_key:
                # Atualiza o total do grupo
                grupos['totais'][grupo_key]['total'] += movimento_conta['total']
                
                # Adiciona a conta à lista de contas do grupo se ainda não estiver
                if conta not in grupos['totais'][grupo_key]['contas']:
                    grupos['totais'][grupo_key]['contas'].append(conta)
                
                # Processa os períodos da conta
                for periodo, periodo_data in movimento_conta['periodos'].items():
                    # Inicializa o período no grupo se necessário
                    if periodo not in grupos['totais'][grupo_key]['periodos']:
                        grupos['totais'][grupo_key]['periodos'][periodo] = 0
                    
                    # Atualiza o total do período no grupo
                    grupos['totais'][grupo_key]['periodos'][periodo] += periodo_data['total']

                    # Inicializa os períodos para os totais calculados
                    for key in ['receitaLiquida', 'resultadoBruto', 'resultadoLiquido']:
                        if periodo not in grupos['totais'][key]['periodos']:
                            grupos['totais'][key]['periodos'][periodo] = 0

        # Calcula os totais e subtotais para cada período
        todos_periodos = set()
        for grupo_key in ['receitaBruta', 'deducoes', 'custos', 'despesas', 'financeiro']:
            todos_periodos.update(grupos['totais'][grupo_key]['periodos'].keys())

        for periodo in todos_periodos:
            receita_bruta = grupos['totais']['receitaBruta']['periodos'].get(periodo, 0)
            deducoes = grupos['totais']['deducoes']['periodos'].get(periodo, 0)
            custosVariaveis = grupos['totais']['custos variaveis']['periodos'].get(periodo, 0)
            custosFixos = grupos['totais']['custos fixos']['periodos'].get(periodo, 0)
            custos = custosVariaveis + custosFixos
            despesas = grupos['totais']['despesas']['periodos'].get(periodo, 0)
            financeiro = grupos['totais']['financeiro']['periodos'].get(periodo, 0)

            # Receita Líquida = Receita Bruta + Deduções
            grupos['totais']['receitaLiquida']['periodos'][periodo] = receita_bruta + deducoes
            # Resultado Bruto = Receita Líquida + Custos
            grupos['totais']['resultadoBruto']['periodos'][periodo] = receita_bruta + deducoes + custosVariaveis + custosFixos
            # Resultado Líquido = Resultado Bruto + Despesas + Financeiro
            grupos['totais']['custos']['periodos'][periodo] = custosVariaveis + custosFixos            
            grupos['totais']['resultadoLiquido']['periodos'][periodo] = receita_bruta + deducoes + custos + despesas + financeiro

        # Calcula os totais gerais
        receita_bruta = grupos['totais']['receitaBruta']['total']
        deducoes = grupos['totais']['deducoes']['total']
        custos = grupos['totais']['custos']['total']
        despesas = grupos['totais']['despesas']['total']

        grupos['totais']['receitaLiquida']['total'] = receita_bruta + deducoes
        grupos['totais']['resultadoBruto']['total'] = receita_bruta + deducoes + custos
        grupos['totais']['resultadoLiquido']['total'] = receita_bruta + deducoes + custos + despesas + financeiro

        # Agrega os custos (variáveis e fixos) no grupo 'custos'
        custos_variaveis = grupos["totais"]["custos variaveis"]
        custos_fixos = grupos["totais"]["custos fixos"]
        custos_agg = grupos["totais"]["custos"]

        # Unifica os períodos existentes em custos variáveis e fixos
        all_periodos = set(custos_variaveis["periodos"].keys()) | set(custos_fixos["periodos"].keys())
        for periodo in all_periodos:
            custos_agg["periodos"][periodo] = custos_variaveis["periodos"].get(periodo, 0) + custos_fixos["periodos"].get(periodo, 0)

        # Soma dos totais dos custos variáveis e fixos
        custos_agg["total"] = custos_variaveis["total"] + custos_fixos["total"]

        print("movimento antes de querer saber o periodo_tipo",movimento)
        # Determina o tipo de periodização baseado no movimento.periodo_tipo
        if movimento['periodo_tipo'] == 'D':
            grupos['periodizacao'] = 'diária'
        elif movimento['periodo_tipo'] == 'S':
            grupos['periodizacao'] = 'semanal'
        elif movimento['periodo_tipo'] == 'M':
            grupos['periodizacao'] = 'mensal'
        else:  # A
            grupos['periodizacao'] = 'anual'
   
   
        grupos['periodos'] = movimento['periodos']
        
        return grupos

    async def dre_movimento(self, filter: dict):
        print("##### DEBUG DRE MOVIMENTO #####")
        negocio_idx = filter['negocio_idx']
        data_inicio = filter['data_inicio']
        data_termino = filter['data_termino']

        print(f"Data Início: {data_inicio}")
        print(f"Data Término: {data_termino}")
        print(f"Negócio IDX: {negocio_idx}")
        
        # Garante que as datas estejam no formato string para a query SQL
        if isinstance(data_inicio, datetime):
            data_inicio_str = data_inicio.strftime('%Y-%m-%d')
        else:
            data_inicio_str = data_inicio

        if isinstance(data_termino, datetime):
            data_termino_str = data_termino.strftime('%Y-%m-%d')
        else:
            data_termino_str = data_termino

        # Converte as datas para datetime se necessário
        if isinstance(data_inicio, str):
            data_inicio = datetime.strptime(data_inicio, '%Y-%m-%d')
        if isinstance(data_termino, str):
            data_termino = datetime.strptime(data_termino, '%Y-%m-%d')

        period_type = self.get_period_type(data_inicio, data_termino)

        # Primeiro, vamos buscar todas as contas que tiveram movimento no período
        query_contas = """
            SELECT DISTINCT
                CC.ID,
                CC.NOME,
                CC.CODIGO,
                CC.NATUREZA_ID,
                CC.TIPO_ID
            FROM CONTABIL_CONTA CC
            JOIN CONTABIL_LANCAMENTO_CONTA CLC ON (CLC.CONTA_DEBITO = CC.ID OR CLC.CONTA_CREDITO = CC.ID)
            JOIN CONTABIL_LANCAMENTO CL ON CL.IDX = CLC.LANCAMENTO_IDX
            WHERE CC.NEGOCIO_IDX = '{}' 
            AND CC.EXCLUIDO = 0
            AND CL.EXCLUIDO = 0
            AND CC.NIVEL = 'A'
            AND CC.TIPO_ID IN (3, 4, 5, 6, 7)  -- Códigos para RECEITA, DEDUCAO, CUSTO, DESPESA, FINANCEIRO
            AND (CL.DATA BETWEEN '{}' AND '{}')
        """.format(negocio_idx, data_inicio_str, data_termino_str)

        contas = await self.mysql.query(query_contas)

        # Busca os lançamentos
        query = """
            SELECT 
                CC.ID,
                CC.CODIGO,
                CC.NATUREZA_ID,
                CC.TIPO_ID,
                CL.DATA,
                CLC.CONTA_CREDITO,
                CLC.CONTA_DEBITO,
                CLC.VALOR
            FROM CONTABIL_CONTA CC
            JOIN CONTABIL_LANCAMENTO_CONTA CLC ON (CLC.CONTA_DEBITO = CC.ID OR CLC.CONTA_CREDITO = CC.ID)
            JOIN CONTABIL_LANCAMENTO CL ON CL.IDX = CLC.LANCAMENTO_IDX
            WHERE CC.NEGOCIO_IDX = '{}' 
            AND CC.EXCLUIDO = 0
            AND CL.EXCLUIDO = 0
            AND CC.NIVEL = 'A'
            AND CC.TIPO_ID IN (3, 4, 5, 6, 7)  -- Códigos para RECEITA, DEDUCAO, CUSTO, DESPESA, FINANCEIRO
            AND (CL.DATA BETWEEN '{}' AND '{}')
            ORDER BY CL.DATA, CC.CODIGO
        """.format(negocio_idx, data_inicio_str, data_termino_str)

        results = await self.mysql.query(query)
        #print(f"Número de registros encontrados: {len(results)}")
        
        # Calcula todos os períodos uma única vez
        periodos = self._calcular_periodos(data_inicio, data_termino, period_type)
        #print("Periodos:", periodos)
        # Cria um dicionário para armazenar as contas e seus períodos
        contas_estrutura = {}
        
        # Inicializa a estrutura para todas as contas com os períodos pré-calculados
        for conta in contas:
            codigo = conta['CODIGO']
            if codigo not in contas_estrutura:
                contas_estrutura[codigo] = {
                    "conta_nome": conta["NOME"],
                    "conta_codigo": codigo,
                    "conta_id": conta["ID"],
                    "conta_natureza": conta["NATUREZA_ID"],
                    "conta_grupo": conta["TIPO_ID"],
                    "total": 0,
                    "periodos": {}
                }
                # Inicializa os períodos para esta conta
                for p in periodos:
                    contas_estrutura[codigo]['periodos'][p['periodo']] = {
                        "inicio": p["inicio"],
                        "termino": p["termino"],
                        "total": 0
                    }

        # Agora processa os lançamentos
        for row in results:
            data_str = str(row['DATA'])
            try:
                if isinstance(row['DATA'], datetime):
                    data = row['DATA']
                elif ',' in data_str:
                    year, month, day = map(int, data_str.split(','))
                    data = datetime(year, month, day)
                else:
                    data = datetime.strptime(data_str, '%Y-%m-%d')
            except (ValueError, TypeError):
                print(f"Erro ao converter data: {data_str}")
                continue

            periodo = self.formatar_periodo(data, period_type, data_inicio)
            codigo = row['CODIGO']
            
            # Calcula o valor do lançamento
            valor = row['VALOR']
            if row['CONTA_CREDITO'] == row['ID']:
                valor_final = valor
            else:
                valor_final = -valor

            # Atualiza o total da conta e do período
            contas_estrutura[codigo]['total'] += valor_final
            contas_estrutura[codigo]['periodos'][periodo]['total'] += valor_final

        # Converte o dicionário em uma lista para retorno
        contas = []
        for conta_data in contas_estrutura.values():
            # Ordena os período
            periodos_ordenados = dict(sorted(conta_data['periodos'].items()))
            conta_data['periodos'] = periodos_ordenados
            contas.append(conta_data)

        movimento = {
            'periodo_tipo': period_type,
            'periodos': periodos,
            'contas': contas
        }

        return movimento

    async def dre_saldo(self, filter: dict):
        #print("##### dre_saldo() #####")
        negocio_idx = filter['negocio_idx']
        data_inicio = filter['data_inicio']
        data_termino = filter['data_termino']

        query = f"""
            WITH SALDOS AS (
                SELECT 
                    CC.IDX as CONTA_IDX,
                    CC.CODIGO as CONTA_CODIGO,
                    CC.NOME as CONTA_NOME,
                    CC.TIPO_ID as CONTA_GRUPO,
                    CC.NATUREZA_ID as CONTA_NATUREZA,
                    COALESCE(SUM(
                        CASE 
                            WHEN CLC.CONTA_CREDITO = CC.ID THEN CLC.VALOR
                            WHEN CLC.CONTA_DEBITO = CC.ID THEN -CLC.VALOR
                            ELSE 0
                        END
                    ), 0) as SALDO
                FROM CONTABIL_CONTA CC
                LEFT JOIN CONTABIL_LANCAMENTO_CONTA CLC ON (CLC.CONTA_DEBITO = CC.ID OR CLC.CONTA_CREDITO = CC.ID)
                LEFT JOIN CONTABIL_LANCAMENTO CL ON CL.IDX = CLC.LANCAMENTO_IDX
                WHERE CC.NEGOCIO_IDX = '{negocio_idx}'
                AND CC.EXCLUIDO = 0
                AND CL.EXCLUIDO = 0
                AND CC.TIPO_ID IN (3, 4, 5, 6, 7)  -- Códigos para RECEITA, DEDUCAO, CUSTO, DESPESA, FINANCEIRO
                AND (CL.DATA BETWEEN '{data_inicio}' AND '{data_termino}')
                GROUP BY CC.IDX, CC.CODIGO, CC.NOME, CC.TIPO_ID, CC.NATUREZA_ID
            )
            SELECT 
                CONTA_GRUPO,
                CONTA_NATUREZA,
                SUM(SALDO) as TOTAL,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'idx', CONTA_IDX,
                        'codigo', CONTA_CODIGO,
                        'nome', CONTA_NOME,
                        'saldo', SALDO
                    )
                ) as CONTAS
            FROM SALDOS
            GROUP BY CONTA_GRUPO, CONTA_NATUREZA
            ORDER BY CONTA_GRUPO, CONTA_NATUREZA;
        """

        result = await self.mysql.query(query)

        # Estrutura o resultado
        dre = {
            
                "receitaBruta": {"total": 0, "contas": []},
                "deducoes": {"total": 0, "contas": []},
                "receitaLiquida": {"total": 0},
                "custos variaveis": {"total": 0, "contas": []},
                "custos fixos": {"total": 0, "contas": []},
                "custos": {"total": 0, "contas": []},
                "resultadoBruto": {"total": 0},
                "despesas": {"total": 0, "contas": []},
                "financeiro": {"total": 0, "contas": []},   
                "resultadoLiquido": {"total": 0}
            
        }

        # Mapeamento de TIPO_ID para grupos do DRE
        grupo_map = {
            3: 'RECEITA',
            4: 'DEDUCAO',
            5: 'CUSTOS VARIÁVEIS',
            6: 'CUSTOS FIXOS',
            7: 'DESPESAS',
            8: 'FINANCEIRO'
        }

        for row in result:
            grupo = grupo_map.get(row['CONTA_GRUPO'])
            if not grupo:
                continue

            contas = json.loads(row['CONTAS'])
            total = float(row['TOTAL'])

            if grupo == 'RECEITA':
                dre["receitaBruta"]["total"] = total
                dre["receitaBruta"]["contas"] = contas
            elif grupo == 'DEDUCAO':
                dre["deducoes"]["total"] = total
                dre["deducoes"]["contas"] = contas
            elif grupo == 'CUSTOS VARIÁVEIS':
                dre["custos variaveis"] = {"total": total, "contas": contas}
            elif grupo == 'CUSTOS FIXOS':
                dre["custos fixos"] = {"total": total, "contas": contas}
            elif grupo == 'DESPESAS':
                dre["despesas"]["total"] = total
                dre["despesas"]["contas"] = contas
            elif grupo == 'FINANCEIRO':
                dre["financeiro"] = {"total": total, "contas": contas}

        # Agrega os custos variaveis e fixos no grupo 'custos'
        custos_var = dre.get("custos variaveis", {"total": 0, "contas": []})
        custos_fix = dre.get("custos fixos", {"total": 0, "contas": []})

        custos_total = custos_var["total"] + custos_fix["total"]
        contas_agg = custos_var["contas"] + custos_fix["contas"]
        dre["custos"] = {"total": custos_total, "contas": contas_agg}

        # Calcula os totais usando os grupos atualizados
        receita_bruta = dre["receitaBruta"]["total"]
        deducoes = dre["deducoes"]["total"]
        custos = dre["custos"]["total"]
        despesas = dre["despesas"]["total"]
        financeiro = dre.get("financeiro", {"total": 0})["total"]

        dre["receitaLiquida"]["total"] = receita_bruta + deducoes
        dre["resultadoBruto"]["total"] = receita_bruta + deducoes + custos
        dre["resultadoLiquido"]["total"] = receita_bruta + deducoes + custos + despesas + financeiro

        for key, value in dre.items():
            if isinstance(value, dict) and 'total' in value:
                percentual = (value['total'] / receita_bruta) * 100 if receita_bruta != 0 else 0
                dre[key]["percentual"] = percentual

        #print("saldo dre", dre)
        return dre

    def is_child_account(self,child_code, parent_code):
            return child_code.startswith(parent_code + '.') or child_code == parent_code


    async def dre_contas(self, negocio_idx: int, data_inicio: str, data_termino: str, periodo_type: str, periodos: list):
        # Inicialização das listas para armazenar partes da query principal
        periodo_columns = []
        
        # Construção das colunas de período (apenas totais) para a query principal
        for periodo in periodos:
            periodo_nome = f"PERIODO_{periodo['periodo']}"
            inicio = datetime.strptime(periodo['inicio'], '%d/%m/%Y').strftime('%Y-%m-%d')
            termino = datetime.strptime(periodo['termino'], '%d/%m/%Y').strftime('%Y-%m-%d')
            
            # Valor total do período
            periodo_columns.append(f"""
            COALESCE(SUM(
                CASE 
                    WHEN c.NATUREZA_ID = 'D' THEN
                        CASE
                            WHEN lc.CONTA_DEBITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA >= '{inicio}' AND l.DATA <= '{termino}' THEN lc.VALOR
                                    ELSE 0
                                END
                            WHEN lc.CONTA_CREDITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA >= '{inicio}' AND l.DATA <= '{termino}' THEN -lc.VALOR
                                    ELSE 0
                                END
                            ELSE 0
                        END
                    ELSE
                        CASE
                            WHEN lc.CONTA_DEBITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA >= '{inicio}' AND l.DATA <= '{termino}' THEN -lc.VALOR
                                    ELSE 0
                                END
                            WHEN lc.CONTA_CREDITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA >= '{inicio}' AND l.DATA <= '{termino}' THEN lc.VALOR
                                    ELSE 0
                                END
                            ELSE 0
                        END
                END
            ), 0) as {periodo_nome}
            """)
        
        # Combinar colunas para a query
        periodo_columns_str = ",\n".join(periodo_columns)
        
        # Query principal para obter os totais por conta
        query = f"""
        SELECT 
            c.ID,
            c.IDX,
            c.IDX_PAI, 
            c.NIVEL,
            c.CODIGO, 
            c.NOME,
            COALESCE(SUM(
                CASE 
                    WHEN c.NATUREZA_ID = 'D' THEN
                        CASE
                            WHEN lc.CONTA_DEBITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA < '{data_inicio}' THEN lc.VALOR
                                    ELSE 0
                                END
                            WHEN lc.CONTA_CREDITO = c.ID THEN 
                                CASE 
                                    WHEN l.DATA < '{data_inicio}' THEN -lc.VALOR
                                    ELSE 0
                                END
                        ELSE 0
                    END
                ELSE
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN -lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
            END
        ), 0) as SALDO_ANTERIOR,
        COALESCE(SUM(
            CASE 
                WHEN c.NATUREZA_ID = 'D' THEN
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN -lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
                ELSE
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN -lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
            END
        ), 0) as MOVIMENTO,
        
        -- Colunas de período (apenas totais)
        {periodo_columns_str},
        
        -- Cálculo do SALDO_ATUAL
        COALESCE(SUM(
            CASE 
                WHEN c.NATUREZA_ID = 'D' THEN
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN -lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
                ELSE
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN -lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA < '{data_inicio}' THEN lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
            END
        ), 0) 
        +
        COALESCE(SUM(
            CASE 
                WHEN c.NATUREZA_ID = 'D' THEN
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN -lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
                ELSE
                    CASE
                        WHEN lc.CONTA_DEBITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN -lc.VALOR
                                ELSE 0
                            END
                        WHEN lc.CONTA_CREDITO = c.ID THEN 
                            CASE 
                                WHEN l.DATA >= '{data_inicio}' AND l.DATA <= '{data_termino}' THEN lc.VALOR
                                ELSE 0
                            END
                        ELSE 0
                    END
            END
        ), 0) as SALDO_ATUAL
    FROM CONTABIL_CONTA c
    LEFT JOIN CONTABIL_LANCAMENTO_CONTA lc ON (lc.CONTA_DEBITO = c.ID OR lc.CONTA_CREDITO = c.ID)
    LEFT JOIN CONTABIL_LANCAMENTO l ON l.IDX = lc.LANCAMENTO_IDX AND l.EXCLUIDO = 0
    WHERE c.NEGOCIO_IDX = '{negocio_idx}'
    AND c.EXCLUIDO = 0 
    AND c.CODIGO LIKE '3%'
    GROUP BY c.ID
    ORDER BY c.CODIGO
    """
    
        # Executar a query principal para obter os totais
        contas = await self.mysql.query(query)
  
    
        # Para cada conta e período, buscar os lançamentos individuais em consultas separadas
        for item in contas:
            
          
          
    
            conta_id = item['ID']
            for periodo in periodos:
              periodo_nome = f"PERIODO_{periodo['periodo']}"

              if(item[periodo_nome] ==0):  # se nao houver movimento, criar o periodo com zero
                 
                    item[periodo_nome] = {
                            "TOTAL": 0,
                            "LANCAMENTOS": []
                    }
              else:  # se houver movimento, buscar os lançamentos do período para esta conta
                
                #print("############# vou buscar os lançamentos do período para esta conta")
                inicio = datetime.strptime(periodo['inicio'], '%d/%m/%Y').strftime('%Y-%m-%d')
                termino = datetime.strptime(periodo['termino'], '%d/%m/%Y').strftime('%Y-%m-%d')
                # Query para buscar os lançamentos do período para esta conta
                lancamentos_query = f"""
                SELECT 
                    lc.ID,
                    lc.IDX,
                    CASE WHEN lc.CONTA_DEBITO = {conta_id} THEN lc.VALOR ELSE 0 END as DEBITO,
                    CASE WHEN lc.CONTA_CREDITO = {conta_id} THEN lc.VALOR ELSE 0 END as CREDITO,
                    l.IDX as LANCAMENTO_IDX,
                    l.DATA,
                    l.HISTORICO,
                    l.DOCUMENTO
                FROM CONTABIL_LANCAMENTO_CONTA lc
                JOIN CONTABIL_LANCAMENTO l ON l.IDX = lc.LANCAMENTO_IDX
                WHERE (lc.CONTA_DEBITO = {conta_id} OR lc.CONTA_CREDITO = {conta_id})
                AND l.DATA >= '{inicio}' AND l.DATA <= '{termino}'
                AND l.EXCLUIDO = 0
                """
                #print("lancamentos_query", lancamentos_query);
                # Executar a consulta para obter os lançamentos
                lancamentos = await self.mysql.query(lancamentos_query)
                #print(f"  Período {periodo['periodo']}: {len(lancamentos)} lançamentos encontrados")
                #print("lancamentos:")
                #for lancamento in lancamentos:
                #    print(f"  - {lancamento}")
                
                # Guardar o total do período
                total = item.get(periodo_nome, 0)
                
                # Criar a estrutura aninhada
                item[periodo_nome] = {
                    "TOTAL": total,
                    "LANCAMENTOS": lancamentos if lancamentos else []
                }
        # Debug - imprimir exemplo de estrutura para verificar
        #if contas and len(contas) > 0:
            #print("\nExemplo de estrutura de dados:")
            #import json
            #print(json.dumps(contas[0], default=str, indent=2))


        # Atualiza os saldos das contas sinteticas
        base_percentual = 0;
        for conta in contas:
            
            if conta['NIVEL'] == 'S':  # Se for uma conta sintética
                saldo_anterior = 0
                movimento = 0
                saldo_atual = 0
                # Inicializa um dicionário para acumular os valores dos período dinâmicos
                periodos_sinteticos = {
                    f"PERIODO_{periodo['periodo']}": {
                        "TOTAL": 0,
                        "LANCAMENTOS": []
                    } for periodo in periodos
                }

                # Procura todas as contas que são filhas desta conta sintética
                for potential_child in contas:
                    if self.is_child_account(potential_child['CODIGO'], conta['CODIGO']):
                        # Soma os valores da conta filha (seja analítica ou sintética)
                        saldo_anterior += potential_child['SALDO_ANTERIOR']
                        movimento += potential_child['MOVIMENTO']
                        saldo_atual += potential_child['SALDO_ATUAL']

                        # Soma os valores das colunas de período dinâmicas
                        for periodo in periodos:
                            periodo_nome = f"PERIODO_{periodo['periodo']}"
                            
                            # Aqui é onde o erro ocorre, vamos imprimir detalhes antes de tentar acessar
                            try:
                                periodos_sinteticos[periodo_nome]['TOTAL'] += potential_child[periodo_nome]['TOTAL']
                            except Exception as e:
                                print(f"    ERRO ao processar {periodo_nome} para conta {potential_child['CODIGO']}: {str(e)}")
                                print(f"    Conteúdo completo da conta problemática: {potential_child}")
                                # Não propague o erro, apenas registre-o para diagnóstico

                        # Atualiza os totais da conta sintética
                        conta['SALDO_ANTERIOR'] = saldo_anterior
                        conta['MOVIMENTO'] = movimento
                        conta['SALDO_ATUAL'] = saldo_atual

                        # Atualiza os valores das colunas de período dinâmicas para a conta sintética
                        for periodo_nome, valor in periodos_sinteticos.items():
                            conta[periodo_nome] = valor

                    if conta['CODIGO'] == '3.1.1':
                        conta['PERCENTUAL'] = 100
                        base_percentual = conta['SALDO_ATUAL']
                    else:
                        if conta['SALDO_ATUAL'] != 0 and base_percentual != 0:
                            conta['PERCENTUAL'] = round(conta['SALDO_ATUAL'] / base_percentual * 100, 2)
                        else:
                            conta['PERCENTUAL'] = 0       


        for conta in contas:
            if conta['SALDO_ATUAL'] != 0 and base_percentual != 0:
               conta['PERCENTUAL'] = round(conta['SALDO_ATUAL'] / base_percentual * 100, 2) 
            else:
                conta['PERCENTUAL'] = 0
            if conta['CODIGO'] == '3.1.1':
                break

        
        
        
        #Calcula percentual
        

        
        
        return contas


    def _calcular_periodos(self, data_inicio: datetime, data_termino: datetime, period_type: str) -> list:
        #print("===== calcular_periodos() =====" ,data_inicio, data_termino, period_type)
        """
        Calcula todos os período
        Retorna uma lista de dicionários com informações de cada período.
        """
        periodos = []
        data_atual = data_inicio
        
        while data_atual <= data_termino:
            periodo = self.formatar_periodo(data_atual, period_type, data_inicio)
            periodo_inicio, periodo_fim = self.get_period_start_end(data_atual, period_type, data_inicio, data_termino)
            
            periodos.append({
                'periodo': periodo,
                'inicio': periodo_inicio.strftime('%d/%m/%Y'),
                'termino': periodo_fim.strftime('%d/%m/%Y')
            })
            
            # Avança para o próximo período
            if period_type == 'D':
                data_atual += timedelta(days=1)
            elif period_type == 'S':
                data_atual += timedelta(days=7)
            elif period_type == 'M':
                # Avança um mês
                if data_atual.month == 12:
                    data_atual = data_atual.replace(year=data_atual.year + 1, month=1)
                else:
                    data_atual = data_atual.replace(month=data_atual.month + 1)
            else:  # A
                data_atual = data_atual.replace(year=data_atual.year + 1)
                
        #print("periodos calculados",periodos)
        return periodos


    # Função para plano de contas com cache
    async def fetch_account_cached(self,colunas: str, negocio_idx: str, tipo_id: str, formato: str):
        #print("===== fetch_account_cached() =====")
        key = f"account_{negocio_idx}"
        #print("key", key)
        if key not in cache:
            #print("key not in cache")
            agent_accountant = Accountant()
            plano_contas_dados = await self.fetch_account(colunas, negocio_idx, tipo_id, formato)
            cache[key] = plano_contas_dados
        else:
            #print("key in cache")
            pass
        return cache[key]
    

    async def gera_agente_gerador_contabil(self,nome: str=None,plano_contas_dados: dict={}, modelo_str: str=None, modelo_cliente_openai: OpenAIChatCompletionsModel=None, ferramentas: list=None, negocio_idx: str=None, saida_formato: str=None, usuario_idx: str=None, data_atual: str=None):
        print("===== transaction_generate() =====")
        data_atual = datetime.now().strftime('%Y/%m/%d')

        lancamento_dados =    {
            "CONTABIL_LANCAMENTO": {
              "IDX": "**********",
              "DATA": "CURDATE()",
              "HISTORICO": "Pagamento de conta de luz",
              "DOCUMENTO": "NF 102035",
              "NEGOCIO_IDX": "**********",
              "USUARIO_IDX": "**********",
              "EXCLUIDO": 0,
              "contas": [
                {
                  "CONTABIL_LANCAMENTO_CONTA": {
                    "IDX": "5809318036",
                    "CODIGO": "3.3.2.02",
                    "NOME": "Energia elétrica",
                    "CONTA_DEBITO": 5402,
                    "CONTA_CREDITO": 0,
                    "VALOR": 100.00,
                    "LANCAMENTO_IDX": "**********",
                    "EXCLUIDO": 0
                  }
                },
                {
                  "CONTABIL_LANCAMENTO_CONTA": {
                    "IDX": "5809318037",
                    "CODIGO": "1.1.1.01",
                    "NOME": "Caixa",
                    "CONTA_DEBITO": 0,
                    "CONTA_CREDITO": 5311,
                    "VALOR": 100.00,
                    "LANCAMENTO_IDX": "**********",
                    "EXCLUIDO": 0
                  }
                }
              ]
            }
          }



        agente_gerador_contabil = {
        "name": nome,
        "instructions": 
            f"""Você é um auxiliar contábil do sistema DRE360 especializado em gerar lançamentos contábeis.

            Este é o plano de contas da empresa que deve ser usado na geração de lançamentos contábeis. Ele é uma lista de dicionários, onde cada dicionário representa uma conta contábil. Cada dicionário possui as seguintes chaves:
            - "ID": o id da conta contábil
            - "CODIGO": o código da conta contábil
            - "NOME": o nome da conta contábil
            - "NIVEL": o nivel da conta contábil
            - "NATUREZA_ID": o id da natureza da conta contábil
            # inicio do plano de contas
            {plano_contas_dados}
            # fim do plano de contas
            
            São 3 tabelas que devem ser utilizadas para a geração de lançamentos contábeis:
            - CONTABIL_CONTA
                  Estrutura da tabela:
                      CREATE TABLE `CONTABIL_CONTA` (
                      `ID` int(10) NOT NULL,
                      `SISTEMA_ID` int(10) NOT NULL DEFAULT 0,
                      `IDX_PAI` varchar(10) NOT NULL DEFAULT '0',
                      `TIPO_ID` smallint(1) NOT NULL DEFAULT 0,
                      `NIVEL` varchar(1) DEFAULT NULL,
                      `NATUREZA_ID` varchar(1) DEFAULT NULL,
                      `IDX` varchar(10) DEFAULT NULL,
                      `CODIGO` varchar(30) DEFAULT NULL,
                      `NOME` varchar(50) DEFAULT NULL,
                      `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                      `EXCLUIDO` smallint(1) NOT NULL DEFAULT 0,
                      `SALDO_ANTERIOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `DEBITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `CREDITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `SALDO_ATUAL` decimal(10,2) NOT NULL DEFAULT 0.00
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém o plano de contas. Deve ser utilizada para obter informações sobre o saldo inicial (SALDO_ANTERIOR), movimento (DEBITO e CREDITO) e saldo atual (SALDO_ATUAL) de cada conta contábil, bem com outros dados da conta, como ID, NIVEL, NATUREZA_ID, etc.

            - CONTABIL_LANCAMENTO
                  Estrutura da tabela:
                    CREATE TABLE `CONTABIL_LANCAMENTO` (
                     `ID` int(10) NOT NULL,
                     `IDX` varchar(10) DEFAULT NULL,
                     `DATA` date DEFAULT NULL,
                     `CRIADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `ATUALIZADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `DOCUMENTO` varchar(20) DEFAULT NULL,
                     `HISTORICO` varchar(500) DEFAULT NULL,
                     `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                     `USUARIO_IDX` varchar(10) DEFAULT NULL,
                     `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém os dados principais dos lançamentos contábeis. Deve ser utilizada para obter informações sobre os lançamentos contábeis, como  DATA, HISTORICO, DOCUMENTO, ou sobre totais lançados, lançamento de um determinado periodo, conta, etc.

            - CONTABIL_LANCAMENTO_CONTA
                  Estrutura da tabela:
                  CREATE TABLE `CONTABIL_LANCAMENTO_CONTA` (
                `ID` int(11) NOT NULL,
                `IDX` varchar(10) DEFAULT NULL,
                `CONTA_DEBITO` int(10) DEFAULT 0,
                `CONTA_CREDITO` int(10) DEFAULT 0
                `VALOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                `LANCAMENTO_IDX` varchar(10) DEFAULT NULL,
                `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;    

            LANÇAMENTOS CONTÁBEIS:
            - Lançamentos devem ser criados com base no plano de contas da empresa.
            - Cada lançamento deve conter pelo menos 1 conta de débito e 1 conta de crédito.
            - Cada conta deve ficar em um registro separado na tabela CONTABIL_LANCAMENTO_CONTA.
              Exemplo: 
              {lancamento_dados}                
            - O objeto deve conter o histórico do lançamento, que deve ser uma descrição do lançamento.
            - O objeto deve conter o documento do lançamento, que deve ser um numero de documento, como boleto, nota fiscal, etc.
            - O objeto deve conter a data do lançamento, que deve ser uma data válida.
              O objeto deve conter o codigo da conta.
              O objeto deve conter o nome da conta.
            - O objeto deve conter a(s) conta(s) de debito(s) e a(s) conta(s) de credito(s), com seus respectivos valores, sendo que os totais dos debitos e creditos devem ser iguais.
            - O idx do negócio (NEGOCIO_IDX) será sempre o idx do negocio da empresa, no caso {negocio_idx}
            
            - O idx do usuario (USUARIO_IDX) será sempre o idx do usuario que solicitou a criação do lançamento, no caso {usuario_idx}
            - O idx do lançamento (IDX) deve ser gerado pela função generate_idx(), e deve ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres. O mesmo vale para o idx criado para as contas de debito e credito. Os IDX da CONTABIL_LANCAMENTO_CONTA devem ser criados individualmente  com a funcao generate_idx() e devem ser únicos. Somente o idx da coluna LANCAMENTO_IDX que é o mesmo idx da CONTABIL_LANCAMENTO, pois trata-se de uma chave estrangeira.
             Quando o usuário solicitar a criação de um lançamento, . Com base na descrição, verfique o que ja tem de dados para criar o lançamento.Procure determinar quais são as contas de debito e credito do lançamento, e utilize as contas mais apropriadas.
             Retorne apenas o objeto JSON, sem nenhum outro texto ou explicação antes ou depois do objeto.


             INFORMAÇÕES OBRIGATÓRIAS:
            1. Data do lançamento
            2. Valor do lançamento. O valor deve ser um numero superior a zero. se o valor não for informado, for negativo ou igual a zero, inclua o valor na lista de informações faltantes.
            3. Conta(s) de débito (s)
            4. Conta(s) de crédito (s)

            INFORMAÇÕES OPCIONAIS:
            1. Histórico do lançamento
            2. Documento do lançamento


            EXCEÇÕES:

                 caso no histórico não haja indicação explicita da data do lançamento, uitilie a {data_atual}
            
            Caso no historico não possua todas  as informações necessárias, como valor ,  e não seja possivel determinar qual a conta devedora ou credora, retorne um  array com um objeto com a lista de informações faltantes. Um exemplo:
           {{
                "erro": "Informações faltantes",
                "informações_faltantes": ["valor", "conta devedora", "conta credora"]
            }}

            Deve ser retornar apenas um objeto json, ou o de dados ou o de erro. Se há alguma informação faltante, o ojeto de dados não será gerado nem retornado.

            Então a regra é:
            - Se há alguma informação faltante, o ojeto de dados não será gerado nem retornado. Sera gerado e retornado o objeto de erro.
            - Se não há informações faltantes, o ojeto de dados será gerado e retornado.
       

            """,
        "model": modelo_cliente_openai if modelo_cliente_openai else modelo_str,
        "tools": ferramentas,
        "handoff_description": "Auxiliar contábil especializado criar lançamentos contábeis",
        "handoffs": [],
        "output_type": saida_formato,
        "input_guardrails": [],
        "output_guardrails": []
    }

        return agente_gerador_contabil
   


    async def gera_agente_lancador_contabil(self,nome: str=None,plano_contas_dados: dict={}, modelo_str: str=None, modelo_cliente_openai: OpenAIChatCompletionsModel=None, ferramentas: list=None, negocio_idx: str=None, saida_formato: str=None, usuario_idx: str=None):
        print("===== transaction_create() =====")

        agente_lancador_contabil = {
        "name": nome,
        "instructions": 
            f"""Você é um auxiliar contábil do sistema DRE360 especializado em criar lançamentos contábeis.

            Este é o plano de contas da empresa que deve ser usado na criação de lançamentos contábeis. Ele é uma lista de dicionários, onde cada dicionário representa uma conta contábil. Cada dicionário possui as seguintes chaves:
            - "ID": o id da conta contábil
            - "CODIGO": o código da conta contábil
            - "NOME": o nome da conta contábil
            - "NIVEL": o nivel da conta contábil
            - "NATUREZA_ID": o id da natureza da conta contábil
            # inicio do plano de contas
            {plano_contas_dados}
            # fim do plano de contas
            
            São 3 tabelas que devem ser utilizadas para a criação de lançamentos contábeis:
            - CONTABIL_CONTA
                  Estrutura da tabela:
                      CREATE TABLE `CONTABIL_CONTA` (
                      `ID` int(10) NOT NULL,
                      `SISTEMA_ID` int(10) NOT NULL DEFAULT 0,
                      `IDX_PAI` varchar(10) NOT NULL DEFAULT '0',
                      `TIPO_ID` smallint(1) NOT NULL DEFAULT 0,
                      `NIVEL` varchar(1) DEFAULT NULL,
                      `NATUREZA_ID` varchar(1) DEFAULT NULL,
                      `IDX` varchar(10) DEFAULT NULL,
                      `CODIGO` varchar(30) DEFAULT NULL,
                      `NOME` varchar(50) DEFAULT NULL,
                      `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                      `EXCLUIDO` smallint(1) NOT NULL DEFAULT 0,
                      `SALDO_ANTERIOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `DEBITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `CREDITO` decimal(10,2) NOT NULL DEFAULT 0.00,
                      `SALDO_ATUAL` decimal(10,2) NOT NULL DEFAULT 0.00
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém o plano de contas. Deve ser utilizada para obter informações sobre o saldo inicial (SALDO_ANTERIOR), movimento (DEBITO e CREDITO) e saldo atual (SALDO_ATUAL) de cada conta contábil, bem com outros dados da conta, como ID, NIVEL, NATUREZA_ID, etc.

            - CONTABIL_LANCAMENTO
                  Estrutura da tabela:
                    CREATE TABLE `CONTABIL_LANCAMENTO` (
                     `ID` int(10) NOT NULL,
                     `IDX` varchar(10) DEFAULT NULL,
                     `DATA` date DEFAULT NULL,
                     `CRIADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `ATUALIZADO` datetime NOT NULL DEFAULT current_timestamp(),
                     `DOCUMENTO` varchar(20) DEFAULT NULL,
                     `HISTORICO` varchar(500) DEFAULT NULL,
                     `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
                     `USUARIO_IDX` varchar(10) DEFAULT NULL,
                     `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
                    Contém os dados principais dos lançamentos contábeis. Deve ser utilizada para obter informações sobre os lançamentos contábeis, como  DATA, HISTORICO, DOCUMENTO, ou sobre totais lançados, lançamento de um determinado periodo, conta, etc.

            - CONTABIL_LANCAMENTO_CONTA
                  Estrutura da tabela:
                  CREATE TABLE `CONTABIL_LANCAMENTO_CONTA` (
                `ID` int(11) NOT NULL,
                `IDX` varchar(10) DEFAULT NULL,
                `CONTA_DEBITO` int(10) DEFAULT 0,
                `CONTA_CREDITO` int(10) DEFAULT 0
                `VALOR` decimal(10,2) NOT NULL DEFAULT 0.00,
                `LANCAMENTO_IDX` varchar(10) DEFAULT NULL,
                `EXCLUIDO` tinyint(1) NOT NULL DEFAULT 0
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;    

            LANÇAMENTOS CONTÁBEIS:
            - Lançamentos devem ser criados com base no plano de contas da empresa.
            - Cada lançamento deve conter pelo menos 1 conta de débito e 1 conta de crédito.
            - Cada conta deve ficar em um registro separado na tabela CONTABIL_LANCAMENTO_CONTA.
              Exemplo: 
              deposito 100,00 em conta corrente . serão feitos 1 registro  na tabela CONTABIL_LANCAMENTO e 2 registros na tabela CONTABIL_LANCAMENTO_CONTA:
              INSERT INTO CONTABIL_LANCAMENTO (IDX, DATA, HISTORICO, DOCUMENTO, NEGOCIO_IDX, USUARIO_IDX, EXCLUIDO) VALUES (idx_lancamento, data_lancamento, historico_lancamento, documento_lancamento, negocio_idx, usuario_idx, 0);
              INSERT INTO CONTABIL_LANCAMENTO_CONTA (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX, EXCLUIDO) VALUES (idx_lancamento_conta_debito, conta_debito, 0, valor, idx_lancamento, 0);
              INSERT INTO CONTABIL_LANCAMENTO_CONTA (IDX, CONTA_DEBITO, CONTA_CREDITO, VALOR, LANCAMENTO_IDX, EXCLUIDO) VALUES (idx_lancamento_conta_credito, 0,conta_credito, valor, idx_lancamento, 0);

            - O lançamento deve conter o histórico do lançamento, que deve ser uma descrição do lançamento.
            - O lançamento deve conter o documento do lançamento, que deve ser um numero de documento, como boleto, nota fiscal, etc.
            - O lançamento deve conter a data do lançamento, que deve ser uma data válida.
            - O lançamento deve conter a(s) conta(s) de debito(s) e a(s) conta(s) de credito(s), com seus respectivos valores, sendo que os totais dos debitos e creditos devem ser iguais.
            - O idx do negócio (NEGOCIO_IDX) será sempre o idx do negocio da empresa, no caso {negocio_idx}
            
            - O idx do usuario (USUARIO_IDX) será sempre o idx do usuario que solicitou a criação do lançamento, no caso {usuario_idx}
            - O idx do lançamento (IDX) deve ser gerado pela função generate_idx(), e deve ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres. O mesmo vale paa o IDX criado para as contas de debito e credito. Os IX da CONTABIL_LANCAMENTO_CONTA devem ser criados individualmente  com a funcao generate_idx() e devem ser únicos. Somente o idx da coluna LANCAMENTO_IDX que é o mesmo idx da CONTABIL_LANCAMENTO, pois trata-se de uma chave estrangeira.
             Quando o usuário solicitara criação de um lançamento, Solicite que ele descreva o que deseja que seja lançado, para que ele conte o histórico do lançamento. Com base na descrição, verfique o que ja tem de dados para criar o lançamento.Procure determinar quais são as contas de debito e credito do lançamento, e sugira as contas mais apropriadas.
            E solicite as informações que não foram incluidas no relato inicial. As informações devem ser solicitadas uma por vez, e nao devem ser solicitadas todas de uma vez. Só pergunte a informação seguinte quando a anterior for fornecida e interpretada.

            INFORMAÇÕES OBRIGATÓRIAS:
            1. Data do lançamento
            2. Valor do lançamento
            3. Conta(s) de débito (s)
            4. Conta(s) de crédito (s)

            INFORMAÇÕES OPCIONAIS:
            1. Histórico do lançamento
            2. Documento do lançamento

            

            IMPORTANTE: Vjocê DEVE SEMPRE executar a função executa_query_mysql() para cada query de INSERT. 
            Não basta apenas gerar as queries, é necessário executá-las uma a uma.

            Exemplo de como deve ser feito:

            1. Gerar IDX:
            idx_lancamento = await generate_idx()
            o idx deve ser ser criado desta forma. Não invente um idx, nem altere o idx gerado. Ele TEM QUE TER no maximo 10 caracteres.

            2. Executar as queries:
            query1 = "INSERT INTO CONTABIL_LANCAMENTO ..."
            await executa_query_mysql(query1)

            query2 = "INSERT INTO CONTABIL_LANCAMENTO_CONTA ..."
            await executa_query_mysql(query2)

            3. Somente após confirmar que todas as queries foram executadas, retornar mensagem de sucesso.

            NUNCA retorne mensagem de sucesso sem ter executado todas as queries necessárias.
            """,
        "model": modelo_cliente_openai if modelo_cliente_openai else modelo_str,
        "tools": ferramentas,
        "handoff_description": "Auxiliar contábil especializado criar lançamentos contábeis",
        "handoffs": [],
        "output_type": saida_formato,
        "input_guardrails": [],
        "output_guardrails": []
    }

        return agente_lancador_contabil





        
        print("data", data)
        return {"message": "Transaction generated successfully"}

@router.post("/account/create")
async def create_account(data: dict):
    accountant = Accountant()
    result = await accountant.create_account(data)
    return result


@router.post("/account_type/create")
async def create_account_type(data: dict):
    accountant = Accountant()
    result = await accountant.create_account_type(data)
    return result


@router.post("/account/fetch/{negocio_idx}/{area_id}")
async def fetch_account(negocio_idx: str, area_id: str, data: dict):
    print("@@@@@ fetch_account()")
    accountant = Accountant()
    #area_id = 1
    columns_str = ",".join(data["colunas_nome"])
    result = await accountant.fetch_account(columns_str, negocio_idx, area_id)
    print("resul2", result)
    return result


@router.post("/accounts_dre/fetch/{negocio_idx}")
async def fetch_accounts_dre(negocio_idx: str, data: dict):
    accountant = Accountant()
    columns_str = ",".join(data["colunas_nome"])
    result = await accountant.fetch_accounts_dre(negocio_idx, columns_str)
    return result

@router.post("/account_type/fetch")
async def fetch_account_types(data: dict):
    accountant = Accountant()
    columns_str = ",".join(data["columns"])
    result = await accountant.fetch_account_types(columns_str)
    return result


@router.post("/account/analytics/fetch/{negocio_idx}/{tipo_id}")
async def fetch_account_analytics_fetch(negocio_idx: str, tipo_id: str, data: dict):

    def filtrar_contas_sem_filhos(contas):
        # print("@@@@@ filtrar_contas_sem_filhos",contas)
        # return []
        return [
            conta for conta in contas
            if not any(
                outra_conta['IDX_PAI'] == conta['IDX']
                for outra_conta in contas
            )
        ]

    accountant = Accountant()
    columns_str = ",".join(data["colunas_nome"])
    result = await accountant.fetch_account_analytics(columns_str, negocio_idx)
    if not result:
        result = await accountant.fetch_accountant_model(tipo_id, negocio_idx)
        if result:
            result = filtrar_contas_sem_filhos(result)

    return result


@router.post("/account/update")
async def update_account(data: dict):
    accountant = Accountant()
    result = await accountant.update_account(data)
    return result


@router.post("/account/fetch/model/{negocio_tipo}/{negocio_idx}")
async def fetch_account_model(negocio_tipo: str, negocio_idx: str):
    accountant = Accountant()
    result = await accountant.fetch_accountant_model(negocio_tipo, negocio_idx)
    return result


@router.get("/accounts/search")
async def accounts_search(business_id: str, keywords: str, columns: str):
    accountant = Accountant()
    result = await accountant.accounts_search(business_id, keywords, columns)
    return result


@router.post("/transaction/create_update/{negocio_idx}")
async def create_update_transaction(negocio_idx: str, data: dict):
    accountant = Accountant()
    operacao = data.get("contabil_lancamento").get("OPERACAO")
    print
    ("operacao", operacao)
    if data.get("contabil_lancamento").get("OPERACAO") == "update":
        result = await accountant.update_transaction(negocio_idx, data)
    else:
        result = await accountant.create_transaction(negocio_idx, data)
    return result


@router.post("/transactions/fetch/{negocio_idx}")
async def fetch_transactions(negocio_idx: str, filters: dict):
    print("@@@@@ fetch_transactions")
    accountant = Accountant()
    # print("filters",filters)
    # print("negocio_idx",negocio_idx)
    result = await accountant.fetch_transactions(negocio_idx, filters)
    # print("result",result)
    return result



@router.get("/transaction/fetch/{lancamento_idx}")
async def fetch_transaction(lancamento_idx: str):

    accountant = Accountant()
    result = await accountant.fetch_transaction(lancamento_idx)
    return result


@router.get("/transaction/delete/{idx}")
async def delete_transaction(idx: str):
    print("@@@@@ delete_transaction", idx)
    accountant = Accountant()
    result = await accountant.delete_transaction(idx)
    return result


@router.post("/dre/", operation_id="dre_generate1")
async def dre_generate(filter: dict):
    accountant = Accountant()
    #print("===== dre_generate() =====")
    
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")
    dre = {}

    #print("data_inicio1", data_inicio)
    #print("data_termino1", data_termino)

    if data_inicio and re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(data_inicio, '%Y-%m-%d').strftime('%d/%m/%Y')
    if data_termino and re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(data_termino, '%Y-%m-%d').strftime('%d/%m/%Y')

    #print("data_inicio2", data_inicio)
    #print("data_termino2", data_termino)

    # Verificar se as datas existem antes de tentar converter
    if not data_inicio or not data_termino:
        # Usar datas padrão se não forem fornecidas
        hoje = datetime.now()
        primeiro_dia_mes = datetime(hoje.year, hoje.month, 1)
        
        if not data_inicio:
            data_inicio = primeiro_dia_mes.strftime('%d/%m/%Y')
        if not data_termino:
            data_termino = hoje.strftime('%d/%m/%Y')
    
    data_inicio = datetime.strptime(data_inicio, '%d/%m/%Y')
    data_termino = datetime.strptime(data_termino, '%d/%m/%Y')
    

    period_type = accountant.get_period_type(data_inicio, data_termino)
    #print(f"Tipo do Período: {period_type}")

    periodos = accountant._calcular_periodos(data_inicio, data_termino, period_type)
    #print("Períodos Calculados:")
    #for periodo in periodos:
    #    print(periodo)

    dre = await accountant.dre_contas(negocio_idx, data_inicio, data_termino, period_type, periodos)
   
    #print("dre",dre)
   
    #headers = ["ID", "CODIGO", "NOME", "SALDO_ANTERIOR", "MOVIMENTO", "PERIODO_01", "PERIODO_02", "PERIODO_03", "PERIODO_04", "PERIODO_05", "SALDO_ATUAL", "PERCENTUAL"]
    #dre_tabular = [[conta.get(h, '') for h in headers] for conta in dre]
    #print(tabulate(dre_tabular, headers=headers))
   
    return {
        "dre":dre,
        "periodos": periodos,
        "periodos_tipo": period_type
        }


@router.post("/dre/", operation_id="dre_generate2")
async def dre_generate(filter: dict):
 
    accountant = Accountant()
    #print("===== dre_generate() =====")
    
    negocio_idx = filter.get("negocio_idx")
    data_inicio = filter.get("data_inicio")
    data_termino = filter.get("data_termino")
    dre = {}

    #print("data_inicio1", data_inicio)
    #print("data_termino1", data_termino)

    if data_inicio and re.match(r'\d{4}-\d{2}-\d{2}', data_inicio):
        data_inicio = datetime.strptime(data_inicio, '%Y-%m-%d').strftime('%d/%m/%Y')
    if data_termino and re.match(r'\d{4}-\d{2}-\d{2}', data_termino):
        data_termino = datetime.strptime(data_termino, '%Y-%m-%d').strftime('%d/%m/%Y')

    #print("data_inicio2", data_inicio)
    #print("data_termino2", data_termino)

    # Verificar se as datas existem antes de tentar converter
    if not data_inicio or not data_termino:
        # Usar datas padrão se não forem fornecidas
        hoje = datetime.now()
        primeiro_dia_mes = datetime(hoje.year, hoje.month, 1)
        
        if not data_inicio:
            data_inicio = primeiro_dia_mes.strftime('%d/%m/%Y')
        if not data_termino:
            data_termino = hoje.strftime('%d/%m/%Y')
    
    data_inicio = datetime.strptime(data_inicio, '%d/%m/%Y')
    data_termino = datetime.strptime(data_termino, '%d/%m/%Y')
    

    period_type = accountant.get_period_type(data_inicio, data_termino)
    #print(f"Tipo do Período: {period_type}")

    periodos = accountant._calcular_periodos(data_inicio, data_termino, period_type)
    #print("Períodos Calculados:")
    #for periodo in periodos:
    #    print(periodo)

    dre = await accountant.dre_contas(negocio_idx, data_inicio, data_termino, period_type, periodos)
   
    #print("dre",dre)
   
    #headers = ["ID", "CODIGO", "NOME", "SALDO_ANTERIOR", "MOVIMENTO", "PERIODO_01", "PERIODO_02", "PERIODO_03", "PERIODO_04", "PERIODO_05", "SALDO_ATUAL", "PERCENTUAL"]
    #dre_tabular = [[conta.get(h, '') for h in headers] for conta in dre]
    #print(tabulate(dre_tabular, headers=headers))
   
    return {
        "dre":dre,
        "periodos": periodos,
        "periodos_tipo": period_type
        }

   
@router.post("/transaction/generate")
async def transaction_generate(data: dict):
    print("===== transaction_generate() =====")
    print("data", data)
    llm = LLM()
    mysql = Mysql()
    negocio_idx = data.get("negocio_idx")
    tipo_id = data.get("tipo_id")
    accountant = Accountant()
    plano_contas_dados = await accountant.fetch_account_cached("CODIGO,NOME,ID,NIVEL,NATUREZA_ID",negocio_idx,tipo_id,"tupla")
    modelo_idx = data.get("modelo_idx")
    modelo = llm.get_model_idx(modelo_idx)
    negocio_idx = data.get("negocio_idx")
    usuario_idx = data.get("usuario_idx")
    modelo_cliente_openai = None
    modelo_str = None;
    # Verificar se modelo é uma instância de OpenAIChatCompletionsModel
    if isinstance(modelo, OpenAIChatCompletionsModel):
        print("Modelo é uma instância de OpenAIChatCompletionsModel")
        modelo_cliente_openai = modelo
        
        # Lógica para modelos OpenAIChatCompletionsModel
    elif isinstance(modelo, str):
        print("Modelo não é uma instância de OpenAIChatCompletionsModel")
        # Lógica para outros tipos de modelos
        modelo_str = modelo 


    agente_gerador_contabil = await accountant.gera_agente_gerador_contabil(
        nome = "agente_gerador_contabil",
        plano_contas_dados = plano_contas_dados,
        modelo_str = modelo_str,
        modelo_cliente_openai = modelo_cliente_openai,
        ferramentas = [
            generate_idx,
        ],
        negocio_idx = negocio_idx,
        usuario_idx = usuario_idx,
        saida_formato = None
    )

    agente_gerador_contabil_obj = await llm.model_openai.agent_create(**agente_gerador_contabil)       
                                                                         
    #print("agente_gerador_contabil_obj", agente_gerador_contabil_obj)
      # Executar o agente com o histórico de mensagens
    response = await llm.model_openai.agent_run(agente_gerador_contabil_obj,  data.get("historico"))
    #print("response", response)
    #print("response.final_output", response.final_output)
    result = response.final_output
    return {"success": True, "data": result}


   
@router.post("/transaction/create")
async def transaction_create(data: dict):
    print("===== transaction_create() =====")
    llm = LLM()
    mysql = Mysql()
    negocio_idx = data.get("negocio_idx")
    tipo_id = data.get("tipo_id")
    accountant = Accountant()
    plano_contas_dados = await accountant.fetch_account_cached("CODIGO,NOME,ID,NIVEL,NATUREZA_ID",negocio_idx,tipo_id,"tupla")
    modelo_idx = data.get("modelo_idx")
    modelo = llm.get_model_idx(modelo_idx)
    negocio_idx = data.get("negocio_idx")
    usuario_idx = data.get("usuario_idx")
    modelo_cliente_openai = None
    modelo_str = None;
    # Verificar se modelo é uma instância de OpenAIChatCompletionsModel
    if isinstance(modelo, OpenAIChatCompletionsModel):
        print("Modelo é uma instância de OpenAIChatCompletionsModel")
        modelo_cliente_openai = modelo
        
        # Lógica para modelos OpenAIChatCompletionsModel
    elif isinstance(modelo, str):
        print("Modelo não é uma instância de OpenAIChatCompletionsModel")
        # Lógica para outros tipos de modelos
        modelo_str = modelo 


    agente_lancador_contabil = await accountant.gera_agente_lancador_contabil(
        nome = "agente_lancador_contabil",
        plano_contas_dados = plano_contas_dados,
        modelo_str = modelo_str,
        modelo_cliente_openai = modelo_cliente_openai,
        ferramentas = [
            generate_idx,
            executa_query_mysql
        ],
        negocio_idx = negocio_idx,
        usuario_idx = usuario_idx,
        saida_formato = None
    )

    agente_lancador_contabil_obj = await llm.model_openai.agent_create(**agente_lancador_contabil)       
                                                                         
    #print("agente_lancador_contabil_obj", agente_lancador_contabil_obj)
      # Executar o agente com o histórico de mensagens
    response = await llm.model_openai.agent_run(agente_lancador_contabil_obj,  data.get("historico"))
    #print("response", response)
    #print("response.final_output", response.final_output)
    result = response.final_output
    return result

def parse_date(date_str):
    """
    Tenta converter uma string de data em diferentes formatos para datetime.
    Aceita formatos: DD/MM/YYYY ou YYYY-MM-DD
    
    Args:
        date_str (str): String contendo a data
        
    Returns:
        datetime: Objeto datetime convertido
    """
    try:
        # Tenta primeiro o formato DD/MM/YYYY
        return datetime.strptime(date_str, '%d/%m/%Y')
    except ValueError:
        try:
            # Tenta o formato YYYY-MM-DD
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Data deve estar no formato DD/MM/YYYY ou YYYY-MM-DD")


if __name__ == "__main__":
    import asyncio
    
    
    async def test_dregenerate_query():
        accountant = Accountant()
        myssql = Mysql()
            # Exemplo de uso
        #periodos = [
        #    {'periodo': '01', 'inicio': '30/12/2024', 'termino': '05/01/2025'},
        #    {'periodo': '02', 'inicio': '06/01/2025', 'termino': '12/01/2025'},
        #    {'periodo': '03', 'inicio': '13/01/2025', 'termino': '19/01/2025'},
        #    {'periodo': '04', 'inicio': '20/01/2025', 'termino': '26/01/2025'},
        #    {'periodo': '05', 'inicio': '27/01/2025', 'termino': '31/01/2025'}
        #]
        
               
        negocio_idx = "**********"
        data_inicio = "01/01/2025"
        data_termino = "31/01/2025"

        data_inicio = datetime(2025, 1, 1)
        data_termino = datetime(2025, 1, 4)   


        period_type = accountant.get_period_type(data_inicio, data_termino)
        #print(f"Tipo do Período: {period_type}")

        periodos = accountant._calcular_periodos(data_inicio, data_termino, period_type)
        #print(f"Períodos Calculados: {periodos}")

        query = await accountant.generate_query(periodos, negocio_idx, data_inicio, data_termino)
        result = await myssql.query(query)
        #print("query",  query)
        #print("result", result)
    
        headers = ["ID", "CODIGO", "NOME", "SALDO_ANTERIOR", "MOVIMENTO", "PERIODO_01", "PERIODO_02", "PERIODO_03", "PERIODO_04", "PERIODO_05", "SALDO_ATUAL", "PERCENTUAL"]
        headers = ["ID", "CODIGO", "NOME",  "SALDO_ATUAL", "PERCENTUAL"]
        contas_tabular = [[conta.get(h, '') for h in headers] for conta in result]
        print(tabulate(contas_tabular, headers=headers))


    def parse_date(date_str):
        """
        Tenta converter uma string de data em diferentes formatos para datetime.
        Aceita formatos: DD/MM/YYYY ou YYYY-MM-DD
        
        Args:
            date_str (str): String contendo a data
            
        Returns:
            datetime: Objeto datetime convertido
        """
        try:
            # Tenta primeiro o formato DD/MM/YYYY
            return datetime.strptime(date_str, '%d/%m/%Y')
        except ValueError:
            try:
                # Tenta o formato YYYY-MM-DD
                return datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                raise ValueError("Data deve estar no formato DD/MM/YYYY ou YYYY-MM-DD")



    async def test_dre():
        
        # Parâmetros de teste
        data_inicio = "01/03/2025"
        data_termino = "31/03/2025"
        
        data_inicio = "2025-01-01"
        data_termino = "2025-02-23"
        
                
        
        filter = {
            "negocio_idx": "**********",  # ID de negócio usado no teste do DRE
            "data_inicio": data_inicio,
            "data_termino": data_termino
        }
        
        
        # Executa a busca
        result = await dre_generate(filter)
        dre = result['dre']
        periodos = result['periodos']
        periodos_tipo = result['periodos_tipo']

        #print("dre",dre)
        #print("periodos",periodos)
        #print("periodos_tipo",periodos_tipo)
        
        
        headers = ["ID", "CODIGO", "NOME", "SALDO_ANTERIOR", "MOVIMENTO", "PERIODO_01", "PERIODO_02", "PERIODO_03", "PERIODO_04", "PERIODO_05", "SALDO_ATUAL", "PERCENTUAL"]
        headers = ["ID", "CODIGO", "NOME",  "SALDO_ATUAL", "PERCENTUAL"]
        contas_tabular = [[conta.get(h, '') for h in headers] for conta in dre]
        #print(tabulate(contas_tabular, headers=headers))





    async def test_fetch_accounts_dre():
        
        print("\nTestando fetch_accounts_dre...")
        accountant = Accountant()
        
        # Parâmetros de teste
        negocio_idx = "**********"  # ID de negócio usado no teste do DRE
        data = {
            "colunas_nome": ["IDX", "CODIGO", "NOME", "NATUREZA_ID", "TIPO_ID,"]
        }
        
        # Executa a busca
        columns_str = ",".join(data["colunas_nome"])
        result = await accountant.fetch_accounts_dre(negocio_idx, columns_str)
        
        if result:
            print("Contas DRE encontradas:")
            table_data = []
            for item in result:
                table_data.append(item)
            print(tabulate(table_data, headers="keys", tablefmt="psql"))
        else:
            print("Nenhuma conta DRE encontrada")




    async def test_update_transaction_query():
        accountant = Accountant()

        # Removida a vírgula extra que estava criando uma tupla
        lancamento = {
            "IDX": "123456",
            "CONTA_DEBITO": 5311,
            "CONTA_CREDITO": 0,
            "VALOR": 7,
            "LANCAMENTO_IDX": "L001"
        }
        
        query = await accountant.update_transaction_query(lancamento)
        print("query", query)


    async def test_atualizacao_contabil():
        mysql = Mysql()
        # Exemplo de uso da função
        lancamentos = [
            {
                "IDX": "123456",
                "CONTA_DEBITO": 5311,
                "CONTA_CREDITO": 0,
                "VALOR": 7,
                "LANCAMENTO_IDX": "L001"
            },
            {
                "IDX": "123457",
                "CONTA_DEBITO": 0,
                "CONTA_CREDITO": 5312,
                "VALOR": 7,
                "LANCAMENTO_IDX": "L002"
            },

            {
                "IDX": "123457",
                "CONTA_DEBITO": 0,
                "CONTA_CREDITO": 5312,
                "VALOR": 20,
                "LANCAMENTO_IDX": "L002"
            },

            {
                "IDX": "123457",
                "CONTA_DEBITO": 5352,
                "CONTA_CREDITO": 0,
                "VALOR": 20,
                "LANCAMENTO_IDX": "L002"
            },
        
        ]
    


        """
        Gera um script SQL para atualizar a tabela CONTABIL_CONTA com base   nos lançamentos contábeis.
        
        Args:
            lancamentos (list): Lista de dicionários representando   lançamentos contábeis.
            
        Returns:
            str: Script SQL completo para atualização das contas.
        """
        query = "START TRANSACTION;\n"
        
        for lancamento in lancamentos:
            idx = lancamento.get("IDX", "")
            conta_debito = lancamento.get("CONTA_DEBITO", 0)
            conta_credito = lancamento.get("CONTA_CREDITO", 0)
            valor = lancamento.get("VALOR", 0)
            lancamento_idx = lancamento.get("LANCAMENTO_IDX", "")
            
            # Processamento da conta de débito (se existir)
            if conta_debito != 0:
                query += f"""
    -- Atualizando conta de débito {conta_debito} para lançamento {idx}
    UPDATE CONTABIL_CONTA 
    SET 
        DEBITO = CASE 
            WHEN NATUREZA_ID = 'D' THEN DEBITO + {valor}  -- Caso 1.1
            WHEN NATUREZA_ID = 'C' THEN DEBITO - {valor}  -- Caso 2.1
            ELSE DEBITO
        END,
        SALDO_ATUAL = CASE 
            WHEN NATUREZA_ID = 'D' THEN SALDO_ATUAL + {valor}  -- Caso 1.2
            WHEN NATUREZA_ID = 'C' THEN SALDO_ATUAL - {valor}  -- Caso 2.2
            ELSE SALDO_ATUAL
        END
    WHERE ID = {conta_debito};
    """
            
            # Processamento da conta de crédito (se existir)
            if conta_credito != 0:
                query += f"""
    -- Atualizando conta de crédito {conta_credito} para lançamento {idx}
    UPDATE CONTABIL_CONTA 
    SET 
        CREDITO = CASE 
            WHEN NATUREZA_ID = 'D' THEN CREDITO - {valor}  -- Caso 3.1
            WHEN NATUREZA_ID = 'C' THEN CREDITO + {valor}  -- Caso 4.1
            ELSE CREDITO
        END,
        SALDO_ATUAL = CASE 
            WHEN NATUREZA_ID = 'D' THEN SALDO_ATUAL - {valor}  -- Caso 3.2
            WHEN NATUREZA_ID = 'C' THEN SALDO_ATUAL + {valor}  -- Caso 4.2
            ELSE SALDO_ATUAL
        END
    WHERE ID = {conta_credito};
    """
        
        query += "COMMIT;"
        print(query)
        result = await mysql.query(query)
        print(result)
    
    
    async def test_create_transaction():
        data = {
            'contabil_lancamento': {
                'IDX': '**********',
                'DATA': '2025-03-18',
                'DOCUMENTO': 'cupom 102530',
                'HISTORICO': 'Deposito',
                'NEGOCIO_IDX': '**********',
                'USUARIO_IDX': '**********',
                'OPERACAO': 'insert'
            },
            'contabil_lancamento_conta': [
                {
                    'IDX': '**********',
                    'CONTA_DEBITO': '5312',
                    'CONTA_CREDITO': 0,
                    'VALOR': 5,
                    'LANCAMENTO_IDX': '**********'
                },
                {
                    'IDX': '**********',
                    'CONTA_DEBITO': 0,
                    'CONTA_CREDITO': '5311',
                    'VALOR': 5,
                    'LANCAMENTO_IDX': '**********'
                }
            ]
        }
        negocio_idx = "**********"
        accountant = Accountant()   
        result = await accountant.create_transaction(negocio_idx,data)
        print("result", result)


    async def test_transaction_generate():
        accountant = Accountant()
        data = {
            "negocio_idx": "**********",
            "tipo_id": "1",
            "modelo_idx": "**********",
            "historico": "Conta de água a pagar no valor de R$ 56,68  para COMPANHIA DE SANEAMENTO BASICO DO ESTADO DE SAO PAULO - SABESP",
            "usuario_idx": "**********",
            "data":"30/03/25"   
        }

        result = await transaction_generate(data)
       # print("==========================================================================")
        print(result)


    async def test_transaction_create():
        accountant = Accountant()
        data = {
            "negocio_idx": "**********",
            "tipo_id": "1",
            "modelo_idx": "**********",
            "historico": "Pagamento de conta de luz no valor de R$ 100,00 com dinheiro do caixa.",
            "usuario_idx": "**********",
            "data":"30/03/25"   
        }

        result = await transaction_create(data)
       # print("==========================================================================")
        print(result)


    async def test_fetch_account():
        """
        Testa o endpoint /account/fetch/{negocio_idx}/{area_id}
        """
        # Dados de teste
        negocio_idx = "**********"
        area_id = 2  # ID da área (ex: 1 para Ativo)
        data = {
            "colunas_nome": [
                "IDX",
                "CODIGO",
                "NOME",
                "SALDO_ATUAL"
            ]
        }
    
        # Faz a requisição POST
     
        response =await fetch_account(negocio_idx,area_id,data)
        print("response",response)


    #asyncio.run(test_fetch_accounts_dre())
    #asyncio.run(test_dre())
    #asyncio.run(test_generate_query())
    #asyncio.run(test_atualizacao_contabil())
    #asyncio.run(test_update_transaction_query())
    #asyncio.run(test_atualizacao_contabil())
    #asyncio.run(test_create_transaction())
    #asyncio.run(test_transaction_generate())
    asyncio.run(test_fetch_account())






    