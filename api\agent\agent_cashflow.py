from fastapi import APIRouter
from .agent_mysql import Mysql
from pydantic import BaseModel
import math
from datetime import datetime

router = APIRouter()


class Cashflow:
    def __init__(self):
        self.mysql = Mysql()


    

    def formatar_data(self, data_str: str) -> str:
        if data_str == "":
            return ""
        
        """Converte uma string de data no formato 'YYYY,MM,DD' para 'DD/MM/YY'."""
        # Substitui as vírgulas por hífens
        data_str = data_str.replace(',', '-')
        
        # Converte a string para um objeto datetime
        data_object = datetime.strptime(data_str, "%Y-%m-%d")
        
        # Formata o objeto datetime para o formato desejado
        return data_object.strftime("%d/%m/%y")  # Formato: dia/mês

    async def create(self, data: dict):
        print("/create #####",data)
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CAIXA ({columns}) VALUES ({values});"
            inserts.append(query)
        
        query_insert = " ".join(inserts)
        print("query_insert",query_insert);
        result = await self.mysql.query(query_insert)
        print ("result",result)
        return result

    async def create_supply(self, data: dict):
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CAIXA_SUPRIMENTO ({columns}) VALUES ({values});"
            inserts.append(query)
        
        query_insert = " ".join(inserts)
        result = await self.mysql.query(query_insert)
        return result

    async def create_withdrawal(self, data: dict):
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CAIXA_SANGRIA ({columns}) VALUES ({values});"
            inserts.append(query)
        
        query_insert = " ".join(inserts)
        result = await self.mysql.query(query_insert)
        return result

    async def create_verified_payment(self, data: dict):
        if not data["adicionados"]:
            raise ValueError("A lista 'adicionados' está vazia")

        primeiro_item = data["adicionados"][0]
        caixa_idx = primeiro_item["CAIXA_IDX"]
        form_pagto = primeiro_item["FORM_PAGTO"]


        query_delete = f"""
        DELETE FROM CAIXA_RECEBIMENTO
        WHERE CAIXA_IDX = '{caixa_idx}' AND FORM_PAGTO = {form_pagto}
        """
   
        
        delete_result = await self.mysql.query(query_delete)
   

        # Resto do código para inserção...
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO CAIXA_RECEBIMENTO ({columns}) VALUES ({values});"
            inserts.append(query)
        
        query_insert = " ".join(inserts)
  
        
        insert_result = await self.mysql.query(query_insert)
  
        
        return insert_result

    async def fetch(self, business_idx: str, user_idx: str, columns: list):
        columns_str = ", ".join(columns)  # Junta as colunas corretamente
        query = f"""
        SELECT {columns_str} 
        FROM VISAO_CAIXA_OPERADOR_NEGOCIO
        WHERE NEGOCIO_IDX = '{business_idx}' 
        AND OPERADOR_IDX = '{user_idx}' 
        AND EXCLUIDO = 0 
        AND FECHADO = 0
        """
      
        result = await self.mysql.query(query)
        return result[0] if result else None

    async def balance_by_payment(self, caixa_idx: str):
        query = f"""
        SELECT 
            vfp.ID,
            vfp.NOME,
            SUM(
                CASE 
                    WHEN v.PAGTO1 = vfp.IDX THEN COALESCE(v.TOTALPAGO1_RS, 0)
                    WHEN v.PAGTO2 = vfp.IDX THEN COALESCE(v.TOTALPAGO2_RS, 0)
                    ELSE 0
                END
            ) - SUM(
                CASE 
                    WHEN v.PAGTO1 = vfp.IDX THEN COALESCE(v.TROCO, 0)
                    ELSE 0
                END
            ) AS TOTAL
        FROM 
            VENDA v
        CROSS JOIN 
            VENDA_FORMA_PAGAMENTO vfp
        WHERE 
            v.CAIXA_IDX = '{caixa_idx}'
            AND (v.PAGTO1 = vfp.IDX OR v.PAGTO2 = vfp.IDX)
        GROUP BY 
            vfp.ID, vfp.NOME
        ORDER BY 
            TOTAL DESC
        """
        print("query", query)
        result = await self.mysql.query(query)
        print("result", result)
        return result

    async def supply_by_payment(self, caixa_idx: str):
        query = f"""
        SELECT 
            vfp.ID,
            vfp.NOME,
            COALESCE(SUM(cs.TOTAL), 0) AS TOTAL
        FROM 
            CAIXA_SUPRIMENTO cs
        LEFT JOIN 
            VENDA_FORMA_PAGAMENTO vfp ON vfp.ID = cs.FORMA_PAGTO_ID
        WHERE 
            cs.CAIXA_IDX = '{caixa_idx}'
            AND cs.EXCLUIDO = 0
        GROUP BY 
            vfp.ID, vfp.NOME
        ORDER BY 
            TOTAL DESC
        """
        result = await self.mysql.query(query)
        return result

    async def withdrawal_by_payment(self, caixa_idx: str):
        query = f"""
        SELECT 
            vfp.ID,
            vfp.NOME,
            COALESCE(SUM(cs.VALOR), 0) AS TOTAL
        FROM 
            CAIXA_SANGRIA cs
        LEFT JOIN 
            VENDA_FORMA_PAGAMENTO vfp ON vfp.ID = cs.FORMA_PAGTO_ID
        WHERE 
            cs.CAIXA_IDX = '{caixa_idx}'
            AND cs.EXCLUIDO = 0
        GROUP BY 
            vfp.ID, vfp.NOME
        ORDER BY 
            TOTAL DESC
        """
        result = await self.mysql.query(query)
        return result

    async def verified_by_payment(self, caixa_idx: str):
        query = f"""
        SELECT 
            vfp.ID,
            vfp.NOME,
            COALESCE(SUM(cr.TOTAL), 0) AS TOTAL
        FROM 
            CAIXA_RECEBIMENTO cr
        LEFT JOIN 
            VENDA_FORMA_PAGAMENTO vfp ON vfp.ID = cr.FORM_PAGTO
        WHERE 
            cr.CAIXA_IDX = '{caixa_idx}'
        GROUP BY 
            vfp.ID, vfp.NOME
        ORDER BY 
            TOTAL DESC
        """
        result = await self.mysql.query(query)
        return result

    async def fetch_verified_payment(self, caixa_idx: str, form_pagto: int):
        query = f"""
        SELECT 
            VR_UNIT,
            QTDE,
            FORM_PAGTO,
            TOTAL
        FROM 
            CAIXA_RECEBIMENTO
        WHERE 
            CAIXA_IDX = '{caixa_idx}'
            AND FORM_PAGTO = {form_pagto}
        ORDER BY 
            FORM_PAGTO
        """
        result = await self.mysql.query(query)
        return result

    async def sale_summary(self, caixa_idx: str):
        print("sales_summary()", caixa_idx)
        query = f"""
        SELECT 
        LPAD('1', 3, '0') AS VENDA_INICIAL,
        LPAD(COUNT(*), 3, '0') AS VENDA_FINAL,
        SUM(CASE WHEN STATUS_PAGAMENTO = 'C' THEN 1 ELSE 0 END) AS VENDAS_CANCELADAS_NR,
        COALESCE(SUM(CASE WHEN STATUS_PAGAMENTO = 'C' THEN TOTAL_RS ELSE 0 END), 0) AS VENDAS_CANCELADAS_TOTAL,
        SUM(CASE WHEN STATUS_PAGAMENTO = 'P' THEN 1 ELSE 0 END) AS VENDAS_PENDENTES_NR,
        COALESCE(SUM(CASE WHEN STATUS_PAGAMENTO = 'P' THEN TOTAL_RS ELSE 0 END), 0) AS VENDAS_PENDENTES_TOTAL,
        SUM(CASE WHEN STATUS_PAGAMENTO = 'R' THEN 1 ELSE 0 END) AS VENDAS_REALIZADAS_NR,
            COALESCE(SUM(CASE WHEN STATUS_PAGAMENTO = 'R' THEN TOTAL_RS ELSE 0 END), 0) AS VENDAS_REALIZADAS_TOTAL
        FROM 
            VENDA
        WHERE 
            CAIXA_IDX = '{caixa_idx}'
        """
        print("query", query)
        result = await self.mysql.query(query)
        print("result", result)
        return result[0] if result else None

    async def close(self, caixa_idx: str):
        query = f"""
        UPDATE CAIXA 
        SET FECHADO = 1, 
            FECHAMENTO_DT = CURDATE(), 
            FECHAMENTO_HORA = TIME_FORMAT(CURTIME(), '%H:%i')
        WHERE IDX = '{caixa_idx}' AND FECHADO = 0
        """
        result = await self.mysql.query(query)
        return result

    async def products_sold(self, caixa_idx: str):
        query = f"""
        SELECT 
            VISAO_VENDA_PRODUTO_CAIXA.* 
        FROM 
            VISAO_VENDA_PRODUTO_CAIXA
        WHERE 
            CAIXA_IDX = '{caixa_idx}' 
        ORDER BY 
            NOME
        """
        result = await self.mysql.query(query)
        return result

    async def fetch_withdrawals2(self, caixa_idx: str):
        query = f"""
        SELECT DOCUMENTO, HISTORICO, VALOR, CONTA, FORMA_PAGAMENTO
        FROM VISAO_SANGRIA
        WHERE CAIXA_IDX = '{caixa_idx}'
        ORDER BY DATA_HORA DESC
        """
        
        result = await self.mysql.query(query)
        return result

    async def fetch_withdrawals(self, caixa_idx: str):
        query = f"""
        SELECT DOCUMENTO, HISTORICO, VALOR, CONTA, FORMA_PAGAMENTO
        FROM VISAO_SANGRIA
        WHERE CAIXA_IDX = '{caixa_idx}'
        """
        result = await self.mysql.query(query)
        
        
        return result

    async def fetch_taxes(self, caixa_idx: str):
        query = f"""
        SELECT COALESCE(SUM(TAXA_ENTREGA), 0) AS TOTAL_TAXA_ENTREGA
        FROM VENDA
        WHERE CAIXA_IDX = '{caixa_idx}'
        """

        result = await self.mysql.query(query)
        
   
        
        
        return result[0] if result else {"TOTAL_TAXA_ENTREGA": 0}

    async def fetch_history(self, negocio_idx: str, filters: dict):
        where_clauses = [f"CAIXA.NEGOCIO_IDX = '{negocio_idx}'", "CAIXA.EXCLUIDO = 0"]
        
        if filters.get('caixa_idx'):
            where_clauses.append(f"CAIXA.IDX LIKE '%{filters['caixa_idx']}%'")
        if filters.get('data_inicial'):
            where_clauses.append(f"CAIXA.ABERTURA >= '{filters['data_inicial']}'")
        if filters.get('data_final'):
            where_clauses.append(f"CAIXA.ABERTURA <= '{filters['data_final']}'")
        if filters.get('hora_inicial'):
            where_clauses.append(f"CAIXA.ABERTURA_HORA >= '{filters['hora_inicial']}'")
        if filters.get('hora_final'):
            where_clauses.append(f"CAIXA.ABERTURA_HORA <= '{filters['hora_final']}'")

        where_clause = " AND ".join(where_clauses)
        
        order_by = "DESC" if filters.get('ordem', 'DESC').upper() == 'DESC' else "ASC"
        limit = int(filters.get('limite', 10))
        offset = int(filters.get('inicio', 0))
        
        query = f"""
        SELECT 
            CAIXA.IDX, CAIXA.NEGOCIO_IDX, CAIXA.ABERTURA, CAIXA.ABERTURA_HORA,
            CAIXA.ATUALIZADO, CAIXA.EXCLUIDO, CAIXA.FECHADO, CAIXA.FECHAMENTO_DT,
            CAIXA.FECHAMENTO_HORA, CAIXA.OBSERVACAO, CAIXA.SALDO_CONFERIDO,
            CAIXA.SALDO_REGISTRADO, CAIXA.SANGRIA, CAIXA.SUPRIMENTO, CAIXA.VENDAS,
            CAIXA.OPERADOR_IDX, USUARIO.NOME AS OPERADOR_NOME
        FROM CAIXA 
        LEFT JOIN USUARIO ON CAIXA.OPERADOR_IDX = USUARIO.IDX
        WHERE {where_clause}
        ORDER BY CAIXA.ABERTURA {order_by}, CAIXA.ABERTURA_HORA {order_by}
        LIMIT {limit} OFFSET {offset}
        """

        #print("query", query)
        result = await self.mysql.query(query)
        count_query = f"""
        SELECT COUNT(*) as total
        FROM CAIXA
        WHERE {where_clause}
        """
        
        count_result = await self.mysql.query(count_query)
        total_caixas = count_result[0]['total'] if count_result else 0

      


        # Formatando os resultados
        formatted_result = []
        for caixa in result:
            formatted_caixa = {
                "IDX": caixa["IDX"],
                "NEGOCIO_IDX": caixa["NEGOCIO_IDX"],
                "ABERTURA": self.formatar_data(caixa["ABERTURA"]) if caixa["ABERTURA"] else None,
                "ABERTURA_HORA": caixa["ABERTURA_HORA"],
                "ATUALIZADO": caixa["ATUALIZADO"],
                "EXCLUIDO": bool(caixa["EXCLUIDO"]),
                "FECHADO": bool(caixa["FECHADO"]),
                "FECHAMENTO_DT":  self.formatar_data(caixa["FECHAMENTO_DT"]) if caixa["FECHAMENTO_DT"] else None,
                "FECHAMENTO_HORA": caixa["FECHAMENTO_HORA"],
                "OBSERVACAO": caixa["OBSERVACAO"],
                "SALDO_CONFERIDO": float(caixa["SALDO_CONFERIDO"]) if caixa["SALDO_CONFERIDO"] is not None else None,
                "SALDO_REGISTRADO": float(caixa["SALDO_REGISTRADO"]) if caixa["SALDO_REGISTRADO"] is not None else None,
                "SANGRIA": float(caixa["SANGRIA"]) if caixa["SANGRIA"] is not None else None,
                "SUPRIMENTO": float(caixa["SUPRIMENTO"]) if caixa["SUPRIMENTO"] is not None else None,
                "VENDAS": float(caixa["VENDAS"]) if caixa["VENDAS"] is not None else None,
                "OPERADOR_IDX": caixa["OPERADOR_IDX"],
                "OPERADOR_NOME": caixa["OPERADOR_NOME"]
            }
            formatted_result.append(formatted_caixa)
        
        return {
            "total": total_caixas,
            "paginas": math.ceil(total_caixas / limit),
            "caixas": formatted_result
        }

    async def reopen(self, caixa_idx: str, usuario_id: str):
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        query = f"""
        UPDATE CAIXA 
        SET FECHADO = 0, 
            FECHAMENTO_DT = NULL, 
            FECHAMENTO_HORA = NULL 
        WHERE IDX = '{caixa_idx}' AND FECHADO = 1
        """
        result = await self.mysql.query(query)
        
        if result is not None:
            caixa = await self.get_cashflow(caixa_idx)
            return {"success": True, "caixa": caixa}
        else:
            return {"error": "Falha na execução da query"}

    async def get_cashflow(self, caixa_idx: str):
        query = f"""
        SELECT *
        FROM VISAO_CAIXA_OPERADOR_NEGOCIO
        WHERE IDX = '{caixa_idx}'
        """
        result = await self.mysql.query(query)
        return result[0] if result else None

    async def delete_cashflow(self, caixa_idx: str):
        try:
            # Prepara as queries para exclusão lógica
            queries = [
                # Marca o caixa como excluído
                f"UPDATE CAIXA SET EXCLUIDO = 1 WHERE IDX = '{caixa_idx}'",
                
                # Marca as sangrias do caixa como excluídas
                f"UPDATE CAIXA_SANGRIA SET EXCLUIDO = 1 WHERE CAIXA_IDX = '{caixa_idx}'",
                
                # Marca os suprimentos do caixa como excluídos
                f"UPDATE CAIXA_SUPRIMENTO SET EXCLUIDO = 1 WHERE CAIXA_IDX = '{caixa_idx}'",
                
                # Atualiza vendas relacionadas ao caixa
                f"UPDATE VENDA SET STATUS_PAGAMENTO = 'C' WHERE CAIXA_IDX = '{caixa_idx}'",
                
                # Marca produtos das vendas do caixa como excluídos
                f"""UPDATE VENDA_PRODUTO vp 
                   INNER JOIN VENDA v ON v.IDX = vp.VENDA_ID
                   SET vp.EXCLUIDO = 1 
                   WHERE v.CAIXA_IDX = '{caixa_idx}'"""
            ]
            
            # Executa todas as queries em sequência
            for query in queries:
                await self.mysql.query(query)
            
            return {"success": True, "message": "Caixa e registros relacionados excluídos com sucesso"}
            
        except Exception as e:
            print(f"Erro ao excluir caixa: {str(e)}")
            return {"success": False, "message": f"Erro ao excluir caixa: {str(e)}"}

@router.post("/reopen/{caixa_idx}/{usuario_id}", operation_id="reopen_cashflow1")
async def reopen_cashflow(caixa_idx: str, usuario_id: str):
    print("@@@@@@@x   reopen_cashflow()")
    print("caixa_idx", caixa_idx)
    print("usuario_id", usuario_id)
    cashflow = Cashflow()
    try:
        result = await cashflow.reopen(caixa_idx, usuario_id)
        print("result inicial bruto", result)
        if "error" in result:
            return {"status": "erro", "mensagem": result["error"]}
        else:
            return {"status": "sucesso", "mensagem": "Caixa reaberto com sucesso", "caixa": result["caixa"]}
    except Exception as e:
        print("Erro ao reabrir o caixa:", str(e))
        return {"status": "erro", "mensagem": f"Erro ao reabrir o caixa: {str(e)}"}

@router.get("/products_sold/{caixa_idx}")
async def get_products_sold(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.products_sold(caixa_idx)
    return result

@router.post("/close/{caixa_idx}")
async def close_cashflow(caixa_idx: str):

    cashflow = Cashflow()
    try:
        result = await cashflow.close(caixa_idx)
     
        return {"status": "sucesso", "mensagem": "Caixa fechado com sucesso"}
    except Exception as e:
        print("Erro ao fechar o caixa:", str(e))
        return {"status": "erro", "messagem": f"Erro ao fechar o caixa: {str(e)}"}

@router.get("/sale_summary/{caixa_idx}")
async def get_sale_summary(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.sale_summary(caixa_idx)
    return result


@router.post("/create")
async def create(data: dict):
    print("##### /agent/cashflow/create #####",data)
    cashflow = Cashflow()
    result = await cashflow.create(data)
    return result

@router.post("/supply/create")
async def create_supply(data: dict):
    cashflow = Cashflow()
    result = await cashflow.create_supply(data)
    return result

@router.post("/withdrawal/create")
async def create_withdrawal(data: dict):
    cashflow = Cashflow()
    result = await cashflow.create_withdrawal(data)
    return result

@router.post("/verified_payment/create")
async def create_verified_payment(data: dict):


    cashflow = Cashflow()
    result = await cashflow.create_verified_payment(data)
    return result

@router.post("/fetch")
async def fetch_cashflow(data: dict):
    print("fetch()", data);
    cashflow = Cashflow()
    # Não é necessário juntar as colunas aqui, pois isso será feito no método fetch
    result = await cashflow.fetch(data["business_idx"], data["user_idx"], data["columns"])
    print("result", result);
    return result

@router.get("/balance_by_payment/{caixa_idx}")
async def balance_by_payment(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.balance_by_payment(caixa_idx)
    return result

@router.get("/supply_by_payment/{caixa_idx}")
async def supply_by_payment(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.supply_by_payment(caixa_idx)
    return result

@router.get("/withdrawal_by_payment/{caixa_idx}")
async def withdrawal_by_payment(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.withdrawal_by_payment(caixa_idx)
    return result

@router.get("/verified_by_payment/{caixa_idx}")
async def verified_by_payment(caixa_idx: str):
    cashflow = Cashflow()
    result = await cashflow.verified_by_payment(caixa_idx)
    return result

@router.get("/verified_payment/fetch/{caixa_idx}/{form_pagto}")
async def fetch_verified_payment(caixa_idx: str, form_pagto: int):
    cashflow = Cashflow()
   
    result = await cashflow.fetch_verified_payment(caixa_idx, form_pagto)
    return result

@router.get("/withdrawal/fetch/{caixa_idx}")
async def fetch_withdrawals(caixa_idx: str):
    cashflow = Cashflow()
    try:
        result = await cashflow.fetch_withdrawals(caixa_idx)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.get("/taxes/fetch/{caixa_idx}")
async def fetch_taxes(caixa_idx: str):
    cashflow = Cashflow()
    try:
        result = await cashflow.fetch_taxes(caixa_idx)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post("/history/fetch/{negocio_idx}")
async def fetch_cashflow_history(negocio_idx: str, filters: dict):
    #print("fetch_cashflow_history()", negocio_idx, filters)
    cashflow = Cashflow()
    try:
        result = await cashflow.fetch_history(negocio_idx, filters)
        #print("result", result)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post(
    "/reopen/{caixa_idx}/{usuario_id}",
    operation_id="reabrir_caixa"
)
async def reopen_cashflow(caixa_idx: str, usuario_id: str):
    print("@@@@@@@x   reopen_cashflow()")
    print("caixa_idx", caixa_idx)
    print("usuario_id", usuario_id)
    cashflow = Cashflow()
    try:
        result = await cashflow.reopen(caixa_idx, usuario_id)
        print("result inicial bruto", result)
        if "error" in result:
            return {"status": "erro", "mensagem": result["error"]}
        else:
            return {"status": "sucesso", "mensagem": "Caixa reaberto com sucesso", "caixa": result["caixa"]}
    except Exception as e:
        print("Erro ao reabrir o caixa:", str(e))
        return {"status": "erro", "mensagem": f"Erro ao reabrir o caixa: {str(e)}"}

# Não se esqueça de registrar o Blueprint na sua aplicação principal
# app.register_blueprint(agent_cashflow, url_prefix='/api/agent')

@router.get("/get/{caixa_idx}")
async def get_cashflow(caixa_idx: str):
    cashflow = Cashflow()
    try:
        result = await cashflow.get_cashflow(caixa_idx)
        if result:
            return {"success": True, "data": result}
        else:
            return {"success": False, "message": "Caixa não encontrado"}
    except Exception as e:
        print(f"Erro ao buscar o caixa: {str(e)}")
        return {"success": False, "message": f"Erro ao buscar o caixa: {str(e)}"}

@router.delete("/delete/{caixa_idx}")
async def delete_cashflow(caixa_idx: str):
    cashflow = Cashflow()
    try:
        result = await cashflow.delete_cashflow(caixa_idx)
        if result["success"]:
            return {"status": "sucesso", "mensagem": result["message"]}
        else:
            return {"status": "erro", "mensagem": result["message"]}
    except Exception as e:
        print(f"Erro ao excluir o caixa: {str(e)}")
        return {"status": "erro", "mensagem": f"Erro ao excluir o caixa: {str(e)}"}

if __name__ == "__main__":
    import asyncio
    from fastapi.testclient import TestClient
    from api.main import app  # Supondo que sua aplicação FastAPI principal está em api/main.py
    import datetime

    async def test_create():
        client = TestClient(app)
        test_data = {
            "adicionados": [
                {
                    "IDX": "1234567890",
                    "NEGOCIO_IDX": "0987654321",
                    "ABERTURA": datetime.datetime.now().strftime("%Y-%m-%d"),
                    "ABERTURA_HORA": datetime.datetime.now().strftime("%H:%M"),
                    "EXCLUIDO": 0,
                    "FECHADO": 0,
                    "OPERADOR_IDX": "1234567890"
                }
            ]
        }
        
        response = client.post("/api/agent/cashflow/create", json=test_data)
        #print("Resultado do teste de criação:", response.json())

    async def test_create_supply():
        client = TestClient(app)
        test_data = {
            "adicionados": [
                {
                    "IDX": "1234567891",
                    "CAIXA_IDX": "0987654322",
                    "TOTAL": "101.07",
                    "FORMA_PAGTO_ID": 2,
                    "CONTA_ID": 0
                }
            ]
        }
        
        response = client.post("/api/agent/cashflow/supply/create", json=test_data)

    async def test_fetch_cashflow():
        client = TestClient(app)
        
        # Teste 1: Buscar caixa com todos os parâmetros
        request_data = {
            "business_idx": "1038754321",
            "user_idx": "5206550291",
            "columns": ["IDX", "ABERTURA", "ABERTURA_HORA","FECHADO"],
        }
        
        response = client.post("/api/agent/cashflow/fetch", json=request_data)

        # Teste 2: Buscar caixa sem user_idx
        #response = client.get(f"/api/agent/cashflow/fetch?business_idx={business_idx}", params={"columns": columns})
        #print("Resultado do teste de busca de caixa (sem user_idx):", response.json())

        # Teste 3: Buscar caixa com colunas padrão
        #response = client.get(f"/api/agent/cashflow/fetch?business_idx={business_idx}")
        #print("Resultado do teste de busca de caixa (colunas padrão):", response.json())

    async def test_create_withdrawal():
        client = TestClient(app)
        test_data = {
            "adicionados": [
                {
                    "IDX": "1234567892",
                    "CAIXA_IDX": "0987654323",
                    "VALOR": "50.00",
                    "DOCUMENTO": "DOC123456",
                    "HISTORICO": "Compra de um pacote de bolacha",
                    "FORMA_PAGTO_ID": 1,
                    "CONTA_ID": 0,
                    "EXCLUIDO": 0
                }
            ]
        }
        response = client.post("/api/agent/cashflow/withdrawal/create", json=test_data)

    async def test_balance_by_payment():
        cashflow = Cashflow()
        caixa_idx = "1966425878"  # Substitua pelo ID de um caixa real para teste
        result = await cashflow.balance_by_payment(caixa_idx)

    async def test_supply_by_payment():
        cashflow = Cashflow()
        caixa_idx = "196664"  # Substitua pelo ID de um caixa real para teste
        result = await cashflow.supply_by_payment(caixa_idx)

    async def test_withdrawal_by_payment():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/withdrawal_by_payment/{caixa_idx}")
        #print("Resultado do teste de sangria por forma de pagamento:", response.json())

    async def test_create_verified_payment():
        client = TestClient(app)
        test_data = {
            "adicionados": [
                {
                    "CAIXA_IDX": "3894036387",
                    "VR_UNIT": "51.00",
                    "QTDE": 2,
                    "FORM_PAGTO": 1,
                    "TOTAL": "102.00"
                },
                {
                    "CAIXA_IDX": "3894036387",
                    "VR_UNIT": "25.50",
                    "QTDE": 3,
                    "FORM_PAGTO": 2,
                    "TOTAL": "76.50"
                }
            ]
        }
        
        response = client.post("/api/agent/cashflow/verified_payment/create", json=test_data)

    async def test_fetch_verified_payment():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste,
        form_pagto = 1
        
        response = client.get(f"/api/agent/cashflow/verified_payment/fetch/{caixa_idx}?form_pagto={form_pagto}")

    async def test_verified_by_payment():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/verified_by_payment/{caixa_idx}")

    async def test_sale_summary():
        client = TestClient(app)
        caixa_idx = "9607804212"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/sale_summary/{caixa_idx}")

    async def test_close_cashflow():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.post(f"/api/agent/cashflow/close/{caixa_idx}")

    async def test_products_sold():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/products_sold/{caixa_idx}")

    async def test_fetch_withdrawals():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/withdrawal/fetch/{caixa_idx}")

    async def test_fetch_taxes():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        
        response = client.get(f"/api/agent/cashflow/taxes/fetch/{caixa_idx}")

    async def test_fetch_history():
        client = TestClient(app)
        filters = {
            "data_inicial": "",
            "data_final": "",
            "hora_inicial": "00:00",
            "hora_final": "23:59",
            "ordem": "DESC",
            "limite": 5, #quantidade de registros
            "inicio": 0 #offset
       }

        
        
        negocio_idx = "1038754322"  # IDX do negócio da Fernanda
        endpoint = f"/api/agent/cashflow/history/fetch/{negocio_idx}"
        response = client.post(endpoint, json=filters)

    async def test_reopen_cashflow():
        client = TestClient(app)
        caixa_idx = "1830214912"  # Substitua pelo ID de um caixa real para teste
        usuario_id = "5206550291"  # Substitua pelo ID de um usuário real para teste
        
        response = client.post(f"/api/agent/cashflow/reopen/{caixa_idx}/{usuario_id}")

    async def test_delete_cashflow():
        client = TestClient(app)
        caixa_idx = "3894036387"  # Substitua pelo ID de um caixa real para teste
        

    #asyncio.run(test_balance_by_payment())
    #asyncio.run(test_create())
    #asyncio.run(test_create_supply())
    #asyncio.run(test_fetch_cashflow())
    #asyncio.run(test_create_withdrawal())
    #asyncio.run(test_supply_by_payment())
    #asyncio.run(test_withdrawal_by_payment())
    #asyncio.run(test_create_verified_payment())
    #asyncio.run(test_verified_by_payment())
    asyncio.run(test_sale_summary())
    #asyncio.run(test_close_cashflow())
    #asyncio.run(test_products_sold())
    #asyncio.run(test_fetch_withdrawals()) 
    #asyncio.run(test_fetch_taxes())
    #asyncio.run(test_fetch_history())
    #asyncio.run(test_reopen_cashflow())
    #asyncio.run(test_delete_cashflow())
