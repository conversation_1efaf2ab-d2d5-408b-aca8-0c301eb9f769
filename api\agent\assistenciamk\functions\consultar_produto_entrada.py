import json
import os
import asyncio
import re
from typing import Dict


from api.agent.agent import salva_mensagem
from ...agent_neo4j import AgentNeo4j
from agents.tool import function_tool
from api.agent.decorators import usuario  # Importar o decorator personalizado
import platform
from ...agent_secret import Secret




# ---------- CACHE GLOBAL ----------
cache_var = {}   # int -> variação original
var_cnt   = 1    # contador global

secret = Secret()
is_local = platform.system() == "Windows"


# Carregar o esquema do Neo4j
schema_path = os.path.join(os.path.dirname(__file__), 'schemas', 'consultar_produto_entrada_neo4j.json')
with open(schema_path, 'r', encoding='utf-8') as f:
    consultar_produto_entrada_neo4j = json.load(f)

# Inicializar cliente Neo4j
neo4j = AgentNeo4j()

# Query padrão fixa - mantém o formato original para gerar cartões corretamente
QUERY_PADRAO = """
  MATCH (n:Negocio {idx: $negocio_idx})
    CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termos_produto) YIELD node AS p
    MATCH (n)-[:POSSUI_PRODUTO]->(p)
    WHERE p.excluido = 0
    OPTIONAL MATCH (p)-[:TEM_COR]->(c:Cor)<-[:DISPONIBILIZA_COR]-(n)
    WITH p.nome AS nome,
         head(collect(p.preco)) AS preco,
         head(collect(p.idx)) AS idx,
         head(collect(p.url_imagem)) AS url_imagem,
         collect(DISTINCT {codigo: p.codigo,
                           cor: COALESCE(c.nome, 'Cor única')}) AS variacoes
    RETURN nome,idx, preco, url_imagem, variacoes
    ORDER BY nome
"""

def processar_termos_compostos(termos):
    import re

    if not termos or not isinstance(termos, str):
        return termos

    termos = termos.strip()

    if ' AND ' in termos.upper() or ' OR ' in termos.upper():
        return termos

    if termos.isdigit():
        return termos

    palavras_ignoradas = {
        'de', 'da', 'do', 'das', 'dos', 'a', 'o', 'as', 'os', 'um', 'uma', 'uns', 'umas',
        'para', 'por', 'per', 'com', 'sem', 'sob', 'sobre', 'em', 'no', 'na', 'nos', 'nas',
        'a', 'à', 'às', 'ao', 'aos', 'pelo', 'pela', 'pelos', 'pelas', 'até', 'entre',
        'durante', 'contra', 'desde', 'após', 'antes', 'depois', 'como', 'que',
        'se', 'e', 'ou', 'mas', 'mais', 'muito', 'pouco', 'muitos', 'poucos', 'este',
        'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas', 'aquele', 'aquela',
        'aqueles', 'aquelas', 'meu', 'minha', 'meus', 'minhas', 'seu', 'sua', 'seus', 'suas'
    }

    palavras_invariaveis = {'jeans'}
    preservar_plural_diante_de = {'sapatos'}
    preposicoes_de = {'de', 'do', 'da', 'dos', 'das'}

    # ✅ REGRAS CORRIGIDAS: ordem importa
    regras_pt = [
        (r'(ões)$', 'ão'),
        (r'(ães)$', 'ão'),
        (r'(is)$', 'l'),
        (r'(éis)$', 'el'),
        (r'(eis)$', 'el'),
        (r'(óis)$', 'ol'),
        (r'(uis)$', 'ul'),
        (r'(ns)$', 'm'),
        (r'(rs)$', 'r'),
        (r'(is)$', 'il'),
        (r'(?<!e)s$', ''),  # ✅ remove 's' final, mas NÃO remove 'es'
    ]

    # ✅ REGRAS EM INGLÊS CORRIGIDAS: ordem importa
    regras_en = [
        (r'(ies)$', 'y'),   # babies → baby
        (r'(ves)$', 'f'),   # knives → knife
        (r'(ses)$', 's'),   # ✅ shoes → shoe (corrigido)
        (r'(s)$', ''),      # cats → cat
    ]

    def singularizar_palavra(palavra):
        palavra_lower = palavra.lower()

        if palavra_lower in palavras_invariaveis:
            return palavra_lower

        for plural, singular in regras_pt:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)

        for plural, singular in regras_en:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)

        return palavra_lower

    palavras = [p.strip() for p in termos.split()]
    palavras_processadas = []

    for i, palavra in enumerate(palavras):
        p_lower = palavra.lower()
        prox = palavras[i + 1].strip().lower() if i + 1 < len(palavras) else ''

        # ✅ Preserva plural só se vier antes de "de/do/da..."
        if p_lower in preservar_plural_diante_de and prox in preposicoes_de:
            palavra_final = p_lower
        else:
            palavra_final = singularizar_palavra(palavra)

        if palavra_final not in palavras_ignoradas:
            palavras_processadas.append(palavra_final)

    # ✅ Se não sobrou nada útil, retorna vazio
    if not palavras_processadas:
        return ""

    if len(palavras_processadas) == 1:
        return palavras_processadas[0]
    return ' AND '.join(palavras_processadas)

def aplicar_busca_fonetica(termos, distancia=None):
    """
    Adiciona sufixo ~ (ou ~n) a cada termo pesquisável para ativar busca fuzzy (Lucene),
    sem alterar operadores booleanos nem a estrutura da query.
    
    Regras:
    - Mantém AND/OR/NOT e parênteses como estão
    - Não altera frases entre aspas (para não virar consulta de proximidade)
    - Não altera números (ex.: códigos)
    - Não altera tokens com ':' (ex.: campo:valor)
    - Não duplica se já houver ~ ou ~n no final do token
    - distancia=None -> usa apenas "~"
      distancia=1/2 -> usa "~1" ou "~2"
    """
    import re

    if not termos or not isinstance(termos, str):
        return termos

    # Tokeniza preservando operadores, parênteses e frases entre aspas
    tokens = re.findall(r'"[^"]*"|\(|\)|\bAND\b|\bOR\b|\bNOT\b|[^\s()]+', termos)
    sufixo = '~' if distancia is None else f'~{distancia}'

    out = []
    for t in tokens:
        if t in ('AND', 'OR', 'NOT', '(', ')'):
            out.append(t)
            continue

        # Frases entre aspas: preserva
        if t.startswith('"') and t.endswith('"'):
            out.append(t)
            continue

        # Field query (campo:valor): preserva
        if ':' in t:
            out.append(t)
            continue

        # Números: preserva
        if re.fullmatch(r'\d+', t):
            out.append(t)
            continue

        # Já possui fuzzy explicitado
        if re.search(r'~\d*$', t):
            out.append(t)
            continue

        # Aplica fuzzy
        out.append(f'{t}{sufixo}')

    result = ' '.join(out)
    # Ajuste cosmético de espaços com parênteses
    result = re.sub(r'\(\s+', '(', result)
    result = re.sub(r'\s+\)', ')', result)
    return result
@function_tool
@usuario
async def consultar_produto_entrada(
                             usuario_whatsapp: str,
                             tipo_resposta: str, 
                             mensagem_inicial: str,
                             mensagem_final: str,
                             em_resposta_idx: str,
                             agente_idx: str,
                             conversa_idx: str,
                             usuario_idx: str,
                             canal_idx: str,
                             parametros: str,
                             ) -> Dict:
    """
    VISÃO GERAL
    
    Realiza pesquisa de um produto no catalogo do negocio.

    A função executa a consulta no Neo4j usando uma QUERY PADRÃO FIXA, utilizando os parametros recebidos.


    ===============================================================================
    🚨🚨🚨 REGRA CRÍTICA - LEIA COM ATENÇÃO 🚨🚨🚨
    ===============================================================================
    TODAS as consultas DEVEM retornar as variações de cor, MESMO QUE HAJA APENAS UM PRODUTO.
    Isso é OBRIGATÓRIO para o correto funcionamento do sistema.
    NUNCA use uma consulta que não retorne as variações, mesmo para um único produto.
    ===============================================================================

    PARÂMETROS
    ----------
    1. canal - str. obrigatório   
    identificador do canal (whatsapp, web_app, etc.)
    
    2. tipo_resposta - str. obrigatório   
    opções: 'cartao' | 'livre' -  
    Informa se haverá dados cadastrais de um ou mais produtos (cartao) ou um texto apenas com alguma informação  
    
    3. mensagem_inicial: str - opcional 
    mensagem que antecede aos dados. Visa apresentar ou dizer o que são os dados.
    Exemplo: "Encontrei estes 4 produtos que são da cor que solicitou:"
    
    4. mensagem_final: str - opcional
    mensagem que procede os dados. Normalmente um comentário ou explicação dos dados.
    Exemplo: "Para compartilhar com o cliente, siga este procedimento..."
    
    5. parametros: str - obrigatório 
    String JSON com os parâmetros para a query fixa.
    
    ===============================================================================
    QUERY FIXA UTILIZADA:
    ===============================================================================
  MATCH (n:Negocio {idx: $negocio_idx})
    CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termos_produto) YIELD node AS p
    MATCH (n)-[:POSSUI_PRODUTO]->(p)
    WHERE p.excluido = 0
    OPTIONAL MATCH (p)-[:TEM_COR]->(c:Cor)<-[:DISPONIBILIZA_COR]-(n)
    WITH p.nome AS nome,
         head(collect(p.preco)) AS preco,
         head(collect(p.url_imagem)) AS url_imagem,
         collect(DISTINCT {codigo: p.codigo,
                           cor: COALESCE(c.nome, 'Cor única')}) AS variacoes
    RETURN nome, preco, url_imagem, variacoes
    ORDER BY nome
    ===============================================================================
    
    ===============================================================================
    PARÂMETROS OBRIGATÓRIOS PARA A QUERY:
    ===============================================================================
    
    1. **negocio_idx** (obrigatório): ID do negócio
    2. **termos** (obrigatório): Termos de busca 
    
    EXEMPLO DE PARÂMETROS:
    ```json
    {
        "negocio_idx": "5544332211",
        "termos_produto": "batom AND matte AND manteiga",
        "termos_cor": ["peach"]   ← pode ser null ou [] quando não houver filtro de cor
    }


    ```



===============================================================================
PROCESSO PARA O AGENTE:
===============================================================================

1.  Identificar os termos da solicitação do usuário  
    Exemplo: "liste os batons da marca mate" → termos_produto: "batom AND mate"

2.  Corrigir termos que estão claramente escritos de forma errada  
    Exemplo: "batttons" → "batom"

3.  Passar os termos para o singular  
    Exemplo: "batons" → "batom"

4.  Detectar filtro de COR  
    - Se o usuário mencionar a palavra-chave "cor" (ou "cor:"), entenda que:
         • TUDO que estiver ANTES de "cor" → termos_produto  
         • TUDO que estiver DEPOIS de "cor" → termos_cor (lista de palavras)
    - Exemplos de quebra:
        "me mostre o batom matte com manteiga cor peach"  
        termos_produto: "batom AND matte AND manteiga"  
        termos_cor: ["peach"]

        "blush compacto cor rosa pink"  
        termos_produto: "blush AND compacto"  
        termos_cor: ["rosa", "pink"]

    - Se NÃO houver "cor", mantenha termos_cor = null

5.  Regras de montagem dos termos_produto (mesmas do modelo antigo)

    PARA CÓDIGOS DE PRODUTOS:
    - Use apenas números exatos, sem ~ (til)
    - Exemplo: "10102674"

    PARA NOMES/TERMOS DE PRODUTOS:
    - Busca parcial, sem ~ (til)
    - Máx. 3-4 termos; use AND ou OR
    - Exemplos:
      "batom AND matte AND vermelho"
      "gloss OR batom OR lip"
      "10102674 OR batom"

6.  Montar o JSON de parâmetros

    a) Busca sem filtro de cor
    {
      "negocio_idx": "123456",
      "termos_produto": "batom AND matte",
      "termos_cor": null
    }

    b) Busca com filtro de cor
    {
      "negocio_idx": "123456",
      "termos_produto": "batom AND matte AND manteiga",
      "termos_cor": ["peach"]
    }

    c) Busca por código + cor
    {
      "negocio_idx": "123456",
      "termos_produto": "10102674",
      "termos_cor": ["red"]
    }

7.  Busca fonética / tolerante a erro  
    - A plataforma aplica busca fonética (fuzzy) AUTOMATICAMENTE:  
         • termos_produto → distância ≤ 2  
         • termos_cor    → distância ≤ 2 + match parcial (substring)  
    - NÃO adicione símbolos como ~; envie apenas as palavras corrigidas que o LLM
      entendeu. O sistema corrige pequenos erros ("peech" → "peach", "rosaa" → "rosa").

8.  Chamar a função com o JSON acima.
    
    ===============================================================================
    ESTRUTURA DO BANCO DE DADOS (NEO4J):
    ===============================================================================
    - Um NEGÓCIO (Negocio) possui vários PRODUTOS (relacionamento POSSUI_PRODUTO)
    - Um PRODUTO pode ter várias CORES (relacionamento TEM_COR)
    - Um NEGÓCIO disponibiliza CORES específicas (relacionamento DISPONIBILIZA_COR)

    ===============================================================================
    EXEMPLO DE RESULTADO CORRETO:
    ===============================================================================
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'variacoes': [
        {'codigo': '123', 'cor': 'Vermelho'},
        {'codigo': '124', 'cor': 'Azul'}
      ]
    }

    ===============================================================================
    EXEMPLO DE RESULTADO INCORRETO (produto duplicado):
    ===============================================================================
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'variacoes': [{'codigo': '123', 'cor': 'Vermelho'}]
    }
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'variacoes': [{'codigo': '124', 'cor': 'Azul'}]
    }

    Esquema de dados:
    Esta função irá utilizar o seguinte esquema extraído do banco de dados neo4j da plataforma:
    {consultar_produto_entrada_neo4j}  

    Retorno
    -------
    json com as chaves:
        status: success ou error
        message: string com dados do resultado ou mensagem de erro
       
    🚨🚨🚨 A RESPOSTA DEVE SER ENVIADA EXATAMENTE ASSIM, SEM ALTERAÇÕES. O USUÁRIO ESPERA RECEBER UM JSON COM RESPOSTA. ENTREGUE, PORTANTO, A STRING DO JSON RECEBIDO COMO RESPOSTA, SEM AJUSTES, SEM CONVERSÕES, SEM COMENTÁRIOS.
    """
    
    print("==============================")  
    print("#### consultar_produto_entrada()")
    print("===============================")
    print("usuario_whatsapp:", usuario_whatsapp)
    print("tipo_resposta:", tipo_resposta)
    print("mensagem_inicial:", mensagem_inicial) 
    print("mensagem_final:", mensagem_final)
    print("em_resposta_idx:", em_resposta_idx)
    print("agente_idx:", agente_idx)
    print("conversa_idx:", conversa_idx)
    print("usuario_idx:", usuario_idx)
    print("canal_idx:", canal_idx)
    print("parametros:", parametros)
    
    # Processar parâmetros
    try:
        if parametros:
            parametros_dict = json.loads(parametros)
        else:
            return {
                "status": "error", 
                "message": "Parâmetros são obrigatórios."
            }
    except json.JSONDecodeError:
        return {
            "status": "error", 
            "message": "Parâmetros inválidos. Deve ser um JSON válido."
        }
    
    # Validar parâmetros obrigatórios
    if "negocio_idx" not in parametros_dict:
        return {
            "status": "error", 
            "message": "Parâmetro 'negocio_idx' é obrigatório."
        }
    
    if "termos_produto" not in parametros_dict:
        return {
            "status": "error", 
            "message": "Parâmetro 'termos' é obrigatório."
        }
    
    # Processar termos compostos
    parametros_dict["termos_produto"] = processar_termos_compostos(parametros_dict["termos_produto"])
    
    # NOVO: aplicar busca fonética (fuzzy) preservando operadores AND/OR/NOT e parênteses
    parametros_dict["termos_produto"] = aplicar_busca_fonetica(parametros_dict["termos_produto"], distancia=2)
    
    # Executar a query padrão fixa
    try:
        print("@@@@@ query:", QUERY_PADRAO)
        
        # Mapear os parâmetros para os nomes esperados pela query
        parametros_query = {
            'negocio_idx': parametros_dict['negocio_idx'],
            'termos_produto': parametros_dict['termos_produto']  # Mapeia 'termos' para 'termos_produto'
        }
        
        # Processar termos_cor se existir
        termos_cor = parametros_dict.get('termos_cor')
        if termos_cor:
            if isinstance(termos_cor, str) and termos_cor.strip():
                # Se for string, converter para lista
                termos_cor = [termo.strip() for termo in termos_cor.split(' AND ')]
            elif isinstance(termos_cor, list):
                # Se já for lista, garantir que todos os itens sejam strings
                termos_cor = [str(termo).strip() for termo in termos_cor if str(termo).strip()]
            else:
                termos_cor = None
            
            if termos_cor:
                # Aplicar busca fonética a cada termo da lista
                termos_cor = [aplicar_busca_fonetica(termo, distancia=2) for termo in termos_cor]
                parametros_query['termos_cor'] = termos_cor
            else:
                parametros_query['termos_cor'] = None
        else:
            parametros_query['termos_cor'] = None
        
        # Imprimir parâmetros para debug
        print("@@@@@ parametros_dict:", parametros_dict)
        
        print("@@@@@ parametros_query:", parametros_query)
        resultado = await neo4j.execute_read_query(QUERY_PADRAO, parametros_query)
        print("@@@@@ resultado da consultar_produto_entrada:", resultado)
    except Exception as e:
        return {
            "status": "error",
            "message": f"Erro ao executar consulta: {str(e)}"
        }

    if not resultado:
        mensagem = "produto não encontrado"
        return {"status": "success",
        "message": "Nenhum produto encontrado.",
        "function": "consultar_produto_entrada"
        }

    # Se houver mais de um produto, retorna apenas o primeiro
    if len(resultado) > 1:
        print(f"@@@@@ Encontrados {len(resultado)} produtos. ")
        resultado = [resultado[0]]
    
    
    # Aplicar filtro de cor, caso haja termos de cor especificados
    produto = resultado[0] 
    termos_cor = parametros_dict.get('termos_cor')
    if termos_cor:
        # Se for string, converter para lista
        if isinstance(termos_cor, str):
            termos_cor = [termo.strip().lower() for termo in termos_cor.split(' AND ')]
        
        # Se houver resultado, verificar as cores
        if resultado and len(resultado) > 0:
            
            
            # Verificar se o produto tem variações
            if 'variacoes' in produto and produto['variacoes']:
                variacoes_filtradas = []
                
                # Para cada variação do produto
                for variacao in produto['variacoes']:
                    if 'cor' in variacao and variacao['cor']:
                        # Verifica se algum dos termos de cor está presente na cor da variação (case insensitive)
                        cor_variacao = variacao['cor'].lower()
                        if any(termo.lower() in cor_variacao for termo in termos_cor if termo):
                            variacoes_filtradas.append(variacao)
                
                # Se encontrou alguma cor correspondente, substitui as variações
                if variacoes_filtradas:
                    produto['variacoes'] = variacoes_filtradas
                else:
                    # Se não encontrou nenhuma cor correspondente, retorna produto não encontrado
                    return {
                        "status": "success",
                        "message": "Nenhum produto encontrado com a cor especificada.",
                        "function": "consultar_produto_entrada"
                    }
    if 'variacoes' in produto:
        print("@@@@@ Variacoes:", produto['variacoes'])
        total_variacoes = len(produto['variacoes'])
        print("@@@@@ Total de variações:", total_variacoes)
        if total_variacoes > 1:
            resposta = {
                "status": "success",
                "message": "Informe o código da variação ou nome da cor desejada",
                 "dados": produto,
            }
            return resposta

    resposta = {
        "status": "success",
        "dados": produto,
        "function": "consultar_produto_entrada"
    }
    return resposta


if __name__ == "__main__":
    # Função de teste para consultar produtos
    async def testa_consultar_produto_entrada():
        print("\n=== INICIANDO TESTE DE CONSULTA DE PRODUTOS ===")
        print("Diretório atual:", os.getcwd())
        print("Arquivo atual:", __file__)

        canal_idx = "3245124231"
        whatsapp = "553184198720"
        tipo_resposta = "cartao"
        mensagem_inicial = "Aqui estão os produtos que você solicitou:"
        mensagem_final = "Caso precise de mais informações, só chamar."
        em_resposta_idx = "0129001290"
        agente_idx = "1508250458"
        conversa_idx = "8355885236"

        print("\nParâmetros do teste:")
        print(f"- canal_idx: {canal_idx}")
        print(f"- whatsapp: {whatsapp}")
        print(f"- tipo_resposta: {tipo_resposta}")
        print(f"- mensagem_inicial: {mensagem_inicial}")
        print(f"- mensagem_final: {mensagem_final}")
        print(f"- em_resposta_idx: {em_resposta_idx}")
        print(f"- agente_idx: {agente_idx}")
        print(f"- conversa_idx: {conversa_idx}")

        # Parâmetros da consulta
        texto = "me mostre o produto 10253731"
        # Primeiro teste: busca apenas por termos
        parametros = """{
            "negocio_idx": "5544332211",
            "termos_produto": "ultimate",
            "termos_cor": ""
        }"""

        usuario_idx = "1234567890"  # ID do usuário para teste

        # Teste 1: Busca apenas por termos
        print("\n=== TESTE 1: Busca por termos ===")
        print(f"- Parametros: {parametros}")

        try:
            resultado = await consultar_produto_entrada(
                usuario_whatsapp=whatsapp,
                tipo_resposta=tipo_resposta,
                mensagem_inicial=mensagem_inicial,
                mensagem_final=mensagem_final,
                em_resposta_idx=em_resposta_idx,
                agente_idx=agente_idx,
                conversa_idx=conversa_idx,
                usuario_idx=usuario_idx,
                canal_idx=canal_idx,
                parametros=parametros
            )

            print("=== Resultado do Teste ===")
            print(resultado)
            return resultado
        except Exception as e:
            print(f"\n!!! ERRO AO EXECUTAR CONSULTA: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    asyncio.run(testa_consultar_produto_entrada())