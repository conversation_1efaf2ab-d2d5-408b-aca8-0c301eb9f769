import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
from api.agent.agent_mysql import Mysql
from api.agent.agent_secret import Secret
from app_sl.geral.util import generate_unique_id
from datetime import datetime

async def testar_conexao_loja247():
    secret = Secret()
    
    if not all([secret.LOJA247_MYSQL_HOST, secret.LOJA247_MYSQL_USER, 
                secret.LOJA247_MYSQL_PASSWORD, secret.LOJA247_MYSQL_DATABASE]):
        print("Erro: As variáveis de ambiente para LOJA247 não estão completamente definidas.")
        return None
    
    loja247_db = Mysql(
        host=secret.LOJA247_MYSQL_HOST,
        user=secret.LOJA247_MYSQL_USER,
        password=secret.LOJA247_MYSQL_PASSWORD,
        database=secret.LOJA247_MYSQL_DATABASE
    )
    
    try:
        conn = await loja247_db.conecta()
        print("Conexão com o banco LOJA247 estabelecida com sucesso!")
        return loja247_db
    except Exception as e:
        print(f"Erro ao conectar com o banco LOJA247: {e}")
        return None

async def testar_conexao_gptalk():
    gptalk_db = Mysql()  # Usa as configurações padrão do GPTALK_01
    
    try:
        conn = await gptalk_db.conecta()
        print("Conexão com o banco GPTALK_01 estabelecida com sucesso!")
        return gptalk_db
    except Exception as e:
        print(f"Erro ao conectar com o banco GPTALK_01: {e}")
        return None

async def importar_usuarios(loja247_db, gptalk_db):
    LOJA = 10
    NEGOCIO_IDX = "1038754322"
    APP_IDX = "4567890123"

    query = f"SELECT * FROM USUARIO WHERE LOJA = {LOJA}"
    usuarios_loja247 = await loja247_db.query(query)

    for usuario in usuarios_loja247:
        idx = generate_unique_id()
        nome_completo = usuario['NOME'].strip()
        primeiro_nome = nome_completo.split()[0] if nome_completo else 'Usuário'

        usuario_gptalk = {
            "IDX": idx,
            "NOME": nome_completo,
            "EMAIL": usuario['EMAIL'],
            "SENHA": '12345',
            "DATA_CAD": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        app_negocio_usuario = {
            "APP_IDX": APP_IDX,
            "NEGOCIO_IDX": NEGOCIO_IDX,
            "USUARIO_EMAIL": usuario['EMAIL'],
            "NIVEL_ID": usuario['FUNCAO_ID'],
            "USUARIO_APELIDO": primeiro_nome
        }

        # Inserir na tabela USUARIO
        usuario_id = await gptalk_db.add("USUARIO", usuario_gptalk)
        if usuario_id:
            print(f"Usuário {nome_completo} adicionado com ID: {usuario_id}")
        else:
            print(f"Falha ao adicionar usuário {nome_completo}")

        # Inserir na tabela APP_NEGOCIO_USUARIO
        app_negocio_usuario_id = await gptalk_db.add("APP_NEGOCIO_USUARIO", app_negocio_usuario)
        if app_negocio_usuario_id:
            print(f"APP_NEGOCIO_USUARIO adicionado para {nome_completo} com ID: {app_negocio_usuario_id}")
        else:
            print(f"Falha ao adicionar APP_NEGOCIO_USUARIO para {nome_completo}")

        print(f"Usuário {nome_completo} importado com sucesso!")

async def main():
    loja247_db = await testar_conexao_loja247()
    gptalk_db = await testar_conexao_gptalk()

    if loja247_db and gptalk_db:
        print("\nIniciando importação de usuários...")
        await importar_usuarios(loja247_db, gptalk_db)
    else:
        print("Não foi possível estabelecer conexão com um ou ambos os bancos de dados.")

if __name__ == "__main__":
    asyncio.run(main())
