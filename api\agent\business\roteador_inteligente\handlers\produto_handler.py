# handlers/produto_handler.py
import logging
from typing import Dict, Any, List
from datetime import datetime

class ProdutoHandler:
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
    
    def processar(self, parametros: Dict[str, Any]) -> Dict[str, Any]:
        """Processa busca de produtos com formato visual"""
        try:
            query_cypher = self._montar_query(parametros)
            resultados = self._executar_query(query_cypher, parametros)
            produtos_formatados = self._formatar_cards(resultados)
            
            return {
                "tipo": "produto_visual",
                "dados": produtos_formatados,
                "total": len(produtos_formatados),
                "query_gerada": query_cypher,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao processar produtos: {str(e)}")
            return {
                "tipo": "erro",
                "mensagem": str(e),
                "dados": []
            }
    
    def _montar_query(self, params: Dict[str, Any]) -> str:
        """Monta query Cypher dinamicamente baseada nos parâmetros"""
        
        # Query base
        base = "MATCH (p:Produto)"
        
        # Filtros dinâmicos
        filtros = []
        parametros_query = {}
        
        if params.get('codigo'):
            filtros.append("p.codigo = $codigo")
            
        if params.get('categoria'):
            filtros.append("p.categoria = $categoria")
            
        if params.get('preco_min'):
            filtros.append("p.preco >= $preco_min")
            
        if params.get('preco_max'):
            filtros.append("p.preco <= $preco_max")
            
        if params.get('status'):
            filtros.append("p.status = $status")
        
        # Monta WHERE
        where_clause = f" WHERE {' AND '.join(filtros)}" if filtros else ""
        
        # Ordenação
        order_by = ""
        if "mais caros" in str(params).lower():
            order_by = " ORDER BY p.preco DESC"
        elif "mais vendidos" in str(params).lower():
            order_by = " ORDER BY p.vendas DESC"
        elif "mais baratos" in str(params).lower():
            order_by = " ORDER BY p.preco ASC"
        
        # Limite
        limite = f" LIMIT {params.get('limite', 50)}"
        
        # Campos a retornar
        return_fields = """
            p.codigo as codigo,
            p.nome as nome,
            p.descricao as descricao,
            p.preco as preco,
            p.categoria as categoria,
            p.status as status,
            p.imagem_url as imagem_url,
            p.vendas as vendas
        """
        
        return f"{base}{where_clause} RETURN {return_fields}{order_by}{limite}"
    
    def _executar_query(self, query: str, parametros: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Executa query no Neo4j"""
        try:
            with self.db.session() as session:
                result = session.run(query, parametros)
                return [dict(record) for record in result]
                
        except Exception as e:
            self.logger.error(f"Erro na query Neo4j: {str(e)}")
            # Retorna dados mock para testes
            return self._mock_data(parametros)
    
    def _formatar_cards(self, produtos: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Formata produtos no formato de cards visual"""
        cards = []
        
        for produto in produtos:
            card = {
                "id": produto.get('codigo', ''),
                "titulo": produto.get('nome', 'Produto sem nome'),
                "descricao": produto.get('descricao', ''),
                "preco": float(produto.get('preco', 0)),
                "categoria": produto.get('categoria', 'Sem categoria'),
                "status": produto.get('status', 'ativo'),
                "imagem": produto.get('imagem_url', ''),
                "vendas": produto.get('vendas', 0),
                "formato": "card_visual",
                "badge": self._gerar_badge(produto)
            }
            cards.append(card)
        
        return cards
    
    def _gerar_badge(self, produto: Dict[str, Any]) -> str:
        """Gera badge baseado em características do produto"""
        if produto.get('vendas', 0) > 100:
            return "🔥 Mais Vendido"
        elif produto.get('preco', 0) > 100:
            return "💎 Premium"
        elif produto.get('status') == 'novo':
            return "🆕 Novo"
        else:
            return "📦 Disponível"
    
    def _mock_data(self, parametros: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Dados mock para testes sem banco"""
        return [
            {
                "codigo": "123456",
                "nome": "Hambúrguer Especial",
                "descricao": "Hambúrguer artesanal com queijo e bacon",
                "preco": 29.90,
                "categoria": "Lanches",
                "status": "ativo",
                "imagem_url": "/images/hamburguer.jpg",
                "vendas": 150
            },
            {
                "codigo": "789012",
                "nome": "Pizza Margherita",
                "descricao": "Pizza tradicional com molho de tomate e muçarela",
                "preco": 45.90,
                "categoria": "Pizzas",
                "status": "ativo",
                "imagem_url": "/images/pizza.jpg",
                "vendas": 89
            }
        ]

# Teste rápido
if __name__ == "__main__":
    # Mock database connection para testes
    class MockDB:
        def session(self):
            return self
    
        def run(self, query, params):
            return []
    
    handler = ProdutoHandler(MockDB())
    
    testes = [
        {"codigo": 123456},
        {"categoria": "Lanches", "limite": 5},
        {"preco_max": 30}
    ]
    
    for params in testes:
        resultado = handler.processar(params)
        print(f"Parâmetros: {params}")
        print(f"Query: {resultado['query_gerada']}")
        print(f"Cards: {len(resultado['dados'])}")
        print("-" * 40)