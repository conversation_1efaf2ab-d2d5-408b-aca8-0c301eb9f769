import google.generativeai as genai
import asyncio,sys,time
import requests,json
from .agent_secret import Secret
import pdf2image
import os
from PIL import Image
import io
import base64


class Gemini:
    def __init__(self):
        self.secret = Secret()
        self.functions = {}
        self.max_attempts = 3
        self.geminipro1 = 'models/gemini-pro'
        self.geminipro15 = 'models/gemini-1.5-pro-latest'
        self.gemini15flash="models/gemini-1.5-flash"
        self.geminiprovision="google/gemini-pro-vision"
        self.api_key  = self.secret.GEMINI_API_KEY
        
        genai.configure(api_key = self.api_key)

    async def convert_pdf_to_image(self, pdf_url):
        try:
            # Download do PDF
            response = requests.get(pdf_url)
            if response.status_code != 200:
                raise Exception(f"Erro ao baixar PDF: {response.status_code}")
            
            # Salva o PDF temporariamente
            temp_pdf = "temp.pdf"
            with open(temp_pdf, "wb") as f:
                f.write(response.content)
            
            # Converte PDF para imagem
            images = pdf2image.convert_from_path(temp_pdf, first_page=1, last_page=1)
            
            # Converte a primeira página para base64
            img_byte_arr = io.BytesIO()
            images[0].save(img_byte_arr, format='JPEG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # Remove o arquivo temporário
            os.remove(temp_pdf)
            
            return f"data:image/jpeg;base64,{base64.b64encode(img_byte_arr).decode()}"
            
        except Exception as e:
            raise Exception(f"Erro ao converter PDF para imagem: {str(e)}")

    async  def chat(self,message=None,llm="models/gemini-pro",llmtype = None,tools=None,functions=None):
        print("#####Gemini.chat()#####",llm)
        
        if isinstance(message, list):
           # Chama a função auxiliar para concatenar o conteúdo
            prompt = await self.concatenate_text_contents(message)
            #print("*********prompt:",prompt)
        else:
            prompt = message
        
        
        if llmtype == 'image':
            model = genai.GenerativeModel(llm)
            print("model:",model)
            print('run()',message,llm)


            #print("prompt",prompt)
            response =   model.generate_image(prompt)
            #print("translated:",response.text)
            return response
            

        else:
      
            url =  f"https://generativelanguage.googleapis.com/v1beta/{llm}:generateContent?key={self.api_key}"
            print("url:",url)
            print("llm:",llm)
            headers = {'Content-Type': 'application/json'}
            
            data_dict = {
            "contents": [
    
              {
                  "role": "user",
                   "parts": [
                {
                    "text": prompt
                },
                
            ]
            }]}
            
            attempts = 0
            
            while attempts < 3:
                # Convertendo o dicionário para JSON
                data_json = json.dumps(data_dict)
                response =  requests.post(url, headers=headers, data=data_json)
            
                if response.status_code == 200:
                    # Convertendo a resposta para JSON
                    data = response.json()
                    #print("data gemini.chat xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", data)
                    return data 
                        
                                        
       
                else:
                    print("Falha na requisição: Status", response.status_code)
                    print(response)
                    #return response .text

                # Incrementa o contador de tentativas
                attempts += 1
                if attempts < 3:
                    print(f"Aguardando 30 segundos antes da tentativa {attempts + 1}...")
                    time.sleep(30)  # Aguarda 30 segundos antes da próxima tentativa

                print("Não foi possível obter uma resposta válida após 3 tentativas.")

    #==============================
    async def concatenate_text_contents(self,text_list):
        #print("concatenate_text_contents",text_list)
        """
        Processa uma lista de dicionários, concatenando o valor de 'content' de cada dicionário,
        separado por quebras de linha.
        """
        concatenated_text = ""
        for item in text_list:
            if item['content']:
                 concatenated_text += item['role']+ "\n" + item['content'] + "\n"
        return concatenated_text


    async def vision_query(self, data: dict):
        print("===== vision_query() =====")
        print("data.url:",data.get("url", ""))

        headers = {
        "Authorization": f"Bearer {data.get('api_key', '')}",
        "HTTP-Referer": "http://localhost:5000",
        "Content-Type": "application/json"
        }

        print("headers:",headers)
        try:
            image_data = data["url"]
            
            # Verifica se é um PDF
            if image_data.lower().endswith('.pdf'):
                image_data = await self.convert_pdf_to_image(image_data)
            # Verifica se já é uma URL de dados (base64)
            elif image_data.startswith('data:image'):
                image_url = image_data
            # Verifica se é uma URL web
            elif image_data.startswith(('http://', 'https://')):
                image_url = image_data
            # Se não for nenhum dos dois, assume que é base64 puro e adiciona o prefixo
            else:
                image_url = f"data:image/jpeg;base64,{image_data}"
            
            payload = {
                "model": data.get("modelo", "google/gemini-pro-vision"),
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""
                                TABELA DE TIPOS DE DOCUMENTOS VÁLIDOS:
                                1-boleto
                                2-Conta de Luz
                                3-Conta de água
                                4-Conta de internet
                                5-nota fiscal de compra de mercadorias para revenda
                                6-nota fiscal de serviços recebidos
                                7-nota fiscal de compra de compra de móveis
                                8-nota fiscal de compra de compra de equipamentos
                                9-Contrato de prestação de serviço
                                10-Recibo
                                11-Cupom Fiscal

                                INSTRUÇÕES CRÍTICAS:
                                1. PRIMEIRO, verifique se a imagem é um documento financeiro/contábil válido, comparando com a tabela acima.
                                2. SE NÃO FOR um documento válido da tabela, responda APENAS com: {{"erro": "A imagem não é um documento válido"}}
                                3. SE FOR um documento válido, continue com as instruções abaixo:

                                INSTRUÇÕES DE PROCESSAMENTO:
                                1. Responda APENAS com base nos dados visíveis no documento.
                                2. Não faça suposições ou inferências.
                                3. Não use conhecimento externo.
                                4. Se um dado não estiver visível no documento, indique claramente que não foi possível encontrar.
                                5. Mantenha-se estritamente fiel aos dados apresentados.
                                6. Não tente completar ou adivinhar informações faltantes.
                                7. Seja objetivo e direto nas respostas.
                                8. Não adicione explicações ou interpretações além do necessário.
                                9. Use exatamente os termos e valores encontrados no documento.
                                10. Se houver ambiguidade, mencione isso explicitamente.
                                11. Se não conseguir identificar claramente o tipo de documento, retorne APENAS: {{"erro": "Não foi possível identificar o tipo de documento"}}

                                {data.get("query", "")}
                                """
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_url
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1,  # Reduz a criatividade do modelo
                "top_p": 0.1,       # Limita a diversidade de respostas
                "max_tokens": 1000,  # Limita o tamanho da resposta
                "presence_penalty": -2.0,  # Penaliza respostas criativas
                "frequency_penalty": -2.0,   # Penaliza repetições
                "stop": ["\n\n"]  # Para evitar respostas muito longas
            }
            print("payload:",payload)
            response = requests.post(data.get("api_url", ""), headers=headers, json=payload)
            print("response:",response)

            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Erro na API: {response.status_code}", "message": response.text}
        except FileNotFoundError:
            return {"error": f"Arquivo não encontrado"}
        except Exception as e:
            return {"error": f"Erro ao processar imagem: {str(e)}"}
    



    
    
if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        gemini = Gemini()
        message = "Olá, tudo bem?"
        result = await gemini.chat(message)
        print("result:",result)
        print("")
        print("texto:",result['candidates'][0]['content']['parts'][0]['text'])

    async def test_vision_query():
        gemini = Gemini()

        objeto_a_retornar = { 
                      "DOCUMENTO_TIPO": "", 
                      "A_PAGAR": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VALOR":0,
                        "VENCIMENTO": "",
                        "JA_PAGO": "",
                        "DATA_PAGAMENTO": ""
                          } 
                        ]
                      }
                      ,
                      "A_RECEBER":{
                        "TOTAL": 0, 
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VENCIMENTO": "",
                        "JA_RECEBIDO": "",
                        "DATA_PAGAMENTO": ""
                        }
                      ]}
                      ,
                      "DADOS_EMISSOR": { 
                        "RELACAO_TIPO": "",
                        "PESSOA_TIPO": "",
                        "RAZAO_SOCIAL": "",
                        "NOME_FANTASIA": "",
                        "CNPJ": "",
                        "CPF": "",
                        "TELEFONE": "",
                        "ENDEREÇO": { 
                            "CEP": "",
                            "LOGRADOURO": "",
                            "NUMERO": "",
                            "COMPLEMENTO": "",
                            "BAIRRO": "",
                            "CIDADE": "",
                            "UF": ""
                        }
                      },
                      "DISCRIMINAÇÃO": ""
                    }


        objeto_exemplo = { 
                      "DOCUMENTO_TIPO": "3-Conta de água", 
                      "A_PAGAR": {
                          "TOTAL": 100.00,
                          "NR_PARCELAS": 1,
                          "PARCELAS": [{
                          "VALOR": 100.00,
                          "VENCIMENTO": "01/04/25",
                          "JA_PAGO": "N",
                          "DATA_PAGAMENTO": ""
                          }]
                        },
                      "A_RECEBER": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS": []
                      },
                      "DADOS_EMISSOR": { 
                          "RELACAO_TIPO": "2 - Fornecedor",
                          "PESSOA_TIPO": "1 - Jurídica",
                          "RAZAO_SOCIAL": "COPASA ABASTECIMENTOS S/A",
                          "NOME_FANTASIA": "COPASA",
                          "CNPJ": "37.753.978/0001-03",
                          "CPF": "",
                          "TELEFONE": "(31)984784825",
                          "ENDEREÇO": { 
                              "CEP": "31910720",
                              "LOGRADOURO": "RUA LIDIA",
                              "NUMERO": "125",
                              "COMPLEMENTO": "",
                              "BAIRRO": "CENTRO",
                              "CIDADE": "BELO HORIZONTE",
                              "UF": "MG"
                          }
                      },
                      "DISCRIMINAÇÃO": "CONTA DE MARÇO/25"
                    }


        data = {
            "url": "http://localhost/gptalk/negocios/0068108573/documentos/contabilidade/2305562857.pdf",
            "url": "https://gptalk.com.br/negocios/0068108573/imagens/contabilidade/1234567890.jpg",
            "url": "https://gptalk.com.br/imagens/logo.png",
            "query": f"""
                    Preciso que analise o documento na imagem e preecha e retorne o objeto abaixo com os dados possiveis e encontrados no documento. Utilize as tabelas auxiliares para preecher 
                    
                    TABELAS AUXILIARES PARA CRIAÇÃO DO OBJETO
                    
                    Tabela para DOCUMENTO_TIPO:
                        1-boleto
                        2-Conta de Luz
                        3-Conta de água
                        4-Conta de internet
                        5-nota fiscal de compra de mercadorias para revenda
                        6-nota fiscal de serviços recebidos
                        7-nota fiscal de compra de compra de móveis
                        8-nota fiscal de compra de compra de equipamentos
                    	9-Contrato de prestação de serviço
                    	10-Recibo
                    	11-Cupom Fiscal    
                        
                        RELACAO_TIPO:
                        1 - Cliente
                        2 - Fornecedor
                        3 -Funcionário
                        4 - Sócio ou proprietário
                        
                        PESSOA_TIPO
                        1 - Jurídica
                        2 - Física
                        
                     OBSERVAÇÕES:
                     
                     Se nas chaves A_PAGAR ou  A_RECEBER não houverem dados, o valos deverá ser um array vazio: [] 
                     
                     **IMPORTANTE**: A resposta DEVE ser uma string JSON válida, utilizando aspas duplas (") para todas as chaves e valores de string. NÃO use aspas simples (').
                     
                     objeto a ser retornado:
                     {objeto_a_retornar}  
                      
                    
                    Exemplo do objto preenchido com os dados da imagem de uma conta de água (JSON válido):

                    {objeto_exemplo}

                    Em caso de erro, não retorne o objeto de dados. Retorne apenas este:
                    {{"erro": "Informe aqui o numero e a mensagem do erro ocorrido"}}
                """, 
            "modelo": gemini.geminiprovision,
            "api_url": "https://openrouter.ai/api/v1/chat/completions",
            "api_key": "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"
        }
        result = await gemini.vision_query(data)
        print("result:",result)
        print("")
        print(result['choices'][0]['message']['content'])

        
    async def test_chat():
        gemini = Gemini()
        message = "Olá, tudo bem?"
        result = await gemini.chat(message,llm="models/gemini-2.0-flash")
        print("result completo:",result) # Mantém o print do resultado completo para depuração

        try:
            # Tenta extrair o texto da primeira candidata
            if result and 'candidates' in result and len(result['candidates']) > 0 and \
               'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content'] and \
               len(result['candidates'][0]['content']['parts']) > 0 and 'text' in result['candidates'][0]['content']['parts'][0]:
                
                texto_resposta = result['candidates'][0]['content']['parts'][0]['text']
                print("\nTexto da Resposta Extraído:")
                print(texto_resposta)
            else:
                print("\nNão foi possível extrair o texto da resposta. Estrutura inesperada.")
        except Exception as e:
            print(f"\nOcorreu um erro ao tentar extrair o texto da resposta: {e}")

    async def test_direct_chat():
        print("===== test_direct_chat() =====")
        gemini = Gemini()
        
        # Se você quiser usar uma API key específica para este teste, descomente e altere a linha abaixo:
        # gemini.api_key = "SUA_API_KEY_DE_TESTE_AQUI"
        # Lembre-se que genai.configure(api_key = self.api_key) já é chamado no __init__
        # Se alterar a api_key aqui, pode ser necessário reconfigurar o genai ou ajustar a lógica de chamada direta via requests.
        # Para a chamada via requests (que é o caso do erro 404), alterar gemini.api_key será suficiente.

        message = "bom dia gemini, como vc está?"
        # Usando o nome do modelo já corrigido com o prefixo "models/"
        # e também o valor que definimos como padrão na função chat.
        model_to_use = "models/gemini-pro" 
        print(f"Chamando chat com modelo: {model_to_use}")

        try:
            result = await gemini.chat(message=message, llm=model_to_use)
            print("\nResultado do test_direct_chat:")
            if isinstance(result, dict) and 'candidates' in result:
                print(json.dumps(result, indent=2, ensure_ascii=False))
                if result['candidates'][0]['content']['parts'][0]['text']:
                    print("\nTexto da resposta:", result['candidates'][0]['content']['parts'][0]['text'])
            elif isinstance(result, dict) and 'error' in result:
                print(f"Erro na API: {result['error']}")
                if 'message' in result:
                    print(f"Detalhes: {result['message']}")
            else:
                print(result)
        except Exception as e:
            print(f"Ocorreu um erro durante o test_direct_chat: {e}")

    async def test_barebone_api_call():
        print("===== test_barebone_api_call() =====")
        
        # ** IMPORTANTE: Substitua pela sua API key real **
        api_key = "AIzaSyD5goZGpCwzlwRu-czlyJPQHF7LGY3EVW8" 
        #api_key ="AIzaSyCh8RgSbA1CoujMBTrGrahGczmCv-GaqRE" 
        
        # Verifique se a API key foi substituída
        if api_key == "SUA_API_KEY_DIRETA_AQUI":
            print("\n!!!! ATENÇÃO: Por favor, substitua 'SUA_API_KEY_DIRETA_AQUI' pela sua chave de API real na função test_barebone_api_call. !!!!\n")
            #return # Pode-se descomentar para evitar a chamada com uma chave inválida

        model_name = "models/gemini-2.0-flash" # Modelo já com o prefixo
        prompt_text = "Escreva um poema curto sobre a programação."

        url = f"https://generativelanguage.googleapis.com/v1beta/{model_name}:generateContent?key={api_key}"
        headers = {'Content-Type': 'application/json'}
        
        data_dict = {
            "contents": [
                {
                    "role": "user",
                    "parts": [
                        {
                            "text": prompt_text
                        }
                    ]
                }
            ]
        }
        data_json = json.dumps(data_dict)

        print(f"Enviando requisição para: {url}")
        #print(f"Com payload: {data_json}") # Descomente para depurar o payload

        try:
            response = requests.post(url, headers=headers, data=data_json)
            
            print(f"Status da Resposta: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("\nResposta da API (JSON):")
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                if response_data.get('candidates') and response_data['candidates'][0].get('content', {}).get('parts', [])[0].get('text'):
                    print("\nTexto do poema:", response_data['candidates'][0]['content']['parts'][0]['text'])
                else:
                    print("\nNão foi possível extrair o texto da resposta ou a estrutura é inesperada.")
            else:
                print("\nErro na requisição:")
                try:
                    print(response.json()) # Tenta imprimir o erro como JSON
                except json.JSONDecodeError:
                    print(response.text) # Se não for JSON, imprime como texto

        except requests.exceptions.RequestException as e:
            print(f"Ocorreu um erro na requisição HTTP: {e}")
        except Exception as e:
            print(f"Ocorreu um erro inesperado: {e}")

    #asyncio.run(main())
    asyncio.run(test_chat())
    #asyncio.run(test_direct_chat())
    #asyncio.run(test_barebone_api_call())






