from .agent_mysql import Mysql
from .agent_neo4j import AgentNeo4j
from api.functions.util import generate_unique_id

mysql = Mysql()
neo4j = AgentNeo4j()

async def exporta_cores():
    """
    Exporta as cores do mysql para o neo4j. Somente as que possuem o NEGOCIO_IDX = "5544332211"

    Tabela do mysql: COR
    Colunas:
         NOME
         CODIGO
         HEXADECIMAL
         NEGOCIO_IDX

    No neo4j: Cor
    Propriedades:
         nome: NOME
         codigo: CODIGO
         hexadecimal: HEXADECIMAL

         Se o COR.NEGOCIO_IDX = "5544332211":
             Adicionar a cor no NEO4J
                 Label: Cor
                 Propriedades:
                     nome: COR.NOME
                     codigo: COR.CODIGO
                     hexadecimal: COR.HEXADECIMAL
             Criar a relação da Cor com o Negócio
                 MATCH (n:Negocio {idx: '5544332211'}), (c:Cor {idx: COR.IDX})
                 MERGE (n)-[:POSSUI_COR]->(c)
    """
    try:
        print("🎨 Iniciando exportação de cores do MySQL para Neo4j...")
        
        # 1. Buscar cores do MySQL com NEGOCIO_IDX = "5544332211"
        query_mysql = """
        SELECT  NOME, CODIGO, HEXADECIMAL, NEGOCIO_IDX 
        FROM COR 
        WHERE NEGOCIO_IDX = %s
        """
        
        cores = await mysql.query(query_mysql, params=("5544332211",))
        
        if not cores:
            print("❌ Nenhuma cor encontrada com NEGOCIO_IDX = '5544332211'")
            return {"success": False, "message": "Nenhuma cor encontrada"}
        
        print(f"🎨 Encontradas {len(cores)} cores para exportar")
        print("⚠️ MODO DE TESTE: Processando apenas as primeiras 2 cores")
        
        cores_exportadas = 0
        cores_com_erro = 0
        
        # 2. Para cada cor, criar nó no Neo4j e relação com Negocio
        for i, cor in enumerate(cores):
            try:
                # QUEBRA DE EXECUÇÃO: Processar apenas as primeiras 2 cores
                #if i >= 2:
                #    print("\n🛑 PARADA DE TESTE: Processadas 2 cores.")
                #    print("   Verifique se os dados estão corretos no Neo4j antes de continuar.")
                #    print("   Para processar todas as cores, remova ou modifique esta condição.")
                #    break
                
                print(f"\n🎨 Processando cor {i+1}/{min(2, len(cores))}: {cor['NOME']}")
                
                # Gerar novo IDX único para o Neo4j
                novo_idx = generate_unique_id()
                
                # Criar nó Cor no Neo4j
                query_create_cor = """
                MERGE (c:Cor {idx: $idx})
                SET c.nome = $nome,
                    c.idx = $idx,
                    c.codigo = $codigo,
                    c.hexadecimal = $hexadecimal
                RETURN c
                """
                
                params_cor = {
                    "idx": novo_idx,
                    "nome": cor["NOME"],
                    "codigo": cor["CODIGO"],
                    "hexadecimal": cor["HEXADECIMAL"]
                }
                #print("query_create_cor", query_create_cor)
                #print("params_cor", params_cor)

                # Executar criação da cor
                resultado_cor = await neo4j.execute_write_query(query_create_cor, params_cor)
                
                if "erro" in resultado_cor:
                    print(f"❌ Erro ao criar cor {cor['NOME']}: {resultado_cor['erro']}")
                    cores_com_erro += 1
                    continue
                
                # Criar relação POSSUI_COR entre Negocio e Cor
                query_relacao = """
                MATCH (n:Negocio {idx: $negocio_idx}), (c:Cor {idx: $cor_idx})
                MERGE (n)-[:POSSUI_COR]->(c)
                RETURN n, c
                """
                
                params_relacao = {
                    "negocio_idx": "5544332211",
                    "cor_idx": novo_idx
                }
                
                # Executar criação da relação
                resultado_relacao = await neo4j.execute_write_query(query_relacao, params_relacao)
                
                if "erro" in resultado_relacao:
                    print(f"⚠️ Cor {cor['NOME']} criada, mas erro na relação: {resultado_relacao['erro']}")
                else:
                    print(f"✅ Cor {novo_idx} - {cor['NOME']} exportada com sucesso")
                
                cores_exportadas += 1
                
            except Exception as e:
                print(f"❌ Erro ao processar cor {cor['NOME']}: {str(e)}")
                cores_com_erro += 1
                continue
        
        # 3. Relatório final
        print(f"\n📊 RELATÓRIO DE EXPORTAÇÃO (TESTE):")
        print(f"   Total de cores encontradas: {len(cores)}")
        print(f"   Cores processadas (teste): {min(2, len(cores))}")
        print(f"   Cores exportadas com sucesso: {cores_exportadas}")
        print(f"   Cores com erro: {cores_com_erro}")
        print(f"   Cores restantes: {max(0, len(cores) - 2)}")
        
        return {
            "success": True,
            "message": "Exportação de teste concluída (2 cores)",
            "total_encontradas": len(cores),
            "processadas_teste": min(2, len(cores)),
            "exportadas": cores_exportadas,
            "erros": cores_com_erro,
            "restantes": max(0, len(cores) - 2)
        }
        
    except Exception as e:
        print(f"❌ ERRO GERAL na exportação: {str(e)}")
        return {
            "success": False,
            "message": f"Erro geral: {str(e)}"
        }


async def exporta_produtos():
    """
    Exporta os produtos do mysql para o neo4j. Somente os produtos que possuem o NEGOCIO_IDX = "5544332211"

    Tabela do mysql: PRODUTO
    Colunas:
        IDX
        NOME
        CODIGO
        DESCR
        PRECO
        PRECO_MAIOR
        ESTOQUE
        COR_CODIGO
        URL_IMAGEM
        NEGOCIO_IDX

    No neo4j: Produto
    Propriedades:
        idx: IDX
        nome: NOME
        codigo: CODIGO
        descricao: DESCR
        preco: PRECO
        preco_maior: PRECO_MAIOR
        estoque: ESTOQUE
        url_imagem: URL_IMAGEM


        Se o PRODUTO.NEGOCIO_IDX = "5544332211":
            Adicionar o produto no NEO4
                Label: Produto
                Propriedades:
                    idx: PRODUTO.IDX
                    nome: PRODUTO.NOME
                    codigo: PRODUTO.CODIGO
                    descricao: PRODUTO.DESCR
                    preco: PRODUTO.PRECO
                    preco_maior: PRODUTO.PRECO_MAIOR
                    estoque: PRODUTO.ESTOQUE
                    url_imagem: PRODUTO.URL_IMAGEM

            Criar a relação do Produto com o Fabricante
                MATCH (n:Negocio {idx: '5544332211'}), (p:Produto {idx: PRODUTO.IDX})
                MERGE (n)-[:POSSUI_PRODUTO]->(p)
    """
    try:
        print("🚀 Iniciando exportação de produtos do MySQL para Neo4j...")
        
        # 1. Buscar produtos do MySQL com NEGOCIO_IDX = "5544332211"
        query_mysql = """
        SELECT IDX, NOME, CODIGO, DESCR, PRECO, PRECO_MAIOR, ESTOQUE, COR_CODIGO,URL_IMAGEM,NEGOCIO_IDX 
        FROM PRODUTO 
        WHERE NEGOCIO_IDX = %s
        """
        
        produtos = await mysql.query(query_mysql, params=("5544332211",))
        
        if not produtos:
            print("❌ Nenhum produto encontrado com NEGOCIO_IDX = '5544332211'")
            return {"success": False, "message": "Nenhum produto encontrado"}
        
        print(f"📦 Encontrados {len(produtos)} produtos para exportar")
        print("⚠️ MODO DE TESTE: Processando apenas os primeiros 2 produtos")
        
        produtos_exportados = 0
        produtos_com_erro = 0
        
        # 2. Para cada produto, criar nó no Neo4j e relação com Negocio
        for i, produto in enumerate(produtos):
            try:
                # QUEBRA DE EXECUÇÃO: Processar apenas os primeiros 2 produtos
                #if i >= 413:
                #    print("\n🛑 PARADA DE TESTE: Processados 2 produtos.")
                #    print("   Verifique se os dados estão corretos no Neo4j antes de continuar.")
                #    print("   Para processar todos os produtos, remova ou modifique esta condição.")
                #    break
                
                print(f"\n📝 Processando produto {i+1}/2: {produto['NOME']} (IDX: {produto['IDX']})")
                
                # Criar nó Produto no Neo4j
                query_create_produto = """
                MERGE (p:Produto {idx: $idx})
                SET p.nome = $nome,
                    p.idx = $idx,
                    p.codigo = $codigo,
                    p.descr = $descr,
                    p.preco = $preco,
                    p.preco_maior = $preco_maior,
                    p.estoque = $estoque,
                    p.url_imagem = $url_imagem

                RETURN p
                """
                
                params_produto = {
                    "idx": produto["IDX"],
                    "nome": produto["NOME"],
                    "codigo": produto["CODIGO"],
                    "descr": produto["DESCR"],
                    "preco": float(produto["PRECO"]) if produto["PRECO"] else 0.0,
                    "preco_maior": float(produto["PRECO_MAIOR"]) if produto["PRECO_MAIOR"] else 0.0,
                    "estoque": int(produto["ESTOQUE"]) if produto["ESTOQUE"] else 0,
                    "url_imagem": produto["URL_IMAGEM"] if produto["URL_IMAGEM"] else ""
                }
                
                # Executar criação do produto
                resultado_produto = await neo4j.execute_write_query(query_create_produto, params_produto)
                
                if "erro" in resultado_produto:
                    print(f"❌ Erro ao criar produto {produto['IDX']}: {resultado_produto['erro']}")
                    produtos_com_erro += 1
                    continue
                
                # Criar relação POSSUI_PRODUTO entre Negocio e Produto
                query_relacao = """
                MATCH (n:Negocio {idx: $negocio_idx}), (p:Produto {idx: $produto_idx})
                MERGE (n)-[:POSSUI_PRODUTO]->(p)
                RETURN n, p
                """
                
                params_relacao = {
                    "negocio_idx": "5544332211",
                    "produto_idx": produto["IDX"]
                }
                
                # Executar criação da relação
                # Executar criação da relação POSSUI_PRODUTO
                resultado_relacao = await neo4j.execute_write_query(query_relacao, params_relacao)
                
                if "erro" in resultado_relacao:
                    print(f"⚠️ Produto {produto['IDX']} criado, mas erro na relação: {resultado_relacao['erro']}")
                    cores_com_erro += 1
                    continue
                
                # Criar relação TEM_COR se o produto tiver COR_CODIGO
                if produto.get("COR_CODIGO") and produto["COR_CODIGO"].strip():
                    query_relacao_cor = """
                    MATCH (p:Produto {idx: $produto_idx})
                    MATCH (c:Cor {codigo: $cor_codigo})
                    MERGE (p)-[:TEM_COR]->(c)
                    RETURN p, c
                    """
                    
                    params_relacao_cor = {
                        "produto_idx": produto["IDX"],
                        "cor_codigo": produto["COR_CODIGO"].strip()
                    }
                    
                    # Executar criação da relação TEM_COR
                    resultado_relacao_cor = await neo4j.execute_write_query(query_relacao_cor, params_relacao_cor)
                    
                    if "erro" in resultado_relacao_cor:
                        print(f"⚠️ Produto {produto['IDX']} criado, mas erro na relação TEM_COR: {resultado_relacao_cor['erro']}")
                    else:
                        print(f"🎨 Relação TEM_COR criada: Produto {produto['IDX']} -> Cor {produto['COR_CODIGO']}")
                
                produtos_exportados += 1
                
            except Exception as e:
                print(f"❌ Erro ao processar produto {produto.get('IDX', 'DESCONHECIDO')}: {str(e)}")
                produtos_com_erro += 1
                continue
        
        # 3. Relatório final
        print(f"\n📊 RELATÓRIO DE EXPORTAÇÃO (TESTE):")
        print(f"   Total de produtos encontrados: {len(produtos)}")
        print(f"   Produtos processados (teste): {min(2, len(produtos))}")
        print(f"   Produtos exportados com sucesso: {produtos_exportados}")
        print(f"   Produtos com erro: {produtos_com_erro}")
        print(f"   Produtos restantes: {max(0, len(produtos) - 2)}")
        
        return {
            "success": True,
            "message": "Exportação de teste concluída (2 produtos)",
            "total_encontrados": len(produtos),
            "processados_teste": min(2, len(produtos)),
            "exportados": produtos_exportados,
            "erros": produtos_com_erro,
            "restantes": max(0, len(produtos) - 2)
        }
        
    except Exception as e:
        print(f"❌ ERRO GERAL na exportação: {str(e)}")
        return {
            "success": False,
            "message": f"Erro geral: {str(e)}"
        }


if __name__ == "__main__":
    import asyncio
    asyncio.run(exporta_produtos())
    #asyncio.run(exporta_cores())

