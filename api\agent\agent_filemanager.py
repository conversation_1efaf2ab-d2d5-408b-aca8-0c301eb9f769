import os 

class FileManager:
    def __init__(self,user=None):
        self.name = ""
        user= None
        

    #==========
    async def save_text_to_file(self, directory, file, content ):
             
       
             # Ensure the directory exists
             os.makedirs(directory, exist_ok=True)
        
             # Full path for the file
             filepath = os.path.join(directory, file)
        
             # Writing content to the file
             with open(filepath, 'w', encoding='utf-8') as file:
               file.write(content)
               #print(f"File saved: {filepath}")
               
    #==========
    async def load_text_file(self, directory, file):
        diretorio_atual = os.path.dirname(os.path.realpath(__file__))
        #print("O diretório atual do arquivo em execução é:", diretorio_atual) 
        fullpath =directory + "\\" + file
        #print("fullpath", fullpath)
        try:
           
            with open(fullpath, 'r', encoding='utf-8', errors='replace') as file:
                contents = file.read()
                #print("contents",contents)
                return contents
        except FileNotFoundError:
            print(f"File '{fullpath}' not found.")
            return None

    async def load_text_file(self, directory, file):
        diretorio_atual = os.path.dirname(os.path.realpath(__file__))
        #print("O diretório atual do arquivo em execução é:", diretorio_atual) 
        #print("directory",directory)
        # Construindo o caminho correto para o arquivo dentro da subpasta 'webdesigner' na pasta 'agent'
        fullpath = os.path.join(diretorio_atual, directory, file)
        #print("fullpath", fullpath)
        try:
            with open(fullpath, 'r', encoding='utf-8', errors='replace') as file:
                contents = file.read()
                #print("contents",contents)
                return contents
        except FileNotFoundError:
            print(f"File '{fullpath}' not found.")
            return None
   
import asyncio                                
async def main():
   
   html_code = """

<!DOCTYPE html>
<html lang="pt">
<head>
<meta charset="utf-8"/>
<title>Página Básica</title>
</head>
<body>
<h1>Olá, Mundo!</h1>
<p>Esta é uma página HTML básica.</p>
<ul>
<li><img alt="Ração para cachorro" id="img_1713310820" src="https://makeplaceholder.com/?size=400x400"/></li>
<li><img alt="Sache para gatos" id="img_1713310821" src="https://makeplaceholder.com/?size=400x400"/></li>
<li><img alt="Brinquedos pet" id="img_1713310822" src="https://makeplaceholder.com/?size=400x400"/></li>
<li><img alt="Logo da empresa" id="img_1713310823" src="https://makeplaceholder.com/?size=400x400"/></li>
</ul>
</body>
</html>


"""

   fileMan = FileManager()


   #await fileMan.save_text_to_file("./site","teste.html",html_code)
   result = await fileMan.load_text_file("api/agent/site/template/default","json_string.js")
   print(result)
#asyncio.run(main())