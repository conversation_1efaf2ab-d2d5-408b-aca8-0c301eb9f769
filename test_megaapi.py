#!/usr/bin/env python3
"""
Script de teste completo para MegaAPI
"""
import requests
import json
import os
from dotenv import load_dotenv

# Carrega variáveis do .env
load_dotenv()

# Configurações da MegaAPI
BASE_URL = os.getenv("MEGA_API_BASE_URL")
TOKEN = os.getenv("MEGA_API_TOKEN") 
INSTANCE_KEY = os.getenv("MEGA_API_INSTANCE_KEY")

def test_instance_status():
    """Testa o status da instância"""
    print("🔍 Testando status da instância...")
    
    url = f"{BASE_URL}/rest/instance/{INSTANCE_KEY}"
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('error'):
                print("✅ Instância conectada com sucesso!")
                return True
            else:
                print("❌ Instância não conectada")
                return False
        else:
            print(f"❌ Erro na requisição: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_send_message(to_number, message_text):
    """Testa envio de mensagem"""
    print(f"📤 Testando envio de mensagem para {to_number}...")
    
    url = f"{BASE_URL}/rest/sendMessage/{INSTANCE_KEY}/text"
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "messageData": {
            "to": f"{to_number}@s.whatsapp.net",
            "text": message_text
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('error'):
                print("✅ Mensagem enviada com sucesso!")
                print(f"Message ID: {data.get('key', {}).get('id')}")
                return True
            else:
                print(f"❌ Erro no envio: {data.get('message')}")
                return False
        else:
            print(f"❌ Erro na requisição: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_check_whatsapp_number(number):
    """Verifica se o número tem WhatsApp"""
    print(f"🔍 Verificando se {number} tem WhatsApp...")
    
    url = f"{BASE_URL}/rest/instance/isOnWhatsApp/{INSTANCE_KEY}"
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    params = {
        "jid": f"{number}@s.whatsapp.net"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('exists'):
                print("✅ Número tem WhatsApp!")
                return True
            else:
                print("❌ Número não tem WhatsApp")
                return False
        else:
            print(f"❌ Erro na verificação: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes da MegaAPI...")
    print(f"Base URL: {BASE_URL}")
    print(f"Instance Key: {INSTANCE_KEY}")
    print(f"Token: {TOKEN[:10]}...")
    print("-" * 50)
    
    # 1. Teste de status
    if not test_instance_status():
        print("❌ Teste de status falhou. Verifique as configurações.")
        return
    
    print("-" * 50)
    
    # 2. Número para teste (altere conforme necessário)
    test_number = input("Digite o número para teste (apenas números, ex: 5531XXXXXXXX): ")
    
    # 3. Verificar se tem WhatsApp
    if not test_check_whatsapp_number(test_number):
        print("❌ Número não tem WhatsApp. Teste cancelado.")
        return
    
    print("-" * 50)
    
    # 4. Teste de envio
    test_message = "🤖 Teste automático da MegaAPI - " + str(int(time.time()))
    test_send_message(test_number, test_message)
    
    print("-" * 50)
    print("✅ Testes concluídos!")

if __name__ == "__main__":
    import time
    main() 