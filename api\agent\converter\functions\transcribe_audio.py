from ...agent_logger import AgentLogger
from ...agent_openai import OpenAi
oai = OpenAi()
logger = AgentLogger()


async def transcribe_audio(audio_base64: str, audio_format: str = "mp3") -> str:
    """
    Converte áudio em base64 para texto usando OpenAI Whisper
    Suporta formatos: mp3, wav, webm, m4a, flac, ogg
    """
    try:
        import base64
        import tempfile
        import os
        
        # Mapear formatos para extensões corretas
        format_mapping = {
            "mp3": "mp3",
            "wav": "wav", 
            "webm": "webm",
            "m4a": "m4a",
            "flac": "flac",
            "ogg": "ogg",
            "mpeg": "mp3",
            "mpga": "mp3"
        }
        
        # Usar formato mapeado ou padrão
        file_extension = format_mapping.get(audio_format.lower(), "mp3")
        
        print(f"Transcrevendo áudio: formato={audio_format}, extensão={file_extension}")
        
        # Decodificar base64 para bytes
        audio_bytes = base64.b64decode(audio_base64)
        print(f"Áudio decodificado: {len(audio_bytes)} bytes")
        
        # Criar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Usar OpenAI Whisper para transcrição
            with open(temp_file_path, "rb") as audio_file:
                transcript = await oai.transcribe_audio(audio_file)
                result_text = transcript.get("text", "") if isinstance(transcript, dict) else str(transcript)
                print(f"Transcrição concluída: '{result_text}'")
                return result_text
        finally:
            # Limpar arquivo temporário
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"Erro na transcrição: {str(e)}")
        return ""
    
if __name__ == "__main__":
    logger.info("Iniciando teste de transcri o de udio")

    import asyncio
    
    async def test_transcribe_audio():
        import base64
        
        # Ler o arquivo de áudio e converter para base64
        arq_audio = r"C:\Users\<USER>\Documents\GitHub\gptalk\server\output_audio\tts_output_1982746343.wav"
        
        with open(arq_audio, "rb") as audio_file:
            audio_bytes = audio_file.read()
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
        
        print(f"Arquivo lido: {len(audio_bytes)} bytes, base64: {len(audio_base64)} caracteres")
        
        # Agora chamar a função com o base64
        result = await transcribe_audio(audio_base64, "wav")
        print("result:", result)
       
    asyncio.run(test_transcribe_audio())
    
    #execucao
    #python -m api.agent.converter.functions.transcribe_audio