from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()


@function_tool
async def atualiza_produto(query: str, params: str):
    """
    Atualização dos dados  de PRODUTOS, exceto cor .

    NÓS DISPONÍVEIS:
    - Produto  {idx, codigo, nome, descricao, preco, preco_maior, estoque, excluido}
    - Cor      {idx, nome, codigo, hexadecimal}

    RELAÇÕES:
    - (p:Produto)-[:TEM_COR]->(c:Cor)

    Regras obrigatórias na query:
    - MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
    - WHERE p.excluido <> 1
    - SET ...

    Exemplos prontos:
    1. Atualizar preço de um produto:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $codigo})
               WHERE p.excluido <> 1
               SET p.preco = $novo_preco
               RETURN p"
       params: '{"negocio_idx": "5544332211", "codigo": "PROD001", "novo_preco": 99.90}'

    2. Atualizar estoque de produtos por nome:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
               WHERE p.excluido <> 1 AND p.nome CONTAINS $nome_produto
               SET p.estoque = p.estoque + $quantidade
               RETURN p.nome, p.estoque"
       params: '{"negocio_idx": "5544332211", "nome_produto": "Camiseta", "quantidade": 10}'

    3. Atualizar descrição de um produto:
       query: "MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {idx: $produto_idx})
               WHERE p.excluido <> 1
               SET p.descricao = $nova_descricao
               RETURN p"
       params: '{"negocio_idx": "5544332211", "produto_idx": "abc123", "nova_descricao": "Nova descrição do produto"}'


    """
    print("===== atualiza_produto() =====")
    try:
        p = json.loads(params)
        if p.get("negocio_idx") is None:
            return {"status": "error", "message": "negocio_idx obrigatório"}

        q = query.upper()
        if "MATCH" not in q or "SET" not in q:
            return {"status": "error", "message": "Query precisa de MATCH e SET"}
        if "$NEGOCIO_IDX" not in q or "EXCLUIDO" not in q:
            return {"status": "error", "message": "Faltam filtros obrigatórios ($negocio_idx ou excluido)"}

        print("query", query)
        print("params", params)

        result = await neo4j.execute_write_query(query, p)
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
   import asyncio

   async def testa_atualiza_produto():
      # Query específica para atualizar o preço de um produto
      query = """MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $codigo})
               WHERE p.excluido <> 1
               SET p.preco = $novo_preco
               RETURN p.nome, p.preco"""
      params = '{"negocio_idx": "5544332211", "codigo": "PROD001", "novo_preco": 149.90}'
      # O decorator function_tool retorna um objeto, use .func para acessar a função original
      result = await atualiza_produto.func(query, params)
      print(result)

   asyncio.run(testa_atualiza_produto())
   # Execução:
   # python -m api.agent.assistenciamk.functions.atualiza_produto