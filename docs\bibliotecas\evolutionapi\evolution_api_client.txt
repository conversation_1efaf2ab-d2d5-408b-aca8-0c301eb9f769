Directory structure:
└── evolutionapi-evolution-client-python/
    ├── README.md
    ├── publish.sh
    ├── setup.py
    ├── test_evolution.py
    └── evolutionapi/
        ├── __init__.py
        ├── client.py
        ├── exceptions.py
        ├── models/
        │   ├── __init__.py
        │   ├── call.py
        │   ├── chat.py
        │   ├── group.py
        │   ├── instance.py
        │   ├── label.py
        │   ├── message.py
        │   ├── presence.py
        │   ├── profile.py
        │   └── websocket.py
        └── services/
            ├── __init__.py
            ├── call.py
            ├── chat.py
            ├── group.py
            ├── instance.py
            ├── instance_operations.py
            ├── label.py
            ├── message.py
            ├── profile.py
            └── websocket.py


Files Content:

================================================
FILE: README.md
================================================
# Evolution Client Python

Python client to interact with the evolutionapi.

## Installation

```bash
pip install evolutionapi
```

## Basic Usage

### Initializing the Client

```python
from evolutionapi.client import EvolutionClient

client = EvolutionClient(
    base_url='http://your-server:port',
    api_token='your-api-token'
)
```

### Instance Management

#### List Instances

```python
instances = client.instances.fetch_instances()
```

#### Create New Instance

```python
from evolutionapi.models.instance import InstanceConfig

# Configuração básica
config = InstanceConfig(
    instanceName="minha-instancia",
    integration="WHATSAPP-BAILEYS",
    qrcode=True
)

# Configuração completa
config = InstanceConfig(
    instanceName="minha-instancia",
    integration="WHATSAPP-BAILEYS",
    token="token_da_instancia",
    number="5511999999999",
    qrcode=True,
    rejectCall=True,
    msgCall="Mensagem de chamada rejeitada",
    groupsIgnore=True,
    alwaysOnline=True,
    readMessages=True,
    readStatus=True,
    syncFullHistory=True
)

new_instance = client.instances.create_instance(config)
```

#### Configure Webhook

```python
from evolutionapi.models.instance import WebhookConfig

config = WebhookConfig(
    url="https://seu-servidor.com/webhook",
    byEvents=True,
    base64=True,
    headers={
        "Authorization": "Bearer seu-token"
    },
    events=[
        "messages.upsert",
        "messages.update",
        "messages.delete",
        "groups.upsert",
        "groups.update",
        "groups.delete",
        "group-participants.update",
        "contacts.upsert",
        "contacts.update",
        "contacts.delete",
        "presence.update",
        "chats.upsert",
        "chats.update",
        "chats.delete",
        "call"
    ]
)

response = client.instances.set_webhook(instance_id, config, instance_token)
```

#### Configure Events

```python
from evolutionapi.models.instance import EventsConfig

config = EventsConfig(
    enabled=True,
    events=[
        "messages.upsert",
        "messages.update",
        "messages.delete",
        "groups.upsert",
        "groups.update",
        "groups.delete",
        "group-participants.update",
        "contacts.upsert",
        "contacts.update",
        "contacts.delete",
        "presence.update",
        "chats.upsert",
        "chats.update",
        "chats.delete",
        "call"
    ]
)

response = client.instances.set_events(instance_id, config, instance_token)
```

#### Configure Chatwoot Integration

```python
from evolutionapi.models.instance import ChatwootConfig

config = ChatwootConfig(
    accountId="seu-account-id",
    token="seu-token",
    url="https://seu-chatwoot.com",
    signMsg=True,
    reopenConversation=True,
    conversationPending=False,
    importContacts=True,
    nameInbox="evolution",
    mergeBrazilContacts=True,
    importMessages=True,
    daysLimitImportMessages=3,
    organization="Evolution Bot",
    logo="https://evolution-api.com/files/evolution-api-favicon.png"
)

response = client.instances.set_chatwoot(instance_id, config, instance_token)
```

#### Delete Instance

```python
response = client.instances.delete_instance(instance_id, instance_token)
```

#### Get Instance Info

```python
response = client.instances.get_instance_info(instance_id, instance_token)
```

#### Get Instance QR Code

```python
response = client.instances.get_instance_qrcode(instance_id, instance_token)
```

#### Get Instance Status

```python
response = client.instances.get_instance_status(instance_id, instance_token)
```

#### Logout Instance

```python
response = client.instances.logout_instance(instance_id, instance_token)
```

#### Restart Instance

```python
response = client.instances.restart_instance(instance_id, instance_token)
```

### Instance Operations

#### Connect Instance

```python
state = client.instance_operations.connect(instance_id, instance_token)
```

#### Check Connection State

```python
state = client.instance_operations.get_connection_state(instance_id, instance_token)
```

#### Set Presence

```python
from evolutionapi.models.presence import PresenceConfig, PresenceStatus

# Definir como disponível
config = PresenceConfig(
    presence=PresenceStatus.AVAILABLE
)

# Definir como indisponível
config = PresenceConfig(
    presence=PresenceStatus.UNAVAILABLE
)

response = client.instance_operations.set_presence(instance_id, config, instance_token)
```

### Sending Messages

#### Text Message

```python
from evolutionapi.models.message import TextMessage, QuotedMessage

# Mensagem simples
message = TextMessage(
    number="5511999999999",
    text="Olá, como você está?",
    delay=1000  # delay opcional em ms
)

# Mensagem com menções
message = TextMessage(
    number="5511999999999",
    text="@everyone Olá a todos!",
    mentionsEveryOne=True,
    mentioned=["5511999999999", "5511888888888"]
)

# Mensagem com link preview
message = TextMessage(
    number="5511999999999",
    text="Confira este link: https://exemplo.com",
    linkPreview=True
)

# Mensagem com citação
quoted = QuotedMessage(
    key={
        "remoteJid": "<EMAIL>",
        "fromMe": False,
        "participant": "<EMAIL>",
        "id": "123456789",
        "owner": "<EMAIL>"
    }
)

message = TextMessage(
    number="5511999999999",
    text="Esta é uma resposta",
    quoted=quoted
)

response = client.messages.send_text(instance_id, message, instance_token)
```

#### Media Message

```python
from evolutionapi.models.message import MediaMessage, MediaType, QuotedMessage

# Mensagem com imagem
message = MediaMessage(
    number="5511999999999",
    mediatype=MediaType.IMAGE.value,
    mimetype="image/jpeg",
    caption="Minha imagem",
    media="base64_da_imagem_ou_url",
    fileName="imagem.jpg",
    delay=1000  # delay opcional
)

# Mensagem com vídeo
message = MediaMessage(
    number="5511999999999",
    mediatype=MediaType.VIDEO.value,
    mimetype="video/mp4",
    caption="Meu vídeo",
    media="base64_do_video_ou_url",
    fileName="video.mp4"
)

# Mensagem com documento
message = MediaMessage(
    number="5511999999999",
    mediatype=MediaType.DOCUMENT.value,
    mimetype="application/pdf",
    caption="Meu documento",
    media="base64_do_documento_ou_url",
    fileName="documento.pdf"
)

# Mensagem com menções
message = MediaMessage(
    number="5511999999999",
    mediatype=MediaType.IMAGE.value,
    mimetype="image/jpeg",
    caption="@everyone Olhem esta imagem!",
    media="base64_da_imagem",
    mentionsEveryOne=True,
    mentioned=["5511999999999", "5511888888888"]
)

response = client.messages.send_media(instance_id, message, instance_token)
```

#### Status Message

```python
from evolutionapi.models.message import StatusMessage, StatusType, FontType

# Status de texto
message = StatusMessage(
    type=StatusType.TEXT,
    content="Meu status de texto",
    caption="Legenda opcional",
    backgroundColor="#FF0000",
    font=FontType.BEBASNEUE_REGULAR,
    allContacts=True
)

# Status de imagem
message = StatusMessage(
    type=StatusType.IMAGE,
    content="base64_da_imagem",
    caption="Minha imagem de status"
)

# Status de vídeo
message = StatusMessage(
    type=StatusType.VIDEO,
    content="base64_do_video",
    caption="Meu vídeo de status"
)

# Status de áudio
message = StatusMessage(
    type=StatusType.AUDIO,
    content="base64_do_audio",
    caption="Meu áudio de status"
)

response = client.messages.send_status(instance_id, message, instance_token)
```

#### Location Message

```python
from evolutionapi.models.message import LocationMessage

message = LocationMessage(
    number="5511999999999",
    name="Localização",
    address="Endereço completo",
    latitude=-23.550520,
    longitude=-46.633308,
    delay=1000  # delay opcional
)

response = client.messages.send_location(instance_id, message, instance_token)
```

#### Contact Message

```python
from evolutionapi.models.message import ContactMessage, Contact

contact = Contact(
    fullName="Nome Completo",
    wuid="5511999999999",
    phoneNumber="5511999999999",
    organization="Empresa",
    email="<EMAIL>",
    url="https://exemplo.com"
)

message = ContactMessage(
    number="5511999999999",
    contact=[contact]
)

response = client.messages.send_contact(instance_id, message, instance_token)
```

#### Reaction Message

```python
from evolutionapi.models.message import ReactionMessage

message = ReactionMessage(
    key={
        "remoteJid": "<EMAIL>",
        "fromMe": False,
        "participant": "<EMAIL>",
        "id": "123456789",
        "owner": "<EMAIL>"
    },
    reaction="👍"
)

response = client.messages.send_reaction(instance_id, message, instance_token)
```

#### Poll Message

```python
from evolutionapi.models.message import PollMessage

message = PollMessage(
    number="5511999999999",
    name="Minha Enquete",
    selectableCount=1,  # número de opções que podem ser selecionadas
    values=["Opção 1", "Opção 2", "Opção 3"],
    delay=1000  # delay opcional
)

response = client.messages.send_poll(instance_id, message, instance_token)
```

#### Button Message

```python
from evolutionapi.models.message import ButtonMessage, Button

# Botão de resposta simples
buttons = [
    Button(
        type="reply",
        displayText="Opção 1",
        id="1"
    ),
    Button(
        type="reply",
        displayText="Opção 2",
        id="2"
    )
]

# Botão com URL
buttons = [
    Button(
        type="url",
        displayText="Visitar Site",
        url="https://exemplo.com"
    )
]

# Botão com número de telefone
buttons = [
    Button(
        type="phoneNumber",
        displayText="Ligar",
        phoneNumber="5511999999999"
    )
]

# Botão com código de cópia
buttons = [
    Button(
        type="copyCode",
        displayText="Copiar Código",
        copyCode="ABC123"
    )
]

message = ButtonMessage(
    number="5511999999999",
    title="Título",
    description="Descrição",
    footer="Rodapé",
    buttons=buttons,
    delay=1000  # delay opcional
)

response = client.messages.send_buttons(instance_id, message, instance_token)
```

#### List Message

```python
from evolutionapi.models.message import ListMessage, ListSection, ListRow

rows = [
    ListRow(
        title="Item 1",
        description="Descrição do item 1",
        rowId="1"
    ),
    ListRow(
        title="Item 2",
        description="Descrição do item 2",
        rowId="2"
    )
]

section = ListSection(
    title="Seção 1",
    rows=rows
)

message = ListMessage(
    number="5511999999999",
    title="Título da Lista",
    description="Descrição da lista",
    buttonText="Clique aqui",
    footerText="Rodapé",
    sections=[section],
    delay=1000  # delay opcional
)

response = client.messages.send_list(instance_id, message, instance_token)
```

### Group Management

#### Create Group

```python
from evolutionapi.models.group import CreateGroup

config = CreateGroup(
    subject="Nome do Grupo",
    participants=["5511999999999", "5511888888888"],
    description="Descrição do grupo"
)

response = client.group.create_group(instance_id, config, instance_token)
```

#### Update Group Picture

```python
from evolutionapi.models.group import GroupPicture

config = GroupPicture(
    image="base64_da_imagem"
)

response = client.group.update_group_picture(instance_id, "group_jid", config, instance_token)
```

#### Update Group Subject

```python
from evolutionapi.models.group import GroupSubject

config = GroupSubject(
    subject="Novo Nome do Grupo"
)

response = client.group.update_group_subject(instance_id, "group_jid", config, instance_token)
```

#### Update Group Description

```python
from evolutionapi.models.group import GroupDescription

config = GroupDescription(
    description="Nova descrição do grupo"
)

response = client.group.update_group_description(instance_id, "group_jid", config, instance_token)
```

#### Send Group Invite

```python
from evolutionapi.models.group import GroupInvite

config = GroupInvite(
    groupJid="group_jid",
    description="Convite para o grupo",
    numbers=["5511999999999", "5511888888888"]
)

response = client.group.send_group_invite(instance_id, config, instance_token)
```

#### Manage Participants

```python
from evolutionapi.models.group import UpdateParticipant

# Adicionar participantes
config = UpdateParticipant(
    action="add",
    participants=["5511999999999", "5511888888888"]
)

# Remover participantes
config = UpdateParticipant(
    action="remove",
    participants=["5511999999999"]
)

# Promover a administrador
config = UpdateParticipant(
    action="promote",
    participants=["5511999999999"]
)

# Rebaixar de administrador
config = UpdateParticipant(
    action="demote",
    participants=["5511999999999"]
)

response = client.group.update_participant(instance_id, "group_jid", config, instance_token)
```

#### Update Group Settings

```python
from evolutionapi.models.group import UpdateSetting

# Ativar modo anúncio
config = UpdateSetting(
    action="announcement"
)

# Desativar modo anúncio
config = UpdateSetting(
    action="not_announcement"
)

# Bloquear grupo
config = UpdateSetting(
    action="locked"
)

# Desbloquear grupo
config = UpdateSetting(
    action="unlocked"
)

response = client.group.update_setting(instance_id, "group_jid", config, instance_token)
```

#### Toggle Ephemeral Messages

```python
from evolutionapi.models.group import ToggleEphemeral

config = ToggleEphemeral(
    expiration=86400  # 24 horas em segundos
)

response = client.group.toggle_ephemeral(instance_id, "group_jid", config, instance_token)
```

### Profile Management

#### Fetch Profile

```python
from evolutionapi.models.profile import FetchProfile

config = FetchProfile(
    number="5511999999999"
)

response = client.profile.fetch_profile(instance_id, config, instance_token)
```

#### Update Profile Name

```python
from evolutionapi.models.profile import ProfileName

config = ProfileName(
    name="Novo Nome"
)

response = client.profile.update_profile_name(instance_id, config, instance_token)
```

#### Update Status

```python
from evolutionapi.models.profile import ProfileStatus

config = ProfileStatus(
    status="Novo status"
)

response = client.profile.update_profile_status(instance_id, config, instance_token)
```

#### Update Profile Picture

```python
from evolutionapi.models.profile import ProfilePicture

config = ProfilePicture(
    picture="base64_da_imagem"
)

response = client.profile.update_profile_picture(instance_id, config, instance_token)
```

#### Configure Privacy Settings

```python
from evolutionapi.models.profile import PrivacySettings

config = PrivacySettings(
    readreceipts="all",           # "all" ou "none"
    profile="contacts",           # "all", "contacts", "contact_blacklist" ou "none"
    status="contacts",            # "all", "contacts", "contact_blacklist" ou "none"
    online="all",                 # "all" ou "match_last_seen"
    last="contacts",              # "all", "contacts", "contact_blacklist" ou "none"
    groupadd="contacts"           # "all", "contacts" ou "contact_blacklist"
)

response = client.profile.update_privacy_settings(instance_id, config, instance_token)
```

### Chat Operations

#### Check WhatsApp Numbers

```python
from evolutionapi.models.chat import CheckIsWhatsappNumber

config = CheckIsWhatsappNumber(
    numbers=["5511999999999", "5511888888888"]
)

response = client.chat.check_is_whatsapp_numbers(instance_id, config, instance_token)
```

#### Mark Message as Read

```python
from evolutionapi.models.chat import ReadMessage

message = ReadMessage(
    remote_jid="<EMAIL>",
    from_me=False,
    id="message_id"
)

response = client.chat.mark_message_as_read(instance_id, [message], instance_token)
```

#### Archive Chat

```python
from evolutionapi.models.chat import ArchiveChat

config = ArchiveChat(
    last_message={
        "key": {
            "remoteJid": "<EMAIL>",
            "fromMe": False,
            "id": "message_id",
            "participant": "<EMAIL>"
        },
        "message": {
            "conversation": "Última mensagem"
        }
    },
    chat="<EMAIL>",
    archive=True  # True para arquivar, False para desarquivar
)

response = client.chat.archive_chat(instance_id, config, instance_token)
```

#### Mark Chat as Unread

```python
from evolutionapi.models.chat import UnreadChat

config = UnreadChat(
    last_message={
        "key": {
            "remoteJid": "<EMAIL>",
            "fromMe": False,
            "id": "message_id",
            "participant": "<EMAIL>"
        },
        "message": {
            "conversation": "Última mensagem"
        }
    },
    chat="<EMAIL>"
)

response = client.chat.unread_chat(instance_id, config, instance_token)
```

#### Get Chat Profile Picture

```python
from evolutionapi.models.chat import ProfilePicture

config = ProfilePicture(
    number="5511999999999"
)

response = client.chat.get_chat_profile_picture(instance_id, config, instance_token)
```

#### Download Media Message

```python
from evolutionapi.models.chat import MediaMessage

config = MediaMessage(
    message={
        "key": {
            "remoteJid": "<EMAIL>",
            "fromMe": False,
            "id": "message_id",
            "participant": "<EMAIL>"
        },
        "message": {
            "imageMessage": {
                "jpegThumbnail": "base64_da_imagem"
            }
        }
    },
    convert_to_mp4=True  # opcional, para converter vídeos para MP4
)

response = client.chat.download_media_message(instance_id, config, instance_token)
```

#### Update Message

```python
from evolutionapi.models.chat import UpdateMessage

config = UpdateMessage(
    number="5511999999999",
    key={
        "remoteJid": "<EMAIL>",
        "fromMe": False,
        "id": "message_id",
        "participant": "<EMAIL>"
    },
    text="Mensagem atualizada"
)

response = client.chat.update_message(instance_id, config, instance_token)
```

#### Set Presence

```python
from evolutionapi.models.chat import Presence

config = Presence(
    number="5511999999999",
    delay=1000,  # delay em ms
    presence="composing"  # "composing", "recording", "paused"
)

response = client.chat.set_presence(instance_id, config, instance_token)
```

### Calls

#### Simulate Call

```python
from evolutionapi.models.call import FakeCall

# Chamada de voz
config = FakeCall(
    number="5511999999999",
    isVideo=False,
    callDuration=30  # duração em segundos
)

# Chamada de vídeo
config = FakeCall(
    number="5511999999999",
    isVideo=True,
    callDuration=30  # duração em segundos
)

response = client.calls.fake_call(instance_id, config, instance_token)
```

### Labels

#### Manage Labels

```python
from evolutionapi.models.label import HandleLabel

# Adicionar etiqueta
config = HandleLabel(
    number="5511999999999",
    label_id="label_id",
    action="add"
)

# Remover etiqueta
config = HandleLabel(
    number="5511999999999",
    label_id="label_id",
    action="remove"
)

response = client.label.handle_label(instance_id, config, instance_token)
```

## WebSocket

The Evolution API client supports WebSocket connection to receive real-time events. Here's a guide on how to use it:

### Prerequisites

Before using WebSocket, you need to:

1. Enable WebSocket in your Evolution API by setting the environment variable:

```bash
WEBSOCKET_ENABLED=true
```

2. Configure WebSocket events for your instance using the WebSocket service:

```python
from evolutionapi.models.websocket import WebSocketConfig

# Configure WebSocket events
config = WebSocketConfig(
    enabled=True,
    events=[
        "APPLICATION_STARTUP",
        "QRCODE_UPDATED",
        "MESSAGES_SET",
        "MESSAGES_UPSERT",
        "MESSAGES_UPDATE",
        "MESSAGES_DELETE",
        "SEND_MESSAGE",
        "CONTACTS_SET",
        "CONTACTS_UPSERT",
        "CONTACTS_UPDATE",
        "PRESENCE_UPDATE",
        "CHATS_SET",
        "CHATS_UPSERT",
        "CHATS_UPDATE",
        "CHATS_DELETE",
        "GROUPS_UPSERT",
        "GROUP_UPDATE",
        "GROUP_PARTICIPANTS_UPDATE",
        "CONNECTION_UPDATE",
        "LABELS_EDIT",
        "LABELS_ASSOCIATION",
        "CALL",
        "TYPEBOT_START",
        "TYPEBOT_CHANGE_STATUS"
    ]
)

# Set WebSocket configuration
response = client.websocket.set_websocket(instance_id, config, instance_token)

# Get current WebSocket configuration
websocket_info = client.websocket.find_websocket(instance_id, instance_token)
print(f"WebSocket enabled: {websocket_info.enabled}")
print(f"Configured events: {websocket_info.events}")
```

### Basic Configuration

There are two ways to create a WebSocket manager:

1. Using the client's helper method (recommended):

```python
# Create WebSocket manager using the client
websocket = client.create_websocket(
    instance_id="test",
    api_token="your_api_token",
    max_retries=5,        # Maximum number of reconnection attempts
    retry_delay=1.0       # Initial delay between attempts in seconds
)
```

2. Creating the manager directly:

```python
from evolutionapi.client import EvolutionClient
import logging

# Logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Initialize client
client = EvolutionClient(
    base_url="http://localhost:8081",
    api_token="your-api-token"
)

# Create WebSocket manager
websocket = client.create_websocket(
    instance_id="test",
    api_token="your_api_token",
    max_retries=5,
    retry_delay=1.0
)
```

### Registering Event Handlers

You can register handlers for different types of events:

```python
def handle_message(data):
    print(f"New message received: {data}")

def handle_qrcode(data):
    print(f"QR Code updated: {data}")

# Registering handlers
websocket.on("messages.upsert", handle_message)
websocket.on("qrcode.updated", handle_qrcode)
```

### Available Events

The available events are:

#### Instance Events

- `application.startup`: Triggered when the application starts
- `instance.create`: Triggered when a new instance is created
- `instance.delete`: Triggered when an instance is deleted
- `remove.instance`: Triggered when an instance is removed
- `logout.instance`: Triggered when an instance logs out

#### Connection and QR Code Events

- `qrcode.updated`: Triggered when the QR Code is updated
- `connection.update`: Triggered when connection status changes
- `status.instance`: Triggered when instance status changes
- `creds.update`: Triggered when credentials are updated

#### Message Events

- `messages.set`: Triggered when messages are set
- `messages.upsert`: Triggered when new messages are received
- `messages.edited`: Triggered when messages are edited
- `messages.update`: Triggered when messages are updated
- `messages.delete`: Triggered when messages are deleted
- `send.message`: Triggered when a message is sent
- `messaging-history.set`: Triggered when messaging history is set

#### Contact Events

- `contacts.set`: Triggered when contacts are set
- `contacts.upsert`: Triggered when new contacts are added
- `contacts.update`: Triggered when contacts are updated

#### Chat Events

- `chats.set`: Triggered when chats are set
- `chats.update`: Triggered when chats are updated
- `chats.upsert`: Triggered when new chats are added
- `chats.delete`: Triggered when chats are deleted

#### Group Events

- `groups.upsert`: Triggered when groups are created/updated
- `groups.update`: Triggered when groups are updated
- `group-participants.update`: Triggered when group participants are updated

#### Presence Events

- `presence.update`: Triggered when presence status is updated

#### Call Events

- `call`: Triggered when there's a call

#### Typebot Events

- `typebot.start`: Triggered when a typebot starts
- `typebot.change-status`: Triggered when typebot status changes

#### Label Events

- `labels.edit`: Triggered when labels are edited
- `labels.association`: Triggered when labels are associated/disassociated

### Example with Specific Events

```python
def handle_messages(data):
    logger.info(f"New message: {data}")

def handle_contacts(data):
    logger.info(f"Contacts updated: {data}")

def handle_groups(data):
    logger.info(f"Groups updated: {data}")

def handle_presence(data):
    logger.info(f"Presence status: {data}")

# Registering handlers for different events
websocket.on("messages.upsert", handle_messages)
websocket.on("contacts.upsert", handle_contacts)
websocket.on("groups.upsert", handle_groups)
websocket.on("presence.update", handle_presence)
```

### Complete Example

```python
from evolutionapi.client import EvolutionClient
from evolutionapi.models.websocket import WebSocketConfig
import logging
import time

# Logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def handle_message(data):
    logger.info(f"New message received: {data}")

def handle_qrcode(data):
    logger.info(f"QR Code updated: {data}")

def handle_connection(data):
    logger.info(f"Connection status: {data}")

def main():
    # Initialize client
    client = EvolutionClient(
        base_url="http://localhost:8081",
        api_token="your-api-token"
    )

    # Configure WebSocket
    websocket_config = WebSocketConfig(
        enabled=True,
        events=[
            "MESSAGES_UPSERT",
            "QRCODE_UPDATED",
            "CONNECTION_UPDATE"
        ]
    )

    # Set WebSocket configuration
    client.websocket.set_websocket("instance_id", websocket_config, "instance_token")

    # Create WebSocket manager
    websocket = client.create_websocket(
        instance_id="instance_id",
        api_token="your_api_token",
        max_retries=5,
        retry_delay=1.0
    )

    # Register handlers
    websocket.on("messages.upsert", handle_message)
    websocket.on("qrcode.updated", handle_qrcode)
    websocket.on("connection.update", handle_connection)

    try:
        # Connect to WebSocket
        websocket.connect()
        logger.info("Connected to WebSocket. Waiting for events...")

        # Keep the program running
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Closing connection...")
        websocket.disconnect()
    except Exception as e:
        logger.error(f"Error: {e}")
        websocket.disconnect()

if __name__ == "__main__":
    main()
```

### Additional Features

#### Automatic Reconnection

The WebSocket Manager has automatic reconnection with exponential backoff:

```python
websocket = client.create_websocket(
    instance_id="test",
    api_token="your_api_token",
    max_retries=5,        # Maximum number of reconnection attempts
    retry_delay=1.0       # Initial delay between attempts in seconds
)
```

#### Logging

The WebSocket Manager uses Python's logging system. You can adjust the log level as needed:

```python
# For more details
logging.getLogger("evolutionapi.services.websocket").setLevel(logging.DEBUG)
```

### Error Handling

The WebSocket Manager has robust error handling:

- Automatic reconnection on disconnection
- Detailed error logs
- Invalid event handling
- Data validation

### Usage Tips

1. Always use try/except when connecting to WebSocket
2. Implement handlers for all events you need to monitor
3. Use logging for debugging and monitoring
4. Consider implementing a heartbeat mechanism if needed
5. Keep your API token secure and don't expose it in logs
6. Keep your API token secure and don't expose it in logs



================================================
FILE: publish.sh
================================================
#!/bin/bash

# Limpa diretório dist anterior se existir
rm -rf dist/*

# Gera os arquivos de distribuição
python setup.py sdist bdist_wheel

# Faz upload para o PyPI
twine upload dist/*

echo "Pacote publicado com sucesso!"



================================================
FILE: setup.py
================================================
from setuptools import setup, find_packages

setup(
    name='evolutionapi',
    version='0.1.1',
    description='Client Python para a API Evolution',
    author='Davidson Gomes',
    author_email='<EMAIL>',
    packages=find_packages(),
    package_data={'': ['*']},
    include_package_data=True,
    install_requires=[
        'requests>=2.25.1',
        'requests_toolbelt>=1.0.0',
        'python-socketio>=5.11.1'
    ],
    python_requires='>=3.6',
)



================================================
FILE: test_evolution.py
================================================
from evolutionapi.client import EvolutionClient
from evolutionapi.models.instance import InstanceConfig
from evolutionapi.models.message import TextMessage, MediaMessage, MediaType
from evolutionapi.models.websocket import WebSocketConfig
import time
import logging

# Configuração do logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

print("Iniciando cliente")

client = EvolutionClient(
    base_url='http://localhost:8081',
    api_token='429683C4C977415CAAFCCE10F7D57E11'
)

instance_token = "82D55E57CBBC-48A5-98FB-E99655AE7148"
instance_id = "teste"

# Configurando eventos do WebSocket
websocket_config = WebSocketConfig(
    enabled=True,
    events=[
        "APPLICATION_STARTUP",
        "QRCODE_UPDATED",
        "MESSAGES_SET",
        "MESSAGES_UPSERT",
        "MESSAGES_UPDATE",
        "MESSAGES_DELETE",
        "SEND_MESSAGE",
        "CONTACTS_SET",
        "CONTACTS_UPSERT",
        "CONTACTS_UPDATE",
        "PRESENCE_UPDATE",
        "CHATS_SET",
        "CHATS_UPSERT",
        "CHATS_UPDATE",
        "CHATS_DELETE",
        "GROUPS_UPSERT",
        "GROUP_UPDATE",
        "GROUP_PARTICIPANTS_UPDATE",
        "CONNECTION_UPDATE",
        "LABELS_EDIT",
        "LABELS_ASSOCIATION",
        "CALL",
        "TYPEBOT_START",
        "TYPEBOT_CHANGE_STATUS"
    ]
)

# Configurando WebSocket para a instância
logger.info("Configurando WebSocket...")
response = client.websocket.set_websocket(instance_id, websocket_config, instance_token)
logger.info(f"Configuração WebSocket: {response}")

# Obtendo configuração atual do WebSocket
websocket_info = client.websocket.find_websocket(instance_id, instance_token)
logger.info(f"WebSocket habilitado: {websocket_info.enabled}")
logger.info(f"Eventos configurados: {websocket_info.events}")

# Criando gerenciador WebSocket usando o cliente
logger.info("Criando gerenciador WebSocket...")
websocket_manager = client.create_websocket(
    instance_id=instance_id,
    api_token=instance_token,
    max_retries=5,
    retry_delay=1.0
)
    
def on_message(data):
    """Handler para evento de mensagens"""
    try:
        if 'data' in data:
            message_data = data['data']
            logger.info("=== Mensagem Recebida ===")
            logger.info(f"De: {message_data['key']['remoteJid']}")
            logger.info(f"Tipo: {message_data['messageType']}")
            
            # Extrai o conteúdo baseado no tipo da mensagem
            if 'message' in message_data:
                if 'conversation' in message_data['message']:
                    logger.info(f"Conteúdo: {message_data['message']['conversation']}")
                elif 'extendedTextMessage' in message_data['message']:
                    logger.info(f"Conteúdo: {message_data['message']['extendedTextMessage']['text']}")
                elif 'imageMessage' in message_data['message']:
                    logger.info(f"Conteúdo: [Imagem] {message_data['message']['imageMessage'].get('caption', '')}")
                else:
                    logger.info(f"Conteúdo: {message_data['message']}")
            
            logger.info("=======================")
    except Exception as e:
        logger.error(f"Erro ao processar mensagem: {e}", exc_info=True)

def on_qrcode(data):
    """Handler para evento de QR Code"""
    logger.info("=== QR Code Atualizado ===")
    logger.info(f"QR Code: {data}")
    logger.info("=======================")

def on_connection(data):
    """Handler para evento de conexão"""
    logger.info("=== Status de Conexão ===")
    logger.info(f"Status: {data}")
    logger.info("=======================")

logger.info("Registrando handlers de eventos...")

# Registrando handlers de eventos
websocket_manager.on('messages.upsert', on_message)
websocket_manager.on('qrcode.updated', on_qrcode)
websocket_manager.on('connection.update', on_connection)

try:
    logger.info("Iniciando conexão WebSocket...")
    # Conectando ao WebSocket
    websocket_manager.connect()
    
    # Mantendo o programa rodando para receber eventos
    logger.info("Aguardando eventos...")
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    logger.info("Encerrando conexão WebSocket...")
finally:
    websocket_manager.disconnect()

# Exemplos de outras operações (comentados)
# response = client.group.fetch_all_groups(instance_id, instance_token, False)
# print(response)

# text_message = TextMessage(
#     number="557499879409",
#     text="Olá, como vai?",
#     delay=1200
# )

# response = client.messages.send_text(instance_id, text_message, instance_token)
# print("Mensagem de texto enviada")
# print(response)

# media_message = MediaMessage(
#     number="557499879409",
#     mediatype="document",
#     mimetype="application/pdf",
#     caption="Olá, como vai?",
#     fileName="arquivo.pdf"
# )

# response = client.messages.send_media(instance_id, media_message, instance_token, "arquivo.pdf")
# print("Mensagem de mídia enviada")
# print(response)


================================================
FILE: evolutionapi/__init__.py
================================================
[Empty file]


================================================
FILE: evolutionapi/client.py
================================================
import requests
from requests_toolbelt import MultipartEncoder
from .exceptions import EvolutionAuthenticationError, EvolutionNotFoundError, EvolutionAPIError
from .services.instance import InstanceService
from .services.instance_operations import InstanceOperationsService
from .services.message import MessageService
from .services.call import CallService
from .services.chat import ChatService
from .services.label import LabelService
from .services.profile import ProfileService
from .services.group import GroupService
from .services.websocket import WebSocketService, WebSocketManager

class EvolutionClient:
    """
    Cliente para interagir com a API Evolution.

    Args:
        base_url (str): A URL base do servidor da API Evolution.
        api_token (str): O token de autenticação para acessar a API.
    """

    def __init__(self, base_url: str, api_token: str):
        self.base_url = base_url.rstrip('/')
        self.api_token = api_token
        self.instances = InstanceService(self)
        self.instance_operations = InstanceOperationsService(self)
        self.messages = MessageService(self)
        self.calls = CallService(self)
        self.chat = ChatService(self)
        self.label = LabelService(self)
        self.profile = ProfileService(self)
        self.group = GroupService(self)
        self.websocket = WebSocketService(self)
        
    def _get_headers(self, instance_token: str = None):
        return {
            'apikey': instance_token or self.api_token,
            'Content-Type': 'application/json'
        }

    def _get_full_url(self, endpoint):
        return f'{self.base_url}/{endpoint}'

    def _handle_response(self, response):
        if response.status_code == 401:
            raise EvolutionAuthenticationError('Falha na autenticação.')
        elif response.status_code == 404:
            raise EvolutionNotFoundError('Recurso não encontrado.')
        elif response.ok:
            try:
                return response.json()
            except ValueError:
                return response.content
        else:
            error_detail = ''
            try:
                error_detail = f' - {response.json()}'
            except:
                error_detail = f' - {response.text}'
            raise EvolutionAPIError(f'Erro na requisição: {response.status_code}{error_detail}')

    def get(self, endpoint: str, instance_token: str = None):
        """Faz uma requisição GET."""
        url = self._get_full_url(endpoint)
        response = requests.get(url, headers=self._get_headers(instance_token))
        return self._handle_response(response)

    def post(self, endpoint: str, data: dict = None, instance_token: str = None, files: dict = None):
        url = f'{self.base_url}/{endpoint}'
        headers = self._get_headers(instance_token)
        
        if files:
            # Remove o Content-Type do header quando enviando arquivos
            if 'Content-Type' in headers:
                del headers['Content-Type']
            
            # Prepara os campos do multipart
            fields = {}
            
            # Adiciona os campos do data
            for key, value in data.items():
                fields[key] = str(value) if not isinstance(value, (int, float)) else (None, str(value), 'text/plain')
            
            # Adiciona o arquivo
            file_tuple = files['file']
            fields['file'] = (file_tuple[0], file_tuple[1], file_tuple[2])
            
            # Cria o multipart encoder
            multipart = MultipartEncoder(fields=fields)
            headers['Content-Type'] = multipart.content_type
            
            response = requests.post(
                url, 
                headers=headers,
                data=multipart
            )
        else:
            response = requests.post(
                url, 
                headers=headers, 
                json=data
            )
        
        return response.json()

    def put(self, endpoint, data=None):
        """Faz uma requisição PUT."""
        url = self._get_full_url(endpoint)
        response = requests.put(url, headers=self.headers, json=data)
        return self._handle_response(response)

    def delete(self, endpoint: str, instance_token: str = None):
        """Faz uma requisição DELETE."""
        url = self._get_full_url(endpoint)
        response = requests.delete(url, headers=self._get_headers(instance_token))
        return self._handle_response(response)

    def create_websocket(self, instance_id: str, api_token: str, max_retries: int = 5, retry_delay: float = 1.0) -> WebSocketManager:
        """
        Create a WebSocket manager for the specified instance.
        
        Args:
            instance_id (str): The instance ID
            api_token (str): The API token
            max_retries (int): Maximum number of reconnection attempts
            retry_delay (float): Initial delay between attempts in seconds
            
        Returns:
            WebSocketManager: The WebSocket manager instance
        """
        return WebSocketManager(
            base_url=self.base_url,
            instance_id=instance_id,
            api_token=api_token,
            max_retries=max_retries,
            retry_delay=retry_delay
        )



================================================
FILE: evolutionapi/exceptions.py
================================================
class EvolutionAPIError(Exception):
    """Erro genérico da API Evolution."""
    pass

class EvolutionAuthenticationError(EvolutionAPIError):
    """Erro de autenticação com a API Evolution."""
    pass

class EvolutionNotFoundError(EvolutionAPIError):
    """Recurso não encontrado na API Evolution."""
    pass



================================================
FILE: evolutionapi/models/__init__.py
================================================
[Empty file]


================================================
FILE: evolutionapi/models/call.py
================================================
class BaseCall:
    def __init__(self, **kwargs):
        self.__dict__.update({k: v for k, v in kwargs.items() if v is not None})

class FakeCall(BaseCall):
    def __init__(
        self,
        number: str,
        isVideo: bool,
        callDuration: int
    ):
        super().__init__(
            number=number,
            isVideo=isVideo,
            callDuration=callDuration
        )


================================================
FILE: evolutionapi/models/chat.py
================================================
from typing import List, Optional, Dict, Any

class BaseChat:
    def __init__(self, **kwargs):
        self.__dict__.update({k: v for k, v in kwargs.items() if v is not None})

class CheckIsWhatsappNumber(BaseChat):
    def __init__(
        self,
        numbers: List[str]
    ):
        super().__init__(
            numbers=numbers
        )

class MessageKey:
    def __init__(
        self,
        remote_jid: str,
        from_me: bool,
        id: str,
        participant: Optional[str] = None
    ):
        self.remoteJid = remote_jid
        self.fromMe = from_me
        self.id = id
        self.participant = participant

class ReadMessage:
    def __init__(
        self,
        remote_jid: str,
        from_me: bool,
        id: str
    ):
        self.remoteJid = remote_jid
        self.fromMe = from_me
        self.id = id

class ArchiveChat:
    def __init__(
        self,
        last_message: Dict[str, Any],
        chat: str,
        archive: bool
    ):
        self.lastMessage = last_message
        self.chat = chat
        self.archive = archive

class UnreadChat:
    def __init__(
        self,
        last_message: Dict[str, Any],
        chat: str
    ):
        self.lastMessage = last_message
        self.chat = chat

class ProfilePicture:
    def __init__(self, number: str):
        self.number = number

class MediaMessage:
    def __init__(
        self,
        message: Dict[str, Any],
        convert_to_mp4: bool = False
    ):
        self.message = message
        self.convertToMp4 = convert_to_mp4

class UpdateMessage:
    def __init__(
        self,
        number: str,
        key: Dict[str, Any],
        text: str
    ):
        self.number = number
        self.key = key
        self.text = text

class Presence:
    def __init__(
        self,
        number: str,
        delay: int,
        presence: str
    ):
        self.number = number
        self.delay = delay
        self.presence = presence


================================================
FILE: evolutionapi/models/group.py
================================================
from typing import List, Optional, Literal
from dataclasses import dataclass

@dataclass
class CreateGroup:
    subject: str
    participants: List[str]
    description: Optional[str] = None

@dataclass
class GroupPicture:
    image: str

@dataclass
class GroupSubject:
    subject: str

@dataclass
class GroupDescription:
    description: str

@dataclass
class GroupInvite:
    groupJid: str
    description: str
    numbers: List[str]

@dataclass
class UpdateParticipant:
    action: Literal["add", "remove", "promote", "demote"]
    participants: List[str]

@dataclass
class UpdateSetting:
    action: Literal["announcement", "not_announcement", "locked", "unlocked"]

@dataclass
class ToggleEphemeral:
    expiration: int


================================================
FILE: evolutionapi/models/instance.py
================================================
from typing import Optional, List, Dict

class WebhookConfig:
    def __init__(self, url: str = None, byEvents: bool = False, base64: bool = True,
                 headers: Dict = None, events: List[str] = None):
        self.url = url
        self.byEvents = byEvents
        self.base64 = base64
        self.headers = headers
        self.events = events

class EventsConfig:
    def __init__(self, enabled: bool = True, events: List[str] = None):
        self.enabled = enabled
        self.events = events

class ChatwootConfig:
    def __init__(self, accountId: str = None, token: str = None, url: str = None,
                 signMsg: bool = True, reopenConversation: bool = True,
                 conversationPending: bool = False, importContacts: bool = True,
                 nameInbox: str = "evolution", mergeBrazilContacts: bool = True,
                 importMessages: bool = True, daysLimitImportMessages: int = 3,
                 organization: str = "Evolution Bot",
                 logo: str = "https://evolution-api.com/files/evolution-api-favicon.png"):
        self.chatwootAccountId = accountId
        self.chatwootToken = token
        self.chatwootUrl = url
        self.chatwootSignMsg = signMsg
        self.chatwootReopenConversation = reopenConversation
        self.chatwootConversationPending = conversationPending
        self.chatwootImportContacts = importContacts
        self.chatwootNameInbox = nameInbox
        self.chatwootMergeBrazilContacts = mergeBrazilContacts
        self.chatwootImportMessages = importMessages
        self.chatwootDaysLimitImportMessages = daysLimitImportMessages
        self.chatwootOrganization = organization
        self.chatwootLogo = logo

class InstanceConfig:
    def __init__(
        self,
        instanceName: str,
        integration: str = None,
        token: str = None,
        number: str = None,
        qrcode: bool = None,
        rejectCall: bool = None,
        msgCall: str = None,
        groupsIgnore: bool = None,
        alwaysOnline: bool = None,
        readMessages: bool = None,
        readStatus: bool = None,
        syncFullHistory: bool = None
    ):
        self.__dict__['instanceName'] = instanceName
        
        for key, value in locals().items():
            if key != 'self' and key != 'instanceName' and value is not None:
                self.__dict__[key] = value


================================================
FILE: evolutionapi/models/label.py
================================================
from typing import Literal

class BaseLabel:
    def __init__(self, **kwargs):
        self.__dict__.update({k: v for k, v in kwargs.items() if v is not None})

class HandleLabel(BaseLabel):
    def __init__(
        self,
        number: str,
        label_id: str,
        action: Literal["add", "remove"]
    ):
        if action not in ["add", "remove"]:
            raise ValueError("action deve ser 'add' ou 'remove'")
            
        super().__init__(
            number=number,
            labelId=label_id,
            action=action
        )


================================================
FILE: evolutionapi/models/message.py
================================================
from enum import Enum
from typing import List, Optional, Union
from dataclasses import dataclass

class MediaType(Enum):
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"

class StatusType(Enum):
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"

class FontType(Enum):
    SERIF = 1
    NORICAN_REGULAR = 2
    BRYNDAN_WRITE = 3
    BEBASNEUE_REGULAR = 4
    OSWALD_HEAVY = 5

class BaseMessage:
    def __init__(self, **kwargs):
        self.__dict__.update({k: v for k, v in kwargs.items() if v is not None})

class QuotedMessage(BaseMessage):
    def __init__(self, key: dict, message: Optional[dict] = None):
        super().__init__(key=key, message=message)

class TextMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        text: str,
        delay: Optional[int] = None,
        quoted: Optional[QuotedMessage] = None,
        linkPreview: Optional[bool] = None,
        mentionsEveryOne: Optional[bool] = None,
        mentioned: Optional[List[str]] = None
    ):
        super().__init__(
            number=number,
            text=text,
            delay=delay,
            quoted=quoted.__dict__ if quoted else None,
            linkPreview=linkPreview,
            mentionsEveryOne=mentionsEveryOne,
            mentioned=mentioned
        )

class MediaMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        media: dict = None,
        mediatype: Optional[str] = None,
        caption: str = None,
        mimetype: str = None,
        fileName: str = None,
        delay: Optional[Union[int, float, str]] = None,
        quoted: Optional[QuotedMessage] = None,
        mentionsEveryOne: Optional[bool] = None,
        mentioned: Optional[List[str]] = None
    ):
        data = {
            'number': number,
            'mediatype': mediatype,
            'caption': caption,
            'mimetype': mimetype,
            'fileName': fileName,
            'quoted': quoted.__dict__ if quoted else None,
            'mentionsEveryOne': mentionsEveryOne,
            'mentioned': mentioned
        }
        
        if delay is not None:
            data['delay'] = delay
        
        if media and media != {}:
            data['media'] = media
            
        super().__init__(**{k: v for k, v in data.items() if v is not None})

class StatusMessage(BaseMessage):
    def __init__(
        self,
        type: StatusType,
        content: str,
        caption: Optional[str] = None,
        backgroundColor: Optional[str] = None,
        font: Optional[FontType] = None,
        allContacts: bool = False,
        statusJidList: Optional[List[str]] = None
    ):
        super().__init__(
            type=type.value,
            content=content,
            caption=caption,
            backgroundColor=backgroundColor,
            font=font.value if font else None,
            allContacts=allContacts,
            statusJidList=statusJidList
        )

class LocationMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        name: str,
        address: str,
        latitude: float,
        longitude: float,
        delay: Optional[int] = None,
        quoted: Optional[QuotedMessage] = None
    ):
        super().__init__(
            number=number,
            name=name,
            address=address,
            latitude=latitude,
            longitude=longitude,
            delay=delay,
            quoted=quoted.__dict__ if quoted else None
        )

class Contact(BaseMessage):
    def __init__(
        self,
        fullName: str,
        wuid: str,
        phoneNumber: str,
        organization: Optional[str] = None,
        email: Optional[str] = None,
        url: Optional[str] = None
    ):
        super().__init__(
            fullName=fullName,
            wuid=wuid,
            phoneNumber=phoneNumber,
            organization=organization,
            email=email,
            url=url
        )

class ContactMessage(BaseMessage):
    def __init__(self, number: str, contact: List[Contact]):
        super().__init__(
            number=number,
            contact=[c.__dict__ for c in contact]
        )

class ReactionMessage(BaseMessage):
    def __init__(self, key: dict, reaction: str):
        super().__init__(key=key, reaction=reaction)

class PollMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        name: str,
        selectableCount: int,
        values: List[str],
        delay: Optional[int] = None,
        quoted: Optional[QuotedMessage] = None
    ):
        super().__init__(
            number=number,
            name=name,
            selectableCount=selectableCount,
            values=values,
            delay=delay,
            quoted=quoted.__dict__ if quoted else None
        )

class ListRow(BaseMessage):
    def __init__(self, title: str, description: str, rowId: str):
        super().__init__(
            title=title,
            description=description,
            rowId=rowId
        )

class ListSection(BaseMessage):
    def __init__(self, title: str, rows: List[ListRow]):
        super().__init__(
            title=title,
            rows=[r.__dict__ for r in rows]
        )

class ListMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        title: str,
        description: str,
        buttonText: str,
        footerText: str,
        sections: List[ListSection],
        delay: Optional[int] = None,
        quoted: Optional[QuotedMessage] = None
    ):
        super().__init__(
            number=number,
            title=title,
            description=description,
            buttonText=buttonText,
            footerText=footerText,
            sections=[s.__dict__ for s in sections],
            delay=delay,
            quoted=quoted.__dict__ if quoted else None
        )

class Button(BaseMessage):
    def __init__(
        self,
        type: str,
        displayText: str,
        id: Optional[str] = None,
        copyCode: Optional[str] = None,
        url: Optional[str] = None,
        phoneNumber: Optional[str] = None,
        currency: Optional[str] = None,
        name: Optional[str] = None,
        keyType: Optional[str] = None,
        key: Optional[str] = None
    ):
        super().__init__(
            type=type,
            displayText=displayText,
            id=id,
            copyCode=copyCode,
            url=url,
            phoneNumber=phoneNumber,
            currency=currency,
            name=name,
            keyType=keyType,
            key=key
        )

class ButtonMessage(BaseMessage):
    def __init__(
        self,
        number: str,
        title: str,
        description: str,
        footer: str,
        buttons: List[Button],
        delay: Optional[int] = None,
        quoted: Optional[QuotedMessage] = None
    ):
        super().__init__(
            number=number,
            title=title,
            description=description,
            footer=footer,
            buttons=[b.__dict__ for b in buttons],
            delay=delay,
            quoted=quoted.__dict__ if quoted else None
        )


================================================
FILE: evolutionapi/models/presence.py
================================================
from enum import Enum

class PresenceStatus(Enum):
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"

class PresenceConfig:
    def __init__(self, presence: PresenceStatus):
        self.presence = presence.value


================================================
FILE: evolutionapi/models/profile.py
================================================
from typing import Literal

class BaseProfile:
    def __init__(self, **kwargs):
        self.__dict__.update({k: v for k, v in kwargs.items() if v is not None})

class FetchProfile(BaseProfile):
    def __init__(
        self,
        number: str,
    ):
        super().__init__(
            number=number,
        )

class ProfileName(BaseProfile):
    def __init__(
        self,
        name: str,
    ):
        super().__init__(
            name=name,
        )

class ProfileStatus(BaseProfile):
    def __init__(
        self,
        status: str,
    ):
        super().__init__(
            status=status,
        )

class ProfilePicture(BaseProfile):
    def __init__(
        self,
        picture: str,
    ):
        super().__init__(
            picture=picture,
        )

class PrivacySettings(BaseProfile):
    def __init__(
        self,
        readreceipts: Literal["all", "none"],
        profile: Literal["all", "contacts", "contact_blacklist", "none"],
        status: Literal["all", "contacts", "contact_blacklist", "none"],
        online: Literal["all", "match_last_seen"],
        last: Literal["all", "contacts", "contact_blacklist", "none"],
        groupadd: Literal["all", "contacts", "contact_blacklist"],
    ):
        super().__init__(
            readreceipts=readreceipts,
            profile=profile,
            status=status,
            online=online,
            last=last,
            groupadd=groupadd,
        )


================================================
FILE: evolutionapi/models/websocket.py
================================================
from typing import List, Optional
from dataclasses import dataclass

@dataclass
class WebSocketConfig:
    enabled: bool
    events: List[str]

    def __init__(self, enabled: bool, events: List[str]):
        self.enabled = enabled
        self.events = events

@dataclass
class WebSocketInfo:
    enabled: bool
    events: List[str]

    def __init__(self, **kwargs):
        self.enabled = kwargs.get('enabled', False)
        self.events = kwargs.get('events', []) 


================================================
FILE: evolutionapi/services/__init__.py
================================================
[Empty file]


================================================
FILE: evolutionapi/services/call.py
================================================
from typing import Union, BinaryIO
from ..models.call import *

class CallService:
    def __init__(self, client):
        self.client = client

    def fake_call(self, instance_id: str, data: FakeCall, instance_token: str):
        return self.client.post(
            f'call/offer/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )


================================================
FILE: evolutionapi/services/chat.py
================================================
from typing import Union, BinaryIO, Optional
from ..models.chat import *

class ChatService:
    def __init__(self, client):
        self.client = client

    def check_is_whatsapp_numbers(self, instance_id: str, data: CheckIsWhatsappNumber, instance_token: str):
        return self.client.post(
            f'chat/checkIsWhatsappNumber/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )
    
    def mark_message_as_read(self, instance_id: str, messages: List[ReadMessage], instance_token: str):
        return self.client.post(
            f'chat/markMessageAsRead/{instance_id}',
            data={"readMessages": [m.__dict__ for m in messages]},
            instance_token=instance_token
        )

    def archive_chat(self, instance_id: str, data: ArchiveChat, instance_token: str):
        return self.client.post(
            f'chat/archiveChat/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def mark_chat_unread(self, instance_id: str, data: UnreadChat, instance_token: str):
        return self.client.post(
            f'chat/markChatUnread/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def delete_message_for_everyone(self, instance_id: str, data: MessageKey, instance_token: str):
        return self.client.delete(
            f'chat/deleteMessageForEveryone/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def fetch_profile_picture_url(self, instance_id: str, data: ProfilePicture, instance_token: str):
        return self.client.post(
            f'chat/fetchProfilePictureUrl/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def get_base64_from_media_message(self, instance_id: str, data: MediaMessage, instance_token: str):
        return self.client.post(
            f'chat/getBase64FromMediaMessage/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_message(self, instance_id: str, data: UpdateMessage, instance_token: str):
        return self.client.post(
            f'chat/updateMessage/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def send_presence(self, instance_id: str, data: Presence, instance_token: str):
        return self.client.post(
            f'chat/sendPresence/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )
    
    def get_messages(
        self, 
        instance_id: str, 
        remote_jid: str, 
        instance_token: str, 
        message_id: Optional[str] = None,
        whatsapp_message_id: Optional[str] = None,
        from_me: Optional[bool] = None,
        message_type: Optional[str] = None,
        source: Optional[str] = None,
        timestamp_start: Optional[str] = None,
        timestamp_end: Optional[str] = None,
        page: int = 1, 
        offset: int = 50
    ):
        '''
        Obtém mensagens de um chat com filtros opcionais
        
        Args:
            timestamp_start: Data inicial no formato ISO (ex: "2025-01-16T00:00:00Z")
            timestamp_end: Data final no formato ISO (ex: "2025-01-16T23:59:59Z")
        '''
        where = {"key": {"remoteJid": remote_jid}}
        
        if message_id:
            where["id"] = message_id
        if whatsapp_message_id:
            where["key"]["id"] = whatsapp_message_id
        if from_me is not None:
            where["key"]["fromMe"] = from_me
        if message_type:
            where["messageType"] = message_type
        if source:
            where["source"] = source
        if timestamp_start or timestamp_end:
            where["messageTimestamp"] = {}
            if timestamp_start:
                where["messageTimestamp"]["gte"] = timestamp_start
            if timestamp_end:
                where["messageTimestamp"]["lte"] = timestamp_end
            
        payload = {
            "where": where,
            "page": page,
            "offset": offset,
        }
        
        return self.client.post(
            f'chat/findMessages/{instance_id}', 
            data=payload,
            instance_token=instance_token,
        )



================================================
FILE: evolutionapi/services/group.py
================================================
from typing import Optional
from ..models.group import *

class GroupService:
    def __init__(self, client):
        self.client = client

    def create_group(self, instance_id: str, data: CreateGroup, instance_token: str):
        return self.client.post(
            f'group/create/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_group_picture(self, instance_id: str, group_jid: str, data: GroupPicture, instance_token: str):
        return self.client.post(
            f'group/updateGroupPicture/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_group_subject(self, instance_id: str, group_jid: str, data: GroupSubject, instance_token: str):
        return self.client.post(
            f'group/updateGroupSubject/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_group_description(self, instance_id: str, group_jid: str, data: GroupDescription, instance_token: str):
        return self.client.post(
            f'group/updateGroupDescription/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def get_invite_code(self, instance_id: str, group_jid: str, instance_token: str):
        return self.client.get(
            f'group/inviteCode/{instance_id}?groupJid={group_jid}',
            instance_token=instance_token
        )

    def revoke_invite_code(self, instance_id: str, group_jid: str, instance_token: str):
        return self.client.post(
            f'group/revokeInviteCode/{instance_id}?groupJid={group_jid}',
            instance_token=instance_token
        )

    def send_invite(self, instance_id: str, data: GroupInvite, instance_token: str):
        return self.client.post(
            f'group/sendInvite/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def get_invite_info(self, instance_id: str, invite_code: str, instance_token: str):
        return self.client.get(
            f'group/inviteInfo/{instance_id}?inviteCode={invite_code}',
            instance_token=instance_token
        )

    def get_group_info(self, instance_id: str, group_jid: str, instance_token: str):
        return self.client.get(
            f'group/findGroupInfos/{instance_id}?groupJid={group_jid}',
            instance_token=instance_token
        )

    def fetch_all_groups(self, instance_id: str, instance_token: str, get_participants: bool = False):
        url = f'group/fetchAllGroups/{instance_id}?getParticipants={str(get_participants).lower()}'
        return self.client.get(
            url,
            instance_token=instance_token
        )

    def get_participants(self, instance_id: str, group_jid: str, instance_token: str):
        return self.client.get(
            f'group/participants/{instance_id}?groupJid={group_jid}',
            instance_token=instance_token
        )

    def update_participant(self, instance_id: str, group_jid: str, data: UpdateParticipant, instance_token: str):
        return self.client.post(
            f'group/updateParticipant/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_setting(self, instance_id: str, group_jid: str, data: UpdateSetting, instance_token: str):
        return self.client.post(
            f'group/updateSetting/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def toggle_ephemeral(self, instance_id: str, group_jid: str, data: ToggleEphemeral, instance_token: str):
        return self.client.post(
            f'group/toggleEphemeral/{instance_id}?groupJid={group_jid}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def leave_group(self, instance_id: str, group_jid: str, instance_token: str):
        return self.client.delete(
            f'group/leaveGroup/{instance_id}?groupJid={group_jid}',
            instance_token=instance_token
        )


================================================
FILE: evolutionapi/services/instance.py
================================================
class InstanceService:
    def __init__(self, client):
        self.client = client

    def fetch_instances(self):
        return self.client.get('instance/fetchInstances')

    def create_instance(self, config):
        return self.client.post('instance/create', data=config.__dict__)


================================================
FILE: evolutionapi/services/instance_operations.py
================================================
from ..models.presence import PresenceStatus, PresenceConfig

class InstanceOperationsService:
    def __init__(self, client):
        self.client = client

    def connect(self, instance_id: str, instance_token: str):
        return self.client.get(f'instance/connect/{instance_id}', instance_token)

    def restart(self, instance_id: str, instance_token: str):
        return self.client.post(f'instance/restart/{instance_id}', instance_token=instance_token)

    def set_presence(self, instance_id: str, presence: PresenceStatus, instance_token: str):
        config = PresenceConfig(presence)
        return self.client.post(
            f'instance/setPresence/{instance_id}',
            data=config.__dict__,
            instance_token=instance_token
        )

    def get_connection_state(self, instance_id: str, instance_token: str):
        return self.client.get(f'instance/connectionState/{instance_id}', instance_token)

    def logout(self, instance_id: str, instance_token: str):
        return self.client.delete(f'instance/logout/{instance_id}', instance_token)

    def delete(self, instance_id: str, instance_token: str):
        return self.client.delete(f'instance/delete/{instance_id}', instance_token)



================================================
FILE: evolutionapi/services/label.py
================================================
from typing import Union, BinaryIO
from ..models.label import *

class LabelService:
    def __init__(self, client):
        self.client = client

    def find_labels(self, instance_id: str, instance_token: str):
        return self.client.get(
            f'label/findLabels/{instance_id}',
            instance_token=instance_token
        )

    def handle_label(self, instance_id: str, data: HandleLabel, instance_token: str):
        return self.client.post(
            f'label/handleLabel/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )


================================================
FILE: evolutionapi/services/message.py
================================================
from typing import Union, BinaryIO
from ..models.message import *
from requests_toolbelt import MultipartEncoder
import mimetypes
import requests

class MessageService:
    def __init__(self, client):
        self.client = client

    def send_text(self, instance_id: str, message: TextMessage, instance_token: str):
        # Preparar os dados como JSON
        data = {
            'number': message.number,
            'text': message.text
        }
        
        if hasattr(message, 'delay') and message.delay is not None:
            data['delay'] = message.delay
        
        # Usar o método post do cliente que já trata JSON corretamente
        return self.client.post(
            f'message/sendText/{instance_id}',
            data=data,
            instance_token=instance_token
        )

    def send_media(self, instance_id: str, message: MediaMessage, instance_token: str, file: Union[BinaryIO, str] = None):
        # Preparar os dados do formulário
        fields = {
            'number': (None, message.number, 'text/plain'),
            'mediatype': (None, message.mediatype, 'text/plain'),
            'mimetype': (None, message.mimetype, 'text/plain'),
            'caption': (None, message.caption, 'text/plain'),
            'fileName': (None, message.fileName, 'text/plain'),
        }
        
        # Adicionar delay apenas se existir
        if hasattr(message, 'delay') and message.delay is not None:
            fields['delay'] = (None, str(message.delay), 'text/plain; type=number')
        
        # Adicionar o arquivo se fornecido
        if file:
            if isinstance(file, str):
                mime_type = mimetypes.guess_type(file)[0] or 'application/octet-stream'
                fields['file'] = ('file', open(file, 'rb'), mime_type)
            else:
                fields['file'] = ('file', file, 'application/octet-stream')
        
        # Criar o multipart encoder
        multipart = MultipartEncoder(fields=fields)
        
        # Preparar os headers
        headers = self.client._get_headers(instance_token)
        headers['Content-Type'] = multipart.content_type
        
        # Fazer a requisição diretamente
        url = f'{self.client.base_url}/message/sendMedia/{instance_id}'
        response = requests.post(
            url,
            headers=headers,
            data=multipart
        )
        
        return response.json()

    def send_ptv(self, instance_id: str, message: dict, instance_token: str, file: Union[BinaryIO, str] = None):
        fields = {}
        
        # Adiciona todos os campos do message como text/plain
        for key, value in message.items():
            if key == 'delay' and value is not None:
                fields[key] = (None, str(value), 'text/plain; type=number')
            else:
                fields[key] = (None, str(value), 'text/plain')
        
        if file:
            if isinstance(file, str):
                mime_type = mimetypes.guess_type(file)[0] or 'application/octet-stream'
                fields['file'] = ('file', open(file, 'rb'), mime_type)
            else:
                fields['file'] = ('file', file, 'application/octet-stream')
        
        multipart = MultipartEncoder(fields=fields)
        headers = self.client._get_headers(instance_token)
        headers['Content-Type'] = multipart.content_type
        
        url = f'{self.client.base_url}/message/sendPtv/{instance_id}'
        response = requests.post(url, headers=headers, data=multipart)
        return response.json()

    def send_whatsapp_audio(self, instance_id: str, message: dict, instance_token: str, file: Union[BinaryIO, str] = None):
        fields = {}
        
        # Adiciona todos os campos do message como text/plain
        for key, value in message.items():
            if key == 'delay' and value is not None:
                fields[key] = (None, str(value), 'text/plain; type=number')
            else:
                fields[key] = (None, str(value), 'text/plain')
        
        if file:
            if isinstance(file, str):
                mime_type = mimetypes.guess_type(file)[0] or 'application/octet-stream'
                fields['file'] = ('file', open(file, 'rb'), mime_type)
            else:
                fields['file'] = ('file', file, 'application/octet-stream')
        
        multipart = MultipartEncoder(fields=fields)
        headers = self.client._get_headers(instance_token)
        headers['Content-Type'] = multipart.content_type
        
        url = f'{self.client.base_url}/message/sendWhatsAppAudio/{instance_id}'
        response = requests.post(url, headers=headers, data=multipart)
        return response.json()

    def send_status(self, instance_id: str, message: StatusMessage, instance_token: str):
        return self.client.post(
            f'message/sendStatus/{instance_id}',
            data=message.__dict__,
            instance_token=instance_token
        )

    def send_sticker(self, instance_id: str, message: dict, instance_token: str, file: Union[BinaryIO, str] = None):
        fields = {}
        
        # Adiciona todos os campos do message como text/plain
        for key, value in message.items():
            if key == 'delay' and value is not None:
                fields[key] = (None, str(value), 'text/plain; type=number')
            else:
                fields[key] = (None, str(value), 'text/plain')
        
        if file:
            if isinstance(file, str):
                mime_type = mimetypes.guess_type(file)[0] or 'application/octet-stream'
                fields['file'] = ('file', open(file, 'rb'), mime_type)
            else:
                fields['file'] = ('file', file, 'application/octet-stream')
        
        multipart = MultipartEncoder(fields=fields)
        headers = self.client._get_headers(instance_token)
        headers['Content-Type'] = multipart.content_type
        
        url = f'{self.client.base_url}/message/sendSticker/{instance_id}'
        response = requests.post(url, headers=headers, data=multipart)
        return response.json()

    def send_location(self, instance_id: str, message: LocationMessage, instance_token: str):
        data = message.__dict__.copy()
        if 'delay' in data and data['delay'] is not None:
            data['delay'] = int(data['delay'])
        
        return self.client.post(
            f'message/sendLocation/{instance_id}',
            data=data,
            instance_token=instance_token
        )

    def send_contact(self, instance_id: str, message: ContactMessage, instance_token: str):
        return self.client.post(
            f'message/sendContact/{instance_id}',
            data=message.__dict__,
            instance_token=instance_token
        )

    def send_reaction(self, instance_id: str, message: ReactionMessage, instance_token: str):
        return self.client.post(
            f'message/sendReaction/{instance_id}',
            data=message.__dict__,
            instance_token=instance_token
        )

    def send_poll(self, instance_id: str, message: PollMessage, instance_token: str):
        data = message.__dict__.copy()
        if 'delay' in data and data['delay'] is not None:
            data['delay'] = int(data['delay'])
        
        return self.client.post(
            f'message/sendPoll/{instance_id}',
            data=data,
            instance_token=instance_token
        )

    def send_list(self, instance_id: str, message: ListMessage, instance_token: str):
        data = message.__dict__.copy()
        if 'delay' in data and data['delay'] is not None:
            data['delay'] = int(data['delay'])
        
        return self.client.post(
            f'message/sendList/{instance_id}',
            data=data,
            instance_token=instance_token
        )

    def send_buttons(self, instance_id: str, message: ButtonMessage, instance_token: str):
        data = message.__dict__.copy()
        if 'delay' in data and data['delay'] is not None:
            data['delay'] = int(data['delay'])
        
        return self.client.post(
            f'message/sendButtons/{instance_id}',
            data=data,
            instance_token=instance_token
        )


================================================
FILE: evolutionapi/services/profile.py
================================================
from typing import Union, BinaryIO
from ..models.profile import *

class ProfileService:
    def __init__(self, client):
        self.client = client

    def fetch_business_profile(self, instance_id: str, data: FetchProfile, instance_token: str):
        return self.client.post(
            f'chat/fetchBusinessProfile/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def fetch_profile(self, instance_id: str, data: FetchProfile, instance_token: str):
        return self.client.post(
            f'chat/fetchProfile/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_profile_name(self, instance_id: str, data: ProfileName, instance_token: str):
        return self.client.post(
            f'chat/updateProfileName/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_profile_status(self, instance_id: str, data: ProfileStatus, instance_token: str):
        return self.client.post(
            f'chat/updateProfileStatus/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def update_profile_picture(self, instance_id: str, data: ProfilePicture, instance_token: str):
        return self.client.post(
            f'chat/updateProfilePicture/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )

    def remove_profile_picture(self, instance_id: str, instance_token: str):
        return self.client.delete(
            f'chat/removeProfilePicture/{instance_id}',
            instance_token=instance_token
        )
        
    def fetch_privacy_settings(self, instance_id: str, instance_token: str):
        return self.client.get(
            f'chat/fetchPrivacySettings/{instance_id}',
            instance_token=instance_token
        )

    def update_privacy_settings(self, instance_id: str, data: PrivacySettings, instance_token: str):
        return self.client.post(
            f'chat/updatePrivacySettings/{instance_id}',
            data=data.__dict__,
            instance_token=instance_token
        )


================================================
FILE: evolutionapi/services/websocket.py
================================================
import socketio
from typing import Callable, Dict, Any
import logging
import time
from typing import Optional
from ..models.websocket import WebSocketConfig, WebSocketInfo

class WebSocketService:
    def __init__(self, client):
        self.client = client

    def set_websocket(self, instance_id: str, config: WebSocketConfig, instance_token: str):
        """
        Configure WebSocket settings for an instance
        
        Args:
            instance_id (str): The instance ID
            config (WebSocketConfig): The WebSocket configuration
            instance_token (str): The instance token
            
        Returns:
            dict: The response from the API
        """
        return self.client.post(
            f'websocket/set/{instance_id}',
            data=config.__dict__,
            instance_token=instance_token
        )

    def find_websocket(self, instance_id: str, instance_token: str) -> WebSocketInfo:
        """
        Get WebSocket settings for an instance
        
        Args:
            instance_id (str): The instance ID
            instance_token (str): The instance token
            
        Returns:
            WebSocketInfo: The WebSocket information
        """
        response = self.client.get(
            f'websocket/find/{instance_id}',
            instance_token=instance_token
        )
        return WebSocketInfo(**response)

class WebSocketManager:
    def __init__(self, base_url: str, instance_id: str, api_token: str, max_retries: int = 5, retry_delay: float = 1.0):
        """
        Initialize the WebSocket manager
        
        Args:
            base_url (str): Base URL of the API
            instance_id (str): Instance ID
            api_token (str): API authentication token
            max_retries (int): Maximum number of reconnection attempts
            retry_delay (float): Initial delay between attempts in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.instance_id = instance_id
        self.api_token = api_token
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_count = 0
        self.should_reconnect = True
        
        # Socket.IO configuration
        self.sio = socketio.Client(
            ssl_verify=False,  # For local development
            logger=False,
            engineio_logger=False,
            request_timeout=30
        )
        
        # Configure class logger to INFO
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # Dictionary to store registered handlers
        self._handlers = {}
        
        # Configure event handlers
        self.sio.on('connect', self._on_connect)
        self.sio.on('disconnect', self._on_disconnect)
        self.sio.on('error', self._on_error)
        
        # Register global handler in instance-specific namespace
        self.sio.on('*', self._handle_event, namespace=f'/{self.instance_id}')
    
    def _on_connect(self):
        """Handler for connection event"""
        self.logger.info("Socket.IO connected")
        self.retry_count = 0  # Reset retry counter after successful connection
    
    def _on_disconnect(self):
        """Handler for disconnection event"""
        self.logger.warning(f"Socket.IO disconnected. Attempt {self.retry_count + 1}/{self.max_retries}")
        if self.should_reconnect and self.retry_count < self.max_retries:
            self._attempt_reconnect()
        else:
            self.logger.error("Maximum number of reconnection attempts reached")
    
    def _on_error(self, error):
        """Handler for error events"""
        self.logger.error(f"Socket.IO error: {str(error)}", exc_info=True)
    
    def _attempt_reconnect(self):
        """Attempt to reconnect with exponential backoff"""
        try:
            delay = self.retry_delay * (2 ** self.retry_count)  # Exponential backoff
            self.logger.info(f"Attempting to reconnect in {delay:.2f} seconds...")
            time.sleep(delay)
            self.connect()
            self.retry_count += 1
        except Exception as e:
            self.logger.error(f"Error during reconnection attempt: {str(e)}", exc_info=True)
            if self.retry_count < self.max_retries:
                self._attempt_reconnect()
            else:
                self.logger.error("All reconnection attempts failed")
    
    def _handle_event(self, event, *args):
        """Global handler for all events"""
        # Only process registered events
        if event in self._handlers:
            self.logger.debug(f"Event received in namespace /{self.instance_id}: {event}")
            self.logger.debug(f"Event data: {args}")
            
            try:
                # Extract event data
                raw_data = args[0] if args else {}
                
                # Ensure we're passing the correct object to the callback
                if isinstance(raw_data, dict):
                    self.logger.debug(f"Calling handler for {event} with data: {raw_data}")
                    self._handlers[event](raw_data)
                else:
                    self.logger.error(f"Invalid data received for event {event}: {raw_data}")
            except Exception as e:
                self.logger.error(f"Error processing event {event}: {str(e)}", exc_info=True)
    
    def connect(self):
        """Connect to Socket.IO server"""
        try:
            # Connect only to instance namespace with authentication header
            self.sio.connect(
                f"{self.base_url}?apikey={self.api_token}",
                transports=['websocket'],
                namespaces=[f'/{self.instance_id}'],
                wait_timeout=30
            )
            
            # Join instance-specific room
            self.sio.emit('subscribe', {'instance': self.instance_id}, namespace=f'/{self.instance_id}')
            
        except Exception as e:
            self.logger.error(f"Error connecting to Socket.IO: {str(e)}", exc_info=True)
            raise
    
    def disconnect(self):
        """Disconnect from Socket.IO server"""
        self.should_reconnect = False  # Prevent reconnection attempts
        if self.sio.connected:
            self.sio.disconnect()
    
    def on(self, event: str, callback: Callable):
        """
        Register a callback for a specific event
        
        Args:
            event (str): Event name
            callback (Callable): Function to be called when the event occurs
        """
        self._handlers[event] = callback

