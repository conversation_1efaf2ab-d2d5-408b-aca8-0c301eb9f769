from ....agent_neo4j import AgentNeo4j
from agents.tool import function_tool 

neo4j = AgentNeo4j()    

@function_tool
async def compra_produto_adicionar(compra_idx: str, produto_idx: str, quantidade: float, preco: float) -> dict:
    """
    Adiciona um produto a uma compra existente, ou ao seu estoque,  pesquisando pelo produto usando a função consultar_produto_entrada().
    
    Frases que que acionam o uso desta função:
    - Adicionar produto ao compra
    - Adicionar produto ao estoque
    - incluir produto no estoque
    - Incluir produto na compra

    Esta função adiciona um único produto a uma compra já existente, atualizando o estoque
    do produto na revenda e criando o relacionamento com a compra.

    Parametros:
    compra_idx: str
        - idx da compra a ser atualizada.
        - Obrigatório. A função não deve ser executada sem esta informação.
        Caso você ainda não tenha acesso ao idx da compra, execute a função compra_nova() para criar uma nova compra ou obter o idx da ultima compra aberta, e passe o idx da compra retornada para esta função.

    produto_idx: str
        - idx do produto a ser adicionado à compra.
        - Obrigatório. A função não deve ser executada sem esta informação.
        - Quando o usuário informar o nome ou código do produto, use a função consultar_produto_entrada()
          para pesquisar por produtos, e passe o idx do produto encontrado para esta função.
        🚨 O idx do produto é um código numérico de 10 digitos. Exemplo: 2142361231
    
    quantidade: float
        - Quantidade do produto a ser adicionada à compra.
        - Obrigatório. A função não deve ser executada sem esta informação.
        
    preco: float
        - Preço unitário do produto.
        - Obrigatório. A função não deve ser executada sem esta informação.

    Retorna:
        dict: Um dicionário com a chave 'proCALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", "batom AND liquido AND matte") YIELD node AS p
RETURN p.nome, p.codigo, p.excluido
duto_adicionado' indicando sucesso (True/False).


    Após adicionar o produto, pergunte ao usuário se ele deseja adicionar um novo produto ou se deseja finalizar a compra, e aguarde a resposta, e em seguida tome uma das seguintes ações:
    1 - caso ele diga que quer adicionar um novo produto, pergunte o nome ou código e faça uma nova pesquisa usando a função consultar_produto_entrada()  
    2 - Caso ele informe que deseja finalizar, execute a função compra_finalizar()

    🚨🚨🚨 ATENÇÃO: NUNCA FINALIZE COMPRA SEM CONFIRMAR COM O USUÁRIO!
    """
    
    print("🚧🚧 ===== compra_produto_adicionar() =====")
    
    
    # Define os parâmetros  
    parametros = {
        "compra_idx": compra_idx,
        "produto_idx": produto_idx,
        "quantidade": quantidade,
        "preco": preco
    }

    query = """
    // Encontra a compra existente
    MATCH (c:Compra {idx: $compra_idx})<-[:COMPROU]-(r:Revenda)
    
    // Encontra o produto
    MATCH (pr:Produto {idx: $produto_idx})
    
    // Cria ou atualiza o ProdutoRevenda
    MERGE (r)-[:REVENDE]->(prv:ProdutoRevenda {produto_idx: $produto_idx})-[:INSTANCIA_DE]->(pr)
    ON CREATE SET
      prv.estoque = toInteger($quantidade),
      prv.preco_compra = toFloat($preco),
      prv.created_at = datetime()
    ON MATCH SET
      prv.estoque = prv.estoque + toInteger($quantidade),
      prv.updated_at = datetime()
    
    // Cria o relacionamento com a compra
    MERGE (c)-[rel:COMPROU_PRODUTO]->(prv)
    SET rel.quantidade = toInteger($quantidade),
        rel.preco_unitario = toFloat($preco)
    
    // Atualiza o total da compra
    WITH c, ($quantidade * $preco) AS valor_item
    SET c.total = COALESCE(c.total, 0) + valor_item
    
    RETURN true as sucesso
    """
        
        
    #valida o produto_idx (idx do produto)
    if not produto_idx.isdigit():
        return {"produto_adicionado": False, "erro": "idx do produto inválido"}    
        
        
    try:
        resultado = await neo4j.execute_write_query(query, parametros)
        # Verifica se o resultado tem dados e se a operação foi bem sucedida
        return {"produto_adicionado": bool(resultado and resultado[0].get("success", False))}
    except Exception as e:
        print(f"Erro ao adicionar produto à compra: {str(e)}")
        return {"produto_adicionado": False, "erro": str(e)}
      

if __name__ == "__main__":
    import asyncio
    
    async def testa_compra_produto_adicionar():
        compra_idx = "7718104383"
        produto_idx = "10221710"
        quantidade = 1
        preco = 100.00
        resultado = await compra_produto_adicionar(compra_idx, produto_idx, quantidade, preco)
        print("Resultado do teste:", resultado)
    
    asyncio.run(testa_compra_produto_adicionar())