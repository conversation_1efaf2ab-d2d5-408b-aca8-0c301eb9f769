#!/usr/bin/env python3
"""
Teste isolado da função cliente_consultar para diagnosticar problemas
"""

import asyncio
import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sys
sys.path.append('C:\\Users\\<USER>\\Documents\\GitHub\\gptalk\\server')

# Importar a função diretamente do módulo
from api.agent.agent_oficinatech import *
from api.agent.agent_logger import AgentLogger

logger = AgentLogger()

async def teste_cliente_consultar():
    """Teste isolado da função cliente_consultar"""
    logger.info("🧪 === TESTE ISOLADO DE cliente_consultar ===")
    
    try:
        # Parâmetros reais para teste
        negocio_idx = "4015743441"  # Use um negócio real do seu banco
        busca = ""  # Busca vazia para listar todos os clientes
        
        logger.info(f"📋 Parâmetros do teste:")
        logger.info(f"   - negocio_idx: {negocio_idx}")
        logger.info(f"   - busca: '{busca}'")
        
        logger.info("🔄 Iniciando chamada para cliente_consultar...")
        
        # Chamada direta da função
        resultado = await cliente_consultar(negocio_idx=negocio_idx, busca=busca)
        
        logger.info("✅ Chamada concluída!")
        logger.info(f"🎯 RESULTADO COMPLETO:")
        logger.info(f"   Tipo: {type(resultado)}")
        logger.info(f"   Conteúdo: {resultado}")
        
        # Verificar se tem success e data
        if isinstance(resultado, dict):
            if "success" in resultado:
                logger.info(f"📊 Success: {resultado['success']}")
            if "data" in resultado:
                data = resultado['data']
                logger.info(f"📦 Data (tipo: {type(data)}, tamanho: {len(data) if hasattr(data, '__len__') else 'N/A'})")
                if isinstance(data, list) and len(data) > 0:
                    logger.info(f"📝 Primeiro item: {data[0]}")
            if "error" in resultado:
                logger.info(f"❌ Error: {resultado['error']}")
        
        return resultado
        
    except Exception as e:
        logger.error(f"❌ ERRO CAPTURADO NO TESTE:")
        logger.error(f"   Tipo: {type(e)}")
        logger.error(f"   Mensagem: {str(e)}")
        logger.error(f"   Args: {e.args}")
        
        import traceback
        logger.error(f"📋 TRACEBACK COMPLETO:")
        logger.error(traceback.format_exc())
        
        return {"erro_teste": str(e)}

if __name__ == "__main__":
    print("Executando teste isolado de cliente_consultar...")
    resultado = asyncio.run(teste_cliente_consultar())
    print(f"\nRESULTADO FINAL: {resultado}")
