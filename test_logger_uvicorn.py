#!/usr/bin/env python3
"""
Teste para verificar se o AgentLogger funciona corretamente com Uvicorn
"""

from fastapi import FastAPI
from api.agent.agent_logger import AgentLogger

app = FastAPI()

# Cria instância do logger
logger = AgentLogger()

@app.get("/test-logger")
async def test_logger_endpoint():
    """Endpoint para testar o logger"""
    
    # Testa diferentes níveis de log
    logger.debug("🔍 TESTE DEBUG - deve aparecer no console")
    logger.info("ℹ️ TESTE INFO - deve aparecer no console")
    logger.warning("⚠️ TESTE WARNING - deve aparecer no console")
    logger.error("❌ TESTE ERROR - deve aparecer no console")
    
    # Força console logging
    logger.force_console_logging()
    logger.info("✅ TESTE APÓS FORCE_CONSOLE_LOGGING - deve aparecer no console")
    
    return {
        "status": "success",
        "message": "Logs enviados - verifique o console do Uvicorn",
        "timestamp": "2025-07-15"
    }

@app.get("/debug-logger")
async def debug_logger_endpoint():
    """Endpoint para debug do logger"""
    logger.debug_logging_levels()
    return {"status": "debug executado - verifique o console"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001)