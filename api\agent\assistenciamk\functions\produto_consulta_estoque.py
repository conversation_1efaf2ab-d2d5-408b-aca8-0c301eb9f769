from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
import inspect
import traceback

neo4j = AgentNeo4j()

@function_tool
async def produto_consulta_estoque(codigo: str, usuario_idx: str, negocio_idx: str):
    """
    Consulta o estoque de um produto na base de dados (Neo4j).
    
    ⚠️⚠️⚠️ ATENÇÃO IMPORTANTE: LEIA COM ATENÇÃO ANTES DE USAR ESTA FUNÇÃO ⚠️⚠️⚠️
    
    USO APROVADO:
    - Apenas durante o fluxo de VENDA de produtos
    - Para verificar a disponibilidade de itens antes de vender
    
    🚫 USO PROIBIDO:
    - Durante o fluxo de COMPRA (entrada de produtos no estoque)
    - Para validar estoque durante o cadastro de produtos
    - Em qualquer situação que não seja uma venda direta ao cliente
    
    REGRAS DE USO:
    1. Use APENAS durante o fluxo de venda
    2. NUNCA use durante o fluxo de compra
    3. Ignore a verificação de estoque se o contexto for de compra
    4. Em caso de dúvida, NÃO use esta função
    
    A função retornará automaticamente um valor alto de estoque se for chamada
    durante um fluxo de compra, para evitar bloqueios indevidos.
    """
    print("===== produto_consulta_estoque() =====")
    print("Código recebido:", codigo)
    print("Usuário IDX:", usuario_idx)
    print("Negócio IDX:", negocio_idx)
    
    # Verificação de contexto - Impede o uso durante o fluxo de compra
    stack = traceback.extract_stack()
    for frame in stack:
        if 'produto_compra' in str(frame):
            print("AVISO: Tentativa de verificar estoque durante fluxo de compra. Ignorando verificação de estoque.")
            # Retorna um valor padrão que não bloqueia o fluxo
            return {
                "status": "success", 
                "estoque": 999999,  # Valor alto para não bloquear
                "aviso": "Verificação de estoque ignorada durante fluxo de compra"
            }

    query = """
    // Encontra a Revenda relacionada à Pessoa e ao Negócio
    MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
    MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod:Produto {codigo: $codigo})
    RETURN pr.estoque as estoque, false as criado
    """
    params = {
        "codigo": codigo,
        "usuario_idx": usuario_idx,
        "negocio_idx": negocio_idx
    }

    try:
        result = await neo4j.execute_read_query(query, params)

        # Garante que result é uma lista de dicionários
        if isinstance(result, dict):
            result = [result]

        if result and len(result) > 0 and isinstance(result[0], dict):
            estoque = result[0].get("estoque", 0)
            criado = result[0].get("criado", False)
            if criado:
                print(f"Novo registro de produto criado com estoque: {estoque}")
            else:
                print(f"Estoque encontrado: {estoque}")
            return {"status": "success", "estoque": estoque, "novo_registro": criado}
        else:
            # Se não encontrar o produto, retorna estoque zero sem erro
            print("Produto não encontrado no estoque. Retornando estoque zero.")
            return {"status": "success", "estoque": 0, "produto_nao_encontrado": True}
            
    except Exception as e:
        print(f"Erro ao consultar estoque: {str(e)}")
        # Em caso de erro, retorna um valor que não bloqueie o fluxo
        return {"status": "success", "estoque": 999999, "erro": str(e), "aviso": "Continuando apesar do erro"}
    
    
#crie aqui um teste para a funcao acima, simulando a chamada dela com parametros validos e imprimindo o resultado
if __name__ == "__main__":
    import asyncio

    async def test():
        print("===== test() =====")
        codigo_teste = "10221710"  # Código de produto de teste
        usuario_idx_teste = "1122334455"  # IDX de usuário de teste
        negocio_idx_teste = "5544332211"  # IDX de negócio de teste

        resultado = await produto_consulta_estoque(codigo_teste, usuario_idx_teste, negocio_idx_teste)
        print("Resultado do teste:", resultado)

    asyncio.run(test())

    #Execução
