#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnóstico para problema de conexão MySQL
Erro: (4151, 'Access denied, this account is locked')
"""

import asyncio
import asyncmy
from dotenv import load_dotenv
import os
import socket
import subprocess
import sys

# Carrega as variáveis de ambiente
load_dotenv()

class MysqlDiagnostico:
    def __init__(self):
        # <PERSON>eg<PERSON> as credenciais do .env
        self.host = os.getenv('GPTALK_01_MYSQL_HOST')
        self.user = os.getenv('GPTALK_01_MYSQL_USER')
        self.password = os.getenv('GPTALK_01_MYSQL_PASSWORD')
        self.database = os.getenv('GPTALK_01_MYSQL_DATABASE')
        self.port = int(os.getenv('GPTALK_01_MYSQL_PORT', 3306))
        
        print("="*60)
        print("🔍 DIAGNÓSTICO DE CONEXÃO MYSQL")
        print("="*60)
        
    def mostrar_configuracao(self):
        """<PERSON>ra as configurações carregadas (mascarando a senha)"""
        print("\n📋 CONFIGURAÇÕES CARREGADAS:")
        print(f"   Host: {self.host}")
        print(f"   Porta: {self.port}")
        print(f"   Usuário: {self.user}")
        print(f"   Senha: {'*' * (len(self.password) if self.password else 0) if self.password else 'NÃO DEFINIDA'}")
        print(f"   Database: {self.database}")
        
        # Verificar se todas as variáveis estão definidas
        variaveis_faltando = []
        if not self.host:
            variaveis_faltando.append('GPTALK_01_MYSQL_HOST')
        if not self.user:
            variaveis_faltando.append('GPTALK_01_MYSQL_USER')
        if not self.password:
            variaveis_faltando.append('GPTALK_01_MYSQL_PASSWORD')
        if not self.database:
            variaveis_faltando.append('GPTALK_01_MYSQL_DATABASE')
            
        if variaveis_faltando:
            print(f"\n❌ VARIÁVEIS DE AMBIENTE FALTANDO: {', '.join(variaveis_faltando)}")
            return False
        else:
            print("✅ Todas as variáveis de ambiente estão definidas")
            return True
    
    async def testar_conectividade_rede(self):
        """Testa se consegue conectar na porta do MySQL"""
        print(f"\n🌐 TESTANDO CONECTIVIDADE DE REDE para {self.host}:{self.port}")
        try:
            # Teste de socket para ver se a porta está aberta
            sock = socket.create_connection((self.host, self.port), timeout=10)
            sock.close()
            print("✅ Porta MySQL está acessível")
            return True
        except socket.timeout:
            print("❌ TIMEOUT - Servidor não responde na porta MySQL")
            return False
        except socket.gaierror as e:
            print(f"❌ ERRO DE DNS - Não conseguiu resolver o hostname: {e}")
            return False
        except ConnectionRefusedError:
            print("❌ CONEXÃO RECUSADA - Porta fechada ou serviço inativo")
            return False
        except Exception as e:
            print(f"❌ ERRO DESCONHECIDO na conectividade: {e}")
            return False
    
    async def testar_conexao_mysql(self):
        """Testa a conexão MySQL com diferentes variações"""
        print(f"\n🔗 TESTANDO CONEXÃO MYSQL")
        
        # Teste 1: Conexão normal
        print("\n1️⃣ Testando conexão normal...")
        try:
            conn = await asyncmy.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            await conn.ensure_closed()
            print("✅ Conexão normal SUCESSO")
            return True
        except asyncmy.errors.OperationalError as e:
            erro_code, erro_msg = e.args
            print(f"❌ ERRO OPERACIONAL: ({erro_code}) {erro_msg}")
            
            # Analisar tipos específicos de erro
            if erro_code == 4151:
                print("   📍 CONTA BLOQUEADA - Este é o erro principal!")
                print("   💡 Possíveis causas:")
                print("      - Muitas tentativas de login falharam")
                print("      - Política de segurança do servidor MySQL")
                print("      - Conta desabilitada pelo administrador")
                print("      - Diferença de configuração entre local e servidor")
            elif erro_code == 1045:
                print("   📍 ACESSO NEGADO - Credenciais incorretas")
            elif erro_code == 2003:
                print("   📍 SERVIDOR INACESSÍVEL")
            
            return False
        except Exception as e:
            print(f"❌ ERRO DESCONHECIDO: {e}")
            return False
    
    async def testar_sem_database(self):
        """Testa conexão sem especificar database"""
        print("\n2️⃣ Testando conexão sem database específico...")
        try:
            conn = await asyncmy.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                port=self.port
            )
            await conn.ensure_closed()
            print("✅ Conexão sem database SUCESSO")
            print("   💡 O problema pode estar relacionado ao database específico")
            return True
        except asyncmy.errors.OperationalError as e:
            erro_code, erro_msg = e.args
            print(f"❌ ERRO: ({erro_code}) {erro_msg}")
            if erro_code == 4151:
                print("   📍 CONTA AINDA BLOQUEADA mesmo sem database")
            return False
        except Exception as e:
            print(f"❌ ERRO: {e}")
            return False
    
    def verificar_diferenca_ambiente(self):
        """Sugere verificações específicas para diferenças servidor/local"""
        print("\n🔄 DIFERENÇAS SERVIDOR vs LOCAL:")
        print("   📍 Verificações recomendadas:")
        print("   1. IP do servidor pode estar bloqueado no MySQL")
        print("   2. Configurações de firewall diferentes")
        print("   3. Versões diferentes do MySQL")
        print("   4. Políticas de segurança mais restritivas no servidor")
        print("   5. Limites de conexão diferentes")
        print("   6. Configurações de rede (bind-address)")
        
        print("\n🛠️  COMANDOS PARA VERIFICAR NO SERVIDOR MySQL:")
        print("   SQL para verificar conta bloqueada:")
        print("   SELECT User, Host, account_locked FROM mysql.user WHERE User = '{0}';".format(self.user))
        print("   ")
        print("   SQL para desbloquear conta (se necessário):")
        print("   ALTER USER '{0}'@'%' ACCOUNT UNLOCK;".format(self.user))
        print("   FLUSH PRIVILEGES;")
        print("   ")
        print("   SQL para verificar tentativas de login:")
        print("   SHOW STATUS LIKE 'Connection_errors%';")
        
    def verificar_sistema_servidor(self):
        """Coleta informações do sistema servidor"""
        print("\n💻 INFORMAÇÕES DO AMBIENTE SERVIDOR:")
        try:
            # Informações básicas do sistema
            import platform
            print(f"   Sistema: {platform.system()} {platform.release()}")
            print(f"   Python: {sys.version}")
            
            # Verificar se é ambiente local ou servidor
            hostname = socket.gethostname()
            print(f"   Hostname: {hostname}")
            
            # Tentar identificar se está em container/servidor
            if os.path.exists('/.dockerenv'):
                print("   🐳 Detectado ambiente Docker")
            elif 'server' in hostname.lower() or 'prod' in hostname.lower():
                print("   🖥️  Detectado ambiente servidor")
            else:
                print("   💻 Detectado ambiente local/desenvolvimento")
                
        except Exception as e:
            print(f"   ❌ Erro ao coletar informações: {e}")
    
    async def executar_diagnostico_completo(self):
        """Executa todos os testes de diagnóstico"""
        print("🚀 INICIANDO DIAGNÓSTICO COMPLETO...\n")
        
        # 1. Verificar configurações
        if not self.mostrar_configuracao():
            print("\n❌ DIAGNÓSTICO INTERROMPIDO - Configurações inválidas")
            return
        
        # 2. Informações do sistema
        self.verificar_sistema_servidor()
        
        # 3. Teste de conectividade de rede
        rede_ok = await self.testar_conectividade_rede()
        
        if not rede_ok:
            print("\n❌ DIAGNÓSTICO INTERROMPIDO - Problemas de rede")
            return
        
        # 4. Testes de conexão MySQL
        conexao_ok = await self.testar_conexao_mysql()
        
        if not conexao_ok:
            # Testar sem database
            await self.testar_sem_database()
            
            # Mostrar sugestões
            self.verificar_diferenca_ambiente()
        
        print("\n" + "="*60)
        print("📋 RESUMO DO DIAGNÓSTICO")
        print("="*60)
        if conexao_ok:
            print("✅ Conexão MySQL funcionando normalmente")
        else:
            print("❌ Problema identificado: CONTA MYSQL BLOQUEADA")
            print("💡 PRÓXIMOS PASSOS:")
            print("   1. Contactar administrador do banco MySQL")
            print("   2. Verificar logs do MySQL no servidor")
            print("   3. Executar comandos SQL para desbloquear conta")
            print("   4. Verificar políticas de segurança do servidor")
            print("   5. Considerar criar uma nova conta MySQL")


async def main():
    """Função principal"""
    diagnostico = MysqlDiagnostico()
    await diagnostico.executar_diagnostico_completo()

if __name__ == "__main__":
    asyncio.run(main()) 