import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class EmailSender:
    GPALK_EMAIL_OUTGOING_SERVER = '**************'  # IP do servidor SMTP
    GPTALK_EMAIL_SMTP_PORT = 465  # Porta para SMTP com SSL
    GPTALK_01_MYSQL_USER = '<EMAIL>'
    GPTALK_EMAIL_DEFAULT_PASSWORD = 'suporte@sh2024'  # Substitua pela senha real

    def send_email(self, to_address, subject, body):
        try:
            message = MIMEMultipart()
            message['From'] = "<EMAIL>"
            message['To'] = "<EMAIL>"
            message['Subject'] = subject
            message.attach(MIMEText(body, 'plain'))

            server = smtplib.SMTP_SSL("**************", self.GPTALK_EMAIL_SMTP_PORT)
            server.login("<EMAIL>", "suporte@sh2024")
            result = server.send_message(message)

            server.quit()
            return "Email enviado com sucesso"

        except smtplib.SMTPException as e:
            print("SMTP error occurred: ", e)
            return "Ocorreu um erro de SMTP ao tentar enviar o email"
        except Exception as e:
            print("Ocorreu um erro ao tentar enviar o email: ", e)
            return "Ocorreu um erro ao tentar enviar o email"

email_sender = EmailSender()
result = email_sender.send_email('<EMAIL>', 'Teste de Envio de Email', 'Olá, este é um teste de envio de email via Python!')
print(result)
