from fastapi import APIRouter
from .agent_time import Time
router = APIRouter()
import requests


class GoogleOauth:
    
    def __init__(self):
        pass

    @staticmethod
    def get_google_auth_url(redirect_uri, client_id,scope=None,state=None):
            #client_id = "************-iohb0gd6s80fghf931ckpunhc51e96fc.apps.googleusercontent.com"
            #state = "12345"  # Um estado aleatório para proteger contra CSRF

            auth_url = (
                       "https://accounts.google.com/o/oauth2/v2/auth"
                       "?response_type=code"
                       f"&client_id={client_id}"
                       f"&redirect_uri={redirect_uri}"
                       f"&scope={scope}"
                       f"&state={state}"
             )

            return auth_url
    

    # Função para obter o token de acesso do Google
    def get_token(self,auth_code, client_id, client_secret, redirect_uri):
        token_url = "https://oauth2.googleapis.com/token"
        token_params = {
            "code": auth_code,
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code"
        }
        print("token_params:", token_params)
        response = requests.post(token_url, data=token_params)
        token_response = response.json()
        
        if "access_token" in token_response:
            return token_response["access_token"]
        else:
            print("Erro ao obter o token de acesso: " + token_response.get("error_description", "Nenhuma descrição do erro disponível."))
            return None




    # Função para processar o código de autenticação do Google e obter dados do usuário
    def get_user(self,code,redirect_uri,client_id,client_secret):
            #client_id = "************-iohb0gd6s80fghf931ckpunhc51e96fc.apps.googleusercontent.com"
            #client_secret = "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"
        
            access_token = self.get_token(code, client_id, client_secret, redirect_uri)
            if access_token:
                user_info_url = "https://www.googleapis.com/oauth2/v1/userinfo"
                user_info_params = {"access_token": access_token}
                user_info_response = requests.get(user_info_url, params=user_info_params)
                user_info = user_info_response.json()
        
                return user_info
        
            return None

    
@router.post("/get_auth_url")
def  get_auth_url(data: dict):
    redirect_uri = data["redirect_uri"]
    client_id = data["client_id"]
    scope = data.get("scope","email profile")
    state = data.get("state",Time.time_now_to_timestamp(only_int=True))

    return GoogleOauth.get_google_auth_url(redirect_uri,client_id,scope,state)  
     
@router.post("/get_user")
def  get_user(data: dict):
   
    redirect_uri = data["redirect_uri"]
    client_id = data["client_id"]
    client_secret = data["client_secret"]
    code = data["code"]

    go = GoogleOauth()
    return go.get_user(code=code,redirect_uri=redirect_uri,client_id=client_id,client_secret=client_secret)



if __name__ == "__main__":
    import asyncio


    async def main():
        
        dados = {
            "redirect_uri": "https://www.gptalk.com.br",
            "client_id": "************-iohb0gd6s80fghf931ckpunhc51e96fc.apps.googleusercontent.com",
            "client_secret": "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",
            "code": "4/0AX4XfWgqg2t8VJy7zJ5M8XzV0z9uJ4KZwz7Q8v2g0f0z6WJrZ8s5jzjvBxV5"
        }
        print("")
        print(get_user(dados))
        print("")
    asyncio.run(main())

