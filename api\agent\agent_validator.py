import json
import re
from lxml import etree
import tinycss2

class Validator:
    def __init__(self):
        pass

    @staticmethod
    def validate_agent_nick(nick:str):
        pattern = r'^[a-zA-Z0-9_.+-]+@gptalk$'
        return bool(re.match(pattern, nick))
    
    
    def validate_object_json(self, obj: dict, required: list):
        # Verifica quais chaves estão faltando ou estão vazias
        missing_or_empty_keys = [key for key in required if key not in obj or not obj[key]]
        
        # Se houver chaves faltando ou vazias, retorna uma mensagem de erro
        if missing_or_empty_keys:
            return {"STATUS": "F", "RESULT": missing_or_empty_keys}
        
        # Se todas as chaves estiverem presentes e não vazias, retorna True
        return {"STATUS": "S", "RESULT": True}
    
    @staticmethod
    
    def validate_agent_nick(nick: str):
      
          pattern = r'^@([a-zA-Z0-9.]+)$'
    
          match = re.match(pattern, nick)
    
          if match:
            return match.group(1)  # Retorna a parte após o '@'
    
          return None
      



    def is_valid_json(self, obj):
        if isinstance(obj, str):
            try:
                json.loads(obj)
                return True
            except ValueError:
                return False
        elif isinstance(obj, dict):
            try:
                json_str = json.dumps(obj)
                json.loads(json_str)
                return True
            except (TypeError, ValueError):
                return False
        else:
            return False

    def validate_html(self, codigo_html):

        tags = []  # Armazenar as tags encontradas
        html_tag_found = False  # Flag para verificar se a tag <html> está presente
        i = 0
        while i < len(codigo_html):
            if codigo_html[i] == '<':
                # Encontrar o fim da tag
                close = codigo_html.find('>', i + 1)
                if close == -1:
                    return False  # Tag não fechada corretamente
                tag = codigo_html[i + 1:close].strip()
                # Verificar se é uma tag de fechamento
                if tag.startswith('/'):
                    tag = tag[1:]  # Remove o '/'
                    if not tags or tags[-1] != tag:
                        return False  # Tag de fechamento sem abertura correspondente
                    tags.pop()
                    if tag == 'html' and not tags:
                        html_tag_found = True  # Verifica se a tag <html> foi fechada corretamente
                else:
                    # Ignora tags auto-fechadas
                    if not tag.endswith('/') and not tag.startswith('!'):
                        tags.append(tag.split()[0])  # Adiciona tag à lista
                        if tag.split()[0] == 'html':
                            html_tag_found = True  # Marca que a tag <html> foi encontrada
            i += 1
        return not tags and html_tag_found  # Retorna True se todas as tags foram fechadas e a tag <html> foi encontrada e fechada




    def validate_css(self, css_string: str):
        try:
            # Primeiro, usamos tinycss2 para analisar a estrutura básica
            rules = tinycss2.parse_stylesheet(css_string, skip_whitespace=True, skip_comments=True)
            
            # Em seguida, verificamos erros específicos
            for rule in rules:
                if rule.type == 'error':
                    print(f"Error: {rule.message} at line {rule.source_line}")
                    return False

            # Verificação adicional com regex para capturar erros comuns
            # Verificamos chaves não fechadas e outros problemas
            unclosed_brace_pattern = r'{[^}]*$'
            if re.search(unclosed_brace_pattern, css_string):
                print("Error: Unclosed brace found")
                return False

            invalid_quotes_pattern = r"[\'\"].*?[\'\"].*?[\'\"]"
            if re.search(invalid_quotes_pattern, css_string):
                print("Error: Mismatched quotes found")
                return False

            return True
        except Exception as e:
            print(f"An error occurred: {e}")
            return False



if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        vld = Validator()
        nick = vld.validate_agent_nick("/gptalk")
        if nick:
            print("nick valido!", nick)
        else:
            print("nick INVÁLIDO", nick)
    asyncio.run(main())