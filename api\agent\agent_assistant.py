from .agent_agent import Agent
from .agent_user import User
from .agent_message import Message
from .agent_llm import LLM
from .task.functions_maps import task_get_map, task_add_map, task_exclude_map, task_update_map
from .agent_functioncall import FunctionCall
import json


class Assistant:

    def __init__(self,agente=None,usuario:User=None):
        self.agente = agente if agente else Agent()
        self.usuario  = usuario if usuario else User()
        self.llm = LLM()
        self.llm_modelo =  self.llm.model_openai.gpt_4_o
        self.functionCall = FunctionCall()

        self.function_map = {
        "task_get": task_get_map,
        "task_add": task_add_map,
        "task_exclude": task_exclude_map,
        "task_update": task_update_map
        }


    def set_agent(self,agente:Agent):
        self.agente = agente
    
    def set_user(self,usuario:User):
        self.usuario = usuario

    
    async def get_system(self):
      return f"""
    Você é a {self.agente.NOME},  uma assistente pessoal criada pela IAPN para ser assistente pessoal de {self.usuario.NOME} , que possui   o USUARIO_ID = {self.usuario.ID}. . Responda suas perguntas e o auxilie a realizar todasas tarefas   possiveis com os recursos que você tem acesso, de forma objetiva, direta, sem enrolação e conversa desnecessária ou informações não solicitadas."
      """
    
        #Você é a {self.agente.NOME},  uma assistente pessoal criada pela IAPN para ser assistente pessoal de {self.usuario.NOME} , que possui   o USUARIO_ID = {self.usuario.ID}. . Responda suas perguntas e o auxilie a realizar todasas tarefas   possiveis com os recursos que você tem acesso, de forma sempre atenciosa,gentil, cordial e também humorada."

    async def chat(self,historico:list,
             llm_modelo:str,mensagem:dict, llm=None,conversa_id=0):

     
        if not llm:
            llm = self.llm

        llm_modelo  = self.llm_modelo if self.llm_modelo else llm_modelo 
        msgSistema = {"role":"system","content":self.get_system()}    
        #print("mensagem recebida", mensagem)
        mensagemCompleta = []
        mensagemCompleta.append(msgSistema)
        if historico:
            mensagemCompleta.extend(historico)
        mensagemCompleta.append(mensagem)
        #print("llm_modelo",llm_modelo)
        resposta =  await llm.run_model(
            message=mensagemCompleta,
            model=llm_modelo,
            functions = list(self.function_map.values())
            )
        
        if llm.FUNCAO_CHAMADA:
            funcao_nome = resposta
            resposta_funcao = await self.functionCall.call_chat_function(funcao_nome=funcao_nome,funcao_mapa=self.function_map.get(funcao_nome),funcao_parametros=llm.FUNCAO_PARAMETROS)
            #print("")
            #print("resposta da função", resposta_funcao,type(resposta_funcao))
            #print("")
            resposta_funcao_str = json.dumps(resposta_funcao)

            msg = Message()
            msg.USUARIO_ID = self.usuario.ID
            msg.CONVERSA_ID = conversa_id
            msg.RECEBIDO = json.dumps(llm.FUNCAO_PARAMETROS)
            msg.RECEBIDO_TKS = llm.TOKEN_PROMPT
            msg.ENVIADO = json.dumps(resposta_funcao)
            msg.ENVIADO_TKS = llm.TOKEN_RESPOSTA
            msg.TOTAL_TKS = llm.TOKEN_PROMPT + llm.TOKEN_RESPOSTA
            msg.FUNCAO_CHAMADA = 1
            #print("recebido_tks", msg.RECEBIDO_TKS)
            #print("enviado_tks", msg.ENVIADO_TKS)
            #print("total_tks", msg.TOTAL_TKS)
            
            result = await msg.add()
            #print("ID do novo registro salvo", result)

            
            mensagemFuncao = [{"role":"function","name":funcao_nome,"content":msg.ENVIADO}]
            #print("mensagemFuncao", mensagemFuncao)
            resposta =  await llm.run_model(
            message=mensagemFuncao,
            model=llm_modelo
            )
            #print("respota final da função",resposta)

    
        return resposta

if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    async def main():
        ags = Assistant()
        llm = LLM()
        historico = []
        llm_modelo = llm.model_openai.gpt_4_o
        mensagem = {'role':'user','content':"quais são as minhas tarefas de ontem e  hoje?"}
        conversa_id = 500
        
        result = await ags.chat(historico,llm_modelo=llm_modelo,mensagem=mensagem,conversa_id=0)
     
    asyncio.run(main())