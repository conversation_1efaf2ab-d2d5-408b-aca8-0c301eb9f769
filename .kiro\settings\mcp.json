{"mcpServers": {"aws-docs": {"command": "uvx", "args": ["awslabs.aws-documentation-mcp-server@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": []}, "filesystem": {"command": "uvx", "args": ["mcp-server-filesystem@latest", "--base-directory", "."], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["read_file", "list_directory"]}, "git": {"command": "uvx", "args": ["mcp-server-git@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["git_status", "git_log"]}}}