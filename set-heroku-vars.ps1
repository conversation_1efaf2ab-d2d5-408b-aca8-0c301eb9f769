# Script para configurar variáveis de ambiente no Heroku a partir do arquivo .env
$envFile = ".env"
$appName = "gptalk-server" # Substitua pelo nome real do seu app no Heroku

# Lê o arquivo .env e remove linhas vazias e comentários
$envVars = Get-Content $envFile | Where-Object { $_ -match '^[^#].*=' }

# Constrói o comando heroku config:set
$configSet = "heroku config:set "
foreach ($line in $envVars) {
    $line = $line.Trim()
    if ($line -match '^([^=]+)=(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        $configSet += "$key=$value "
    }
}

# Adiciona o nome do app
$configSet += "--app $appName"

# Executa o comando
Invoke-Expression $configSet 