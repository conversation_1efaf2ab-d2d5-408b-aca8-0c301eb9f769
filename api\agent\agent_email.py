import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.application import MIMEApplication
from fastapi import APIRouter
from .agent_llm import LLM
from .agent_secret import Secret
from dotenv import load_dotenv
import os
import tempfile
import requests
from urllib.parse import urlparse

load_dotenv()

router = APIRouter()

class Email:
    def __init__(self,user=None,model=None):
        secret= Secret()
        self.model = model
        self.llm = LLM(model=model)
        self.GPTALK_EMAIL_INCOMING_IMAP_PORT = secret.GPTALK_EMAIL_INCOMING_IMAP_PORT
        self.GPTALK_EMAIL_INCOMING_POP3_PORT = secret.GPTALK_EMAIL_INCOMING_POP3_PORT
        self.GPTALK_EMAIL_SMTP_PORT = secret.GPTALK_EMAIL_OUTGOING_SMTP_PORT
        self.GPTALK_01_MYSQL_USER = secret.GP<PERSON>LK_EMAIL_DEFAULT_USER
        self.GP<PERSON>LK_EMAIL_DEFAULT_PASSWORD = secret.GP<PERSON>L<PERSON>_EMAIL_DEFAULT_PASSWORD
        self.GP<PERSON>LK_EMAIL_INCOMING_SERVER = secret.GPTALK_EMAIL_INCOMING_SERVER
        self.GPALK_EMAIL_OUTGOING_SERVER = secret.GPTALK_EMAIL_OUTGOING_SERVER

    async def send(self, data: dict):
        #print("***** email/send *****")
        #print("data", data)
        app = data.get("app").upper()
        conta = data.get("conta").upper()
        data["remetente"] =  os.getenv(f"{app}_EMAIL_{conta}_USER")
        data["login"]= os.getenv(f"{app}_EMAIL_{conta}_USER")
        data["senha"] = os.getenv(f"{app}_EMAIL_{conta}_PASSWORD")
        #print("data atualizada", data)
      
        destinatarios = data.get("destinatario")
        conta =  data.get("conta").upper()
        assunto = data.get("assunto")
        texto = data.get("texto")
        html = data.get("html")
        arquivo = data.get("arquivo")
        remetente = data.get("remetente")
        login = data.get("login")
        senha = data.get("senha")
        #print("remetente",remetente)
        #print("senha",senha)
        
        if not isinstance(destinatarios, list):
            destinatarios = [destinatarios]
        
        resultados = []
        
        # Verificar se os campos obrigatórios estão presentes
        campos_obrigatorios = ["remetente", "senha", "assunto"]
        campos_faltantes = [campo for campo in campos_obrigatorios if not data.get(campo)]
        
        if campos_faltantes:
            return [{"status": "ERRO", "mensagem": f"Campos obrigatórios faltando: {', '.join(campos_faltantes)}"}]
        
        if not texto and not html:
            return [{"status": "ERRO", "mensagem": "É necessário fornecer texto ou HTML para o corpo do e-mail"}]
        
        for destinatario in destinatarios:
            message = MIMEMultipart()
            if texto:
                body_text = MIMEText(texto, 'plain') 
                message.attach(body_text)
            if html:
                body_html = MIMEText(html, 'html')
                message.attach(body_html)
            
            # Verificar e anexar o arquivo, se existir
            if arquivo:
                if isinstance(arquivo, str):
                    # Verifica se o arquivo é uma URL
                    parsed_url = urlparse(arquivo)
                    if parsed_url.scheme and parsed_url.netloc:
                        # É uma URL, baixa o arquivo
                        response = requests.get(arquivo)
                        nome_arquivo = os.path.basename(parsed_url.path) or "arquivo_anexo"
                        conteudo = response.content
                    else:
                        # É um texto, cria um arquivo temporário
                        nome_arquivo = "anexo.txt"
                        conteudo = arquivo.encode('utf-8')
                else:
                    # Assume que é um objeto de arquivo enviado pelo cliente
                    nome_arquivo = arquivo.filename
                    conteudo = arquivo.file.read()

                # Anexa o arquivo ao email
                part = MIMEApplication(conteudo, Name=nome_arquivo)
                part['Content-Disposition'] = f'attachment; filename="{nome_arquivo}"'
                message.attach(part)

            message['From'] = remetente
            message['To'] = destinatario.get("EMAIL") if isinstance(destinatario, dict) else destinatario
            message['Subject'] = assunto

            try:
                #print("remetente",remetente)
                #print("senha",senha)
                server = smtplib.SMTP_SSL("199.167.144.62", self.GPTALK_EMAIL_SMTP_PORT)
                server.login(remetente, senha)
                result = server.send_message(message)
                resultados.append({"destinatario": message['To'], "status": "SUCESSO", "mensagem": "Email enviado com sucesso"})
            except Exception as e:
                resultados.append({"destinatario": message['To'], "status": "ERRO", "mensagem": f"Erro ao enviar email: {str(e)}"})
            finally:
                server.quit()

        return resultados

@router.post("/send")
async def email_send(data: dict):
    #print("***** email/send *****")
    #print("data", data)
    email = Email()
    result = await email.send(data)
    return result

if __name__ == "__main__":
    import asyncio

    async def main():
        emails = [
            {
                "NOME": "Glayson Carlos",
                "EMAIL": "<EMAIL>",
            },
            {
                "NOME": "Carlos Silva",
                "EMAIL": "<EMAIL>",
            },
            {
                "NOME": "Contato IAPN",
                "EMAIL": "<EMAIL>",
            },  
        ]
            
        data = {
            "conta": "notificacoes",
            "app": "gptalk",
            "assunto": "Teste final de envio de email(só texto)",
            "texto": "Enviando o email \n O teste.",
            "destinatario": emails
        }
        result = await email_send(data)
        print("result",result)  

    asyncio.run(main())
