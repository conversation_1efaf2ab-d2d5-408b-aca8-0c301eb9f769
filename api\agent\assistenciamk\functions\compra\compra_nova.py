from ....agent_neo4j import AgentNeo4j
from ....util.functions.gera_id_unico import gera_id_unico
from agents.tool import function_tool 

neo4j = AgentNeo4j()    

@function_tool
async def compra_nova(usuario_idx:str,negocio_idx:str) -> dict:
    """
    inicia uma nova compra. Antes de iniciar uma nova compra, é necessário verificar se já existe uma compra aberta para o usuário. 
    Caso exista, deve-se perguntar ao usuário se deseja :
    1 - adicionar os itens a compra aberta existente,informando a data e hora da compra aberta a ele,
    ou
    2 - finalizar a compra aberta existente e iniciar uma nova.
   

    Caso ele informe que deseja adiconar a conta aberta existente (opção 1), deve-se executar a função compra_produto_adicionar() para adicionar os itens a compra existente.


    Caso o usuário informe que deseja iniciar uma nova compra, deve-se finalizar a compra aberta existente, antes de iniciar uma nova, executando a função compra_finalizar() e depois novamente executar esta função: compra_nova().





    """
    
    total_de_itens = 0

    #Verifica se ja tem alguma compra aberta, e caso sim, informa ao usuario, e pergunta se deseja adicionar os itens a compra aberta existente, ou concluir a compra aberta existente e iniciar uma nova.
    

    
    
        # Define os parâmetros
    parametros = {
        "usuario_idx": usuario_idx,
        "negocio_idx": negocio_idx,
    }

    query = """
    MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
    MATCH (r)-[:COMPROU]->(c:Compra)
    WHERE NOT EXISTS {
    MATCH (c)-[:TEM_STATUS]->(:Status {nome: "Concluido"})
    }
    RETURN c
    """  
    
    

    compra_existente = await neo4j.execute_read_query(query, parametros)
    
    if compra_existente and len(compra_existente) > 0:
        return {"compra_existente": True, 
                "compra_idx": compra_existente[0]["c"]["idx"],
                "data_hora": compra_existente[0]["c"]["data_hora"],
                "proximo_passo": "compra_produto_adicionar"
                }
    
    else:
        compra_idx = gera_id_unico()
        #criar uma nova compra , e executar a função compra_produto_adicionar() para adicionar os itens a compra. 

        # Gera um ID único para a nova compra
        from datetime import datetime
        
        # Query para criar uma nova compra
        query = """
        MERGE (p:Pessoa {idx: $usuario_idx})
        MERGE (p)-[:REALIZA_REVENDA]->(r:Revenda)
        MERGE (n:Negocio {idx: $negocio_idx})
        MERGE (r)-[:REFERENTE]->(n)
        
        // Cria a nova compra
        CREATE (c:Compra {
            idx: $compra_idx, 
            data_hora: datetime(), 
            status: 'Em andamento',
            total: 0.0
        })
        
        // Relaciona a compra com a revenda
        MERGE (r)-[:COMPROU]->(c)
        
        // Cria o status inicial da compra
        MERGE (s:Status {nome: 'Em andamento'})
        MERGE (c)-[:TEM_STATUS]->(s)
        
        RETURN c
        """
        
        # Executa a query para criar a compra
        resultado = await neo4j.execute_write_query(query, {
            "usuario_idx": usuario_idx,
            "negocio_idx": negocio_idx,
            "compra_idx": compra_idx
        })



        return {"compra_iniciada": True,
                "compra_idx": compra_idx,
                "proximo_passo": "compra_produto_adicionar()"
        }

    
if __name__ == "__main__":
    import asyncio

    async def testa_compra_nova():
        usuario_idx = "1122334455"
        negocio_idx = "5544332211"
        result = await compra_nova(usuario_idx, negocio_idx)
        print(result)

    asyncio.run(testa_compra_nova())    
    
    
