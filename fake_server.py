from http.server import BaseHTTPRequestHand<PERSON>, HTTPServer
import json

class Handler(BaseHTTPRequestHandler):
    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        print("=== HEADERS ===")
        for k, v in self.headers.items():
            print(f"{k}: {v}")
        print("\n=== BODY ===")
        print(post_data.decode('utf-8'))

        # Resposta simulando o FastAPI
        self.send_response(200)
        self.send_header("Content-type", "application/json")
        self.end_headers()
        self.wfile.write(b'{"success": true, "message": "oi, bom dia"}')

    def log_message(self, format, *args):
        pass  # silencia logs do servidor

if __name__ == "__main__":
    server = HTTPServer(("localhost", 8000), Handler)
    print("Servidor fake rodando em http://localhost:8000")
    server.serve_forever()
    