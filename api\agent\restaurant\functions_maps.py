from datetime import datetime
import json


#===================
get_meal_day_map = {
  "agent":"agent_restaurant",
  "class":"Restaurant",
  "name": "get_meal_day",
  "agent_direct_response": True,
  "description": "Use esta função para obter os pratos do dia.",
  "parameters":  {
        "type": "object",
        "properties": {
            "negocio_id": {
                "type": "integer",
                "description": "ID do negócio (restaurante) que deseja obter os pratos do dia."
            }
        },
        "required": ["negocio_id"]
    }

}