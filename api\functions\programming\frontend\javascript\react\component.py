from .....secrets import fetch_api_key_env
from openai import OpenAI
from .....llm.openai import  openai_chat
from .....llm.deepseek import deepseek_coder
import asyncio
import os
import shutil
#from api.functions.file import load_file
from .....file import load_file
from .....llm.deepseek import deepseek_coder  
import re
import subprocess
import os

#========================================
async def component_create(promptComp):
  
    #print("----- deepseek_component_create()-----")
  user_content ="Criar o componente  " + promptComp["name"] + ". " + promptComp["description"]

  messages = []

  template = """
O componente a ser criado deverá ter 3 scripts:

1- index - O componente em si, com todos os seus elementos e subelementos. O componente principal poderá utilizar e importar para dentro de si outros compoentes ou bibliotecas necessárias para o seu funcionamento. Receberá as propriedades , estilos e valores inicias passados por props, pelo arquivo gerenciador (2-page.js). O script tem que ter 'use client' na primeira linha .

2 - props - É o script de configuração. Onde estará definida as propriedades, estilos css e valores iniciais de elementos do componente principal.

3 - page  - esta é a pagina/componente orquestrador, que passará carregará os parametros definidos no arquivo de parametros e passará para o componente principal . Nela ficará também os gerenciadores de estado e eventos (onclick, onchange), etc. do componente principal.  O script tem que ter 'use client' na primeira linha .


REGRAS PARA DEFINIÇÃO DOS NOMES

o nome dos componentes deverá obedecer às seguintes normas:

Regra 1 - o nome do componente principal será informado por mim. E você deverá criar os nomes dos outros  2 componentes usando por base o nome que eu fornecer. Veja um exemplo:

Input001 - É o nome do componente principal . Como o objetivo deste componente é fornecer um input, o nome escolhido foi Input001 .

Regra 2 - O nome do componente do script deverá ser Page+Nome do Componente.  principal. No caso do exemplo atual, será PageInput001.

Regra 3 - O nome do objeto com as propriedadades a ser exportado pelo arquivo de propriedades deverá ser o  nome do componente principal, com a primeira letra em minusculo seguido da letra P maiscula . Como no exemplo atual o nome do componente principal é Input001, o nome do objeto a ser exportado será input001P .

RESUMO DO QUE DEVERÁ SER FEITO

criar 3 scripts
index - Conteudo principal do componente, com suas funcionalidades e layout.  Nome do componente: forencido pelo usuario.
props - Propriedades, estilos css, e configurações. Nele ficará as configuracoes do componente principal e suas definições de estilo css.
page - Script de controle , carrega as propriedades do script props e as envia por parametros para o  o componente principal. Aqui ficam os handles de eventos, cliques, mudanças e outras funções necessarias ao funcionamento do componente principal. Exemplos: handleClick(), handleChange(), handleBlue, handleFocus.
Contera tb os armazenadores de estado do componente principal.


Utilize estes padrões, normas e forma de seperar o codigo para criar este novo componente.
Se possivel, utilize componente da biblioteca Material Ui. Não sendo possivel, utilize de outras bibliotecas ou crie caso não exista.
o retorno deverá ser feito de forma tageada, como no exemplo abaixo:
<component>
<index>[codigo do scrip index]</index>
<props>[codigo do script props]</props>
<page>[codigo do script page]</page>
</component>
Não é necessário marcar onde inicia (```jsx) e onde termina (```) o jsx.
Deverá ser retornado apenas este objeto, sem nenhum comentario antes ou depois.


Segue abaixo 3 exemplos de componentes criados seguindo estas instruções e regras  e que você deve usar como modelos para criar os novos solicitados:

EXEMPLO 1

<componente>

<index>
'use client'
import React from "react";
import { TextField } from '@mui/material';
import InputAdornment from '@mui/material/InputAdornment';
import { useState , useEffect} from "react";

const Input001 = ({ textFieldP, initialValue, handleChange=null, handleFocus=null,handleBlur=null}) => {
  //  console.log("input001");
  //  console.log('textFieldP', textFieldP);
  //  console.log('textFieldP.value', textFieldP.value);
      const [currentValue, setCurrentValue] = useState(initialValue);
  //  console.log('currentValue', currentValue);

//============  useEffect  ============
  // Formate o valor inicial quando o componente é montado
  useEffect(() => {

    setCurrentValue(initialValue);

  }, [initialValue]); // A dependência de initialValue  signffica que sera atualizado sempre que o initialValue mudar

  //============ handleChange2 ============
    const handleChange2 = (value, type, name) => {
     // console.log("handleChange2");
    //  console.log("value",value);
      setCurrentValue(value); // Adicione esta linha para atualizar o estado
      if (handleChange) { // Verifique se a propriedade handleChange foi passada
          handleChange(value, type, name);
      }
  }

  const handleFocus2 = (value, type, name) => {
    //console.log("handleBlur2");
    //console.log("value",value);
    setCurrentValue(value); // Adicione esta linha para atualizar o estado
    if (handleFocus) { // Verifique se a propriedade handleChange foi passada
        handleFocus(value, type, name);
    }
  }

  const handleBlur2 = (value, type, name) => {
    //console.log("handleBlur2");
    //console.log("value",value);
    setCurrentValue(value); // Adicione esta linha para atualizar o estado
    if (handleBlur) { // Verifique se a propriedade handleChange foi passada
        handleBlur(value, type, name);
    }
  }

  return (
    <>
 <style jsx>{`
                .bordered-input .MuiInputLabel-outlined {
                    transform: translate(14px, -20px) scale(1) !important;
                }
                .bordered-input .MuiInputLabel-outlined.MuiInputLabel-shrink {
                    transform: translate(14px, -20px) scale(0.75) !important;
                }
                /* Adicione quaisquer outros estilos que você deseja sobrescrever */
            `}</style>

      <TextField
        className="bordered-input"
        name={textFieldP.name}
        value={currentValue}  // Use o estado local para definir o valor
        type={textFieldP.input.type }
        variant={textFieldP.variant}
        helperText={textFieldP.helperText.text}
        FormHelperTextProps={{ style: textFieldP.helperText.css }}
        placeholder={textFieldP.input.placeholder}
        disabled={textFieldP.disabled}
        InputLabelProps={{
          style: textFieldP.label.css,
          shrink: true,
         }}
        style={textFieldP.css}
        label={textFieldP.label.text}
        onChange={(e) => handleChange2(e.target.value, textFieldP.input.type, textFieldP.name)}
        onFocus={(e) =>  handleFocus2(e.target.value, textFieldP.input.type, textFieldP.name)}
        onBlur={(e) => handleBlur2(e.target.value,textFieldP.input.type, textFieldP.name)} // Call our new onBlur function
        inputProps={{
          maxLength: textFieldP.input.maxLength, // Defina o limite de caracteres aqui
          autoComplete: "off",
        }}
        InputProps={{
          maxLength: 10,
          readOnly: Boolean(textFieldP.readOnly),
          sx: {
            "& input": {
              ...textFieldP.input.css,
            },
          },
        }}
      />
    </>
  );
}

export default Input001;
</index>

<props>
const textFieldP = {
value: "Glayson Carlos da Silva",
id: "standard-basic",
variant: "outlined", //standard, filled, outlined
type: "input",
name: "gcs",
xs: 12,
sm: 12,
md: 12,
lg: 12,
css:{
width:"80%",
margin: "10px",

},

input: {

    type: "text",
    id: "001",
    //maxLength:40,
    css: {
       // ...cssInput.formikInput,
        width: "100%!important",

    },
    classes: "w-100",
    placeholder: "seu nome completo",
    format: "texto",
    disabled: false,
    placeholder: "seu nome completo",

},


label: {
    text: "Nome",
    css: {
     //   ...cssInput.formikInputLabel,
        marginBottom: "2px",
    },
    classes: {},

},
helperText: {
    text: "",
    css: {
        color: "white",
        backgroundColor: "red",
    }
},

};

export const input001P = {
textFieldP,

}
</props>

<page>
'use client'
import Input001 from "./index"
import {input001P}  from "./props"

export default function PageInput001() {

const handleBlur = (value,type,name) => {
console.log("blur");
console.log(value);
}

const handleChange = (value,type, name) => {
console.log("change 1");
console.log(value);
}

const handleFocus = (value,type, name) => {
console.log("focus");
console.log(value);
}


const handleClick = (id) => {
console.log("clicou");

}


input001P.handleChange = handleChange;
input001P.handleFocus = handleFocus;
input001P.handleBlur = handleBlur;
input001P.handleClick = handleClick;
input001P.initialValue = "Pedro Carlos";

return (
<>
<h1>Input001</h1>

<Input001 {...input001P} />
</>
)
}
</page>
<componente>

EXEMPLO 2

<componente>

<index>
'use client'
import React from "react";
import Button from '@mui/material/Button';

const Button001 = ({button, onClickButtonEvent}) => {
  
  return (
    <>
      <Button 
        style={{ 
          backgroundColor: button.css.background, // Fundo transparente
          color: button.css.color, // Cor do texto dourado
          border: button.css.border, // Bordas douradas
          width: button.css.width, // Largura total
          marginTop: button.css.marginTop, // Margem superior
          '&:hover': {
            backgroundColor: button.css.hover.background, // Fundo levemente dourado no hover
            border: button.css.hover.background, // Bordas mais escuras no hover
          }
        }}
        onClick = {onClickButtonEvent}
        disabled = {button.disabled}
      >
        {button.text  }
        
      </Button>
    
    </>
  );
};

export default Button001;
</index>

<props>
const button = {
    text: "Salvar dados",
    css: {
        background: "transparent",
        color: "gold",
        border: "2px solid gold",
        width: "100%",
        hover: {
            backgroundColor: "rgba(218, 165, 32, 0.1)",
            border: "2px solid goldenrod",
        }
    }
}

export const button001P = {
    button,
}
</props>

<page>
'use client'
import React from "react";
import Button001 from "./index";
import { button001P } from "./props";  

const PageButton001 = () => {
  
  return (
    <>
      <h1>Button001</h1>
      <Button001 {...button001P} />
    </>
  );
};

export default PageButton001;
</page>

</componente>


EXEMPLO 3

<componente>
<index>
'use client'
import React from "react";
import Grid from '@mui/material/Grid';
import Link from 'next/link';
import { Icon } from '@iconify/react';


const SocialNetwork001 = ({ container,  networks }) => {

  const icons = networks.map((network, index) => {
   
    return (
  
        <Grid item key={index} style={container.items.css}>
          {network.url.length > 0
          ?
   
          <Link href={network.url} target="_blank" underline="hover">
            {(container.showIcon )
             ? <Icon icon= {network.icon.name} style={network.icon.css}  />
             : null
            }
            {(container.showLabel && network.url.length > 0)
            ?<span>{network.icon.label}</span>
            : null  
            }
          </Link>
          :        
          <>
            {(container.showIcon )
             ? <Icon icon= {network.icon.name} style={network.icon.css}  />
             : null
            }
            {(container.showLabel && network.url.length > 0)
            ?<span>{network.icon.label}</span>
            : null  
            }
          </>
          }
        </Grid>
      
    );
  });


  return (
    <>
      <Grid container
        style={container.css}
        spacing={container.spacing}
        direction={container.direction}
      >
        {icons}
      </Grid>
    </>
  );
}

export default SocialNetwork001;
</index>
<props>
const container = {
    css: {
      display: 'flex',
      justifyContent: 'space-evenly',
      color: 'black',
      backgroundColor: 'black',
  
  
    },

    items : {
        css: {
          textAlign: 'center',
        }
      },
      
    spacing: 1,
    direction: 'row',
    showIcon: true,
    showLabel: false,
  
  
  };

//------    x   ----------
  const networks = [
    {
      name: 'site',
       url: '',
      id: "site",
      disabled: false,
      icon: {
        name: 'uil:web-grid-alt',
        label: 'facebook',
        url: '',
        css: {
          color: 'gray',
          display: 'block',
          fontSize: '2rem',
          padding: '0 0.0rem',
        }
      },
    
    },
    {
      name: 'instagram',
      url: 'https://www.instagram.com/gptalk.iapn/',
      id: "",
      icon: {
        name: 'bi:instagram',
        label: 'instagram',
        url: '',
        css: {
          color: 'gold',
          display: 'block',
          fontSize: '2rem',
          padding: '0 0.0rem',
        
        }
      },
    },
  

  
    {
      name: 'youtube',
      url: '',
      id: "",
      icon: {
        name: 'teenyicons:youtube-outline',
        label: 'youtube',
        url: '',
        css: {
          color: 'gray',
          display: 'block',
          fontSize: '2rem',
          padding: '0 0.0rem',
        }
      },
    },
  
  
    {
      name: 'Suporte',
      url: '',
      id: "suporte",
      icon: {
        name: 'ic:outline-contact-support',
        label: 'suporte',
        url: '',
        css: {
          color: 'gray',
          display: 'block',
          fontSize: '2rem',
          padding: '0 0.0rem',
        }
      },
    },
  
   
  
  ];


  export const socialNetwork001P = {
 
 container,
 networks,


};
  
</props>
<page>
'use client'
import Socialnetwork001 from "./index";
import { socialNetwork001P } from "./props";

export default function StarterSocialnetwork001() {
    const onClickButtonEvent = (id) => {
        console.log("onClickButtonEvent", id);
    }
    socialNetwork001P.onClickButtonEvent = onClickButtonEvent;
    return (



        <>
            <Socialnetwork001 {...socialNetwork001P} />
        </>
    )
}
</page>
</componente>

"""
  
  system = "Haja como um programador senior, que ja atua há 20 anos como programador front end, e que tem dominio total na criação de interfaces gráficas usando os mais modernos frameworks javascript, principalmente o react, e me ajude no desenvolvimento de um projeto react, criando componentes usando o react e bibliotecas compativeis, conforme eu te solicitar."


  message = {"role":"system","content":system}
  messages.append(message)


  message = {"role":"user", "content": user_content}
  messages.append(message)

  message = {"role":"user","content":template}
  messages.append(message)
  
  response = await deepseek_coder(messages)
  
  return response

#-=======================================
async def component_add_openai(pages,comp, compProps):
  #print("----- component_add_openai()-----")
  messages = []


  system = "Haja como um programador senior, que ja atua há 20 anos como programador front end, e que tem dominio total na criação de interfaces gráficas usando os mais modernos frameworks javascript, principalmente o react, e me ajude no desenvolvimento de um projeto react, criando componentes usando o react e bibliotecas compativeis, conforme eu te solicitar."


  message = {"role":"system","content":system}
  messages.append(message)

  message = {"role":"user", "content": pages}
  messages.append(message)
  
  myPrompt = f"""
  Quero que adicione na pagina 2 <page2> o Componente {comp} e seu arquivo de propriedades {compProps} . Para isto, utilize como referencia a Pagina 1 <page1> , que utiliza o mesmo componente, com seus  hooks e funções auxiliares.
Insira no <page2> todos os hooks e funções que sao utilizados pelo {comp} e {compProps} e utilização do componente na renderização (return). Após fazer as ncessarias alterações no <page2> , me retorne o seu codigo completo, já com as alterações feitas.
Retorne somente o codigo, com formatação utf-8, dentro da tag <page3> , como no exemplo abaixo, sem comentarios antes ou depois. Tudo o mais que havia na <page2> deve ser mantido. 
<page3>
codigo aqui
</page3> 
  """
  
  message = {"role":"user", "content": myPrompt}
  messages.append(message)
  
  response = await openai_chat(messages)

  return response
#========================================  
async def component_add(data):
     # Calculate the root page path based on the target file path
    rootPage = os.path.dirname(data["target"])
    print("rootPage", rootPage)

    # Construct the component's root path based on its source, group, and name
    rootComp = os.path.join(data["source"], data["group"], data["component"])
    print("rootComp", rootComp)
    
    # Construct the source path of the properties file
    propSource = os.path.join(rootComp, "props.js")
    
    # Construct the target path for the properties file
    propName = data["component"].lower() + "P"  # Name pattern for the property
    propTarget = os.path.join(rootPage, "_props", propName + ".js")
    print("propTarget", propTarget)
    # Ensure the target directory exists
    os.makedirs(os.path.dirname(propTarget), exist_ok=True)
    
    # Copy the properties file to the target location
    shutil.copy(propSource, propTarget)
    print("Property file successfully copied")
    
    # Construct the import statement for the component
    importComp = f"import {data['component']} from '{data['importComp']}/{data['group']}/{data['component']}'"
    print("importComp", importComp)
    
    # Construct the import statement for the component's properties
    importProps = f"import {{ {propName} }} from './_props/{propName}'"
    print("importProps", importProps)
    
 
 
    imports = f"{importComp}\n{importProps}"
    code = add_imports_to_code(data['code'], imports)
    
    index =  await load_file({"path":rootComp + "\\index.js"})
    
    page1 = await load_file({"path":rootComp + "\\page.js"})
    page1 = "<page1>\n" + page1 + "\n</page1>"
    #print("==========page1==========:\n", page1)
    #with open('page1.js', 'w', encoding='utf-8') as file:
          #file.write(page1)
  
    page2 = "<page2>\n" + code + "\n</page2>"
    #print("==========page2==========:\n", page2)
    #with open('page2.js', 'w', encoding='utf-8') as file:
          #file.write(page2)       
          
    page3  = await component_add_openai(page1+page2,data["component"], propName)
    #print("==========Page3 antes da junção==========")
    #with open('page3a.js', 'w', encoding='utf-8') as file:
        #file.write(page1+page2)
    #print("==========Page3 apos a junção==========\n", page3)
    #with open('page3b.js', 'w', encoding='utf-8') as file:
        #file.write(page3)   
    page3 = re.sub(r'```jsx\n', '', page3)
    page3 = re.sub(r'```', '', page3)
    page3 = page3.replace("<page3>", "").replace("</page3>", "")
    page3 = page3.strip()
    page3 = page3.lstrip('\n')
    #print("==========Page3 apos a junção==========\n", page3)
    #with open('page3c.js', 'w', encoding='utf-8') as file:
        #file.write(page3)     
      
    package_json =  await load_file({"path":data["package.json"]})    
    
    #print("package.json", package_json)
    importLibs1 = await extract_import_lines(index, data["component"], propName)
    #print("imporLibs1:")
    #print( importLibs1)
    importLibs2 = importLibs1 + "\n" +  await extract_import_lines(page3, data["component"], propName)
    #print("importLibs2")
    #print(importLibs2)
    
    npms = await get_list_npms(package_json,importLibs2) 
    if npms:
      # Caminho para a pasta da aplicação
      app_path = data["app-path"]
      #print("app_path", app_path)
      # Muda o diretório atual para a pasta da aplicação
      prev_dir = os.getcwd()
      os.chdir(app_path)
      # Executa o comando npm install para as bibliotecas necessárias
      subprocess.run(["C:\\Program Files\\nodejs\\npm.cmd", "install", "@iconify/react", "@mui/material"], check=True)
      os.chdir(prev_dir)   
    
    return page3;
  #====================================
#======================================== 

def add_imports_to_code(code, imports):
    # Divide o código em linhas
    lines = code.strip().split('\n')
    
    # Remove espaços em branco extras nos imports e prepara para inserção
    imports_formatted = imports.strip()

    # Inicializa uma variável para verificar se 'use client' foi encontrado
    found_use_client = False
    
    # Encontra a linha que contém 'use client'
    for i, line in enumerate(lines):
        if "'use client'" in line:
            # Insere os imports após a linha que contém 'use client'
            lines.insert(i + 1, imports_formatted)
            found_use_client = True
            break
    
    # Se 'use client' não foi encontrado, insere os imports antes da primeira linha
    if not found_use_client:
        lines.insert(0, imports_formatted)
    
    # Junta as linhas modificadas de volta em uma única string de código
    new_code = '\n'.join(lines)
    return new_code

#======================================
async def get_list_npms(package_json,importLibs2):
  #print("get_list_npms")
  #print ("package.json",package_json)
  #print("importsLibs2", importLibs2)
  myPrompt = f"""
  tenho esta linha de imports de um app next/react:
  {importLibs2}
  

preciso que verifique  qual destas bibliotecas ainda não foi instalada no app, e gere uma linha de comando de instalação do npm para as bibliotecas ainda não instaladas, e que são importadas pelo app.
Este é o package.json do app, que contem a lista de bibliotecas instaladas:
{package_json}

gere e me retorne somente a lista de comandos npm para instalação das bibliotecas não existem no package.json, (1 por linha) sem comentarios adicionais. Cheque isto 2 vezes para não correr o risco de você pedir para adicionar bibliotecas ja instaladas. Desconsidere versão. Se o nome da biblioteca ja estive no package.json, ela não devera ser listada. Não quero atualizações, quero apenas inclusão de alguma que não exista. Caso não haja nenhuma , não retorne nada .
"""

  #print("myPrompt", myPrompt)
  messages = [{ 
        "role":"user", "content": myPrompt 
    }]
  result = await openai_chat(messages)
  #print ("result", result)
  return result
    
#========================================
async def extract_import_lines(code, comp, props):
    # Dividindo o código em linhas
    lines = code.split('\n')
    # Filtrando as linhas que começam com "import" e não contêm os termos especificados
    import_lines = [line for line in lines if line.startswith('import') and comp not in line and props not in line]
 
    # Juntando os imports em uma string, cada import em uma linha separada
    return '\n'.join(import_lines)

#========================================================================================
async def components_list():
 #print("-----components_list")
 components = []
 url_base = "C:\\Users\\<USER>\\Documents\\GitHub\\gptalk-iapn\\src\\app\\components"
  # Iterar sobre as pastas na URL base
 for grupo in os.listdir(url_base):
  grupo_path = os.path.join(url_base, grupo)
  # Verificar se é uma pasta
  if os.path.isdir(grupo_path):
     # Iterar sobre as subpastas da pasta
     for nome in os.listdir(grupo_path):
        # Adicionar o componente ao array
        components.append({
          "ID": str(len(components) + 1),
          "GRUPO": grupo,
           "NOME": nome
        })
 return components


#========================================================================================
async def component_update(component):
  messages = []
  model = "deepseek-coder"
  
  print("component update")
  deepseek_api_key = fetch_api_key_env("DEEPSEEK_API_KEY")
  model = "deepseek-coder"
  messages = []


  client = OpenAI(
    api_key=deepseek_api_key,
    base_url="https://api.deepseek.com/v1"
 
  )
  
  system = "Haja como um programador senior, que ja atua há 20 anos como programador front end, e que tem dominio total na criação de interfaces gráficas usando os mais modernos frameworks javascript, principalmente o react, e me ajude no desenvolvimento de um projeto react, criando componentes usando o react e bibliotecas compativeis, conforme eu te solicitar."
  message = {"role":"system","content":system}
  messages.append(message)  
  
  user_content = component["code"]
  message = {"role":"user", "content": user_content}
  messages.append(message)
  
  
  user_content = component["description"]
  message = {"role":"user", "content": user_content}
  messages.append(message)
  
  user_content = "Faça as alterações necessárias no código fornecido, mas sem remover ou alterar as tags que envolvem os 3 scripts (index, props, e  page), e me retorne o codigo alterado  dentro da mesma estretura de tags, ja com as alterações. Não faça nenhum comentario. Quero apenas o codigo tageado e alterado."
  message = {"role":"user", "content": user_content}
  messages.append(message)
  
  response = client.chat.completions.create(
    messages=messages,
    model=model,
    temperature= 0.2,
    top_p= 1,  # Valor padrão
    n= 1,  # Valor padrão
  )
  
  #print(response.choices[0].message.content)
  return response.choices[0].message.content
  

#==================================
async def save_component(component):
    #print("save_component")
    #print(component)
    #url_base =  "C:\\Users\\<USER>\\Documents\\GitHub\\gptalk-iapn\\src\\app\\components"
    url_base =  "C:\\Users\\<USER>\\Documents\\GitHub\\iapn-next-react-components\\app\\components"


    grupo_nome = component["grupoNome"]
    nome = component["nome"]
    index_js_content = component["index.js"]
    props_js_content = component["props.js"]
    page_js_content = component["page.js"]
    
    # Verificar se a pasta do grupo existe, se não, criar
    grupo_path = os.path.join(url_base, grupo_nome)
    if not os.path.exists(grupo_path):
        os.makedirs(grupo_path)
    
    # Verificar se a pasta do componente existe, se não, criar
    componente_path = os.path.join(grupo_path, nome)
    if not os.path.exists(componente_path):
        os.makedirs(componente_path)
    
    # Salvar o conteúdo nos arquivos correspondentes
    with open(os.path.join(componente_path, "index.js"), "w", encoding="utf-8") as f:
        f.write(index_js_content)
    
    with open(os.path.join(componente_path, "props.js"), "w", encoding="utf-8") as f:
        f.write(props_js_content)
    
    with open(os.path.join(componente_path, "page.js"), "w", encoding="utf-8") as f:
        f.write(page_js_content)
        
    #print("componente salvo com sucesso");
    return "componente salvo com sucesso"
#=======================================
async def main():
    print('Starting...')
    data = {
        "source": r"C:\Users\<USER>\Documents\GitHub\gptalk-iapn\src\app\components",
        "target": r"C:\Users\<USER>\Documents\GitHub\gptalk-iapn\src\app\agents\02\conteudos\conteudo\page.js",
        "package.json":r"C:\Users\<USER>\Documents\GitHub\gptalk-iapn\package.json",
        "group": "tabs",
        "component": "Tab001",
        "importComp": "@components",
        "app-path":r"C:\Users\<USER>\Documents\GitHub\gptalk-iapn",
        "code": """
import React from "react";

const Help001 = () => {
    return (
        <>
            <h1>Help001</h1>
        </>
    );
};

export default Help001;
        """
    }
    
    result = await component_add(data)
    print(result)

# Since we cannot run asyncio here, this call is illustrative
#asyncio.run(main())