from agents import function_tool
from ....agent.agent_neo4j import AgentNeo4j

@function_tool
async def produto_excluir(
    codigo: str
):
    """
    Use esta função para excluir produtos solicitados pelo usuário.
    Marca um produto como excluido no banco de dados. Isto é feito setando a propriedade excluido com o valor 1. 
    Retorna um erro se o produto com o codigo especificado não for encontrado , ou uma mensagem informando o sucesso da operação.
    """
    agent_neo4j = AgentNeo4j()
    # Modificamos a query para retornar o produto. Se nenhum produto for encontrado, o resultado estará vazio.
    query = "MATCH (p:Produto {codigo: $codigo}) SET p.excluido = 1 RETURN p"
    params = {"codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_write_query(query=query, params=params)
        
        # Se o resultado (uma lista) contiver dados, o produto foi encontrado e atualizado.
        if resultado and len(resultado) > 0:
            return "O produto foi excluido com sucesso"
        else:
            # Se o resultado estiver vazio, nenhum produto foi encontrado com o codigo especificado.
            return "Não encontrei o produto com o código especificado"
    except Exception as e:
        return {"success": False, "message": f"Ocorreu uma exceção ao tentar excluir o produto: {str(e)}"}


async def verificar_produto_existe(codigo: str):
    """
    Verifica se um produto existe no banco de dados antes de tentar excluí-lo.
    """
    agent_neo4j = AgentNeo4j()
    query = "MATCH (p:Produto {codigo: $codigo}) RETURN p.codigo, p.nome, p.excluido"
    params = {"codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_read_query(query=query, params=params)
        return resultado
    except Exception as e:
        print(f"Erro ao verificar produto: {str(e)}")
        return None
    
if __name__ == "__main__":
    import asyncio
    
    async def teste_produto_excluir():
        codigo = "10253731"
        
        # Primeiro, verificar se o produto existe
        print(f"🔍 Verificando se o produto {codigo} existe...")
        produto_existe = await verificar_produto_existe(codigo)
        
        if produto_existe:
            print(f"✅ Produto encontrado: {produto_existe}")
        else:
            print(f"❌ Produto {codigo} NÃO foi encontrado no banco de dados!")
            return
        
        # Se existe, tentar excluir
        print(f"\n🗑️ Tentando excluir o produto {codigo}...")
        resultado = await produto_excluir(codigo)
        print(resultado)
        
        if resultado["success"]:
            print("Produto excluído com sucesso!")
        else:
            print(f"Erro ao excluir produto: {resultado['message']}")
            
        # Verificar novamente após a exclusão
        print(f"\n🔍 Verificando estado após exclusão...")
        produto_apos = await verificar_produto_existe(codigo)
        if produto_apos:
            print(f"📊 Estado atual do produto: {produto_apos}")
    
    asyncio.run(teste_produto_excluir())
    #Execução:
    #python -m api.agent.business.functions.produto_excluir