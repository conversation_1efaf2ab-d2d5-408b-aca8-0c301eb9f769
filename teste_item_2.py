#!/usr/bin/env python3
"""
TESTE 2: Enviar mensagem de teste para verificar se a API está funcionando
"""
import requests

# Configurações - usando ID ÚNICO DE CONTROLE
url = 'https://apinocode01.megaapi.com.br/rest/sendMessage/megacode-MqgHxQV7790/text'
headers = {
    'Authorization': 'Bearer 4649a86d-1e20-4dd1-a6ad-e41f1b92c495',
    'Content-Type': 'application/json'
}

# Dados da mensagem - enviando para o próprio número da instância
payload = {
    "number": "<EMAIL>",
    "messageData": {
        "text": "🧪 TESTE 2: Mensagem de teste da API MegaAPI"
    }
}

print('🔍 TESTE 2: Envio de mensagem de teste')
print(f'URL: {url}')
print(f'Token: 4649a86d-1e20-4dd1-a6ad-e41f1b92c495')
print(f'Destinatário: {payload["number"]}')
print(f'Mensagem: {payload["messageData"]["text"]}')
print('-' * 50)

try:
    response = requests.post(url, headers=headers, json=payload)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response completa: {data}')
        
        if data.get('error') == False:
            print('✅ SUCESSO: Mensagem enviada!')
            print('🎯 A API está funcionando!')
        else:
            print('❌ FALHA: Erro no envio')
            print(f'Erro: {data.get("message", "Erro desconhecido")}')
    elif response.status_code == 404:
        print('❌ FALHA: Endpoint não encontrado')
        print('💡 Talvez o endpoint esteja incorreto')
    elif response.status_code == 401:
        print('❌ FALHA: Não autorizado')
        print('💡 Token ou autenticação incorreta')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 2:')
print('Se STATUS CODE = 200 e error = False: ✅ SUCESSO')
print('Caso contrário: ❌ FALHA') 