#!/usr/bin/env python3
import asyncio
import sys
import os
import json
from pathlib import Path

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from api.agent.agent_mysql import Mysql
    from api.agent.agent_logger import AgentLogger
except ImportError as e:
    print(f"Erro ao importar módulos: {e}")
    print("Verifique se está executando do diretório correto")
    sys.exit(1)

from neo4j import GraphDatabase

# Configurar logging
logger = AgentLogger()

class ClienteMigrator:
    def __init__(self):
        self.mysql = Mysql()
        
        # Configurações Neo4j do mcp.json
        self.neo4j_uri = "bolt://5.161.204.141:7687"
        self.neo4j_user = "neo4j"
        self.neo4j_password = "gcs@neo4j"
        self.neo4j_database = "neo4j"
        
        # Conectar ao Neo4j
        self.neo4j_driver = GraphDatabase.driver(
            self.neo4j_uri, 
            auth=(self.neo4j_user, self.neo4j_password)
        )
        
        logger.info("✅ ClienteMigrator inicializado")
    
    def convert_to_neo4j_properties(self, cliente_mysql):
        """
        Converte um cliente do MySQL para propriedades do Neo4j
        - Converte nomes de campos para minúsculas
        - Trata valores None apropriadamente
        - Converte tipos de dados conforme necessário
        """
        neo4j_properties = {}
        
        for key, value in cliente_mysql.items():
            # Converter nome da propriedade para minúscula
            neo4j_key = key.lower()
            
            # Tratar valores None e diferentes tipos
            if value is None or value == "None":
                neo4j_properties[neo4j_key] = None
            elif isinstance(value, (int, float)):
                neo4j_properties[neo4j_key] = value
            elif isinstance(value, str):
                # Manter strings vazias como string vazia, não None
                neo4j_properties[neo4j_key] = value
            else:
                # Para outros tipos, converter para string
                neo4j_properties[neo4j_key] = str(value)
        
        return neo4j_properties
    
    async def buscar_clientes_mysql(self, negocio_idx):
        """Busca clientes do MySQL usando agent_mysql.py"""
        logger.info(f"🔍 Buscando clientes do negócio {negocio_idx} no MySQL...")
        
        try:
            # Buscar clientes usando o método fetch do agent_mysql
            colunas = "*"
            tabela = "CLIENTE"
            filtros = [f"NEGOCIO_IDX = '{negocio_idx}'", "EXCLUIDO = 0"]
            
            clientes = await self.mysql.fetch(colunas, tabela, filtros)
            
            logger.info(f"✅ Encontrados {len(clientes)} clientes no MySQL")
            return clientes
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar clientes do MySQL: {str(e)}")
            raise e
    
    def criar_pessoa_fisica_neo4j(self, cliente_neo4j):
        """Cria um nó PessoaFisica no Neo4j"""
        with self.neo4j_driver.session() as session:
            # Query para criar/atualizar o nó PessoaFisica
            query = """
            MERGE (p:PessoaFisica {idx: $idx, negocio_idx: $negocio_idx})
            SET p += $properties
            RETURN p
            """
            
            # Executar query
            result = session.run(query, {
                "idx": cliente_neo4j.get("idx"),
                "negocio_idx": cliente_neo4j.get("negocio_idx"),
                "properties": cliente_neo4j
            })
            
            record = result.single()
            if record:
                return record["p"]
            else:
                return None
    
    async def migrar_clientes(self, negocio_idx):
        """Migra todos os clientes de um negócio do MySQL para Neo4j"""
        logger.info(f"🚀 Iniciando migração dos clientes do negócio {negocio_idx}")
        
        try:
            # 1. Buscar clientes no MySQL
            clientes_mysql = await self.buscar_clientes_mysql(negocio_idx)
            
            if not clientes_mysql:
                logger.warning(f"⚠️ Nenhum cliente encontrado no MySQL para negócio {negocio_idx}")
                return
            
            # 2. Migrar cada cliente
            clientes_migrados = 0
            clientes_erro = 0
            
            for cliente_mysql in clientes_mysql:
                try:
                    # Converter propriedades para Neo4j
                    cliente_neo4j = self.convert_to_neo4j_properties(cliente_mysql)
                    
                    # Criar nó no Neo4j
                    resultado = self.criar_pessoa_fisica_neo4j(cliente_neo4j)
                    
                    if resultado:
                        clientes_migrados += 1
                        logger.info(f"✅ Cliente migrado: {cliente_mysql.get('NOME', 'N/A')} (IDX: {cliente_mysql.get('IDX', 'N/A')})")
                    else:
                        clientes_erro += 1
                        logger.error(f"❌ Erro ao criar nó para cliente IDX: {cliente_mysql.get('IDX', 'N/A')}")
                        
                except Exception as e:
                    clientes_erro += 1
                    logger.error(f"❌ Erro ao migrar cliente {cliente_mysql.get('IDX', 'N/A')}: {str(e)}")
            
            # 3. Relatório final
            logger.info(f"📊 Migração concluída:")
            logger.info(f"   ✅ Clientes migrados: {clientes_migrados}")
            logger.info(f"   ❌ Clientes com erro: {clientes_erro}")
            logger.info(f"   📋 Total processado: {len(clientes_mysql)}")
            
            return {
                "total_clientes": len(clientes_mysql),
                "clientes_migrados": clientes_migrados,
                "clientes_erro": clientes_erro,
                "sucesso": clientes_migrados > 0
            }
            
        except Exception as e:
            logger.error(f"❌ Erro geral durante a migração: {str(e)}")
            raise e
    
    def verificar_pessoas_fisicas_neo4j(self):
        """Verifica as pessoas físicas criadas no Neo4j"""
        logger.info("🔍 Verificando pessoas físicas no Neo4j...")
        
        with self.neo4j_driver.session() as session:
            # Buscar pessoas físicas do negócio específico
            query = """
            MATCH (p:PessoaFisica) 
            WHERE p.negocio_idx = '4015743441'
            RETURN p.idx as idx, p.nome as nome, p.telefone as telefone, p.email as email
            ORDER BY p.nome
            """
            
            result = session.run(query)
            pessoas = [record.data() for record in result]
            
            logger.info(f"📋 Pessoas físicas encontradas no Neo4j: {len(pessoas)}")
            for pessoa in pessoas:
                logger.info(f"   - {pessoa['nome']} (IDX: {pessoa['idx']}, Tel: {pessoa['telefone']}, Email: {pessoa['email']})")
            
            return pessoas
    
    def close(self):
        """Fecha conexões"""
        if hasattr(self, 'neo4j_driver'):
            self.neo4j_driver.close()
            logger.info("🔒 Conexão Neo4j fechada")

async def main():
    migrator = ClienteMigrator()
    
    try:
        # Migrar clientes do negócio específico
        resultado = await migrator.migrar_clientes("4015743441")
        
        if resultado and resultado["sucesso"]:
            print("\n" + "="*50)
            print("🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!")
            print("="*50)
            print(f"📊 Total de clientes: {resultado['total_clientes']}")
            print(f"✅ Clientes migrados: {resultado['clientes_migrados']}")
            print(f"❌ Clientes com erro: {resultado['clientes_erro']}")
            
            # Verificar resultado no Neo4j
            print("\n" + "="*50)
            print("🔍 VERIFICANDO PESSOAS FÍSICAS NO NEO4J")
            print("="*50)
            pessoas_neo4j = migrator.verificar_pessoas_fisicas_neo4j()
            
            if pessoas_neo4j:
                print(f"✅ {len(pessoas_neo4j)} pessoas físicas verificadas no Neo4j")
            else:
                print("⚠️ Nenhuma pessoa física encontrada no Neo4j")
        else:
            print("❌ Migração falhou ou não foi executada")
            
    except Exception as e:
        print(f"❌ Erro durante a migração: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        migrator.close()

if __name__ == "__main__":
    asyncio.run(main()) 