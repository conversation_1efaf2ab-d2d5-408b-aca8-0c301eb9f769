<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site em Construção - GPTalk</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            text-align: center;
            padding: 2rem;
            max-width: 800px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
        }

        .logo {
            max-width: 300px;
            width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .message {
            font-size: 1.1rem;
            color: #34495e;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .progress-container {
            background: #ecf0f1;
            border-radius: 25px;
            padding: 4px;
            margin: 2rem 0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(90deg, #3498db, #2ecc71);
            height: 12px;
            border-radius: 25px;
            width: 75%;
            animation: progress 2s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes progress {
            from { width: 0%; }
            to { width: 75%; }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            border: 1px solid #dee2e6;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #7f8c8d;
            line-height: 1.5;
        }

        .contact-info {
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            color: white;
        }

        .contact-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .contact-text {
            font-size: 1rem;
            line-height: 1.6;
        }

        .social-links {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .title {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .message {
                font-size: 1rem;
            }

            .logo {
                max-width: 250px;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .social-links {
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 0.5rem;
                padding: 1rem;
            }

            .title {
                font-size: 1.8rem;
            }

            .logo {
                max-width: 200px;
            }

            .feature {
                padding: 1rem;
            }
        }

        /* Animações extras */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo-container">
            <img src="https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png" 
                 alt="GPTalk Logo" 
                 class="logo floating">
        </div>

        <h1 class="title">🚧 Site em Construção</h1>
        
        <p class="subtitle">
            Estamos trabalhando duro para trazer algo incrível para você!
        </p>

        <div class="message">
            <p>Nossa equipe está dedicada em criar uma experiência única e inovadora. 
            Em breve, você terá acesso a todas as funcionalidades que estamos desenvolvendo 
            com muito carinho e atenção aos detalhes.</p>
        </div>

        <div class="progress-container">
            <div class="progress-bar"></div>
        </div>
        <p style="color: #7f8c8d; font-size: 0.9rem; margin-bottom: 2rem;">Progresso: 75% concluído</p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🤖</div>
                <div class="feature-title">IA Avançada</div>
                <div class="feature-desc">Inteligência artificial de última geração para atendimento personalizado</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💬</div>
                <div class="feature-title">Chat Inteligente</div>
                <div class="feature-desc">Conversas naturais e eficientes com nossos agentes virtuais</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">Ferramentas Profissionais</div>
                <div class="feature-desc">Suite completa de ferramentas para seu negócio</div>
            </div>
        </div>

        <div class="contact-info">
            <div class="contact-title">📢 Fique por dentro das novidades!</div>
            <div class="contact-text">
                Assim que estivermos prontos, você será o primeiro a saber. 
                Estamos finalizando os últimos detalhes para oferecer a melhor experiência possível.
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link" title="WhatsApp">📱</a>
                <a href="#" class="social-link" title="Email">✉️</a>
                <a href="#" class="social-link" title="Telegram">📨</a>
            </div>
        </div>

        <div style="margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid #dee2e6; color: #95a5a6; font-size: 0.9rem;">
            <p>© 2025 GPTalk - Todos os direitos reservados</p>
            <p style="margin-top: 0.5rem;">⏰ Previsão de lançamento: Em breve</p>
        </div>
    </div>

    <script>
        // Adiciona interatividade extra
        document.addEventListener('DOMContentLoaded', function() {
            // Animação da barra de progresso
            const progressBar = document.querySelector('.progress-bar');
            
            // Efeito de clique nas features
            const features = document.querySelectorAll('.feature');
            features.forEach(feature => {
                feature.addEventListener('click', function() {
                    this.classList.add('pulse');
                    setTimeout(() => {
                        this.classList.remove('pulse');
                    }, 1000);
                });
            });

            // Contador regressivo simples (opcional)
            const now = new Date().getTime();
            const launchDate = now + (30 * 24 * 60 * 60 * 1000); // 30 dias
            
            function updateCountdown() {
                const now = new Date().getTime();
                const distance = launchDate - now;
                
                if (distance > 0) {
                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    // Você pode adicionar um contador se quiser
                }
            }
            
            // Atualiza a cada hora
            setInterval(updateCountdown, 3600000);
        });
    </script>
</body>
</html> 