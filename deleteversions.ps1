# Listar todas as versões e pegar os IDs, ordenando por data de implantação
$versions = gcloud app versions list --sort-by "~LAST_DEPLOYED" --format="value(VERSION.ID)"

# Verificar se há mais de uma versão para deletar
if ($versions.Count -gt 1) {
    # Manter a versão mais recente e deletar as demais
    $versionsToDelete = $versions[1..$versions.Count]
    Write-Output "Deletando as seguintes versões: $versionsToDelete"
    foreach ($version in $versionsToDelete) {
        gcloud app versions delete $version -q
    }
} else {
    Write-Output "Nenhuma versão encontrada para deletar ou apenas uma versão existente."
}

# Fazer o deploy da nova versão
Write-Output "Fazendo o deploy da nova versão..."
gcloud app deploy
