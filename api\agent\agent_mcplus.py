from fastapi import APIRouter
from ..cache import cache  # Ajuste o caminho conforme a estrutura do seu projeto
from ..functions.util import generate_unique_id
from .agent_openai import OpenAi
from .agent_llm import LLM
import asyncio
from typing import Optional
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from anthropic import Anthropic
from agents.mcp import MCPServer, MCPServerSse, MCPServerStdio
from agents import Agent, Runner, OpenAIChatCompletionsModel, ModelSettings




router = APIRouter()
oai = OpenAi()  # Using your custom OpenAi class

class AgentMCPlus:
    def __init__(self,name="mcplus",usuario_idx=None):
        self.name = name
        self.usuario_idx = usuario_idx
        
        self.instructions = f"""
        Você é um Gerenciador de MCPs (Model Context Protocols). Seu objetivo é genrenciar agentes mcps remotos e locais, facilitando o acesso a eles, e dando aos usuarios informações necessárias sobre eles e recebidas deles.Você também pode ativar ou desativar mcps individuais ou grupos, conforme solicitado pelo usuario. Poderá também criar novos agentes, ou excluir agentes existentes. Ao executar  qualquer uma das operações crud de MCps (inclusão, exclusão, ativação, desativação, busca, atualização), você deve informar o idx {usuario_idx}  ,que é o identificador único do usuario.
        Todas as respostas devem ser em português brasileiro.
        Você não irá dar nenuma informação sobre qualquer outro assunto além da sua função que é gerenciar mcps e invocar agentes mcps que melhor atendam as necessidades do usuario. Caso não encontre um agente mcp com as funções ou informações solicitadas, você deve informar ao usuario que não encontrou o agente mcp com as funções ou informações solicitadas para resolver a necessidade dele, e que ele deve adiconar um mcp que possa resolver a necessidade dele.
        """
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = Anthropic()

    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server
    
        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")
    
        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )
    
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))
    
        await self.session.initialize()
    
        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        print("\nConnected to server with tools:", [tool.name for tool in tools])
            

    def get_agent(self):
        return self.agent

    def set_agent(self, agent):
        self.agent = agent

    def get_instructions(self):
        return self.instructions

    async def process_query(self, query: str) -> str:
        """Process a query using Claude and available tools"""
        messages = [
            {
                "role": "user",
                "content": query
            }
        ]
    
        response = await self.session.list_tools()
        available_tools = [{
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.inputSchema
        } for tool in response.tools]
    
        # Initial Claude API call
        response = self.anthropic.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=1000,
            messages=messages,
            tools=available_tools
        )
    
        # Process response and handle tool calls
        final_text = []
    
        assistant_message_content = []
        for content in response.content:
            if content.type == 'text':
                final_text.append(content.text)
                assistant_message_content.append(content)
            elif content.type == 'tool_use':
                tool_name = content.name
                tool_args = content.input
    
                # Execute tool call
                result = await self.session.call_tool(tool_name, tool_args)
                final_text.append(f"[Calling tool {tool_name} with args {tool_args}]")
    
                assistant_message_content.append(content)
                messages.append({
                    "role": "assistant",
                    "content": assistant_message_content
                })
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "tool_result",
                            "tool_use_id": content.id,
                            "content": result.content
                        }
                    ]
                })
    
                # Get next response from Claude
                response = self.anthropic.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=1000,
                    messages=messages,
                    tools=available_tools
                )
    
                final_text.append(response.content[0].text)
    
        return "\n".join(final_text)  

    async def get_mcp_server_stdio(self, params: dict):
        mcp_server = MCPServerStdio(params, cache_tools_list=True)
        print("mcp_server", mcp_server)

        async with mcp_server:
            tools = await mcp_server.list_tools()
            print(f"Ferramentas disponíveis no servidor Stdio: {[tool.name for tool in tools]}")

        return mcp_server

    async def get_mcp_server_sse(self, params: dict):
        
        mcp_server = MCPServerSse(params, cache_tools_list=True)
        url = params.get("url")
        if not url:
            raise ValueError("url é obrigatório")

        async with mcp_server:
            # Lista ferramentas disponíveis no servidor remoto
            try:
                tools = await mcp_server.list_tools()
                print(f"Ferramentas disponíveis no servidor MCP: {[tool.name for tool in tools]}")
            except Exception as e:
                print(f"Erro ao listar ferramentas: {str(e)}")

        return mcp_server

@router.post("/agent/run")
async def agent_run(data: dict):
    print("===== agent_run() =====")
    print("data", data)

    agentMcplus = AgentMCPlus()
    servers = []

    # Extrair dados necessários
    usuario_idx = data.get("usuario_idx")
    mensagem = data.get("mensagem", "")
    negocio_idx = data.get("negocio_idx", "")
    modelo = data.get("modelo", "")


    if not usuario_idx:
        return {"success": False, "message": "usuario_idx é obrigatório"}

    if not modelo:
        return {"success": False, "message": "modelo é obrigatório"}


    # Verificar se existe conversa ativa
    conversa_idx, historico_mensagens = find_active_conversation(usuario_idx,agentMcplus.name)


    # Se não existir conversa ativa, criar nova
    if not conversa_idx:
        print("Não existe conversa ativa, criando nova conversa")
        conversa_idx = generate_unique_id()
        print("conversa_idx", conversa_idx)
        historico_mensagens = []

    # Adicionar mensagem do usuário ao histórico
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)
    print("historico_mensagens", historico_mensagens)   


    llm = LLM()
    print("modelo", modelo) 
    model = llm.get_model_idx(modelo)
    print("model", model)




   

   # server1 = await agentMcplus.get_mcp_server_stdio(params)
   # print("server1", server1)
   # servers.append(server1)  



    #Servidor 1 = sse
    #mcp_server_url = "https://gitmcp.io/langchain-ai/langchain"
    #server1 = await agentMcplus.get_mcp_server_sse(params={"url": mcp_server_url})
    #print("server1", server1)
    #servers.append(server1)  

    #Servidor 1 = sse
    mcp_server_url = "http://127.0.0.1:8000/mcp"
    server1 = await agentMcplus.get_mcp_server_sse(params={"url": mcp_server_url})
    print("server1", server1)
    servers.append(server1)  




    async with servers[0]:
    #async with servers[0], servers[1]:



        agente_mcplus = {
            "name": "MCP+",
            "instructions": agentMcplus.get_instructions(),
            "model": model,
            "tools": [],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
            "mcp_servers": servers,

        }

        # Criar o agente DRE360
        agente_mcplus_obj = await oai.agent_create(**agente_mcplus)

        # Executar o agente com o histórico de mensagens
        response = await oai.agent_run(agente_mcplus_obj,  historico_mensagens)
        
        print("\n=== TRACES DO AGENTE ===")
        print("Resposta final:", response.final_output)
        #print("Traces completos:", response.trace)
        if hasattr(response, 'tool_outputs'):
            print("Outputs das ferramentas:", response.tool_outputs)
        print("=======================\n")

        # Adicionar resposta ao histórico
        historico_mensagens = add_message_to_history(historico_mensagens, response.final_output, False)

    # Salvar histórico no cache
    cache_key = get_conversa_key(usuario_idx, agentMcplus.name, conversa_idx)
    cache[cache_key] = historico_mensagens

    return {"success": True, "message": response.final_output}

def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{ conversa_idx}"


def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    print("===== find_active_conversation() =====")
    print("usuario_idx", usuario_idx)
    print("agente_nome", agente_nome)
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            
            conversa_idx = key.split('_')[3]
            print("conversa encontrada", conversa_idx)
            return conversa_idx, cache[key]
    print("conversa não encontrada")
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    #print("===== add_message_to_history() =====")
    #print("history", history)
    #print("message", message)
    #print("is_user", is_user)   
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


async def test_agent_run1():
    print("Testando agent_run diretamente")

    # Dados de teste
    test_data = {
        "mensagem": "Olá, preciso de ajuda com minha contabilidade",
        "negocio_idx": "2345678901",
        "modelo": "gemini-pro",
        "usuario_idx": "1234567890"
    }

    try:
        print("\nTestando função agent_run")
        print("Enviando mensagem:", test_data["mensagem"])

        # Chamando a função diretamente
        result = await agent_run(test_data)

        print("Resultado do teste de agent_run:")
        print(result)

        # Verifica se a resposta contém os campos esperados
        assert "success" in result, "Resposta deve conter o campo 'success'"
        assert "message" in result, "Resposta deve conter o campo 'message'"
        assert result["success"] is True, "O campo 'success' deve ser True"

        print("\n✓ Teste de agent_run executado com sucesso")

    except Exception as e:
        print("✕ Erro no teste de agent_run:", str(e))
        raise e

# Adicione este código para executar o teste quando o arquivo for executado diretamente
if __name__ == "__main__":
    import asyncio

    async def test_agent_run():

        test_data = {
            "mensagem": "Quantos registros tem a tabela NEGOCIO?",
            "negocio_idx": "7600786155",
            "modelo": "1234567890",
            "usuario_idx": "4344140157",
       
        }

        
        print("Testando agent_run diretamente")
        result = await agent_run(test_data)
        print("Resultado do teste de agent_run:")
        print(result)

    
    # Executa o teste
    asyncio.run(test_agent_run())
