#!/usr/bin/env python3
import asyncio
import sys
import os

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(__file__))

async def main():
    try:
        from util.mysql import MySqlConnection
        from api.config.logging_config import configure_logger
        
        # Configurar logger
        logger = configure_logger("gptalk")
        mysql = MySqlConnection.get_instance()
        
        print("=== TESTE VERIFICAÇÃO FORMAS PAGAMENTO ===")
        
        # Verificar se a tabela existe
        result_tabela = await mysql.query("SHOW TABLES LIKE 'VENDA_FORMA_PAGAMENTO'")
        print(f"Tabela VENDA_FORMA_PAGAMENTO existe: {result_tabela}")
        
        if not result_tabela:
            # Verificar outras variações do nome da tabela
            result_tabela_alt = await mysql.query("SHOW TABLES LIKE '%FORMA%PAGAMENTO%'")
            print(f"Tabelas com FORMA e PAGAMENTO: {result_tabela_alt}")
            
            result_tabela_alt2 = await mysql.query("SHOW TABLES LIKE '%VENDA%'")
            print(f"Tabelas com VENDA: {result_tabela_alt2}")
        
        # Verificar última venda (ID 23190)
        result_venda = await mysql.query("SELECT * FROM VENDA WHERE ID = 23190")
        print(f"\nDados da venda 23190: {result_venda}")
        
        # Se a tabela existir, verificar formas de pagamento
        if result_tabela:
            # Verificar estrutura da tabela
            result_estrutura = await mysql.query("DESCRIBE VENDA_FORMA_PAGAMENTO")
            print(f"\nEstrutura da tabela VENDA_FORMA_PAGAMENTO: {result_estrutura}")
            
            # Verificar formas de pagamento da venda 23190
            result_formas = await mysql.query("SELECT * FROM VENDA_FORMA_PAGAMENTO WHERE VENDA_ID = 23190")
            print(f"\nFormas de pagamento da venda 23190: {result_formas}")
            
            # Verificar total de registros na tabela
            result_total = await mysql.query("SELECT COUNT(*) as total FROM VENDA_FORMA_PAGAMENTO")
            print(f"\nTotal de formas de pagamento na tabela: {result_total}")
        else:
            print("\n❌ PROBLEMA: Tabela VENDA_FORMA_PAGAMENTO não existe!")
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 