@function_toll
async def produto_catalogo_adicionar(
    nome: str,
    codigo: str,
    preco_maior: float,
    preco_tabela: float,
    preco_desconto: float,
    descricao: str, 
    cor_codigo: str,
    url_imagem: str,
    marykay_idx: str
):
       
       """
       🚨 FUNÇÃO CRÍTICA: Adiciona produto ao catálogo da MaryKay no sistema.

       Solicite os dados do produto ao usuario, um de cada vez e na squencia abaixo.
       Os seguintes dados deverão ser passados para a função:
       
       
       NOME:
       - Nome do produto.
       - Exemplo: "Kit de Básico"
       - Obrigatório
       
       CODIGO:
       - Código do produto.
       - Exemplo: "10142673"
       - Obrigatório
       
       DESCRIÇÃO:
       - Descrição do produto.
       - Exemplo: "Indicado para quem tem pele oleosa. Usar 3x ao dia."
       - Opcional
       
       PRECO_TABELA:
       - Preço de tabela do produto.
       - Exemplo: 120.00
       - Obrigatório



       PRECO_DESCONTO:
       - Preço de venda do produto.
       - Exemplo: 100.00
       - Obrigatório

       
       COR_CODIGO:
       - Código da cor do produto.
       - Exemplo: "red matte"
       - Opcional
       Solicitar ao usuario o nome da cor. Caso ele informe, busque a cor na tabela COR usando a funcao consulta_mary_kay()e obtenha o código. Caso não encontre, informe ao usuario que a cor não foi encontrada e peça que ele verifique o nome correto.

       """
       logger.info("===== produto_catelogo_adicionar ======")
       logger.info(f"nome: {nome}")
       logger.info(f"codigo: {codigo}")
       logger.info(f"preco_tabela: {preco_tabela}")
       logger.info(f"preco_desconto: {preco_desconto}")
       logger.info(f"descricao: {descricao}")
       logger.info(f"cor_codigo: {cor_codigo}")
       logger.info(f"marykay_idx: {marykay_idx}")

       try:
   
           produto_novo = {
               "IDX": generate_unique_id(),
               "NEGOCIO_IDX": marykay_idx,
               "NOME": nome,
               "CODIGO": codigo,  # Código do catálogo Mary Kay
               "PRECO_MAIOR": preco_tabela,
               "PRECO": preco_desconto,
               "DESCR": descricao,
               "COR_CODIGO": cor_codigo,
               "URL_IMAGEM": url_imagem,
           }    
           logger.info(f"produto_novo: {produto_novo}")    
           
           result = await mysql.add("PRODUTO", produto_novo)
           
           # Verifica se o resultado é válido (agora mysql.add já faz logs detalhados)
           if result is None:
               logger.error("❌ ERRO: Falha na inserção do produto - mysql.add() retornou None")
               return {"success": False, "message": "Erro ao inserir produto no banco de dados. Verifique os logs para mais detalhes."}
           
           logger.info("===== produto_catalogo_adicionar SUCESSO =====")
           return {"success": True, "message": f"Produto adicionado com sucesso ao catálogo! ID: {result}"}    
           
       except Exception as e:
           logger.error(f"===== ERRO EM produto_catalogo_adicionar() =====")
           logger.error(f"ERRO TIPO: {type(e).__name__}")
           logger.error(f"ERRO MENSAGEM: {str(e)}")
           logger.error(f"DADOS DO PRODUTO:")
           logger.error(f"  - NOME: {nome}")
           logger.error(f"  - CODIGO: {codigo}")
           logger.error(f"  - PRECO_MAIOR: {preco_tabela}")
           logger.error(f"  - PRECO_DESCONTO: {preco_desconto}")
           logger.error(f"  - DESCRICAO: {descricao}")
           logger.error(f"  - COR_CODIGO: {cor_codigo}")
           logger.error(f"  - URL_IMAGEM: {url_imagem}")
           logger.error(f"  - MARYKAY_IDX: {marykay_idx}")
           logger.error("===== FIM ERRO produto_catalogo_adicionar() =====")
           return {"success": False, "error": f"Erro ao adicionar produto: {str(e)}"}
