#!/usr/bin/env python3
import asyncio
import sys
import os
import json
from pathlib import Path

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from api.agent.agent_mysql import Mysql
    from api.agent.agent_logger import AgentLogger
except ImportError as e:
    print(f"Erro ao importar módulos: {e}")
    print("Verifique se está executando do diretório correto")
    sys.exit(1)

from neo4j import GraphDatabase

# Configurar logging
logger = AgentLogger()

class ProdutoMigrator:
    def __init__(self):
        self.mysql = Mysql()
        
        # Configurações Neo4j do mcp.json
        self.neo4j_uri = "bolt://5.161.204.141:7687"
        self.neo4j_user = "neo4j"
        self.neo4j_password = "gcs@neo4j"
        self.neo4j_database = "neo4j"
        
        # Conectar ao Neo4j
        self.neo4j_driver = GraphDatabase.driver(
            self.neo4j_uri, 
            auth=(self.neo4j_user, self.neo4j_password)
        )
        
        logger.info("✅ ProdutoMigrator inicializado")
    
    def convert_to_neo4j_properties(self, produto_mysql):
        """
        Converte um produto do MySQL para propriedades do Neo4j
        - Converte nomes de campos para minúsculas
        - Trata valores None apropriadamente
        - Converte tipos de dados conforme necessário
        """
        neo4j_properties = {}
        
        for key, value in produto_mysql.items():
            # Converter nome da propriedade para minúscula
            neo4j_key = key.lower()
            
            # Tratar valores None e diferentes tipos
            if value is None or value == "None":
                neo4j_properties[neo4j_key] = None
            elif isinstance(value, (int, float)):
                neo4j_properties[neo4j_key] = value
            elif isinstance(value, str):
                # Manter strings vazias como string vazia, não None
                neo4j_properties[neo4j_key] = value
            else:
                # Para outros tipos, converter para string
                neo4j_properties[neo4j_key] = str(value)
        
        return neo4j_properties
    
    async def buscar_produtos_mysql(self, negocio_idx):
        """Busca produtos do MySQL usando agent_mysql.py"""
        logger.info(f"🔍 Buscando produtos do negócio {negocio_idx} no MySQL...")
        
        try:
            # Buscar produtos usando o método fetch do agent_mysql
            colunas = "*"
            tabela = "PRODUTO"
            filtros = [f"NEGOCIO_IDX = '{negocio_idx}'", "EXCLUIDO = 0"]
            
            produtos = await self.mysql.fetch(colunas, tabela, filtros)
            
            logger.info(f"✅ Encontrados {len(produtos)} produtos no MySQL")
            return produtos
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar produtos do MySQL: {str(e)}")
            raise e
    
    def criar_produto_neo4j(self, produto_neo4j):
        """Cria um nó Produto no Neo4j"""
        with self.neo4j_driver.session() as session:
            # Query para criar/atualizar o nó Produto
            query = """
            MERGE (p:Produto {idx: $idx, negocio_idx: $negocio_idx})
            SET p += $properties
            RETURN p
            """
            
            # Executar query
            result = session.run(query, {
                "idx": produto_neo4j.get("idx"),
                "negocio_idx": produto_neo4j.get("negocio_idx"),
                "properties": produto_neo4j
            })
            
            record = result.single()
            if record:
                return record["p"]
            else:
                return None
    
    async def migrar_produtos(self, negocio_idx):
        """Migra todos os produtos de um negócio do MySQL para Neo4j"""
        logger.info(f"🚀 Iniciando migração dos produtos do negócio {negocio_idx}")
        
        try:
            # 1. Buscar produtos no MySQL
            produtos_mysql = await self.buscar_produtos_mysql(negocio_idx)
            
            if not produtos_mysql:
                logger.warning(f"⚠️ Nenhum produto encontrado no MySQL para negócio {negocio_idx}")
                return
            
            # 2. Migrar cada produto
            produtos_migrados = 0
            produtos_erro = 0
            
            for produto_mysql in produtos_mysql:
                try:
                    # Converter propriedades para Neo4j
                    produto_neo4j = self.convert_to_neo4j_properties(produto_mysql)
                    
                    # Criar nó no Neo4j
                    resultado = self.criar_produto_neo4j(produto_neo4j)
                    
                    if resultado:
                        produtos_migrados += 1
                        logger.info(f"✅ Produto migrado: {produto_mysql.get('NOME', 'N/A')} (IDX: {produto_mysql.get('IDX', 'N/A')})")
                    else:
                        produtos_erro += 1
                        logger.error(f"❌ Erro ao criar nó para produto IDX: {produto_mysql.get('IDX', 'N/A')}")
                        
                except Exception as e:
                    produtos_erro += 1
                    logger.error(f"❌ Erro ao migrar produto {produto_mysql.get('IDX', 'N/A')}: {str(e)}")
            
            # 3. Relatório final
            logger.info(f"📊 Migração concluída:")
            logger.info(f"   ✅ Produtos migrados: {produtos_migrados}")
            logger.info(f"   ❌ Produtos com erro: {produtos_erro}")
            logger.info(f"   📋 Total processado: {len(produtos_mysql)}")
            
            return {
                "total_produtos": len(produtos_mysql),
                "produtos_migrados": produtos_migrados,
                "produtos_erro": produtos_erro,
                "sucesso": produtos_migrados > 0
            }
            
        except Exception as e:
            logger.error(f"❌ Erro geral durante a migração: {str(e)}")
            raise e
    
    def verificar_produtos_neo4j(self):
        """Verifica os produtos criados no Neo4j"""
        logger.info("🔍 Verificando produtos no Neo4j...")
        
        with self.neo4j_driver.session() as session:
            # Buscar produtos do negócio específico
            query = """
            MATCH (p:Produto) 
            WHERE p.negocio_idx = '4015743441'
            RETURN p.idx as idx, p.nome as nome, p.preco as preco, p.codigo as codigo
            ORDER BY p.nome
            """
            
            result = session.run(query)
            produtos = [record.data() for record in result]
            
            logger.info(f"📋 Produtos encontrados no Neo4j: {len(produtos)}")
            for produto in produtos:
                logger.info(f"   - {produto['nome']} (IDX: {produto['idx']}, Código: {produto['codigo']}, Preço: {produto['preco']})")
            
            return produtos
    
    def close(self):
        """Fecha conexões"""
        if hasattr(self, 'neo4j_driver'):
            self.neo4j_driver.close()
            logger.info("🔒 Conexão Neo4j fechada")

async def main():
    migrator = ProdutoMigrator()
    
    try:
        # Migrar produtos do negócio específico
        resultado = await migrator.migrar_produtos("4015743441")
        
        if resultado and resultado["sucesso"]:
            print("\n" + "="*50)
            print("🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!")
            print("="*50)
            print(f"📊 Total de produtos: {resultado['total_produtos']}")
            print(f"✅ Produtos migrados: {resultado['produtos_migrados']}")
            print(f"❌ Produtos com erro: {resultado['produtos_erro']}")
            
            # Verificar resultado no Neo4j
            print("\n" + "="*50)
            print("🔍 VERIFICANDO PRODUTOS NO NEO4J")
            print("="*50)
            produtos_neo4j = migrator.verificar_produtos_neo4j()
            
            if produtos_neo4j:
                print(f"✅ {len(produtos_neo4j)} produtos verificados no Neo4j")
            else:
                print("⚠️ Nenhum produto encontrado no Neo4j")
        else:
            print("❌ Migração falhou ou não foi executada")
            
    except Exception as e:
        print(f"❌ Erro durante a migração: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        migrator.close()

if __name__ == "__main__":
    asyncio.run(main()) 