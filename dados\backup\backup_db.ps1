# Script de backup do banco de dados MySQL
$date = Get-Date -Format "yyyy-MM-dd"
$backupFileName = "gptalkco_01_$date.sql"
$backupPath = "$PSScriptRoot\$backupFileName"

# Dados de conexão com o banco de dados
$mysqlHost = "*************"
$mysqlPort = "3306"
$mysqlUser = "gptalkco_01"
$mysqlPassword = "gcs@gptalkco01"
$mysqlDatabase = "gptalkco_01"

# Caminho para o executável do mysqldump
# Ajuste este caminho se o mysqldump estiver instalado em outro local
$mysqldump = "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe"
if (-not (Test-Path $mysqldump)) {
    $mysqldump = "mysqldump" # Tentar usar o que estiver no PATH
}

# Executar o backup
try {
    Write-Host "Iniciando backup do banco de dados $mysqlDatabase para $backupPath..."
    
    $cmd = "$mysqldump --host=$mysqlHost --port=$mysqlPort --user=$mysqlUser --password=$mysqlPassword --databases $mysqlDatabase > `"$backupPath`""
    
    # Usar Invoke-Expression com o shell para permitir o redirecionamento
    Invoke-Expression "cmd.exe /c `"$cmd`""
    
    if (Test-Path $backupPath) {
        $fileSize = (Get-Item $backupPath).Length / 1MB
        Write-Host "Backup concluído com sucesso! Tamanho do arquivo: $($fileSize.ToString('0.00')) MB"
    } else {
        Write-Host "Erro: O arquivo de backup não foi criado."
    }
} catch {
    Write-Host "Erro ao executar o backup: $_"
} 