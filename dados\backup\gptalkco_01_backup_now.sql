-- Backup da tabela CONTABIL_CONTA
-- Data: 2025-01-03

SET FOREIGN_KEY_CHECKS = 0;

-- <PERSON><PERSON><PERSON>tura da tabela CONTABIL_CONTA
DROP TABLE IF EXISTS `CONTABIL_CONTA`;
CREATE TABLE `CONTABIL_CONTA` (
  `ID` int(10) NOT NULL,
  `SISTEMA_ID` int(10) NOT NULL DEFAULT 0,
  `IDX_PAI` varchar(10) NOT NULL DEFAULT '0',
  `TIPO_ID` smallint(1) NOT NULL DEFAULT 0,
  `NIVEL` varchar(1) DEFAULT NULL,
  `NATUREZA_ID` varchar(1) DEFAULT NULL,
  `IDX` varchar(10) DEFAULT NULL,
  `CODIGO` varchar(30) DEFAULT NULL,
  `NOME` varchar(50) DEFAULT NULL,
  `NEGOCIO_IDX` varchar(10) DEFAULT NULL,
  `EXCLUIDO` smallint(1) NOT NULL DEFAULT 0,
  `SALDO_ANTERIOR` decimal(10,2) NOT NULL DEFAULT 0.00,
  `DEBITO` decimal(10,2) NOT NULL DEFAULT 0.00,
  `CREDITO` decimal(10,2) NOT NULL DEFAULT 0.00,
  `SALDO_ATUAL` decimal(10,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Inserir colunas padrão
INSERT INTO `CONTABIL_CONTA` 
(`ID`, `SISTEMA_ID`, `IDX_PAI`, `TIPO_ID`, `NIVEL`, `NATUREZA_ID`, `IDX`, `CODIGO`, `NOME`, `NEGOCIO_IDX`, `EXCLUIDO`) 
VALUES 
(1, 0, '0', 1, 'S', 'D', '2677151800', '1', 'ATIVO', '0068108573', 0),
(2, 0, '2677151800', 1, 'S', 'D', '2677151801', '1.1', 'ATIVO CIRCULANTE', '0068108573', 0),
(3, 0, '2677151801', 1, 'S', 'D', '2677151802', '1.1.1', 'DISPONIVEL', '0068108573', 0),
(4, 0, '2677151802', 1, 'S', 'D', '2677151803', '********', 'BANCOS', '0068108573', 0),
(5, 0, '2677151803', 1, 'A', 'D', '2677151804', '********.01', 'Banco Bradesco', '0068108573', 0);

SET FOREIGN_KEY_CHECKS = 1; 