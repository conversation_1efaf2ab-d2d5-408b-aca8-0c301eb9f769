import vertexai 
from .agent_secret import Secret



class GeminiVertex:
    def __init__(self):

        secret = Secret()
        PROJECT=  secret.GOOGLE_CLOUD_PROJECT        
        LOCATION= secret.GOOGLE_CLOUD_REGION 
        
        vertexai.init(project=PROJECT ,location=LOCATION)
       
        from vertexai.preview.generative_models import (
             FunctionDeclaration,
            GenerativeModel,
            Part,
            Content,
            Tool,
            ResponseValidationError
            )