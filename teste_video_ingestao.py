#!/usr/bin/env python3
"""
Script de teste para testar a função processa_e_ingere_video corrigida
"""

import sys
import os
import asyncio

# Adicionar o diretório do projeto ao PATH para permitir importações
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar a função corrigida
from api.functions.youtube import get_youtube_metadata
from api.functions.mcp_functions import mcp_neo4j_write_neo4j_cypher
from api.functions.util import generate_unique_id

async def processa_e_ingere_video_teste(url: str, usuario_id: str):
    """
    Versão de teste da função processa_e_ingere_video
    """
    print("===== processa_e_ingere_video_teste() =====")
    
    try:
        # PASSO 1: Extração de Conteúdo
        print("\n--- PASSO 1: Extraindo dados do vídeo ---")
        video_data = get_youtube_metadata(url)
        
        if video_data.get("erro"):
            print(f"❌ Falha ao obter metadados do vídeo: {video_data['erro']}")
            return
        
        print("video_data:", video_data)

        # Usar 'transcricao' ao invés de 'texto_completo'
        texto_para_analise = video_data.get("transcricao")
        print("texto_para_analise:", texto_para_analise)

        # Se não há transcrição, usar a descrição
        if not texto_para_analise:
            print("-> Aviso: Usando apenas a descrição do vídeo para análise.")
            texto_para_analise = video_data.get("descricao")

        if not texto_para_analise:
            print("❌ ERRO: Não foi possível obter nenhum conteúdo textual para análise.")
            return
        
        print(f"✅ Texto obtido para análise: {len(texto_para_analise)} caracteres")
        
        # PASSO 2: Salvar no Neo4j usando função MCP
        print("\n--- PASSO 2: Salvando dados no Neo4j ---")
        
        # Gerar IDX único para o vídeo
        video_idx = generate_unique_id()
        print(f"IDX único gerado para o vídeo: {video_idx}")
        
        # Criar estrutura de dados para salvar
        video_info = {
            "idx": video_idx,
            "titulo": video_data.get("titulo", "Título não disponível"),
            "descricao": video_data.get("descricao", "Descrição não disponível"),
            "transcricao": texto_para_analise,
            "url": url,
            "canal": video_data.get("nome_canal", "Canal não disponível"),
            "url_canal": video_data.get("url_canal", ""),
            "usuario_id": usuario_id
        }
        
        # Salvar usando a função MCP que já funciona
        query = """
        MERGE (v:Video {url: $url})
        SET v.idx = $idx,
            v.titulo = $titulo,
            v.descricao = $descricao,
            v.transcricao = $transcricao,
            v.canal = $canal,
            v.url_canal = $url_canal,
            v.data_ingesta = datetime(),
            v.usuario_id = $usuario_id
        RETURN v
        """
        
        result = mcp_neo4j_write_neo4j_cypher(query, video_info)
        
        print("\n--- ✅ Vídeo processado e salvo com sucesso no Neo4j ---")
        print(f"IDX: {video_idx}")
        print(f"Título: {video_info['titulo']}")
        print(f"Canal: {video_info['canal']}")
        print(f"Caracteres processados: {len(texto_para_analise)}")
        print(f"Resultado Neo4j: {result}")
        
    except Exception as e:
        print(f"\n❌ ERRO durante a execução: {e}")
        import traceback
        print(f"Traceback completo: {traceback.format_exc()}")

async def main():
    """Função principal de teste"""
    print("🚀 Iniciando teste da função processa_e_ingere_video")
    
    # URL de teste
    video_url_teste = "https://www.youtube.com/watch?v=4CZLASQsQgw"
    usuario_id_teste = "user_12345"
    
    await processa_e_ingere_video_teste(video_url_teste, usuario_id_teste)
    
    print("\n🏁 Teste concluído!")

if __name__ == "__main__":
    asyncio.run(main())
