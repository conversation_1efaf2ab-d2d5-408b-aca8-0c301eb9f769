import time
import secrets
from agents import function_tool
import requests
import httpx
import socket


def generate_unique_id():
    # Pega os últimos 5 dígitos do timestamp em milissegundos
    timestamp = int(time.time() * 1000) % 100000
    
    # Gera um número aleatório de até 5 dígitos
    random_part = secrets.randbelow(100000)
    
    # Combina timestamp e parte aleatória para formar um ID de 10 dígitos
    unique_id = f"{timestamp:05}{random_part:05}"
    return unique_id



@function_tool
async def generate_idx():
    return generate_unique_id()



async def cep_data(cep: str):
    """
    Valida e busca informações de um CEP usando a API ViaCEP.
    Retorna False se o CEP for inválido ou um dicionário com os dados do endereço se válido.
    """
    print("===== cep_data() =====" )
    print("cep",cep)
    url = f"https://viacep.com.br/ws/{cep}/json/"
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            if "erro" in data:
                return False
            return data
    except Exception as e:
        print(f"Erro ao consultar o CEP: {e}")
        return False

def is_local():
    """
    Detecta se está rodando localmente (localhost) ou em servidor (www)
    Verifica múltiplos indicadores para determinar se está em ambiente de desenvolvimento
    """
    import os
    
    # Verificar variáveis de ambiente que indicam desenvolvimento local
    if os.environ.get("ENVIRONMENT") == "development":
        return True
    if os.environ.get("DEBUG") == "True":
        return True
    
    # Verificar se está rodando via uvicorn localmente (porta padrão 8000)
    try:
        import psutil
        for conn in psutil.net_connections():
            if conn.laddr.port == 8000 and conn.laddr.ip in ["127.0.0.1", "0.0.0.0"]:
                return True
    except ImportError:
        pass
    
    # Verificações tradicionais de hostname e IP
    hostname = socket.gethostname()
    local_ips = ["127.0.0.1", "localhost"]
    try:
        host_ip = socket.gethostbyname(hostname)
    except Exception:
        host_ip = ""
    
    # Se o IP for de rede privada (192.168.x.x, 10.x.x.x, 172.16-31.x.x) e não estiver em produção
    if (host_ip.startswith("192.168.") or 
        host_ip.startswith("10.") or 
        any(host_ip.startswith(f"172.{i}.") for i in range(16, 32))):
        # Assumir que é local se não houver indicadores de produção
        return True
    
    is_local_result = host_ip in local_ips or host_ip.startswith("127.") or "local" in hostname.lower()
    return is_local_result


if __name__ == "__main__":
    import asyncio
    async def main():
        print("===== main() =====")
        result = await cep_data("31333333")
        print("result", result)

    asyncio.run(main())