from .agent_mysql import Mysql
from .agent_converter import Converter

from .agent_logger import Agent<PERSON>ogger
from fastapi import APIRouter, Query
from typing import Optional
import json

logger = AgentLogger()
router = APIRouter()

class Message:
    def __init__(self):
        self.ID = 0
        self.AGENTE_ID = 0
        self.USUARIO_ID = 0
        self.CONVERSA_IDX = 0
        self.CANAL_ID = 0
        self.DATA_HORA = None
        self.ENVIADO = None
        self.RECEBIDO = None
        self.RECEBIDO_TKS = 0
        self.ENVIADO = None
        self.ENVIADO_TKS = 0
        self.TOTAL_TKS = 0
        self.DATA_HORA =None
        self.LLM_MODELO = None
        self.FUNCAO_CHAMADA = 0
        self.historico = []
        self.mysql = Mysql()



    async def fetch_messages(self,columns:str,filters:list):
        #filters.append("FUNCAO_CHAMADA=0")
        messages = await self.mysql.fetch(columns,"MENSAGEM",filters,order="ID")
        self.historico = messages
        return messages


    async def add(self, message:dict):
        #logger.info(f"===== Message.add() =====")
        #logger.info(f"Salvando mensagem: {message}")
        
        result = await self.mysql.add("MENSAGEM", message)
        
        #logger.info(f"Resultado da inserção: {result}")
        return result


    async def add_log(self, dados:dict):
        mensagem = {}
        mensagem["DADOS"] =json.dumps(dados)
        result = await self.mysql.add("MENSAGEM_LOG",mensagem)
        return result


# ===== ENDPOINTS PARA O MONITOR DE MENSAGENS =====

@router.get("/agentes")
async def listar_agentes():
    """
    Lista todos os agentes disponíveis para o filtro do monitor
    """
    try:
        agent = Agent()
        agentes = await agent.listar_agentes_monitor()
        
        return {
            "status": "success",
            "agentes": agentes
        }
    except Exception as e:
        logger.error(f"Erro ao buscar agentes: {e}")
        return {
            "status": "error", 
            "error": str(e)
        }


@router.get("/monitor")
async def monitor_mensagens(
    agente_id: Optional[int] = Query(None, description="ID do agente"),
    usuario_idx: Optional[str] = Query(None, description="IDX do usuário"), 
    linhas: int = Query(50, description="Número de linhas"),
    data_inicio: Optional[str] = Query(None, description="Data início (YYYY-MM-DD)"),
    data_fim: Optional[str] = Query(None, description="Data fim (YYYY-MM-DD)"),
    hora_inicio: Optional[str] = Query(None, description="Hora início (HH:MM)"),
    hora_fim: Optional[str] = Query(None, description="Hora fim (HH:MM)")
):
    """
    Busca mensagens da VISAO_MENSAGEM com filtros aplicados
    """
    try:
        mysql = Mysql()
        
        # Base da query usando a VISAO_MENSAGEM
        query = """
        SELECT 
            ID,
            ENTRADA,
            SAIDA,
            ENVIADO,
            RECEBIDO,
            ENVIADO_TKS,
            RECEBIDO_TKS,
            TOTAL_TKS,
            CONVERSA_IDX,
            IMAGEM,
            USUARIO_NOME,
            AGENTE_NOME,
            NEGOCIO_NOME,
            AGENTE_ID,
            USUARIO_IDX,
            NEGOCIO_IDX,
            LLM_IDX
        FROM VISAO_MENSAGEM
        WHERE 1=1
        """
        
        filtros_aplicados = {}
        
        # Filtro por agente
        if agente_id is not None:
            query += f" AND AGENTE_ID = {agente_id}"
            filtros_aplicados["agente_id"] = agente_id
        
        # Filtro por usuário
        if usuario_idx:
            query += f" AND USUARIO_IDX = '{usuario_idx}'"
            filtros_aplicados["usuario_idx"] = usuario_idx
        
        # Filtros de data e hora
        if data_inicio:
            if hora_inicio:
                data_hora_inicio = f"{data_inicio} {hora_inicio}:00"
            else:
                data_hora_inicio = f"{data_inicio} 00:00:00"
            query += f" AND ENTRADA >= '{data_hora_inicio}'"
            filtros_aplicados["data_inicio"] = data_inicio
            if hora_inicio:
                filtros_aplicados["hora_inicio"] = hora_inicio
        
        if data_fim:
            if hora_fim:
                data_hora_fim = f"{data_fim} {hora_fim}:59"
            else:
                data_hora_fim = f"{data_fim} 23:59:59"
            query += f" AND ENTRADA <= '{data_hora_fim}'"
            filtros_aplicados["data_fim"] = data_fim
            if hora_fim:
                filtros_aplicados["hora_fim"] = hora_fim
        
        # Ordenação e limite
        query += f" ORDER BY ID DESC LIMIT {linhas}"
        
        logger.info(f"Query monitor: {query}")
        
        # Executar query
        mensagens = await mysql.query(query)
        
        # Calcular tempo de resposta para cada mensagem
        for mensagem in mensagens:
            if mensagem.get('ENTRADA') and mensagem.get('SAIDA'):
                try:
                    from datetime import datetime
                    entrada = datetime.strptime(str(mensagem['ENTRADA']), '%Y-%m-%d %H:%M:%S')
                    saida = datetime.strptime(str(mensagem['SAIDA']), '%Y-%m-%d %H:%M:%S')
                    tempo_resposta = (saida - entrada).total_seconds()
                    mensagem['TEMPO_RESPOSTA'] = f"{tempo_resposta:.2f}s"
                except:
                    mensagem['TEMPO_RESPOSTA'] = "N/A"
            else:
                mensagem['TEMPO_RESPOSTA'] = "N/A"
        
        return {
            "status": "success",
            "mensagens": mensagens,
            "total_encontradas": len(mensagens),
            "filtros_aplicados": filtros_aplicados,
            "parametros": {
                "linhas": linhas,
                "agente_id": agente_id,
                "usuario_idx": usuario_idx,
                "data_inicio": data_inicio,
                "data_fim": data_fim,
                "hora_inicio": hora_inicio,
                "hora_fim": hora_fim
            }
        }
        
    except Exception as e:
        logger.error(f"Erro no monitor de mensagens: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


import asyncio
async def main():
    msg = Message()
    conversa_IDX = 10
    agente_ID = 1

    result = await msg.fetch_history("*",[f"CONVERSA_IDX={conversa_IDX}",f"AGENTE_ID={agente_ID}"])


#asyncio.run(main())