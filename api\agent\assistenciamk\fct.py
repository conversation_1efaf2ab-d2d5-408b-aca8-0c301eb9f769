import os
import json
from agents.tool import function_tool
from ..agent_neo4j import AgentNeo4j
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
import unicodedata
import re
from ..business.functions.gera_cartao_produto import gera_cartao_produto
from ..assistenciamk.functions.produto_catalogo_adicionar import produto_catalogo_adicionar
from ..business.functions.gera_cartao_produto import gera_cartao_produto

# Inicializa o cliente Neo4j
neo4j = AgentNeo4j()

# Cria índices recomendados para melhorar desempenho
async def criar_indices_recomendados():
    """Cria índices recomendados para otimizar as consultas"""
    try:
        # Índice para busca rápida de produtos por negócio e nome
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_negocio_produto_nome IF NOT EXISTS 
            FOR (p:Produto) ON (p.nome)
            """
        )
        
        # Índice para busca rápida de produtos por negócio e código
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_negocio_produto_codigo IF NOT EXISTS 
            FOR (p:Produto) ON (p.codigo)
            """
        )
        
        # Índice para busca rápida de produtos excluídos
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_produto_excluido IF NOT EXISTS 
            FOR (p:Produto) ON (p.excluido)
            """
        )
        
        # Índice para busca rápida de cores por código
        await neo4j.execute_write_query(
            """
            CREATE INDEX idx_cor_codigo IF NOT EXISTS 
            FOR (c:Cor) ON (c.codigo)
            """
        )
        
        logger.info("Índices otimizados criados com sucesso")
    except Exception as e:
        logger.error(f"Erro ao criar índices: {str(e)}")

# Executa a criação dos índices ao importar o módulo
import asyncio
#asyncio.create_task(criar_indices_recomendados())

# Importar o esquema JSON do arquivo
with open(os.path.join(os.path.dirname(__file__), 'esquema_neo4j.json'), 'r', encoding='utf-8') as f:
    esquema_json = json.load(f)


neo4j = AgentNeo4j()


# ---------- schemas compatíveis com strict ----------
class QueryIn(BaseModel):
    model_config = {"extra": "forbid"}
    query: str
    parameters: str | None = None   # JSON string (evita dict)


class QueryOut(BaseModel):
    model_config = {"extra": "forbid"}
    rows: str                       # JSON string (evita dict/list)






@function_tool
async def atualiza_dados_mary_kay(params: QueryIn) -> QueryOut:
    """
    Executa uma query Cypher no Neo4j e retorna os resultados formatados.
    
    Args:
        params: Objeto QueryIn contendo a query e parâmetros
        
    Returns:
        QueryOut: Objeto contendo os resultados da query em formato JSON
    
    Executa atualizações de produtos e cores da Mary Kay no banco de dados neo4j usando cypher.
    
    🚨 REGRAS OBRIGATÓRIAS PARA ATUALIZAÇÕES 🚨
    
    ⚠️ FILTRO NEGOCIO OBRIGATÓRIO:
    - SEMPRE inclua o relacionamento com Negocio {idx: "5544332211"}
    - Todas as queries devem conter o filtro para o negócio Mary Kay
    
    ⚠️ EXCLUSÃO LÓGICA:
    - Para "excluir" produtos, use SET p.excluido = 1 (nunca DELETE)
    - Para "excluir" cores, use SET c.excluido = 1 (nunca DELETE)
    
    ⚠️ VALIDAÇÕES:
    - Sempre verifique se o produto/cor existe antes de atualizar
    - Use MATCH com WHERE para garantir que está atualizando o item correto
    
    EXEMPLOS DE QUERIES DE ATUALIZAÇÃO:
    
    📌 ATUALIZAR DADOS DE UM PRODUTO:
    MATCH (n:Negocio {idx: "5544332211"})-[:POSSUI_PRODUTO]->(p:Produto)
    WHERE p.codigo = "10142673" AND p.excluido = 0
    SET p.nome = "Base Líquida TimeWise 3D", p.preco = 89.90, p.preco_maior = 99.90
    RETURN p
    
    📌 ATUALIZAR COR DE UM PRODUTO:
    MATCH (n:Negocio {idx: "5544332211"})-[:POSSUI_PRODUTO]->(p:Produto)-[:TEM_COR]->(c:Cor)
    WHERE p.codigo = "10142673" AND c.codigo = "BEGE_MEDIO" AND c.excluido = 0
    SET c.nome = "Bege Médio Atualizado", c.hexadecimal = "#D2B48C"
    RETURN p, c
    
    📌 EXCLUIR PRODUTO (lógico):
    MATCH (n:Negocio {idx: "5544332211"})-[:POSSUI_PRODUTO]->(p:Produto)
    WHERE p.codigo = "10142673"
    SET p.excluido = 1
    RETURN p
    
    📌 CRIAR NOVO PRODUTO:
    MATCH (n:Negocio {idx: "5544332211"})
    CREATE (p:Produto {
        idx: "PROD_" + apoc.text.random(8, 'A-Z0-9'),
        nome: "Novo Produto",
        codigo: "12345678",
        preco: 79.90,
        preco_maior: 89.90,
        url: "https://marykay.com.br/produto/12345678",
        url_imagem: "https://marykay.com.br/imagem/12345678.jpg",
        excluido: 0
    })
    CREATE (n)-[:POSSUI_PRODUTO]->(p)
    RETURN p
    
    📌 ADICIONAR COR A UM PRODUTO:
    MATCH (n:Negocio {idx: "5544332211"})-[:POSSUI_PRODUTO]->(p:Produto)
    WHERE p.codigo = "10142673"
    CREATE (c:Cor {
        idx: "COR_" + apoc.text.random(6, 'A-Z0-9'),
        codigo: "NOVA_COR",
        nome: "Nova Cor",
        hexadecimal: "#FF0000",
        excluido: 0
    })
    CREATE (p)-[:TEM_COR]->(c)
    RETURN p, c

    ESQUEMA DO BANCO DE DADOS disponível na variável esquema_json
    {json.dumps(esquema_json, indent=4, ensure_ascii=False)}

    """
    try:
        # Executar a query de atualização usando o agente Neo4j
        resultado = await neo4j.execute_write_query(params.query, params.parameters)
        print("@@@@@@ RESULTADO ATUALIZAÇÃO NEO4J", resultado)
        
        if not resultado:
            resultado_dict = {
                "success": False,
                "message": "Nenhum dado foi atualizado",
                "data": [],
                "affected_rows": 0
            }
            return QueryOut(rows=json.dumps(resultado_dict, ensure_ascii=False))
        
        # Verificar se há erro na resposta
        if isinstance(resultado, list) and len(resultado) > 0 and isinstance(resultado[0], dict) and "erro" in resultado[0]:
            resultado_dict = {
                "success": False,
                "message": resultado[0]["erro"],
                "data": [],
                "affected_rows": 0
            }
            return QueryOut(rows=json.dumps(resultado_dict, ensure_ascii=False))
        
        resultado_dict = {
            "success": True,
            "message": "Atualização executada com sucesso",
            "data": resultado,
            "affected_rows": len(resultado)
        }
        return QueryOut(rows=json.dumps(resultado_dict, ensure_ascii=False))
        
    except Exception as e:
        resultado_dict = {
            "success": False,
            "message": f"Erro ao executar atualização: {str(e)}",
            "data": [],
            "affected_rows": 0
        }
        return QueryOut(rows=json.dumps(resultado_dict, ensure_ascii=False))


#criar uma sessao de execucao de tstes 
if __name__ == "__main__":
    import asyncio
    
    
    asyncio.run(testa_consultar_produtos())


