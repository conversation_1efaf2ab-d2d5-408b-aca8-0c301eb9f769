import re
import platform

from api.agent.agent import salva_mensagem
from ...agent_evolutionzap import AgentEvolutionZap
evolutionApi = AgentEvolutionZap()

import platform
from ...agent_secret import Secret


secret = Secret()

is_local = platform.system() == "Windows"
intancia_id = ""
if is_local:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID_LOCAL")
else:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID")

print("instancia_id", instancia_id)

# Função para singularizar e normalizar termos compostos (adaptada de gera_cartao_produto)
def processar_termos_compostos(termos):
    if not termos or not isinstance(termos, str):
        return termos

    termos = termos.strip()

    if ' AND ' in termos.upper() or ' OR ' in termos.upper():
        return termos

    if termos.isdigit():
        return termos

    palavras_ignoradas = {
        'de', 'da', 'do', 'das', 'dos', 'a', 'o', 'as', 'os', 'um', 'uma', 'uns', 'umas',
        'para', 'por', 'per', 'com', 'sem', 'sob', 'sobre', 'em', 'no', 'na', 'nos', 'nas',
        'a', 'à', 'às', 'ao', 'aos', 'pelo', 'pela', 'pelos', 'pelas', 'até', 'entre',
        'durante', 'contra', 'desde', 'após', 'antes', 'depois', 'como', 'que',
        'se', 'e', 'ou', 'mas', 'mais', 'muito', 'pouco', 'muitos', 'poucos', 'este',
        'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas', 'aquele', 'aquela',
        'aqueles', 'aquelas', 'meu', 'minha', 'meus', 'minhas', 'seu', 'sua', 'seus', 'suas'
    }

    palavras_invariaveis = {'jeans'}
    preservar_plural_diante_de = {'sapatos'}
    preposicoes_de = {'de', 'do', 'da', 'dos', 'das'}

    regras_pt = [
        (r'(ões)$', 'ão'),
        (r'(ães)$', 'ão'),
        (r'(is)$', 'l'),
        (r'(éis)$', 'el'),
        (r'(eis)$', 'el'),
        (r'(óis)$', 'ol'),
        (r'(uis)$', 'ul'),
        (r'(ns)$', 'm'),
        (r'(rs)$', 'r'),
        (r'(is)$', 'il'),
        (r'(?<!e)s$', ''),
    ]

    regras_en = [
        (r'(ies)$', 'y'),
        (r'(ves)$', 'f'),
        (r'(ses)$', 's'),
        (r'(s)$', ''),
    ]

    def singularizar_palavra(palavra):
        palavra_lower = palavra.lower()
        if palavra_lower in palavras_invariaveis:
            return palavra_lower
        for plural, singular in regras_pt:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        for plural, singular in regras_en:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)
        return palavra_lower

    palavras = [p.strip() for p in termos.split()]
    palavras_processadas = []

    for i, palavra in enumerate(palavras):
        p_lower = palavra.lower()
        prox = palavras[i + 1].strip().lower() if i + 1 < len(palavras) else ''
        if p_lower in preservar_plural_diante_de and prox in preposicoes_de:
            palavra_final = p_lower
        else:
            palavra_final = singularizar_palavra(palavra)
        if palavra_final not in palavras_ignoradas:
            palavras_processadas.append(palavra_final)

    if not palavras_processadas:
        return ""
    if len(palavras_processadas) == 1:
        return palavras_processadas[0]
    return ' AND '.join(palavras_processadas)
from agents.tool import function_tool
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()

#@function_tool
async def listar_produtos_estoque_revenda(
    params: str,
    usuario_whatsapp: str, 
    termo_consulta: str,
    mensagem_inicial: str,
    mensagem_final: str,
    conversa_idx: str,
    usuario_idx: str,
    agente_idx: str,
    em_resposta_idx: str,
    canal_idx: str
    ):
        """
        Lista ou mostra os produtos do estoque pessoal ou revenda
        
        Frases que acionam esta função:
        - listar meus produtos
        - listar meu estoque
        - mostre meus produtos
        - consultar meu estoque
        
        ⚠️⚠️⚠️ ATENÇÃO: ESTA FUNÇÃO NÃO DEVE SER USADA DURANTE O FLUXO DE COMPRA ⚠️⚠️⚠️
        
        Esta função deve ser usada APENAS para verificar o estoque PESSOAL do revendedor.
        NÃO use para consultar o catálogo geral de produtos.

        🚫 QUANDO NÃO USAR ESTA FUNÇÃO:
        - Durante o fluxo de compra de produtos (use consultar_catalogo_produtos em vez disso)
        - Para verificar disponibilidade antes de adicionar ao carrinho
        - Para consultar o catálogo geral de produtos
        - Para buscar informações técnicas sobre produtos
        
        ✅ QUANDO USAR ESTA FUNÇÃO:
        - Para verificar o que tem no seu estoque PESSOAL
        - Para verificar quantos itens VOCÊ tem disponíveis para venda
        - Para listar os produtos que VOCÊ está revendendo
        
        🔍 REGRAS IMPORTANTES:
        - NUNCA verifique o estoque durante o fluxo de compra
        - Ignore completamente a informação de estoque ao adicionar itens a uma compra
        - A informação de estoque zero NÃO deve impedir a adição de itens a uma compra
        - NÃO mostre mensagens sobre estoque zero durante o fluxo de compra
        
        🔍 REGRAS OBRIGATÓRIAS NA QUERY:
        - MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
        - MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
        - CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termo_consulta) YIELD node AS prod
        - MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
        - WHERE p.excluido <> 1
        - Retorna dados principais do produto e estoque da instância

        Parâmetros obrigatórios:
            - usuario_idx: ID do usuário revendedor
            - negocio_idx: ID do negócio (Mary Kay)
            - termo_consulta: termo de busca (ex: "batom AND matte")

        Exemplo de uso:
            params = '{"usuario_idx": "123", "negocio_idx": "456"}'
            verificar_estoque_revenda(params, termo_consulta="batom AND matte")
        """
        print("===== listar_produtos_estoque_revenda() =====")
        print(f"Params: {params}")
        print(f"Termo consulta (original): {termo_consulta}")

        termo_normalizado = processar_termos_compostos(termo_consulta)
        print(f"Termo consulta (normalizado): {termo_normalizado}")
        try:
            p = json.loads(params)
            usuario_idx = p.get("usuario_idx")
            negocio_idx = p.get("negocio_idx")
            if not usuario_idx or not negocio_idx:
                return {"status": "error", "message": "usuario_idx e negocio_idx obrigatórios"}

            # Detecta se é para listar tudo
            termo_tudo = not termo_normalizado or termo_normalizado.strip() == "" or termo_normalizado.lower() in ["todos", "todo", "tudo", "all", "*"]
            if termo_tudo:
                query = """
                MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
                MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
                MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
                OPTIONAL MATCH (prod)-[:TEM_COR]->(cor:Cor)
                WHERE p.excluido <> 1
                RETURN prod.codigo AS codigo, prod.nome AS nome, prod.preco AS preco, pr.estoque AS estoque, prod.descricao AS descricao, cor.nome AS cor , prod.url_imagem
                ORDER BY prod.nome
                """
                p.pop("termo_consulta", None)
            else:
                p["termo_consulta"] = termo_normalizado
                query = """
                MATCH (p:Pessoa {idx: $usuario_idx})-[:REALIZA_REVENDA]->(r:Revenda)
                MATCH (r)-[:REFERENTE]->(n:Negocio {idx: $negocio_idx})
                CALL db.index.fulltext.queryNodes('produtoNomeCodigoFT', $termo_consulta) YIELD node AS prod
                MATCH (r)-[:REVENDE]->(pr:ProdutoRevenda)-[:INSTANCIA_DE]->(prod)
                OPTIONAL MATCH (prod)-[:TEM_COR]->(cor:Cor)
                WHERE p.excluido <> 1
                RETURN prod.codigo AS codigo, prod.nome AS nome, prod.preco AS preco, pr.estoque AS estoque, prod.descricao AS descricao, cor.nome AS cor, prod.url_imagem
                ORDER BY prod.nome
                """
            print("@@@@@ query", query)
            print("@@@@@ params", p)

            resultado = await neo4j.execute_read_query(query, p)
            print("@@@@@ Resultado da consulta:", resultado)


            try:
                resultado2 = await enviar_mensagens_zap(
                    whatsapp=usuario_whatsapp,
                    mensagem_inicial=mensagem_inicial,
                    mensagem_final=mensagem_final,
                    dados=resultado,  # <<<<<<< CORRIGIDO: não é resultado["data"]
                    conversa_idx=conversa_idx,
                    usuario_idx=usuario_idx,
                    agente_idx=agente_idx,
                    em_resposta_idx=em_resposta_idx,
                    canal_idx=canal_idx
                )
                print("resultado2", resultado2)
            except Exception as e:
                print("❌ Erro ao chamar enviar_mensagens_zap:", e)
                import traceback
                traceback.print_exc()

            return {"status": "success", "data": resultado}
        except Exception as e:
            return {"status": "error", "message": str(e)}


def formata_whatsapp(item: dict) -> str:
    """Formatação rica com Markdown e emojis - só para WhatsApp.

    ===============================================================================
    EXEMPLO DE RESULTADO CORRETO:
    ===============================================================================
    {
      'nome': 'Batom X',
      'preco': 50.0,
      'estoque': 20,
      'variacoes': [
        {'codigo': '123', 'cor': 'Vermelho'},
        {'codigo': '124', 'cor': 'Azul'}
      ]
    }


    """

    #print("===== formatata_whatsapp() =====")
    #print("type(item)",type(item))
    #print("item",item)





    msg = f"*{item.get('nome', 'Produto')}*\n"
    if item.get('preco') == item.get('preco_maior') or item.get('preco_maior', 0) == 0:
        msg += f"💵 *Preço:* R$ {item['preco']:.2f}\n"
    elif item.get('preco') < item.get("preco_maior"):
        msg += f"🚨🥳💵  *Preço:*  De ~*R$ {item['preco_maior']:.2f}*~  por apenas  *R$ {item['preco']:.2f}*\n"
    msg += f"📦 *Estoque:* {item['estoque']}\n"    
    if item.get('variacoes'):
        msg += "\n*Cores disponíveis:*\n"
        for v in item['variacoes']:
            cor   = v.get('cor', 'Cor única')
            cod   = v.get('codigo', '')
            msg  += f"• {cor}"
            if cod:
                msg += f" (Código: {cod})"
            msg += "\n"
    return msg.strip()





async def enviar_mensagens_zap(whatsapp, mensagem_inicial, mensagem_final, dados, conversa_idx=None, usuario_idx=None, agente_idx=None, em_resposta_idx=None, canal_idx=None):
            print("🔍 [DEBUG] Iniciando enviar_mensagens_zap")
            print(f"📱 WhatsApp: {whatsapp}")
            print(f"💬 Mensagem inicial: {mensagem_inicial}")
            print(f"📝 Mensagem final: {mensagem_final}")
            print(f"📊 Dados recebidos: {type(dados)}")
            print(f"📊 Número de itens: {len(dados) if dados else 0}")
            global instancia_id
            
            if mensagem_inicial:
                print("📤 Enviando mensagem inicial...")
                try:
                    result = await evolutionApi.send_evolution_message(whatsapp, mensagem_inicial, instancia_id)
                    print(f"✅ Mensagem inicial enviada. Resultado: {result}")
                except Exception as e:
                    print(f"❌ Erro ao enviar mensagem inicial: {str(e)}")
                    raise

                #print("📩 mensagem inicial enviada", result)
            

            # ---------- DENTRO DA FUNÇÃO ----------
                # Após o for que envia mensagens, **salva histórico leve**
            print(f"🔄 Processando {len(dados)} itens...")
            for i, item in enumerate(dados, 1):
                try:
                    print(f"item {i}",item)
                    print(f"\n🔹 Processando item {i}/{len(dados)}")
                    print(f"   Código: {item.get('codigo', 'N/A')}")
                    print(f"   Nome: {item.get('nome', 'N/A')}")
                    print(f"   Preço: {item.get('preco', 'N/A')}")
                    print(f"   Estoque: {item.get('estoque', 'N/A')}")
                    # A URL da imagem está em item['prod.url_imagem'] devido ao retorno do Neo4j
                    url_imagem = item.get('prod.url_imagem')
                    print(f"   URL da imagem: {url_imagem if url_imagem else 'N/A'}")
                    print(f"   Variações: {item.get('variacoes', 'N/A')}")

                    # 1. Formatação rica para WhatsApp
                    print("   Formatando mensagem para WhatsApp...")
                    mensagem_whatsapp = formata_whatsapp(item)

                    # 2. Gera histórico leve
                    print("   Gerando histórico...")

                    # 3. Salva histórico
                    print("   Salvando mensagem no histórico...")
                    await salva_mensagem(
                        mensagem=mensagem_whatsapp,
                        conversa_idx=conversa_idx,
                        usuario_idx=usuario_idx,
                        agente_idx=agente_idx,
                        em_resposta_idx=em_resposta_idx,
                        remetente="agente",
                        destinatario="usuario",
                        canal_idx=canal_idx
                    )
                    print("   ✅ Histórico salvo com sucesso")

                    # 4. Envia mídia ou mensagem
                    if url_imagem:
                        print(f"   🖼️  Enviando imagem: {item['url_imagem']}")
                        result = await evolutionApi.send_evolution_media(
                            whatsapp,
                            url_imagem,
                            "image",
                            mensagem_whatsapp
                        )
                        print(f"   ✅ Mídia enviada. Resultado: {result}")
                    else:
                        print("   📝 Enviando mensagem de texto...")
                        result = await evolutionApi.send_evolution_message(
                            whatsapp,
                            mensagem_whatsapp,
                            instancia_id
                        )
                        print(f"   ✅ Mensagem de texto enviada. Resultado: {result}")

                except Exception as e:
                    print(f"❌ Erro ao processar o item {i}: {str(e)}")
                    # Decide se quer continuar o loop ou parar
                    # raise  # Descomente para parar a execução em caso de erro

            # Aguarda um pouco antes da mensagem final
            print("\n⏳ Aguardando 2 segundos...")
            await asyncio.sleep(2)
            
            # Envia mensagem final se existir
            if mensagem_final:
                print("\n📩 Enviando mensagem final...")
                try:
                    result = await evolutionApi.send_evolution_message(whatsapp, mensagem_final, instancia_id)
                    print(f"✅ Mensagem final enviada. Resultado: {result}")
                except Exception as e:
                    print(f"❌ Erro ao enviar mensagem final: {str(e)}")
            
            print("\n🏁 Todas as mensagens foram processadas")
            return {"status": "success", "message": "Mensagens enviadas com sucesso"}




if __name__ == "__main__":
    import asyncio

    async def testa_listar_produtos_estoque_revenda():
        params = '{"usuario_idx": "1122334455", "negocio_idx": "5544332211"}'
        # Exemplo de termo de consulta: busca por "batom AND matte"
        #resultado = await listar_produtos_estoque_revenda(params, termo_consulta="10221715")
        #resultado = await listar_produtos_estoque_revenda(params, termo_consulta="kit noite")
        resultado = await listar_produtos_estoque_revenda(
            usuario_whatsapp="553184198720", 
            mensagem_inicial="Aqui estão os produtos que encontrei em seu estoque:",
            mensagem_final="Caso precise de mais informações, só chamar.",
            conversa_idx="0987654321",
            usuario_idx="1122334455",
            agente_idx="1508250458",
            em_resposta_idx="0129001290",
            canal_idx="3245124231",
            params=params,
            termo_consulta="todos"
            
        )
        #print("Resultado da consulta:", json.dumps(resultado))

    asyncio.run(testa_listar_produtos_estoque_revenda())