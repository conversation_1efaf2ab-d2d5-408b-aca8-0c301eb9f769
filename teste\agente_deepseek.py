import asyncio
import os

from openai import Async<PERSON>penAI
from agents import Agent, OpenAIChatCompletionsModel, Runner, function_tool, set_tracing_disabled

# Configurações do DeepSeek
BASE_URL = "https://api.deepseek.com"
API_KEY = "***********************************"  # Substitua pela sua API key do DeepSeek
MODEL_NAME = "deepseek-chat"  # Pode usar "deepseek-reasoner" se preferir o DeepSeek-R1

# Verifica se as variáveis estão definidas
if not BASE_URL or not API_KEY or not MODEL_NAME:
    raise ValueError(
        "Please set BASE_URL, API_KEY, MODEL_NAME via env var or code."
    )

# Cria o cliente OpenAI para o DeepSeek
client = AsyncOpenAI(base_url=BASE_URL, api_key=API_KEY)
#set_tracing_disabled(disabled=True)

# Ferramenta para obter o clima
@function_tool
def get_weather(city: str):
    print(f"[debug] getting weather for {city}")
    return f"The weather in {city} is sunny."

# Função assíncrona principal
async def main():
    # Cria o agente que responde em haikus
    agent = Agent(
        name="Assistant",
        instructions="You only respond in haikus.",
        model=OpenAIChatCompletionsModel(model=MODEL_NAME, openai_client=client),
        tools=[get_weather],
    )

    # Executa o agente
    result = await Runner.run(agent, "What's the weather in Tokyo?")
    print("Resultado final:", result.final_output)

if __name__ == "__main__":
    asyncio.run(main())