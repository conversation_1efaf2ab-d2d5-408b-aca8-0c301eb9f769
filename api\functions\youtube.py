#geral
import asyncio
import json,re
import os
import time
from urllib.parse import urlparse, parse_qs
from youtube_transcript_api import YouTubeTranscript<PERSON>pi
from pytubefix import YouTube
from pytubefix.exceptions import PytubeFixError
from youtube_transcript_api import <PERSON><PERSON>ran<PERSON><PERSON><PERSON><PERSON>, NoTranscriptFound, TranscriptsDisabled

#local
#from text import count_tokens_openai, summarizer_gemini, summarizer_claude, summarizer_openai,translator_gemini, detect_idiom
#from mysql_functions import add_data

#app
from ..functions.text import  summarizer_gemini, summarizer_claude, summarizer_openai,translator_gemini, detect_idiom
#from ..functions.programming.database.my_sql import mysql_query,mysql_add_data


# @title youtube_getSummarization_map
#youtube_getSummarization
#https://colab.research.google.com/drive/1Iw7EPIMjafxzKSVAuwaMQCfCuY4JyTDC#scrollTo=s-T9ZGtRi7kX&line=1&uniqifier=1

#=======================================================
youtube_getSummarization_map = {
    "name":"youtube_getSummarization",
    "description":
    " O objetivo desta função é obter a sumarização  de um video do youtube."
    "Deverá ser retornado um objeto com as propriedades: resumo, titulo."
    "O resumo deverá conter o titulo do video na primeira linha, ja traduzido   para o idioma solicitado, e a sumarização do video na segunda linha. Não incluir a url do video na resposta."
    
    ,
    "parameters" : {
        "type": "object",
        "properties": {
            "url": {
                "type": "string",
                "description" : "url válida de um video do youtube",
             },
            "idiom": {
                "type": "string",
                "description" : "idioma para tradução do titulo e da sumarização pela função."
                "se não informado, o titulo e a sumarização serão retornados no idioma original e assim devem ser utilizados,"
                "sem nenhuma tradução adicional",
             },
            },
          "required": ["url"],
       }

}
#==================================================================
def is_json_valid(s):
    try:
        json_object = json.loads(s)
    except ValueError as e:
        return False
    return True

#==================================================================
def create_youtube_with_retry(url: str, max_retries: int = 3, delay: float = 2.0):
    """
    Cria um objeto YouTube com retry logic para lidar com rate limits.
    
    Args:
        url: A URL do vídeo do YouTube
        max_retries: Número máximo de tentativas
        delay: Delay entre tentativas (em segundos)
    
    Returns:
        Objeto YouTube ou None se falhar após todas as tentativas
    """
    for attempt in range(max_retries):
        try:
            print(f"Tentativa {attempt + 1}/{max_retries} de conectar ao YouTube...")
            yt = YouTube(url, client='WEB', use_po_token=True)
            # Força a inicialização dos metadados
            _ = yt.title  # Isso força o download dos dados
            print("Conexão estabelecida com sucesso!")
            return yt
        except Exception as e:
            error_msg = str(e)
            print(f"Tentativa {attempt + 1} falhou: {error_msg}")
            
            # Verifica se é um erro de rate limit (429)
            if "429" in error_msg or "Too Many Requests" in error_msg:
                if attempt < max_retries - 1:
                    wait_time = delay * (2 ** attempt)  # Exponential backoff
                    print(f"Rate limit detectado. Aguardando {wait_time:.1f} segundos...")
                    time.sleep(wait_time)
                    continue
            
            # Para outros erros, tenta uma vez mais com delay menor
            elif attempt < max_retries - 1:
                print(f"Aguardando {delay} segundos antes da próxima tentativa...")
                time.sleep(delay)
                continue
            
            # Se chegou aqui, esgotou todas as tentativas
            print(f"Todas as tentativas falharam. Último erro: {error_msg}")
            return None
    
    return None
#=======================================================
async def youtube_getSummarization (args):
 print("----- youtube_getSummarization -----")
  #print("args",args)
 response = ""
 summary = ""
 idiom = ""
 if "idiom" in args:
  idiom = args["idiom"]
  
 
 useCredits = args["useCredits"]
 credits = args["credits"]
 print("useCredits",useCredits)
 print("credits",credits)
 
 print("idiom",idiom)
  #print("useCredits",useCredits)
  #print("credits",credits)
  #print("url",url)
  #print("videoID",videoID)
  #print("transcript",transcript);
  #print("textTranscript",textTranscript);
  #print("totalTokens",totalTokens);
  #print("response",response)

 url = args["url"]
 videoID = youtube_get_video_ID(url)

 title, thumbnail_url , description = await youtube_info(url)
 print("url",url)
 print("videoID",videoID)
 print(f"Título do vídeo: {title}")
 print(f"URL da thumbnail: {thumbnail_url}")

 transcript = await youtube_getTranscript(videoID)
 #print("transcript",transcript);
 textTranscript = ' '.join(item['text'] for item in transcript)
 #print("textTranscript",textTranscript);
 totalTokens  = count_tokens_openai(textTranscript)
 print("totalTokens",totalTokens);

 if useCredits==True:
  print("useCredits neste momento é true ",useCredits)
  if credits < totalTokens:
   response = "Creditos insuficientes para sumarizar o video"
   print("creditos insuficientes")
   return response

  if totalTokens <= 110000:
         print("vou sumarizar usando a openai")
         # Se o número total de tokens for menor ou igual a 110.000
         # Utiliza a função summarizer_openai para gerar o resumo
         summary = await summarizer_openai(textTranscript, 2)
  else:
         print("vou sumarizar usando o claude")
         # Se o número total de tokens for maior que 110.000
         # Utiliza a função summarizer_claude para gerar o resumo
         summary = await summarizer_claude(textTranscript, 2)
 else:
    
     print("nao usar credito")
     # Se a variável usarCredito for falsa
     if totalTokens <= 20000:
         print("vou sumarizar usando a gemini")

         # Se o número total de tokens for menor ou igual a 20.000
         # Utiliza a função summarizer_gemini para gerar o resumo
         summary = await summarizer_gemini(textTranscript, 2)
     else:
        print
        # Se o número total de tokens for maior que 20.000
        # Utiliza a função summarizer_claude para gerar o resumo
        summary = await summarizer_claude(textTranscript, 2)

 print("Summary",summary)

 if(is_json_valid(summary)):
  #print("summary json  valido")
  summary =  json.loads(summary)
  summary["titulo"] = title
 else: 
  print("summary json  invalido")
  response = "Erro ao sumarizar o video. Solicite novamente"
  return response

 if idiom:
  #print("idiom",args["idiom"])
  #print("vou traduzir o summary")
  if idiom != detect_idiom(summary["resumo"]):
   
   summary["resumo"] = await translator_gemini(summary["resumo"],args["idiom"])
   #print("summary traduzido",summary["resumo"])
  if idiom != detect_idiom(summary["titulo"]):
   summary["titulo"] = await translator_gemini(summary["titulo"],args["idiom"])
   #print("titulo traduzido",summary["titulo"])
 print("description", description)
 conteudo = {
      "USUARIO_ID": args["usuario_id"],
        "CONTEUDO_ID": videoID,
        "TITULO": title,
        "DESCRICAO": description,
        "RESUMO": summary["titulo"] + "\n" + summary["resumo"],
        "INTEGRA": textTranscript,
        "TRANSCRICAO": json.dumps(transcript),
        "URL": url,
        "TIPO_ID": 1,
        "CAPA_URL": thumbnail_url
 }
 
 id_conteudo = mysql_add_data(conteudo, "CONTEUDO")
 print ("id_conteudo",id_conteudo)

 return "** Titulo:" + summary["titulo"] + "** " + "\n" +  summary["resumo"]

#========================""===============================
def youtube_get_video_ID(url):
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    if 'v' in query_params:
        video_id = query_params['v'][0]
        return video_id
    else:
        return None
    
#=======================================================
async def youtube_getTranscript(videoID):
  print("----- youtube_getTranscript -----" , videoID)
  global transcript, textTranscript
  transcript = YouTubeTranscriptApi.get_transcript(videoID)
  #textTranscript = ' '.join(item['text'] for item in transcript)
  return transcript


#=======================================================
async def youtube_info(url):
    #print("----- youtube_info -----", url)
    try:
        # Criar um objeto YouTube com a URL do vídeo usando WEB client e PoToken
        video = YouTube(url, client='WEB', use_po_token=True)
        print("video",video)
        
        # Obter o título do vídeo
        title = video.title
        
        # Obter a URL da imagem da capa (thumbnail)
        thumbnail_url = video.thumbnail_url

        description = video.description
        #print("video.description",video.description)
        
        return title, thumbnail_url, description
    except Exception as e:
        print(f"Ocorreu um erro: {e}")
        return None, None, None

#=======================================================
async def youtube_data(link):
    print("youtube_data", youtube_data)
    try:
        # Cria um objeto YouTube passando o link do vídeo usando WEB client e PoToken
        yt = YouTube(link, client='WEB', use_po_token=True)

        # Obtém o título do vídeo
        title = yt.title
        print("title",title)
        # Obtém o link da miniatura do vídeo
        link_thumb = yt.thumbnail_url
        print("link_thumb", link)
        return {'titulo':title,'capa': link_thumb}
    except Exception as e:
        return {'erro': str(e)}

def youtube_validate_url(url: str) -> bool:
    """
    Valida se a URL fornecida corresponde a um padrão de URL do YouTube.
    Aceita formatos padrão (youtube.com/watch?v=), curtos (youtu.be/), embeds, canais e playlists.

    Formatos suportados:
    - https://www.youtube.com/watch?v=VIDEO_ID
    - https://youtu.be/VIDEO_ID
    - https://www.youtube.com/embed/VIDEO_ID
    - https://www.youtube.com/v/VIDEO_ID
    - https://youtube.com/watch?v=VIDEO_ID (sem www)
    - https://m.youtube.com/watch?v=VIDEO_ID (mobile)
    - URLs com parâmetros adicionais (&t=, &list=, etc.)
    """
    if not url or not isinstance(url, str):
        return False

    # Padrões de URL do YouTube mais abrangentes
    youtube_patterns = [
        # Padrão principal: youtube.com/watch?v=
        r'^(https?://)?(www\.|m\.)?youtube\.com/watch\?.*v=([a-zA-Z0-9_-]{11}).*$',

        # Formato curto: youtu.be/
        r'^(https?://)?youtu\.be/([a-zA-Z0-9_-]{11})(\?.*)?$',

        # Embed: youtube.com/embed/
        r'^(https?://)?(www\.)?youtube\.com/embed/([a-zA-Z0-9_-]{11})(\?.*)?$',

        # Formato v: youtube.com/v/
        r'^(https?://)?(www\.)?youtube\.com/v/([a-zA-Z0-9_-]{11})(\?.*)?$',

        # YouTube nocookie
        r'^(https?://)?(www\.)?youtube-nocookie\.com/embed/([a-zA-Z0-9_-]{11})(\?.*)?$',
    ]

    # Testa cada padrão
    for pattern in youtube_patterns:
        if re.match(pattern, url.strip()):
            return True

    return False

def get_youtube_metadata(url: str) -> dict:
    """
    Extrai metadados de um vídeo do YouTube a partir de sua URL.

    Args:
        url: A URL do vídeo do YouTube.

    Returns:
        Um dicionário com os metadados ('titulo', 'descricao', 'nome_canal',
        'url_canal', 'url_video') ou um dicionário de erro.
    """
    print(f"Iniciando processamento para a URL: {url}")

    # Passo 1: Validar a URL
    if not youtube_validate_url(url):
        print("Erro: URL do YouTube inválida.")
        return {"erro": "URL do YouTube inválida."}

    try:
        # Passo 2: Criar o objeto YouTube e extrair os dados com retry logic
        yt = create_youtube_with_retry(url)
        if not yt:
            return {"erro": "Falha ao conectar ao YouTube após múltiplas tentativas", "success": False}

        print("Verificando disponibilidade do vídeo...")
        # Forçar o download dos dados do vídeo
        yt.check_availability()

        print("Extraindo metadados...")
        titulo = yt.title or "Título não disponível"
        descricao = yt.description or "Descrição não disponível"
        nome_canal = yt.author or "Canal não disponível"
        url_canal = yt.channel_url or "URL do canal não disponível"

        print(f"Metadados extraídos com sucesso para o vídeo: '{titulo}'")
        transcricao = ""
        # Passo 3: Extrair ID do vídeo e transcrição com youtube-transcript-api
        try:
            video_id = youtube_get_video_ID(url)
            if video_id:
                transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=['pt', 'en', 'es'])
                # Concatena todos os textos da transcrição em um único bloco
                transcricao = " ".join([item['text'] for item in transcript_list])
                print("Transcrição extraída com sucesso.")
            else:
                print("Aviso: Não foi possível extrair o ID do vídeo.")
        except (NoTranscriptFound, TranscriptsDisabled):
            print("Aviso: Nenhuma transcrição encontrada para este vídeo. O campo 'texto_completo' ficará vazio.")
        except Exception as e:
            print(f"Um erro inesperado ocorreu ao buscar a transcrição: {e}")



        

        # Monta o dicionário de retorno
        metadata = {
            "titulo": titulo,
            "descricao": descricao,
            "transcricao": transcricao,
            "nome_canal": nome_canal,
            "url_canal": url_canal,
            "url_video": url,
            "success": True
        }
        return metadata

    except PytubeFixError as e:
        # Captura erros específicos do pytubefix (vídeo privado, indisponível, etc.)
        error_msg = f"Erro do PyTubeFix: {str(e)}"
        print(error_msg)
        return {"erro": error_msg, "success": False}
    except Exception as e:
        # Captura outros erros inesperados (problemas de rede, etc.)
        error_msg = f"Erro inesperado: {str(e)}"
        print(error_msg)
        return {"erro": error_msg, "success": False}



#=======================================================
def test_youtube_validate_url():
    """
    Função de teste para validar a função youtube_validate_url
    """
    # URLs válidas
    valid_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtube.com/watch?v=dQw4w9WgXcQ",
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://www.youtube.com/embed/dQw4w9WgXcQ",
        "https://www.youtube.com/v/dQw4w9WgXcQ",
        "https://youtube-nocookie.com/embed/dQw4w9WgXcQ",
        "http://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "www.youtube.com/watch?v=dQw4w9WgXcQ",
        "youtube.com/watch?v=dQw4w9WgXcQ",
        "youtu.be/dQw4w9WgXcQ",
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
        "https://youtu.be/dQw4w9WgXcQ?t=30",
    ]

    # URLs inválidas
    invalid_urls = [
        "",
        None,
        "https://vimeo.com/123456789",
        "https://www.youtube.com/",
        "https://www.youtube.com/watch",
        "https://www.youtube.com/watch?v=",
        "https://www.youtube.com/watch?v=invalid",
        "https://youtu.be/",
        "https://youtu.be/invalid",
        "not_a_url",
        "https://fake-youtube.com/watch?v=dQw4w9WgXcQ",
    ]

    print("Testando URLs válidas:")
    for url in valid_urls:
        result = youtube_validate_url(url)
        print(f"  {url} -> {result}")
        assert result == True, f"URL válida rejeitada: {url}"

    print("\nTestando URLs inválidas:")
    for url in invalid_urls:
        result = youtube_validate_url(url)
        print(f"  {url} -> {result}")
        assert result == False, f"URL inválida aceita: {url}"

    print("\n✅ Todos os testes passaram!")

#=======================================================
async def main():
  #print("----- main() -----")
  # Teste da função de validação
  test_youtube_validate_url()

  # Código original comentado
  #args = {}
  #args["url"] = "https://www.youtube.com/watch?v=4CZLASQsQgw"
  #args["idiom"] = "espanhol"
  #args["useCredits"] = False
  #args["credits"] = 10
  #args["usuario_id"] = "x1z2y3"
  #await youtube_getSummarization(args)

def youtube_audio_download(url: str, output_path: str = "temp_audio.mp3") -> str | None:
    """
    Baixa a faixa de áudio de um vídeo do YouTube e a converte para MP3.

    Args:
        url: A URL do vídeo do YouTube.
        output_path: O caminho para salvar o arquivo MP3.

    Returns:
        O caminho do arquivo MP3 se for bem-sucedido, senão None.
    """
    print(f"--- Iniciando Download do Áudio de: {url} ---")
    try:
        yt = YouTube(url, client='WEB', use_po_token=True)
        
        # Filtra para pegar a melhor stream de áudio disponível (geralmente .mp4 ou .webm)
        audio_stream = yt.streams.filter(only_audio=True).first()
        if not audio_stream:
            print("Erro: Nenhuma stream de áudio encontrada.")
            return None

        print(f"-> Baixando áudio do vídeo '{yt.title}'...")
        temp_audio_file = audio_stream.download(filename_prefix="temp_")
        
        print(f"-> Convertendo para MP3...")
        # Converte o áudio baixado para o formato MP3 usando pydub
        audio = AudioSegment.from_file(temp_audio_file)
        audio.export(output_path, format="mp3")
        
        # Limpa o arquivo temporário
        os.remove(temp_audio_file)
        
        print(f"-> Áudio salvo com sucesso em: {output_path}")
        return output_path

    except Exception as e:
        print(f"Ocorreu um erro no download ou conversão do áudio: {e}")
        return None