from validate_docbr import CPF,CNPJ
import re


async def cpf_cnpj_valido(cpf_cnpj):
    """Verifica se um CPF ou CNPJ é válido, removendo a máscara se houver."""
    # Remove caracteres não numéricos
    numeros = re.sub(r'[^0-9]', '', cpf_cnpj)

    if len(numeros) == 11:
        return CPF().validate(numeros)
    elif len(numeros) == 14:
        return CNPJ().validate(numeros)
    else:
        return False
    
    

if __name__ == "__main__":
    print(cpf_cnpj_valido("50518151549"))
    print(cpf_cnpj_valido("37753978000134"))
    print(cpf_cnpj_valido("505.181.515-49"))
    print(cpf_cnpj_valido("37.753.978/0001-34"))