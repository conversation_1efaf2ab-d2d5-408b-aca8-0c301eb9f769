from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j

neo4j = AgentNeo4j()


@function_tool
async def atualiza_cor_produto(negocio_idx: str, produto_codigo: str, cor_nome: str) -> dict:
    """
    Atualiza a COR de um produto no Neo4j removendo todas as cores anteriores e associando uma nova.

    Importante:
    - O LLM NÃO DEVE enviar a query. Esta função usa um script Cypher FIXO, variando apenas os parâmetros.
    - Remove todas as relações (p)-[:TEM_COR]->(:Cor) antes de associar a nova cor.
    - A nova cor é buscada pelo NOME informado, com processamento de termos (AND) e busca fonética via fulltext index.

    Parâmetros (explícitos na assinatura) que o LLM deve enviar:
      - negocio_idx: string (obrigatório)
      - produto_codigo: string (obrigatório). codigo do produto
      - cor_nome: string (obrigatório). nome da cor (pode ter variações; a busca é robusta por termos AND + fonética)
      
    Observação: não envie 'query' nem 'params'. Apenas os três argumentos acima.

    Exemplo do script Cypher fixo utilizado internamente:
      MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})
      WHERE coalesce(p.excluido, 0) <> 1
      OPTIONAL MATCH (p)-[rel:TEM_COR]->(:Cor)
      DELETE rel
      WITH p
      // Busca a cor por nome de forma robusta (fulltext index + termos AND + fonética)
      // 1) Resolve a cor via fulltext e pega o primeiro resultado
      CALL db.index.fulltext.queryNodes("corNomeCodigoFT", $termos_cor) YIELD node AS c, score
      MATCH (n)-[:DISPONIBILIZA_COR]->(c)
      MERGE (p)-[:TEM_COR]->(c)
      RETURN p.nome AS produto, c.nome AS nova_cor
    """
    print("@@@@@@@@@@ atualiza_cor_produto() =====")
    # Utilitários locais: processamento de termos e fonética, semelhantes aos usados em gera_cartao_produto
    def _processar_termos_compostos(termos: str) -> str:
        import re
        if not termos or not isinstance(termos, str):
            return termos
        termos = termos.strip()
        if ' AND ' in termos.upper() or ' OR ' in termos.upper():
            return termos
        if termos.isdigit():
            return termos
        palavras_ignoradas = {
            'de','da','do','das','dos','a','o','as','os','um','uma','uns','umas','para','por','per','com','sem','sob','sobre','em','no','na','nos','nas','a','à','às','ao','aos','pelo','pela','pelos','pelas','até','entre','durante','contra','desde','após','antes','depois','como','que','se','e','ou','mas','mais','muito','pouco','muitos','poucos','este','esta','estes','estas','esse','essa','esses','essas','aquele','aquela','aqueles','aquelas','meu','minha','meus','minhas','seu','sua','seus','suas'
        }
        regras_pt = [
            (r'(ões)$','ão'),(r'(ães)$','ão'),(r'(is)$','l'),(r'(éis)$','el'),(r'(eis)$','el'),(r'(óis)$','ol'),(r'(uis)$','ul'),(r'(ns)$','m'),(r'(rs)$','r'),(r'(is)$','il'),(r'(?<!e)s$','')
        ]
        regras_en = [(r'(ies)$','y'),(r'(ves)$','f'),(r'(ses)$','s'),(r'(s)$','')]
        def singularizar(p: str) -> str:
            pl = p.lower()
            for rx, rep in regras_pt:
                if re.search(rx, pl):
                    return re.sub(rx, rep, pl)
            for rx, rep in regras_en:
                if re.search(rx, pl):
                    return re.sub(rx, rep, pl)
            return pl
        palavras = [w.strip() for w in termos.split()]
        saida = []
        for w in palavras:
            wl = singularizar(w)
            if wl and wl not in palavras_ignoradas:
                saida.append(wl)
        if not saida:
            return ""
        if len(saida) == 1:
            return saida[0]
        return ' AND '.join(saida)

    def _aplicar_busca_fonetica(termos: str, distancia: int = 2) -> str:
        import re
        if not termos or not isinstance(termos, str):
            return termos
        tokens = re.findall(r'"[^"]*"|\(|\)|\bAND\b|\bOR\b|\bNOT\b|[^\s()]+', termos)
        sufixo = f'~{distancia}' if distancia is not None else '~'
        out = []
        for t in tokens:
            if t in ('AND','OR','NOT','(',')'):
                out.append(t); continue
            if t.startswith('"') and t.endswith('"'):
                out.append(t); continue
            if ':' in t:
                out.append(t); continue
            if t.isdigit():
                out.append(t); continue
            if not re.search(r'~\d*$', t):
                out.append(f"{t}{sufixo}")
            else:
                out.append(t)
        res = ' '.join(out)
        res = re.sub(r'\(\s+', '(', res)
        res = re.sub(r'\s+\)', ')', res)
        return res

    try:
        # Monta dicionário de parâmetros a partir dos argumentos explícitos
        p = {
            "negocio_idx": negocio_idx,
            "produto_codigo": produto_codigo,
        }

        # Validações finais
        if p.get("negocio_idx") is None:
            return {"status": "error", "message": "negocio_idx obrigatório"}
        if not p.get("produto_codigo"):
            return {"status": "error", "message": "'produto_codigo' é obrigatório"}
        if not cor_nome:
            return {"status": "error", "message": "'cor_nome' é obrigatório"}

        print("args", p)

        # 1) Verifica se o produto existe
        busca_produto_query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})
            WHERE coalesce(p.excluido, 0) <> 1
            RETURN p
        """
        print("***** busca_produto_query:\n", busca_produto_query)
        produto_result = await neo4j.execute_read_query(busca_produto_query, p)
        if not produto_result or (isinstance(produto_result, list) and len(produto_result) == 0):
            return {"status": "error", "message": "Produto não encontrado"}
        print ("*produto_result:", produto_result)

        # 1.1) Resolver a cor pelo NOME informado (busca por termos + fonética) e obter o codigo
        termos_cor = _processar_termos_compostos(cor_nome)
        termos_cor = _aplicar_busca_fonetica(termos_cor, distancia=2)
        print("***** termos_cor:", termos_cor)

        busca_cor_ft_query = """
            MATCH (n:Negocio {idx: $negocio_idx})
            CALL db.index.fulltext.queryNodes("corNomeCodigoFT", $termos_cor) YIELD node AS c, score
            MATCH (n)-[:DISPONIBILIZA_COR]->(c)
            WHERE coalesce(c.excluido, 0) <> 1
            RETURN c.codigo AS codigo, c.nome AS nome, score
            ORDER BY score DESC, size(nome) ASC
            LIMIT 1
        """
        print("***** busca_cor_ft_query:\n", busca_cor_ft_query)
        cor_row = await neo4j.execute_read_query(busca_cor_ft_query, {**p, "termos_cor": termos_cor})
        print("* cor_row:", cor_row)
        if not cor_row:
            return {"status": "error", "message": "Cor não encontrada pelo nome informado"}
        # Normaliza formato de retorno
        if isinstance(cor_row, list):
            cor_info = cor_row[0]
        else:
            cor_info = cor_row
        cor_codigo = cor_info.get("codigo") if isinstance(cor_info, dict) else None
        if not cor_codigo:
            return {"status": "error", "message": "Cor não encontrada (sem código)"}
        p["cor_codigo"] = cor_codigo
        print("* cor_codigo resolvido:", cor_codigo)

        
   
        
        # 2) Conta relações atuais
        conta_rels_query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})-[rel:TEM_COR]->(:Cor)
            WHERE coalesce(p.excluido, 0) <> 1
            RETURN count(rel) AS qtd
        """
        print("***** conta_rels_query:\n", conta_rels_query)
        rels_count = await neo4j.execute_read_query(conta_rels_query, p)
        print("* Relações TEM_COR atuais:", rels_count)

        # 3) Remove relações de cor existentes
        remove_cores_query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})-[rel:TEM_COR]->(:Cor)
            WHERE coalesce(p.excluido, 0) <> 1
            DELETE rel
        """
        print("***** remove_cores_query (DELETE):\n", remove_cores_query)
        await neo4j.execute_write_query(remove_cores_query, p)

        # 4) Adiciona a nova relação de cor (por código exato resolvido acima)
        adiciona_cor_query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})
            WHERE coalesce(p.excluido, 0) <> 1
            MATCH (c:Cor {codigo: $cor_codigo})
            MERGE (p)-[:TEM_COR]->(c)
            RETURN p.nome AS produto, c.nome AS nova_cor
        """
        print("***** adiciona_cor_query (MERGE) por código:", p.get("cor_codigo"))
        print("query",adiciona_cor_query)
        print("params", p)
        result = await neo4j.execute_write_query(adiciona_cor_query, p)
        print ("*result:", result)
        if not result:
            return {"status": "error", "message": "Cor não encontrada para o 'cor_codigo' informado"}

        # 5) Confirma quantas relações ficaram
        conta_rels_pos_query = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p:Produto {codigo: $produto_codigo})-[:TEM_COR]->(:Cor)
            WHERE coalesce(p.excluido, 0) <> 1
            RETURN count(*) AS qtd
        """
        print("***** conta_rels_pos_query:\n", conta_rels_pos_query)
        rels_after = await neo4j.execute_read_query(conta_rels_pos_query, p)
        print("* Relações TEM_COR após atualização:", rels_after)
        try:
            qtd_after = rels_after[0].get("qtd", 0) if isinstance(rels_after, list) and rels_after else 0
        except Exception:
            qtd_after = 0
        if qtd_after == 0:
            return {"status": "error", "message": "Não foi possível associar a cor ao produto (verificação pós-atualização falhou)"}

        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
   import asyncio

   async def testa_atualiza_cor_produto():
      # Exemplo: agora passamos os 3 parâmetros explicitamente (sem query)
      # O decorator function_tool retorna um objeto, use .func para acessar a função original
      result = await atualiza_cor_produto.func(
         negocio_idx="5544332211",
         produto_codigo="10127613",
         cor_nome="Dark Blond Sobrancelhas",
      )
      print(result)

   asyncio.run(testa_atualiza_cor_produto())
   # Execução:
   # python -m api.agent.assistenciamk.functions.atualiza_cor_produto