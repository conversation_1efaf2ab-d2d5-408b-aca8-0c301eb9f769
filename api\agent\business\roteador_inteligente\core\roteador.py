# core/roteador.py
import logging
from typing import Dict, Any
from .classificador import ClassificadorQuery
from ..handlers.produto_handler import ProdutoHandler
from ..handlers.lista_cores_handler import ListaCoresHandler
from ..handlers.lista_cliente_handler import ListaClientesHandler

class RoteadorPrincipal:
    def __init__(self, db_connection):
        self.classificador = ClassificadorQuery()
        self.handlers = {
            'produto_codigo': <PERSON>du<PERSON><PERSON><PERSON><PERSON>(db_connection),
            'produto_categoria': <PERSON>du<PERSON><PERSON><PERSON><PERSON>(db_connection),
            'produto_visual': <PERSON>du<PERSON><PERSON><PERSON><PERSON>(db_connection),
            'lista_cores': ListaCoresHandler(db_connection),
            'lista_clientes': ListaClientes<PERSON>andler(db_connection),
            'analise_dados': None  # Redireciona para agente
        }
        
        self.logger = logging.getLogger(__name__)
    
    def processar(self, query_usuario: str) -> Dict[str, Any]:
        """
        Ponto de entrada principal do roteador
        Retorna resposta direta ou redireciona para agente
        """
        try:
            self.logger.info(f"Processando query: {query_usuario}")
            
            # 1. Classifica a query
            classificacao = self.classificador.classificar(query_usuario)
            tipo = classificacao['tipo']
            parametros = classificacao['parametros']
            
            self.logger.info(f"Classificação: {tipo} | Confiança: {classificacao['confianca']}")
            
            # 2. Redireciona para agente se necessário
            if tipo == 'agente':
                return {
                    "tipo": "redirecionar_agente",
                    "mensagem": "Query complexa - redirecionando para análise detalhada",
                    "query_original": query_usuario,
                    "sugestao": "Use o agente para análises estatísticas complexas"
                }
            
            # 3. Processa com handler apropriado
            handler = self.handlers[tipo]
            return handler.processar(parametros)
            
        except Exception as e:
            self.logger.error(f"Erro no roteador: {str(e)}")
            return {
                "tipo": "erro",
                "mensagem": f"Erro ao processar: {str(e)}",
                "query_original": query_usuario
            }
    
    def processar_batch(self, queries: list) -> list:
        """Processa múltiplas queries de uma vez"""
        return [self.processar(query) for query in queries]

# Teste completo do sistema
if __name__ == "__main__":
    import json
    
    # Mock database
    class MockDB:
        pass
    
    roteador = RoteadorPrincipal(MockDB())
    
    testes = [
        "Mostre produto código 123456",
        "Listar produtos categoria Lanches",
        "Quais cores disponíveis?",
        "Quantos clientes ativos?",
        "Top 10 produtos mais caros",
        "Analisar tendências de vendas do mês",
        "Qual o produto mais vendido?",
        "Ver produtos preço até 50 reais"
    ]
    
    print("🚀 TESTE COMPLETO DO SISTEMA\n")
    
    for query in testes:
        resultado = roteador.processar(query)
        print(f"📝 Query: {query}")
        print(f"🎯 Tipo: {resultado['tipo']}")
        
        if resultado['tipo'] == 'redirecionar_agente':
            print(f"➡️  Redirecionando para agente...")
        else:
            print(f"📊 Total resultados: {resultado.get('total', 0)}")
        
        print("-" * 50)