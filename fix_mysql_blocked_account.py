#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para resolver problema de conta MySQL bloqueada
Erro: (4151, 'Access denied, this account is locked')
"""

import asyncio
import asyncmy
from dotenv import load_dotenv
import os
import sys

# Carrega as variáveis de ambiente
load_dotenv()

class FixMysqlAccount:
    def __init__(self):
        # Carrega as credenciais do .env
        self.host = os.getenv('GPTALK_01_MYSQL_HOST')
        self.user = os.getenv('GPTALK_01_MYSQL_USER')
        self.password = os.getenv('GPTALK_01_MYSQL_PASSWORD')
        self.database = os.getenv('GPTALK_01_MYSQL_DATABASE')
        self.port = int(os.getenv('GPTALK_01_MYSQL_PORT', 3306))
        
        print("="*60)
        print("🔧 SCRIPT PARA RESOLVER CONTA MYSQL BLOQUEADA")
        print("="*60)
        print(f"Host: {self.host}")
        print(f"Usuário: {self.user}")
        print(f"Database: {self.database}")
    
    async def tentar_conexao_root(self):
        """Tenta conectar com usuário root para desbloquear a conta"""
        print("\n🔐 TENTANDO CONEXÃO COM ROOT PARA DESBLOQUEAR CONTA...")
        
        # Solicitar credenciais root
        print("\n📝 Para desbloquear a conta, precisamos de acesso administrativo:")
        root_user = input("Digite o usuário MySQL administrativo (ex: root): ").strip()
        if not root_user:
            root_user = "root"
        
        root_password = input(f"Digite a senha do usuário {root_user}: ").strip()
        
        try:
            # Conectar como root
            print(f"\n🔗 Conectando como {root_user}...")
            conn = await asyncmy.connect(
                host=self.host,
                user=root_user,
                password=root_password,
                port=self.port
            )
            
            async with conn.cursor() as cursor:
                # 1. Verificar se a conta está bloqueada
                print(f"\n1️⃣ Verificando status da conta '{self.user}'...")
                await cursor.execute(
                    "SELECT User, Host, account_locked, password_expired FROM mysql.user WHERE User = %s",
                    (self.user,)
                )
                user_info = await cursor.fetchall()
                
                if user_info:
                    for user_row in user_info:
                        user, host, locked, expired = user_row
                        print(f"   👤 Usuário: {user}@{host}")
                        print(f"   🔒 Bloqueado: {'SIM' if locked == 'Y' else 'NÃO'}")
                        print(f"   ⏰ Senha expirada: {'SIM' if expired == 'Y' else 'NÃO'}")
                        
                        if locked == 'Y':
                            print(f"   🚨 CONTA BLOQUEADA CONFIRMADA!")
                else:
                    print(f"   ❌ Usuário '{self.user}' não encontrado!")
                    return False
                
                # 2. Verificar tentativas de conexão com erro
                print(f"\n2️⃣ Verificando erros de conexão...")
                await cursor.execute("SHOW STATUS LIKE 'Connection_errors%'")
                connection_errors = await cursor.fetchall()
                
                for error_type, count in connection_errors:
                    if int(count) > 0:
                        print(f"   ⚠️  {error_type}: {count} erros")
                
                # 3. Desbloquear a conta
                print(f"\n3️⃣ Desbloqueando a conta '{self.user}'...")
                
                # Desbloquear conta para todos os hosts possíveis
                hosts_para_tentar = ['%', 'localhost', self.host, '127.0.0.1']
                
                for host_pattern in hosts_para_tentar:
                    try:
                        unlock_query = f"ALTER USER '{self.user}'@'{host_pattern}' ACCOUNT UNLOCK"
                        print(f"   🔓 Executando: {unlock_query}")
                        await cursor.execute(unlock_query)
                        print(f"   ✅ Sucesso para {self.user}@{host_pattern}")
                    except Exception as e:
                        print(f"   ⚠️  Falhou para {host_pattern}: {str(e)}")
                
                # 4. Resetar contadores de erro
                print(f"\n4️⃣ Limpando contadores de erro...")
                await cursor.execute("FLUSH STATUS")
                print("   ✅ Contadores resetados")
                
                # 5. Aplicar mudanças
                print(f"\n5️⃣ Aplicando mudanças...")
                await cursor.execute("FLUSH PRIVILEGES")
                print("   ✅ Privilégios recarregados")
                
                await conn.commit()
                print("   ✅ Mudanças confirmadas")
            
            await conn.ensure_closed()
            print("\n✅ PROCESSO DE DESBLOQUEIO CONCLUÍDO!")
            return True
            
        except asyncmy.errors.OperationalError as e:
            error_code, error_msg = e.args
            print(f"\n❌ ERRO ao conectar como {root_user}: ({error_code}) {error_msg}")
            if error_code == 1045:
                print("   💡 Credenciais do root incorretas")
            elif error_code == 4151:
                print("   💡 A própria conta root está bloqueada!")
            return False
        except Exception as e:
            print(f"\n❌ ERRO DESCONHECIDO: {str(e)}")
            return False
    
    async def testar_conta_desbloqueada(self):
        """Testa se a conta foi desbloqueada com sucesso"""
        print(f"\n🧪 TESTANDO CONTA DESBLOQUEADA...")
        
        try:
            conn = await asyncmy.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            
            # Testar uma query simples
            async with conn.cursor() as cursor:
                await cursor.execute("SELECT 1 as teste")
                result = await cursor.fetchone()
                print(f"   ✅ Query teste: {result}")
            
            await conn.ensure_closed()
            print("✅ CONTA DESBLOQUEADA COM SUCESSO!")
            return True
            
        except asyncmy.errors.OperationalError as e:
            error_code, error_msg = e.args
            print(f"❌ AINDA COM PROBLEMAS: ({error_code}) {error_msg}")
            return False
        except Exception as e:
            print(f"❌ ERRO: {str(e)}")
            return False
    
    def mostrar_solucoes_alternativas(self):
        """Mostra soluções alternativas caso o desbloqueio não funcione"""
        print("\n" + "="*60)
        print("🛠️  SOLUÇÕES ALTERNATIVAS")
        print("="*60)
        
        print("\n📋 Se o desbloqueio não funcionou, tente:")
        print("1️⃣ **REINICIAR SERVIÇO MYSQL**")
        print("   sudo systemctl restart mysql")
        print("   # ou")
        print("   sudo service mysql restart")
        
        print("\n2️⃣ **VERIFICAR LOGS DO MYSQL**")
        print("   sudo tail -f /var/log/mysql/error.log")
        print("   # Procure por mensagens sobre account locking")
        
        print("\n3️⃣ **COMANDOS SQL DIRETOS**")
        print("   mysql -u root -p")
        print(f"   SELECT User, Host, account_locked FROM mysql.user WHERE User = '{self.user}';")
        print(f"   ALTER USER '{self.user}'@'%' ACCOUNT UNLOCK;")
        print("   FLUSH PRIVILEGES;")
        
        print("\n4️⃣ **CRIAR NOVA CONTA MYSQL**")
        print("   Se nada funcionar, considere criar uma nova conta:")
        print(f"   CREATE USER '{self.user}_new'@'%' IDENTIFIED BY 'nova_senha';")
        print(f"   GRANT ALL PRIVILEGES ON {self.database}.* TO '{self.user}_new'@'%';")
        print("   FLUSH PRIVILEGES;")
        
        print("\n5️⃣ **VERIFICAR CONFIGURAÇÕES DE SEGURANÇA**")
        print("   # Verificar se há políticas que bloqueiam automaticamente")
        print("   SHOW VARIABLES LIKE '%validate_password%';")
        print("   SHOW VARIABLES LIKE '%connection%';")
    
    async def executar_fix_completo(self):
        """Executa o processo completo de correção"""
        print("🚀 INICIANDO PROCESSO DE CORREÇÃO...\n")
        
        # 1. Tentar desbloquear com root
        sucesso_desbloqueio = await self.tentar_conexao_root()
        
        if sucesso_desbloqueio:
            # 2. Testar se funcionou
            sucesso_teste = await self.testar_conta_desbloqueada()
            
            if sucesso_teste:
                print("\n🎉 PROBLEMA RESOLVIDO COM SUCESSO!")
                print("💡 Agora seu app deve conseguir conectar no banco MySQL")
                print("💡 O negocio_idx deve ser preenchido corretamente")
            else:
                print("\n⚠️  CONTA DESBLOQUEADA, MAS AINDA HÁ PROBLEMAS")
                self.mostrar_solucoes_alternativas()
        else:
            print("\n❌ NÃO FOI POSSÍVEL DESBLOQUEAR AUTOMATICAMENTE")
            self.mostrar_solucoes_alternativas()

async def main():
    """Função principal"""
    fix = FixMysqlAccount()
    await fix.executar_fix_completo()

if __name__ == "__main__":
    asyncio.run(main()) 