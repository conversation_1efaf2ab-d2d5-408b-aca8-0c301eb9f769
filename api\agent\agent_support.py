from .agent_secret import Secret
from dataclasses import dataclass
from .agent_mysql import Mysql
import asyncio




@dataclass
class Faq:
    AREA: str
    DUVIDA: str
    RESPOSTA: str


class Support:
    def __init__(self):
        self.mysql = Mysql()

    async def fetch_faq(self, negocio_idx: str, produto_idx: str = None,list_faq: bool = False):
        #print("===== fetch_faq() =====")
        #print(f"negocio_idx: {negocio_idx}, produto_idx: {produto_idx}")
        query = f"""
            SELECT 
                DUVIDA,
                RESPOSTA,
                AREA
            FROM VISAO_SUPORTE_FAQ
            WHERE {f"NEGOCIO_IDX = '{negocio_idx}'" if negocio_idx else f"PRODUTO_IDX = '{produto_idx}'"}
        """
      

        result = await self.mysql.query(query)
        if list_faq == False:
            return result
        else:
            faq_formatado = []
            for item in result:
                area = item.get('AREA') or 'Geral'
                duvida = item.get('DUVIDA', '')
                resposta = item.get('RESPOSTA', '')
                
                item_formatado = (
                    f"Area: {area}\n"
                    f"Pergunta: {duvida}\n"
                    f"Resposta: {resposta}\n"
                    f"-------"
                )
                faq_formatado.append(item_formatado)
            return "\n".join(faq_formatado)
    













if __name__ == "__main__":



    async def teste_fetch_faq():
        support = Support()
        result = await support.fetch_faq("7777777777")
        print(result)

    asyncio.run(teste_fetch_faq())