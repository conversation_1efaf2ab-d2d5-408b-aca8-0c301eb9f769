from dataclasses import dataclass
from typing import Optional

@dataclass
class Config:
   
    
    # Performance
    MAX_PRODUTOS_RETORNO: int = 50
    TEMPO_LIMITE_CLASSIFICACAO: int = 10  # ms
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "roteador.log"
    
    # Handlers
    ATIVAR_CACHE: bool = True
    CACHE_TTL: int = 300  # segundos

# Instância global
config = Config()# Configurações do sistema\n