
modelo_dados = {
    "schema_name": "gptalk_01",
    "version": "1.0",
    "general_guidelines": {
        "description": "Este schema descreve o modelo da plataforma GTtalk usando Neo4j. Ele foi projetado para ser interpretado por uma LLM para gerar consultas e operações no banco de dados.",
        "mandatory_properties": {
            "idx": {
                "type": "STRING",
                "description": "ID único de 10 dígitos para cada nó. É a chave primária, obrigatória e deve ser indexada. Usada para buscas e referências diretas.",
                "constraint": "UNIQUE"
            },
            "excluido": {
                "type": "INTEGER",
                "description": "Flag para exclusão lógica. 0 = ativo/visível, 1 = excluído. Consultas devem sempre filtrar por 'excluido = 0' para retornar apenas itens ativos.",
                "default": 0
            }
        },
        "subcategory_concept": {
            "description": "Subcategorias são implementadas usando múltiplos labels em um nó. O label mais específico vem por último. Exemplo: um nó de Produto terá os labels ['Negocio', 'Produto']. Um nó de LLM terá os labels ['Recurso', 'LLM']."
        },
        "relationship_direction": {
            "description": "A direção dos relacionamentos é crucial. 'OUTGOING' significa que o nó atual é a origem (source), e 'INCOMING' significa que é o destino (target)."
        }
    },
    "nodes": {
        "Pessoa": {
            "labels": ["Recurso", "Pessoa"],
            "description": "Representa um indivíduo, que pode ser um cliente, um proprietário de negócio, etc. É uma subcategoria de Recurso.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome completo da pessoa."},
                "email": {"type": "STRING", "description": "Email principal da pessoa."},
                "telefone": {"type": "STRING", "description": "Telefone de contato da pessoa."}
            },
            "subcategories": {
                "PessoaFisica": {
                    "labels": ["Recurso", "Pessoa", "PessoaFisica"],
                    "description": "Representa uma pessoa física com CPF.",
                    "properties": {
                        "cpf": {"type": "STRING", "description": "CPF da pessoa física."},
                        "data_nascimento": {"type": "DATE", "description": "Data de nascimento."}
                    },
                    "relationships": [
                        {"target_node": "Categoria", "type": "PERTENCE_A", "direction": "OUTGOING", "description": "PessoaFisica pertence à categoria Pessoa."}
                    ]
                },
                "PessoaJuridica": {
                    "labels": ["Recurso", "Pessoa", "PessoaJuridica"],
                    "description": "Representa uma pessoa jurídica com CNPJ.",
                    "properties": {
                        "cnpj": {"type": "STRING", "description": "CNPJ da pessoa jurídica."}
                    }
                }
            },
            "relationships": [
                {"type": "POSSUI_NEGOCIO", "direction": "OUTGOING", "target_node": "Negocio",
                 "description": "A Pessoa é proprietária de um Negocio."},
                {"type": "POSSUI_CONTA", "direction": "OUTGOING", "target_node": "Conta",
                    "description": "A Pessoa possui uma Conta para acessar o sistema."},
                {"type": "POSSUI_PROJETO", "direction": "OUTGOING", "target_node": "Projeto",
                 "description": "A Pessoa é dona ou participa de um Projeto."},
                {"type": "POSSUI_TAREFA", "direction": "OUTGOING", "target_node": "Tarefa",
                 "description": "Uma Tarefa é atribuída a uma Pessoa."},
                {"type": "POSSUI_CONHECIMENTO", "direction": "OUTGOING", "target_node": "Conhecimento",
                 "description": "A Pessoa detém uma base de conhecimento."},
                {"type": "POSSUI_RECURSO", "direction": "OUTGOING", "target_node": "Recurso",
                 "description": "A Pessoa possui um Recurso genérico."},
                {"type": "CLIENTE_DE", "direction": "OUTGOING", "target_node": "Negocio",
                 "description": "A Pessoa é cliente de um Negocio."},
                {"type": "INICIOU", "direction": "OUTGOING", "target_node": "Conversa",
                 "description": "A Pessoa iniciou uma Conversa."},
                {"type": "ENVIADA_POR", "direction": "OUTGOING", "target_node": "Interacao",
                 "description": "A Pessoa enviou uma Interacao (mensagem) em uma Conversa."},
                {"type": "SOLICITADA_POR", "direction": "OUTGOING", "target_node": "OrdemServico",
                 "description": "A Pessoa solicitou uma Ordem de Serviço."}
            ]
        },
        "Negocio": {
            "labels": ["Negocio"],
            "description": "Representa a entidade de negócio de um cliente na plataforma, como uma loja, uma oficina, etc. Pode ser de uma PessoaFisica ou PessoaJuridica.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome fantasia do negócio."},
                "razao_social": {"type": "STRING", "description": "Razão social do negócio."},
                "cpf_cnpj": {"type": "STRING", "description": "Documento principal do negócio."},
                "cep": {"type": "STRING", "description": "CEP do endereço do negócio."},
                "logradouro": {"type": "STRING", "description": "Endereço do negócio."},
                "numero": {"type": "STRING", "description": "Número do endereço."},
                "complemento": {"type": "STRING", "description": "Complemento do endereço."},
                "bairro": {"type": "STRING", "description": "Bairro do endereço."},
                "cidade": {"type": "STRING", "description": "Cidade do endereço."},
                "uf": {"type": "STRING", "description": "Estado (UF) do endereço."}
            },
            "relationships": [
                {"type": "POSSUI_NEGOCIO", "direction": "INCOMING", "target_node": "Pessoa",
                 "description": "Relaciona o negócio ao seu proprietário (Pessoa)."},
                {"type": "GERENCIADO_POR", "direction": "OUTGOING", "target_node": "Conta",
                    "description": "A Conta que gerencia o Negócio."},
                {"type": "VENDE_PRODUTO", "direction": "OUTGOING",
                 "target_node": "Produto", "description": "O Negócio vende um Produto."},
                {"type": "PRESTA_SERVICO", "direction": "OUTGOING",
                 "target_node": "Servico", "description": "O Negócio presta um Serviço."},
                {"type": "CLIENTE_DE", "direction": "INCOMING", "target_node": "Pessoa",
                 "description": "Relaciona o negócio aos seus clientes (Pessoas)."},
                {"type": "E_DO_TIPO", "direction": "OUTGOING", "target_node": "Segmento",
                 "description": "Classifica o Negócio em um Segmento de mercado."},
                {"type": "POSSUI_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                 "description": "O Negócio possui categorias próprias para organizar seus itens."},
                {"type": "EMITIDA_POR", "direction": "OUTGOING", "target_node": "OrdemServico",
                 "description": "O Negócio emitiu uma Ordem de Serviço."},
                {"type": "TEM_OS_NUMERADOR", "direction": "OUTGOING", "target_node": "OsNumerador",
                 "description": "O Negócio possui um numerador de OS."}
            ]
        },
        "Produto": {
            "labels": ["Negocio", "Produto"],
            "description": "Representa um produto comercializável. É uma subcategoria de Negocio.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome do produto."},
                "preco": {"type": "FLOAT", "description": "Preço do produto."},
                "descr": {"type": "STRING", "description": "Descrição detalhada do produto."}
            },
            "relationships": [
                {"type": "VENDE_PRODUTO", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Obrigatório. Indica qual Negócio vende este produto."},
                {"type": "SUBPRODUTO_DE", "direction": "OUTGOING", "target_node": "Produto",
                    "description": "Relacionamento recursivo para hierarquia de produtos (e.g., 'Feijão Tropeiro Grande' é SUBPRODUTO_DE 'Feijão Tropeiro')."},
                {"type": "TEM_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                 "description": "Associa o produto a uma Categoria."},
                {"type": "UTILIZA_PRODUTO", "direction": "INCOMING", "target_node": "OrdemServico",
                 "description": "Indica que o produto foi usado em uma Ordem de Serviço.",
                 "properties": {
                    "quantidade": {"type": "INTEGER", "description": "Quantidade do produto utilizado na OS."},
                    "preco": {"type": "FLOAT", "description": "Preço do produto no momento da inclusão na OS."}
                 }}
            ]
        },
        "Servico": {
            "labels": ["Negocio", "Servico"],
            "description": "Representa um serviço prestado. É uma subcategoria de Negocio.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome do serviço."},
                "preco": {"type": "FLOAT", "description": "Preço do serviço."},
                "descr": {"type": "STRING", "description": "Descrição detalhada do serviço."}
            },
            "relationships": [
                {"type": "PRESTA_SERVICO", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Obrigatório. Indica qual Negócio presta este serviço."},
                {"type": "TEM_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                    "description": "Associa o serviço a uma Categoria."},
                {"type": "INCLUI_SERVICO", "direction": "INCOMING", "target_node": "OrdemServico",
                 "description": "Indica que o serviço foi incluído em uma Ordem de Serviço.",
                 "properties": {
                    "quantidade": {"type": "INTEGER", "description": "Quantidade do serviço utilizado na OS."},
                    "preco": {"type": "FLOAT", "description": "Preço do serviço no momento da inclusão na OS."}
                 }}
            ]
        },
        "OrdemServico": {
            "labels": ["Comercio", "OrdemServico"],
            "description": "Representa uma ordem de serviço ou venda, que agrupa produtos e serviços para um cliente.",
            "properties": {
                "numero": {"type": "INTEGER", "description": "Número sequencial da OS."},
                "qtde_parcelas_pagamento": {"type": "INTEGER", "description": "Quantidade de parcelas do pagamento."},
                "total_servicos": {"type": "FLOAT", "description": "Valor total dos serviços na OS."},
                "total_produtos": {"type": "FLOAT", "description": "Valor total dos produtos na OS."},
                "total": {"type": "FLOAT", "description": "Valor total da OS."},
                "entrada": {"type": "DATETIME", "description": "Data e hora de criação da OS."},
                "saida": {"type": "DATETIME", "description": "Data e hora de finalização da OS."},
                "item_nome": {"type": "STRING", "description": "Descrição genérica do item principal da OS (e.g., nome do veículo)."},
                "item_categoria": {"type": "STRING", "description": "Categoria do item."},
                "item_marca": {"type": "STRING", "description": "Marca do item."},
                "item_placa": {"type": "STRING", "description": "Placa do veículo, se aplicável."},
                "item_ano": {"type": "STRING", "description": "Ano do item/veículo."},
                "item_cor": {"type": "STRING", "description": "Cor do item."},
                "item_serie": {"type": "STRING", "description": "Número de série do item."},
                "item_km": {"type": "STRING", "description": "Quilometragem do veículo, se aplicável."}
            },
            "relationships": [
                {"type": "UTILIZA_PRODUTO", "direction": "OUTGOING", "target_node": "Produto",
                 "description": "Opcional. Produtos incluídos na OS.",
                 "properties": {
                    "quantidade": {"type": "INTEGER", "description": "Quantidade do produto utilizado na OS."},
                    "preco": {"type": "FLOAT", "description": "Preço do produto no momento da inclusão na OS."}
                 }},
                {"type": "INCLUI_SERVICO", "direction": "OUTGOING", "target_node": "Servico",
                    "description": "Opcional. Serviços incluídos na OS.",
                    "properties": {
                        "quantidade": {"type": "INTEGER", "description": "Quantidade do serviço utilizado na OS."},
                        "preco": {"type": "FLOAT", "description": "Preço do serviço no momento da inclusão na OS."}
                    }},
                {"type": "SOLICITADA_POR", "direction": "INCOMING", "target_node": "Pessoa",
                 "description": "Obrigatório. Cliente que solicitou a OS."},
                {"type": "EMITIDA_POR", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Obrigatório. Negócio que emitiu a OS."},
                {"type": "TEM_PAGAMENTO", "direction": "OUTGOING", "target_node": "Pagamento",
                 "description": "Obrigatório. Pagamento(s) associado(s) à OS."},
                {"type": "TEM_STATUS", "direction": "OUTGOING", "target_node": "Status",
                 "description": "Status atual da OS (e.g., 'Aberta', 'Finalizada')."},
                {"jjjtype": "TEM_PRIORIDADE", "direction": "OUTGOING",
                 "target_node": "Prioridade", "description": "Nível de prioridade da OS."}
            ]
        },
        "OsNumerador": {
            "labels": ["Comercio", "OsNumerador"],
            "description": "Regisra o ultimo numreo da OS de um determinado Negocio.",
            "properties": {
                "ultimo_numero": {"type": "INTEGER", "description": "Número sequencial da OS."},
            },
            "relationships": [
                {"type": "TEM_OS_NUMERADOR", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Opcional. Existirá somente quando o negócio gerar a primeira OS."}
            ]
        },
        "Pagamento": {
            "labels": ["Comercio", "Pagamento"],
            "description": "Representa uma parcela ou um pagamento único de uma OrdemServico.",
            "properties": {
                "vencimento": {"type": "DATE", "description": "Data de vencimento do pagamento."},
                "parcela": {"type": "INTEGER", "description": "Número da parcela (e.g., 1 de 3)."}
            },
            "relationships": [
                {"type": "TEM_PAGAMENTO", "direction": "INCOMING", "target_node": "OrdemServico",
                 "description": "Link para a Ordem de Serviço à qual o pagamento pertence."},
                {"type": "TEM_STATUS", "direction": "OUTGOING", "target_node": "Status",
                    "description": "Status do pagamento (e.g., 'Pendente', 'Pago')."},
                {"type": "TEM_TIPO", "direction": "OUTGOING", "target_node": "PagamentoTipo",
                 "description": "Tipo de pagamento (e.g., 'Pix', 'Cartão de Crédito', 'Dinheiro')."}
            ]
        },
        "PagamentoTipo": {
            "labels": ["Comercio", "PagamentoTipo"],
            "description": "Representa um tipo de pagamento, como boleto, cartão de crédito, etc.",
            "properties": {
                "idx": {"type": "STRING", "description": "identificador único do tipo de pagamento."}
            },
            "subCategories":["Pix","CartaoCredito","CartaoDebito","Dinheiro","Cheque"],
            "relationships": [
                {"type": "TEM_TIPO", "direction": "INCOMING", "target_node": "Pagamento",
                 "description": "Link para a Ordem de Serviço à qual o pagamento pertence."},
                {"type": "TEM_PAGAMENTO", "direction": "OUTGOING", "target_node": "Pagamento",
                 "description": "Link para o Pagamento à qual o tipo de pagamento pertence."}
            ]
        },
        "Conversa": {
            "labels": ["Comunicacao", "Conversa"],
            "description": "Representa uma conversa (chat, email thread) entre um cliente e um agente.",
            "properties": {
                "assunto": {"type": "STRING", "description": "Assunto ou título da conversa."},
                "inicio": {"type": "DATETIME", "description": "Timestamp de início da conversa."},
                "termino": {"type": "DATETIME", "description": "Timestamp de término da conversa."},
                "ultima_interacao": {"type": "DATETIME", "description": "Timestamp da última mensagem trocada."},
                "canal_id": {"type": "STRING", "description": "ID do canal de comunicação usado."},
                "chat_id": {"type": "STRING", "description": "ID específico do chat na plataforma de origem."}
            },
            "relationships": [
                {"type": "TEM_STATUS", "direction": "OUTGOING", "target_node": "Status",
                 "description": "Obrigatório. Status atual da conversa."},
                {"type": "INICIOU", "direction": "INCOMING", "target_node": "Pessoa",
                    "description": "Pessoa (cliente) que iniciou a conversa."},
                {"type": "ATENDEU", "direction": "INCOMING", "target_node": "Agente",
                 "description": "Agente que atendeu/participou da conversa."},
                {"type": "CANAL_UTILIZADO", "direction": "OUTGOING", "target_node": "Canal",
                 "description": "Canal de comunicação por onde a conversa ocorreu."}
            ]
        },
        "Interacao": {
            "labels": ["Comunicacao", "Interacao"],
            "description": "Representa uma única mensagem ou evento dentro de uma Conversa.",
            "properties": {
                "nome": {"type": "STRING", "description": "Pode ser usado para um título ou resumo da interação."},
                "entrada": {"type": "DATETIME", "description": "Timestamp de quando a mensagem foi recebida."},
                "saida": {"type": "DATETIME", "description": "Timestamp de quando a resposta foi enviada."},
                "enviado": {"type": "STRING", "description": "Conteúdo da mensagem enviada."},
                "enviado_tokens": {"type": "INTEGER", "description": "Contagem de tokens da mensagem enviada."},
                "respondido": {"type": "STRING", "description": "Conteúdo da resposta recebida."},
                "respondido_tokens": {"type": "INTEGER", "description": "Contagem de tokens da resposta recebida."}
            },
            "relationships": [
                {"type": "OCORREU_EM", "direction": "OUTGOING", "target_node": "Conversa",
                 "description": "Obrigatório. Aponta para a Conversa à qual esta interação pertence."},
                {"type": "ENVIADA_POR", "direction": "INCOMING", "target_node": "Pessoa",
                    "description": "Indica que a interação foi enviada por um cliente."},
                {"type": "RESPONDIDA_POR", "direction": "INCOMING", "target_node": "Agente",
                 "description": "Indica que a interação foi uma resposta de um Agente."}
            ]
        },
        "Canal": {
            "labels": ["Comunicacao", "Canal"],
            "description": "Representa um canal de comunicação, como WhatsApp, Email, etc.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome do canal (e.g., 'WhatsApp')."}
            },
            "relationships": [
                {"type": "CANAL_UTILIZADO", "direction": "INCOMING", "target_node": "Conversa",
                 "description": "Link para as conversas que utilizaram este canal."}
            ]
        },
        "Recurso": {
            "labels": ["Recurso"],
            "description": "Nó base para várias entidades do sistema como Pessoas, Contas e Apps. Contém propriedades comuns.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome do recurso."},
                "url": {"type": "STRING", "description": "URL associada ao recurso, se houver."}
            },
            "relationships": [
                {"type": "SUBRECURSO_DE", "direction": "OUTGOING", "target_node": "Recurso",
                 "description": "Relacionamento recursivo para hierarquia. Ex: Uma versão de App é um sub-recurso do App principal."},
                {"type": "TEM_TAG", "direction": "OUTGOING", "target_node": "Tag",
                    "description": "Opcional. Associa o recurso a uma Tag."},
                {"type": "TEM_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                 "description": "Opcional. Associa o recurso a uma Categoria."},
                {"type": "TEM_TIPO", "direction": "OUTGOING", "target_node": "Tipo",
                 "description": "Opcional. Associa o recurso a um Tipo."},
                {"type": "FONTE_DE", "direction": "INCOMING", "target_node": "Conhecimento",
                 "description": "Indica que este recurso é a fonte para uma base de conhecimento."}
            ]
        },
        "App": {
            "labels": ["Recurso", "App"],
            "description": "Representa um aplicativo ou sistema na plataforma, seja interno ou cadastrado por usuários.",
            "properties": {
                "apelido": {"type": "STRING", "description": "Nome único em caixa baixa e sem espaços para o App (e.g., 'oficinatech')."},
                "nome": {"type": "STRING", "description": "Nome de exibição do App."},
                "url": {"type": "STRING", "description": "URL de acesso ao App."}
            },
            "relationships": [
                {"type": "ACESSO_AO_APP", "direction": "INCOMING", "target_node": "Conta",
                 "description": "Obrigatório. Contas que têm acesso a este App."},
                {"type": "CRIADO_POR", "direction": "OUTGOING", "target_node": "Pessoa",
                    "description": "Obrigatório. Pessoa que registrou/criou o App."}
            ]
        },
        "Conta": {
            "labels": ["Recurso", "Conta"],
            "description": "Representa a conta de um usuário para login e acesso aos Apps.",
            "properties": {
                "nome": {"type": "STRING", "description": "Nome associado à conta."},
                "usuario": {"type": "STRING", "description": "Nome de usuário para login."},
                "email": {"type": "STRING", "description": "Email de login."},
                "url": {"type": "STRING", "description": "URL do perfil ou dashboard da conta."}
            },
            "relationships": [
                {"type": "ACESSO_AO_APP", "direction": "OUTGOING", "target_node": "App",
                 "description": "Obrigatório. App ao qual esta conta dá acesso."},
                {"type": "POSSUI_CONTA", "direction": "INCOMING", "target_node": "Pessoa",
                    "description": "Obrigatório. Pessoa dona da conta."},
                {"type": "GERENCIADO_POR", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Opcional. Negócio que gerencia esta conta."},
                {"type": "TEM_TIPO", "direction": "OUTGOING", "target_node": "Tipo",
                 "description": "Obrigatório. Tipo de conta (e.g., 'Admin', 'Usuário')."}
            ]
        },
        "Agente": {
            "labels": ["Recurso", "Pessoa", "Agente"],
            "description": "Representa um agente ou funcionário que interage com clientes. É uma subcategoria de Pessoa.",
            "properties": {},
            "relationships": [
                {"type": "ATENDEU", "direction": "OUTGOING", "target_node": "Conversa",
                 "description": "Agente que participou da Conversa."},
                {"type": "RESPONDIDA_POR", "direction": "OUTGOING", "target_node": "Interacao",
                    "description": "Agente que enviou uma resposta em uma Interacao."}
            ]
        },
        "LLM": {
            "labels": ["Recurso", "LLM"],
            "description": "Representa um modelo de linguagem (LLM) como o ChatGPT, usado como um recurso no sistema.",
            "properties": {},
            "relationships": []
        },
        "Atividade": {
            "labels": ["Atividade"],
            "description": "Nó base para itens relacionados a gestão de projetos e tarefas, como Projeto, Tarefa, Processo.",
            "properties": {},
            "relationships": []
        },
        "Projeto": {
            "labels": ["Atividade", "Projeto"],
            "description": "Representa um projeto com um conjunto de tarefas.",
            "properties": {
                "titulo": {"type": "STRING", "description": "Título ou nome do projeto."}
            },
            "relationships": [
                {"type": "FAZ_PARTE_DE", "direction": "INCOMING", "target_node": "Tarefa",
                 "description": "Tarefas que compõem este projeto."},
                {"type": "POSSUI_PROJETO", "direction": "INCOMING", "target_node": "Pessoa",
                    "description": "Obrigatório. Pessoa responsável pelo projeto. Deve ser definida durante a criação do projeto."}
            ]
        },
        "Tarefa": {
            "labels": ["Atividade", "Tarefa"],
            "description": "Representa uma tarefa, que pode ser parte de um projeto ou avulsa.",
            "properties": {
                "titulo": {"type": "STRING", "description": "Título ou nome da tarefa."}
            },
            "relationships": [
                {"type": "TEM_SUBTAREFA", "direction": "OUTGOING", "target_node": "Tarefa",
                 "description": "Relacionamento recursivo para dividir uma tarefa em subtarefas."},
                {"type": "FAZ_PARTE_DE", "direction": "OUTGOING", "target_node": "Projeto",
                    "description": "Opcional. Indica a qual projeto a tarefa pertence."},
                {"type": "DEPENDE_DE", "direction": "OUTGOING", "target_node": "Tarefa",
                 "description": "Indica que esta tarefa precisa que outra seja concluída primeiro."}
            ]
        },
        "Conhecimento": {
            "labels": ["Conhecimento"],
            "description": "Representa uma unidade de conhecimento, como um artigo, um documento ou uma nota.",
            "properties": {
                "titulo": {"type": "STRING", "description": "Título do conhecimento."},
                "url": {"type": "STRING", "description": "URL para o conteúdo completo."},
                "resumo": {"type": "STRING", "description": "Resumo do conhecimento."}
            },
            "relationships": [
                {"type": "POSSUI_CONHECIMENTO", "direction": "INCOMING",
                 "target_node": "Pessoa", "description": "Pessoa que detém este conhecimento."},
                {"type": "E_SOBRE", "direction": "OUTGOING", "target_node": "Recurso",
                    "description": "Indica o assunto do conhecimento (pode ser um Produto, Serviço, Pessoa, etc.)."},
                {"type": "TEM_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                 "description": "Associa o conhecimento a uma Categoria."},
                {"type": "TEM_TAG", "direction": "OUTGOING", "target_node": "Tag",
                 "description": "Associa o conhecimento a uma Tag."},
                {"type": "TEM_TIPO", "direction": "OUTGOING", "target_node": "Tipo",
                 "description": "Associa o conhecimento a um Tipo."},
                {"type": "FONTE_DE", "direction": "OUTGOING", "target_node": "Recurso",
                 "description": "O Recurso que originou este conhecimento."}
            ]
        },
        "Classificacao": {
            "labels": ["Classificacao"],
            "description": "Nó base para entidades que servem para classificar outras, como Categoria, Tag, Tipo, Status.",
            "properties": {
                "nome": {"type": "STRING", "description": "O nome do item de classificação (e.g., 'Aberto', 'Urgente', 'Eletrônicos')."}
            },
            "relationships": []
        },
        "Categoria": {
            "labels": ["Classificacao", "Categoria"],
            "description": "Usado para categorizar diversos nós como Produtos, Serviços, Conhecimento, etc.",
            "properties": {},
            "relationships": [
                {"type": "TEM_CATEGORIA", "direction": "INCOMING",
                 "target_node": "Produto", "description": "Produtos nesta categoria."},
                {"type": "TEM_CATEGORIA", "direction": "INCOMING",
                    "target_node": "Servico", "description": "Serviços nesta categoria."},
                {"type": "TEM_CATEGORIA", "direction": "INCOMING",
                 "target_node": "Status", "description": "Status nesta categoria."},
                {"type": "TEM_CATEGORIA", "direction": "INCOMING", "target_node": "Conhecimento",
                 "description": "Conhecimentos nesta categoria."},
                {"type": "TEM_CATEGORIA", "direction": "INCOMING",
                 "target_node": "Recurso", "description": "Recursos nesta categoria."}
            ]
        },
        "Tag": {
            "labels": ["Classificacao", "Tag"],
            "description": "Usado para aplicar tags (etiquetas) em nós como Conhecimento e Recurso.",
            "properties": {},
            "relationships": [
                {"type": "TEM_TAG", "direction": "INCOMING", "target_node": "Conhecimento",
                 "description": "Conhecimentos com esta tag."},
                {"type": "TEM_TAG", "direction": "INCOMING",
                    "target_node": "Recurso", "description": "Recursos com esta tag."}
            ]
        },
        "Tipo": {
            "labels": ["Classificacao", "Tipo"],
            "description": "Usado para definir um tipo específico para um nó, como Tipo de Conta ou Tipo de Recurso.",
            "properties": {},
            "relationships": [
                {"type": "TEM_TIPO", "direction": "INCOMING",
                 "target_node": "Segmento", "description": "Segmentos deste tipo."},
                {"type": "TEM_TIPO", "direction": "INCOMING", "target_node": "Conhecimento",
                    "description": "Conhecimentos deste tipo."},
                {"type": "TEM_TIPO", "direction": "INCOMING",
                 "target_node": "Recurso", "description": "Recursos deste tipo."},
                {"type": "TEM_TIPO", "direction": "INCOMING",
                 "target_node": "Conta", "description": "Contas deste tipo."},
                {"target_node": "Categoria", "type": "PERTENCE_A","direction":"OUTGOING", "description": "Tipo desta Categoria."}
            ]
        },
        "Status": {
            "labels": ["Classificacao", "Status"],
            "description": "Define o estado de um nó em um determinado momento (e.g., Status de uma OS, de uma Conversa).",
            "properties": {
                "inicio": {"type": "DATETIME", "description": "Data em que o status foi atribuído."}
            },
            "relationships": [
                {"type": "TEM_CATEGORIA", "direction": "OUTGOING", "target_node": "Categoria",
                 "description": "Um status pode ser categorizado (e.g., status de 'Finalizado' pertence à categoria 'Concluído')."},
                {"type": "TEM_STATUS", "direction": "INCOMING", "target_node": "OrdemServico",
                    "description": "Ordens de Serviço com este status."},
                {"type": "TEM_STATUS", "direction": "INCOMING",
                 "target_node": "Conversa", "description": "Conversas com este status."},
                {"type": "TEM_STATUS", "direction": "INCOMING",
                 "target_node": "Pagamento", "description": "Pagamentos com este status."}
            ]
        },
        "Prioridade": {
            "labels": ["Classificacao", "Prioridade"],
            "description": "Define a prioridade de uma Ordem de Serviço.",
            "properties": {},
            "relationships": [
                {"type": "TEM_PRIORIDADE", "direction": "INCOMING", "target_node": "OrdemServico",
                 "description": "Ordens de Serviço com esta prioridade."}
            ]
        },
        "Segmento": {
            "labels": ["Classificacao", "Segmento"],
            "description": "Define o segmento de mercado de um Negócio.",
            "properties": {},
            "relationships": [
                {"type": "E_DO_TIPO", "direction": "INCOMING", "target_node": "Negocio",
                 "description": "Negócios que pertencem a este segmento."}
            ]
        }
    }
}
