import asyncio
import requests

async def test_search_phone():
    """
    Testa o endpoint search/phone
    """
    print("🧪 Testando endpoint /api/agent/user/search/phone")
    print("=" * 60)
    
    # URL base do servidor
    base_url = "http://127.0.0.1:8001"
    
    # Testa com diferentes formatos de telefone
    test_phones = [
        "553184198720",
        "3184198720", 
        "55 31 8419-8720",
        "(31) 84198-720",
        "31-84198720"
    ]
    
    for phone in test_phones:
        print(f"\n📞 Testando telefone: {phone}")
        print("-" * 30)
        
        try:
            # Faz a requisição GET
            response = requests.get(f"{base_url}/api/agent/user/search/phone", params={"phone": phone})
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Resposta: {data}")
                
                if data.get("found"):
                    print(f"✅ Usuário encontrado!")
                    print(f"   IDX: {data.get('idx')}")
                    print(f"   Nome: {data.get('nome')}")
                    print(f"   Email: {data.get('email')}")
                else:
                    print(f"❌ Usuário não encontrado: {data.get('message', 'Sem mensagem')}")
            else:
                print(f"❌ Erro HTTP: {response.status_code}")
                print(f"Resposta: {response.text}")
                
        except Exception as e:
            print(f"❌ Erro na requisição: {str(e)}")
    
    print(f"\n✅ Teste concluído!")

if __name__ == "__main__":
    asyncio.run(test_search_phone()) 