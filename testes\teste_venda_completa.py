import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== TESTE COMPLETO VENDA 23190 ===")
    
    # 1. Verificar venda principal
    print("1️⃣ VENDA PRINCIPAL:")
    venda = await mysql.query("SELECT * FROM VENDA WHERE ID = 23190")
    if venda:
        v = venda[0]
        print(f"   ✅ ID: {v.get('ID')}")
        print(f"   ✅ CLIENTE_IDX: {v.get('CLIENTE_IDX')}")
        print(f"   ✅ TOTAL_RS: {v.get('TOTAL_RS')}")
        print(f"   ✅ NEGOCIO_IDX: {v.get('NEGOCIO_IDX')}")
        print(f"   ✅ DT: {v.get('DT')}")
    else:
        print("   ❌ Venda principal não encontrada!")
    
    # 2. Verificar produtos da venda
    print("\n2️⃣ PRODUTOS DA VENDA:")
    produtos = await mysql.query("SELECT * FROM VENDA_PRODUTO WHERE VENDA_ID = '23190'")
    if produtos:
        print(f"   ✅ {len(produtos)} produto(s) encontrado(s):")
        for p in produtos:
            print(f"      - PRODUTO_ID: {p.get('PRODUTO_ID')}")
            print(f"      - PRECO: {p.get('PRECO')}")
            print(f"      - QTDE: {p.get('QTDE')}")
            print(f"      - TOTAL: {p.get('TOTAL')}")
    else:
        print("   ❌ Nenhum produto encontrado!")
        
        # Verificar se há qualquer produto na tabela
        total_produtos = await mysql.query("SELECT COUNT(*) as total FROM VENDA_PRODUTO")
        print(f"   📊 Total de produtos na tabela: {total_produtos[0]['total'] if total_produtos else 0}")
    
    # 3. Verificar formas de pagamento
    print("\n3️⃣ FORMAS DE PAGAMENTO:")
    formas = await mysql.query("SELECT * FROM VENDA_FORMA_PAGAMENTO WHERE VENDA_ID = 23190")
    if formas:
        print(f"   ✅ {len(formas)} forma(s) de pagamento encontrada(s):")
        for f in formas:
            print(f"      - FORMA_ID: {f.get('FORMA_ID')}")
            print(f"      - VALOR: {f.get('VALOR')}")
            print(f"      - PARCELA_NR: {f.get('PARCELA_NR')}")
            print(f"      - PAGO: {f.get('PAGO')}")
    else:
        print("   ❌ Nenhuma forma de pagamento encontrada!")
        
        # Verificar registros problemáticos com VENDA_ID = 0
        problemas = await mysql.query("SELECT COUNT(*) as total FROM VENDA_FORMA_PAGAMENTO WHERE VENDA_ID = 0")
        if problemas and problemas[0]['total'] > 0:
            print(f"   ⚠️  Formas com VENDA_ID = 0: {problemas[0]['total']}")
    
    # 4. Resumo do estado
    print("\n📋 RESUMO:")
    if venda and not produtos and not formas:
        print("   🚨 VENDA INCOMPLETA: Apenas a venda principal foi salva!")
        print("   📝 Causa: Mapeamento incorreto de campos nas tabelas relacionadas")
        print("   🔧 Status: Correções aplicadas, próxima venda deve funcionar!")
    elif venda and produtos and formas:
        print("   ✅ VENDA COMPLETA: Todos os dados foram salvos corretamente!")
    else:
        print("   ❌ PROBLEMAS IDENTIFICADOS na estrutura da venda")

if __name__ == "__main__":
    asyncio.run(main()) 