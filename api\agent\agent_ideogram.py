from .agent_secret import Secret
import requests
import json
import base64

secret = Secret()

class Ideogram:
    
    def __init__(self):
        self.api_key = secret.IDEOGRAM_API_KEY
        self.url = "https://api.ideogram.ai/generate"


    def gerar_imagem_ideogram(self, prompt, aspect_ratio="ASPECT_10_16", model="V_2", magic_prompt_option="AUTO"):
        headers = {
            "Api-Key": self.api_key,
            "Content-Type": "application/json"
        }
        
        payload = {
            "image_request": {
                "prompt": prompt,
                "aspect_ratio": aspect_ratio,
                "model": model,
                "magic_prompt_option": magic_prompt_option
            }
        }
        
        response = requests.post(self.url, json=payload, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and len(data['data']) > 0:
                imagens = [imagem['url'] for imagem in data['data']]
                quantidade = len(imagens)
                print(f"Quantidade de imagens geradas: {quantidade}")
                for i, url in enumerate(imagens, 1):
                    print(f"{i} - {url}")
                return imagens
            else:
                print("Erro: A resposta da API não contém dados de imagem.")
                print("Resposta completa da API:", json.dumps(data, indent=2))
        else:
            print(f"Erro ao gerar imagem: {response.status_code}")
            print("Resposta da API:", response.text)
        
        return None

# Exemplo de uso
if __name__ == "__main__":
    prompt = "Um gato fofo brincando com um novelo de lã em um sofá confortável"
    ideogram = Ideogram()
    imagens = ideogram.gerar_imagem_ideogram(prompt)
    if imagens:
        print("Imagens geradas com sucesso!")
