import asyncio
from api.agent.agent_user import User

async def verificar_usuarios_neo4j():
    """
    Verifica se há usuários no Neo4j e mostra os formatos de telefone
    """
    print("🔍 Verificando usuários no Neo4j...")
    print("=" * 60)
    
    user = User()
    
    # Query para buscar alguns usuários com telefone
    query = """
    MATCH (u:Usuario)
    WHERE u.telefone IS NOT NULL
    RETURN u.idx AS idx, u.nome AS nome, u.email AS email, u.telefone AS telefone
    LIMIT 10
    """
    
    try:
        result = await user.neo4j.execute_read_query(query, {})
        
        if result and len(result) > 0:
            print(f"✅ Encontrados {len(result)} usuários:")
            print("-" * 60)
            
            for i, usuario in enumerate(result, 1):
                print(f"{i}. IDX: {usuario.get('idx')}")
                print(f"   Nome: {usuario.get('nome')}")
                print(f"   Email: {usuario.get('email')}")
                print(f"   Telefone: {usuario.get('telefone')}")
                print("-" * 40)
        else:
            print("❌ Nenhum usuário encontrado no Neo4j")
            
    except Exception as e:
        print(f"❌ Erro ao consultar Neo4j: {str(e)}")

if __name__ == "__main__":
    asyncio.run(verificar_usuarios_neo4j()) 