import requests
from fastapi import <PERSON>Router, Request, HTTPException
from fastapi.responses import PlainTextResponse
from .agent_logger import <PERSON><PERSON><PERSON><PERSON>
from .agent_gptalkzap import <PERSON><PERSON><PERSON>lk<PERSON>ap
import  json

from .agent_secret import Secret
from typing import Any, Dict


import requests

import asyncio

logger = AgentLogger()
router = APIRouter()
secret = Secret()
EVOLUTION_API_KEY = secret.get_secret("EVOLUTION_API_KEY")
#print("===== EVOLUTION_API_KEY", EVOLUTION_API_KEY)




# Configuração Evolution API
import os


if os.getenv("ENV") == "production":
    
    EVOLUTION_API_BASE_URL: str = "https://evolutionapi.server.gptalk.com.br"
    API_BASE_URL: str = os.getenv("API_BASE_URL", "")
    EVOLUTION_API_INSTANCE_ID: str = (
        os.getenv("EVOLUTION_API_INSTANCE_ID")
        or str(secret.get_secret("EVOLUTION_API_INSTANCE_ID") or "")
    )
    EVOLUTION_API_INSTANCE_TOKEN: str = (
        os.getenv("EVOLUTION_API_INSTANCE_TOKEN")
        or str(secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN") or "")
    )
    EVOLUTION_API_KEY: str = (
        os.getenv("EVOLUTION_API_KEY")
        or str(secret.get_secret("EVOLUTION_API_KEY") or "")
    )
else:
    EVOLUTION_API_BASE_URL: str = "http://localhost:8081"
    API_BASE_URL: str = "http://localhost:8000/api"
    # Em dev, preferir as variáveis LOCAL; se vazias, cair para Secret; por fim, um fallback seguro
    EVOLUTION_API_INSTANCE_ID: str = (
        os.getenv("EVOLUTION_API_INSTANCE_ID_LOCAL")
        or str(secret.get_secret("EVOLUTION_API_INSTANCE_ID_LOCAL") or "")
        or os.getenv("EVOLUTION_API_INSTANCE_ID")
        or str(secret.get_secret("EVOLUTION_API_INSTANCE_ID") or "")
        or "gptalk-zaplocal"
    )
    EVOLUTION_API_INSTANCE_TOKEN: str = (
        os.getenv("EVOLUTION_API_INSTANCE_TOKEN_LOCAL")
        or str(secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN_LOCAL") or "")
    )
    EVOLUTION_API_KEY: str = (
        os.getenv("EVOLUTION_API_KEY_LOCAL")
        or str(secret.get_secret("EVOLUTION_API_KEY_LOCAL") or "")
    )


def normalize_evo_instance_id(raw_instance_id: str | None) -> str:
    """Normaliza IDs vindos do Evolution (ex.: 'am-zap-3184198720' -> 'amzap-3184198720').
    Se nada for informado, usa o ID padrão configurado.
    """
    if not raw_instance_id:
        return EVOLUTION_API_INSTANCE_ID
    normalized = raw_instance_id.replace("am-zap@", "amzap-").replace("am-zap-", "amzap-")
    return normalized

def is_named_instance(instance_id: str | None) -> bool:
    """Retorna True se parecer um nome de instância Evolution (ex.: 'amzap-3184198720')."""
    if not instance_id:
        return False
    s = instance_id.strip().lower()
    return s.startswith("amzap-") or s.startswith("am-zap-") or s.startswith("am-zap@")







class AgentEvolutionZap:
    def __init__(self):
        self.gptalkzap = AgentGptalkZap()
 

        # Se precisar de autenticação, adicione aqui

    def get_instace_id(self):
        return EVOLUTION_API_INSTANCE_ID
    
    async def process_message(self, body: dict, instance_id: str):
        #print(f"=== PROCESS_MESSAGE INICIADO ===")
        #print(f"Instance ID: {instance_id}")
        #print(f"EVOLUTION_API_INSTANCE_ID: {EVOLUTION_API_INSTANCE_ID}")
        #print("body",body)  

        #logger.info(f"process_message: (instance_id: {instance_id})")
        #logger.info(f"EVOLUTION_API_INSTANCE_ID configurado: {EVOLUTION_API_INSTANCE_ID}")
        
        try:
            data = body.get("data", {})
            event = body.get("event")
            
            if event == "messages.upsert":
                if data.get("key", {}).get("fromMe", False):
                    return None

                message_type = data.get("messageType", "")
                if message_type in ["ack", "reaction", "delete"]:
                    return None

                sender_name = data.get("pushName", "Nome não disponível")
                sender_phone = data.get("key", {}).get("remoteJid", "").replace("@s.whatsapp.net", "")

                message_content = ""
                if message_type == "conversation":
                    message_content = data.get("message", {}).get("conversation", "")
                elif message_type == "extendedTextMessage":
                    message_content = data.get("message", {}).get("extendedTextMessage", {}).get("text", "")
                elif message_type == "imageMessage":
                    caption = body.get("message", {}).get("imageMessage", {}).get("caption", "")
                    message_content = f"[IMAGEM] {caption}" if caption else "[IMAGEM]"
                elif message_type == "audioMessage":
                    message_content = "[ÁUDIO]"
                elif message_type == "videoMessage":
                    caption = body.get("message", {}).get("videoMessage", {}).get("caption", "")
                    message_content = f"[VÍDEO] {caption}" if caption else "[VÍDEO]"
                elif message_type == "documentMessage":
                    filename = body.get("message", {}).get("documentMessage", {}).get("fileName", "documento")
                    message_content = f"[DOCUMENTO] {filename}"
                else:
                    message_content = f"[{message_type.upper()}]"

                self._log_message_info(sender_name, sender_phone, message_content)
                print("vou avaliar se tem conteudo")
                if message_content.strip():
                    data = {
                        "message": message_content,
                        "type": message_type,
                        "sender_name": sender_name,
                        "sender_phone": sender_phone,
                    }

                    #logger.info("@@@@@ EVOLUTION_API_INSTACE_ID")
                    #print("@@@@@@ EVOLUTION_API_INSTANCE_ID",EVOLUTION_API_INSTANCE_ID)
                    #print("###### instance_id",instance_id)
                    #print("ZZZZZ data",data)



                    if instance_id == "gptalk-zap" or instance_id == "gptalk-zaplocal":
                        #print(" ********* === ENTRANDO NO IF LOCAL ===")
                        result = await self.gptalkzap.send(data)
                        #print(f"=== RESULTADO GPTALKZAP: {result} ===")
                        #print(f"AGENTE LOCAL: resultado do send() → {json.dumps(result, ensure_ascii=False) if isinstance(result, dict) else result}")
                        # Propagar a instancia usada para a resposta
                        if isinstance(result, dict) and not result.get("evo_instance_id"):
                            result["evo_instance_id"] = instance_id
                        return result
                    else:
                        data = {
                            "from": sender_phone,
                            "to": sender_phone,
                            "message": message_content,
                            "type": message_type,
                            "sender_name": sender_name,
                            "sender_phone": sender_phone,
                            "evo_instance_id": instance_id
                        }
                        # Endpoint real está montado em /api/agent/agent/evolution_zap_send
                        url = f"{API_BASE_URL}/agent/agent/evolution_zap_send"

                        try:
                            headers = {"Content-Type": "application/json"}
                            resp = await asyncio.to_thread(requests.post, url, json=data, headers=headers, timeout=10)
                            logger.info(f"RETORNO CHAMADA INTERNA ← status={resp.status_code}")
                            content_type = (resp.headers.get("content-type") or "").lower()
                            result: dict | str
                            if "application/json" in content_type:
                                result = resp.json()
                            else:
                                text_body = resp.text
                                try:
                                    result = json.loads(text_body)
                                except Exception:
                                    result = text_body
                            try:
                                if isinstance(result, dict):
                                    logger.info(f"RESPOSTA DO AGENTE (HTTP JSON): {json.dumps(result, ensure_ascii=False)}")
                                else:
                                    logger.info(f"RESPOSTA DO AGENTE (HTTP TEXT): {result}")
                            except Exception:
                                pass
                            # Propagar a instancia usada para a resposta
                            if isinstance(result, dict) and not result.get("evo_instance_id"):
                                result["evo_instance_id"] = instance_id
                            return result
                        except requests.RequestException as e:
                            logger.error(f"Erro ao chamar endpoint do agente: {e}")
                            return {"status": "error", "detail": str(e)}
                    # REMOVA ESTAS DUAS LINHAS:
                    # result = requests.post(url,data)
                    # return result
            else:

                return None
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {str(e)}")

    def _log_message_info(self, sender_name: str, sender_phone: str, message_content: str):
        # Log simples
        pass

    async def send_evolution_message(self, to_number: str, message_text: str, instance_id: str | None = None):
        #print("@@@@@@  send_evolution_message()")
        try:
            #print("=====  send_evolution_message()", to_number,message_text, instance_id)
            effective_instance_id = instance_id or EVOLUTION_API_INSTANCE_ID
            url = f"{EVOLUTION_API_BASE_URL}/message/sendText/{effective_instance_id}"
            payload = {"number": to_number, "text": message_text}
            headers = {"apikey": EVOLUTION_API_KEY, "Content-Type": "application/json"}
            resp = requests.post(url, headers=headers, json=payload, timeout=10)
            #print("resp",resp)
            content_type = (resp.headers.get("content-type") or "").lower()
            if "application/json" in content_type:
                data = resp.json()
                #print("data",data)
            else:
                data_text = resp.text
                try:
                    data = json.loads(data_text)
                except Exception:
                    data = {"raw": data_text}
            if resp.status_code != 200:
                return {"status": "error", "http_status": resp.status_code, "detail": data}
            return data
        except requests.RequestException as e:
            logger.error(f"Erro ao enviar mensagem Evolution API: {e}")
            return {"status": "error", "detail": str(e)}

    async def send_evolution_media(self, to_number: str, media_url: str, media_type: str, caption: str = "", instance_id: str | None = None):
        #print("&&&&&&& send_evolution_media()")
        try:
            effective_instance_id = instance_id or EVOLUTION_API_INSTANCE_ID
            url = f"{EVOLUTION_API_BASE_URL}/message/sendMedia/{effective_instance_id}"
            payload = {
                "number": to_number,
                "mediatype": media_type,
                "fileName": "image.jpg",
                "caption": caption,
                "media": media_url
            }
            headers = {
                "apikey": EVOLUTION_API_KEY,
                "Content-Type": "application/json"
            }
            resp = requests.post(url, headers=headers, json=payload, timeout=10)
            content_type = (resp.headers.get("content-type") or "").lower()
            if "application/json" in content_type:
                data = resp.json()
            else:
                data_text = resp.text
                try:
                    data = json.loads(data_text)
                except Exception:
                    data = {"raw": data_text}
            if resp.status_code != 200:
                return {"status": "error", "http_status": resp.status_code, "detail": data}
            return data
        except requests.RequestException as e:
            logger.error(f"Erro ao enviar mídia Evolution API: {e}")
            return {"status": "error", "detail": str(e)}

    async def get_instance_status(self):
        try:
            url = f"{EVOLUTION_API_BASE_URL}"
            response = requests.get(url)
            response.raise_for_status()
            status_data = response.json()
            return status_data
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao verificar status da instância: {e}")
            raise


@router.get("/webhook/{instance_id}")
async def webhook_verification(instance_id: str, request: Request):

    print("===== webhook_verification() =====")
    return {"status": "webhook evolution verified: " + instance_id}


@router.post("/webhook/{instance_id}")
async def handle_evolution_webhook(instance_id: str, request: Request):
    print("@@@@@@ handle_evolution_webhook")  
    print(f"instance_id: {instance_id}")  
    try:
        body = await request.json()
        #logger.info(f"body: {body}")

        async def process_and_respond():
            try:
                print("=== INICIANDO PROCESS_AND_RESPOND ===")
                evolution_api = AgentEvolutionZap()
                result = await evolution_api.process_message(body, instance_id)
                print(f"=== RESULT RECEBIDO: {result} ===")
                try:
                    if isinstance(result, dict):
                        logger.info(f"RESPOSTA DO AGENTE (BRUTA): {json.dumps(result, ensure_ascii=False)}")
                    else:
                        logger.info(f"RESPOSTA DO AGENTE (BRUTA TEXTO): {result}")
                except Exception:
                    pass

                data = body.get("data", {})
                from_me = data.get("key", {}).get("fromMe", False)
                #print(f"=== FROM_ME: {from_me} ===")
                #print(f"=== RESULT EXISTS: {bool(result)} ===")

                if result and not from_me:
                    sender_jid = data.get("key", {}).get("remoteJid", "")
                    phone_number = sender_jid.replace("@s.whatsapp.net", "").replace("@g.us", "")
                    print(f"=== PHONE_NUMBER: {phone_number} ===")
                    # Filtro desabilitado para testes - permite qualquer número
                    # if phone_number != "553191149571":
                    #     print("=== RETORNANDO - PHONE_NUMBER DIFERENTE ===")
                    #     return
                    
                    print("=== CONTINUANDO PROCESSAMENTO ===")
                    # Resolver a instância a partir de diversas fontes (prioridade):
                    # 1) result["evo_instance_id"] (resposta do agente)
                    # 2) data["evo_instance_id"] (se provedor enviar)
                    # 3) data["instanceId"] (Evolution pode enviar nome ou UUID)
                    # 4) instance_id da rota
                    base_instance_raw = None
                    if isinstance(result, dict):
                        base_instance_raw = result.get("evo_instance_id")
                    if not base_instance_raw:
                        base_instance_raw = body.get("evo_instance_id") or data.get("evo_instance_id")
                    if not base_instance_raw:
                        base_instance_raw = data.get("instanceId")
                    if not base_instance_raw:
                        base_instance_raw = instance_id

                    print(f"=== BASE_INSTANCE_RAW: {base_instance_raw} ===")

                    if is_named_instance(str(base_instance_raw)):
                        normalized_instance = normalize_evo_instance_id(str(base_instance_raw))
                    else:
                        normalized_instance = EVOLUTION_API_INSTANCE_ID

                    #print(f"=== NORMALIZED_INSTANCE: {normalized_instance} ===")

                    # Lista de mensagens
                    if isinstance(result, dict) and result.get("messages") and isinstance(result["messages"], list):
                        print(f"=== PROCESSANDO LISTA DE MENSAGENS: {len(result['messages'])} ===")
                        for i, message_data in enumerate(result["messages"]):
                            try:
                                #print(f"=== PROCESSANDO MENSAGEM {i}: {message_data} ===")
                                # Permitir override por mensagem
                                msg_instance_raw = message_data.get("evo_instance_id") or base_instance_raw
                                msg_instance = msg_instance_raw
                                #print(f"=== MSG_INSTANCE: {msg_instance_raw} ===")
                                
                                if message_data.get("message"):
                                    pass
                                    #logger.info(f"AGENTE -> TEXTO[{i}] : {message_data.get('message')}")
                                if message_data.get("media_url"):
                                    #logger.info(
                                        #f"AGENTE -> MIDIA[{i}] : type={message_data.get('media_type','image')} url={message_data.get###33 #('media_url')} caption={message_data.get('message','')}"
                                    #)
                                    result_media = await evolution_api.send_evolution_media(
                                        phone_number,
                                        message_data["media_url"],
                                        message_data.get("media_type", "image"),
                                        message_data.get("message", ""),
                                        msg_instance
                                    )
                                    #print(f"=== RESULTADO ENVIO MÍDIA {i}: {result_media} ===")
                                elif message_data.get("message"):
                                    #print(f"=== ENVIANDO TEXTO {i} ===")
                                    result_text = await evolution_api.send_evolution_message(
                                        phone_number,
                                        message_data["message"],
                                        msg_instance,
                                    )
                                    #print(f"=== RESULTADO ENVIO TEXTO {i}: {result_text} ===")
                                if i < len(result["messages"]) - 1:
                                    await asyncio.sleep(0.5)
                            except Exception as e:
                                print(f"=== ERRO PROCESSANDO MENSAGEM {i}: {e} ===")
                                continue
                    # Mensagem única com mídia
                    elif isinstance(result, dict) and result.get("media_url"):
                        single_instance_raw = result.get("evo_instance_id") or base_instance_raw
                        single_instance = (
                            normalize_evo_instance_id(single_instance_raw)
                            if is_named_instance(str(single_instance_raw))
                            else normalized_instance
                        )
                        logger.info(
                            f"AGENTE -> MIDIA ÚNICA: type={result.get('media_type','image')} url={result.get('media_url')} caption={result.get('message','')}"
                        )
                        await evolution_api.send_evolution_media(
                            phone_number,
                            result["media_url"],
                            result.get("media_type", "image"),
                            result.get("message", ""),
                            single_instance
                        )
                    # Mensagem única de texto
                    elif isinstance(result, dict) and result.get("message"):
                        single_instance_raw = result.get("evo_instance_id") or base_instance_raw
                        single_instance = (
                            normalize_evo_instance_id(single_instance_raw)
                            if is_named_instance(str(single_instance_raw))
                            else normalized_instance
                        )
                        logger.info(f"AGENTE -> TEXTO ÚNICO: {result.get('message')}")
                        await evolution_api.send_evolution_message(
                            phone_number,
                            result["message"],
                            single_instance,
                        )
            except Exception as e:
                logger.error(f"Erro no processamento assíncrono do webhook: {e}")

        # Agenda processamento em background e responde imediatamente
        asyncio.create_task(process_and_respond())
        return {"status": "accepted"}

    except Exception as e:
        return {"status": "error", "message": str(e)}

@router.post("/webhook/{instance_id}/messages-upsert")
async def handle_evolution_webhook_messages_upsert(instance_id: str, request: Request):
    # Delegar para o mesmo handler para manter a lógica centralizada
    return await handle_evolution_webhook(instance_id, request)

@router.get("/status-evolution")
async def get_status():
    try:
        evolution_api = AgentEvolutionZap()
        status = await evolution_api.get_instance_status()
        return status
    except Exception as e:
        logger.error(f"Erro ao obter status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-message-evolution")
async def send_message_endpoint(request: Request):
    try:
        body = await request.json()
        logger.info(f"body: {body}")
        to_number = body.get("to")
        message = body.get("message")
        if not to_number or not message:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'message' são obrigatórios")
        evolution_api = AgentEvolutionZap()
        result = await evolution_api.send_evolution_message(to_number, message)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-media-evolution")
async def send_media_endpoint(request: Request):
    try:
        body = await request.json()
        to_number = body.get("to")
        media_url = body.get("media_url")
        media_type = body.get("media_type", "document")
        caption = body.get("caption", "")
        if not to_number or not media_url:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'media_url' são obrigatórios")
        evolution_api = AgentEvolutionZap()
        result = await evolution_api.send_evolution_media(to_number, media_url, media_type, caption)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.error(f"Erro ao enviar mídia: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Exemplo de uso (para testes)
if __name__ == "__main__":
    import asyncio
    async def teste_resposta_texto():
        evolution_api = AgentEvolutionZap()
        await evolution_api.send_evolution_message("553184198720", "Oi carlos, boa noite", "gptalk-zaplocal"), 
        status = await evolution_api.get_instance_status()
        print(f"Status: {status}")

    async def teste_resposta_imagem():
        evolution_api = AgentEvolutionZap()
        status = await evolution_api.get_instance_status()
        phone_number = "553184198720"

        message = {
                    "status": "success",
                    "message": "Em que posso ajuda-lo?",
                    "media_url": "https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png",
                    "media_type": "image"
                }
            
        logger.info("=== PASSO 8: Processando resposta única com mídia ===")
        logger.info(f"Enviando mídia única: {message['media_url']}")
        result = await evolution_api.send_evolution_media(
            phone_number,
            message["media_url"],
            message.get("media_type", "image"),
            message.get("message", "")
        )
        logger.info(f"Resultado: {result}")


    asyncio.run(teste_resposta_texto())
    #asyncio.run(teste_resposta_imagem())
