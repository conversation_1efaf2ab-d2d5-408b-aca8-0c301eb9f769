from .agent_filemanager import <PERSON>Manager
from .agent_mysql import Mysql
from .agent_cpanel import <PERSON><PERSON><PERSON>
from .agent_secret import Secret
from .agent_pexels import Pexels
from .agent_image import Image



from bs4 import BeautifulSoup
import json, os,time



class Site:
    def __init__(self,id=None, pages=None, subdomain=None):
        self.USUARIO_ID = None
        self.ID = None
        self.NOME = None
        self.DOMINIO_PADRAO = None
        self.DOMINIO_PROPRIO = None
        self.AREA = None
        self.OBJETIVO = None
        self.PUBLICO_ALVO = None
        self.FUNCIONALIDADES = None
        self.DESIGN_ESTILO = None 
        self.pexels = Pexels()
        self.cpanel = Cpanel()
        self.mysql = Mysql()
        self.PAGES = pages if pages else {}
        self.fileManager = FileManager()
        self.secret = Secret()
        self.SUBDOMINIO =  subdomain if subdomain else self.secret.GPTALK_SITE_SUBDOMAIN
        self.image = Image()
   
    #==========
    async def start_site(self, template=None):
        if not template:
            template = 'default'

        base_dir = os.path.dirname(os.path.abspath(__file__))
        subdir = os.path.join('site', 'template', 'default')
            
        self.PAGES['index.html'] = await  self.fileManager.load_text_file(os.path.join(base_dir, subdir ),"index.html")
        self.PAGES['cabecalho.htm'] = await  self.fileManager.load_text_file(os.path.join(base_dir, subdir), "cabecalho.htm")
        self.PAGES['rodape.htm'] = await  self.fileManager.load_text_file(os.path.join(base_dir, subdir), "rodape.htm")


    async def load_default_page(self):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        subdir = os.path.join('site', 'template','default')  


        default_page = await  self.fileManager.load_text_file(os.path.join(base_dir, subdir),"index.html")
        return default_page 



    async def extract_images_html(self, html_code):
        #print("========== extract_images_html ========== ")
              # Parse the HTML code
        soup = BeautifulSoup(html_code, 'html.parser')

        # Find all image tags
        images = soup.find_all('img')
        #print("images",images)
        # Extract ID, SRC, and ALT from each image
        image_data = [
            {
                "ID": img.get('id'),
                "SRC": img.get('src'),
                "ALT": img.get('alt'),
                "WIDTH": self.image.get_image_placeholder_size(img.get('src'), "w" ),
                "HEIGHT": self.image.get_image_placeholder_size(img.get('src'), "h" )
            }
            for img in images
        ]
        #print("image_Data",image_data)
        return image_data



    async def update_images_pexels(self,html_code):
        #print("========== update_images_pexels() ==========")
        
        image_data = await self.extract_images_html(html_code)
        #print('image_data()',image_data)


        if image_data is None or len(image_data) == 0:
           return html_code

        image_result = []

        # Process each image data dictionary with image_create method
        for data in image_data:
           result = {}
           result['ID'] = data['ID']
           result['ALT'] = data['ALT']
           #print("result parcial",result)
           result['SRC'] = await self.pexels.image_search(data['ID'],result['ALT'],data["WIDTH"], data["HEIGHT"])
           #print("result completo", result)
           image_result.append(result)
         
  
        html_code_updated = await self.replace_images_html(html_code,image_result)
        return html_code_updated

    @staticmethod
    async def replace_images_html(html_code,image_result):
                          # Parse the original HTML code
       soup = BeautifulSoup(html_code, 'html.parser')

       # Update the 'src' and 'alt' attributes for each image tag
       for image in image_result:
          # Find the image tag by its ID
          img_tag = soup.find(id=image['ID'])
          if img_tag:
           #print("Update the 'src' and 'alt' attributes")
            print("src antes",img_tag['src'])
            img_tag['src'] = image['SRC']
            img_tag['alt'] = image['ALT']
            #print("src depois",img_tag['src'])

       # Return the updated HTML as a string
       return str(soup)


    async def get_template(self,name):
        file = "json_string.py"
        templateDir  = name.split('_')[1]
        
        template = await self.fileManager.load_text_file(templateDir,file)
        #print(template)
        return template
    
    #========
    async def fetch(self,id=None):
     
        result = await self.mysql.query(f"SELECT * FROM SITE WHERE ID = {id}")
        result = result[0]
        if result:
            self.ID = result['ID']
            self.USUARIO_ID = result['USUARIO_ID']
            self.NOME = result['NOME']
            self.DOMINIO_PROPRIO = result['DOMINIO_PROPRIO']
            self.DOMINIO_PADRAO = result['DOMINIO_PADRAO']
            self.AREA = result['AREA']
            self.OBJETIVO = result['OBJETIVO']
            self.PUBLICO_ALVO = result['PUBLICO_ALVO']
            self.FUNCIONALIDADES = result['FUNCIONALIDADES']
            self.DESIGN_ESTILO = result['DESIGN_ESTILO']
            await self.fetch_contents() #carrega paginas
            return {"STATUS": "R",  "RESULT": json.dumps(result)}
        return {"STATUS": "F",  "RESULT": "Site não encontrado"}
    
    #==========
    async def save(self):
        site = {}
        if self.ID and self.ID > 0:
            site['ID'] = self.ID
        site['NOME'] = self.NOME
        site['AREA'] = self.AREA
        site['OBJETIVO'] = self.OBJETIVO
        site['FUNCIONALIDADES'] = self.FUNCIONALIDADES
        site['PUBLICO_ALVO'] = self.PUBLICO_ALVO
        site['DESIGN_ESTILO'] = self.DESIGN_ESTILO


        if 'ID' in site and site['ID']>0 :
            await self.mysql.update('SITE',site)
        else:
            id = await self.mysql.add('SITE',site)
            return id
        

    async def fetch_contents(self):
        #print("========== fetch_contents ==========",self.NOME)

        if self.NOME:
            data = {}
        else:
            return {"STATUS":"","RESULT":""}
        
        subdirectory = "site/sites/" + self.NOME
        
        self.PAGES = {}
        response = await self.cpanel.list_files (subdirectory)
        #print("fetch content files",response)

        if response['STATUS'] == "R":
            files = response['RESULT']
            
            for file in files:
              
                #data["FILENAME"] = file
                content = await self.cpanel.get_file_content(subdirectory,file)  # Aqui você precisa de uma função assíncrona real      
                self.PAGES[file] =  content
     
        
        
        
        
    async def save_contents(self):
      #print("########## site_save_content########## ")
      #print("pages",self.pages)
      nome = self.NOME
    
      self.SUBDIRECTORY = "site/sites/" + self.NOME

      for arquivo, cod in self.PAGES.items():
        #print ("arquivo",arquivo)
        #print ("cod",cod)
        # Armazena o nome do arquivo e o código HTML associado
        result = await self.cpanel.subdomain_file_save(content=cod,file_name=arquivo,upload_directory=self.SUBDIRECTORY)
        #print(result)
      return result


    async def add_unique_ids(self,html):
     #print("==========adiciona_id_unicos")
     # Parse the HTML
     soup = BeautifulSoup(html, 'html.parser')

     # Iterate through each element starting from the root
     for element in soup.recursiveChildGenerator():
         if element.name:  # This checks if it is a tag and not a string
             
             if not element.has_attr('id'):  # Check if the element does not have an ID
                # Assign a unique ID using the current time in milliseconds
                element['id'] = str(int(time.time() * 1000))
                # To ensure each ID is unique, pause the script slightly
                time.sleep(0.001)

     # Return the modified HTML

     return str(soup)


