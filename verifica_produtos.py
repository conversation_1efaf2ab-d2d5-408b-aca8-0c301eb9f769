import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== VERIFICAÇÃO PRODUTOS DA VENDA ===")
    
    # Verificar se a tabela existe
    tabelas = await mysql.query("SHOW TABLES LIKE 'VENDA_PRODUTO'")
    print(f"Tabela VENDA_PRODUTO existe: {bool(tabelas)}")
    
    if tabelas:
        # Verificar estrutura da tabela
        estrutura = await mysql.query("DESCRIBE VENDA_PRODUTO")
        print(f"\nEstrutura da tabela VENDA_PRODUTO:")
        for campo in estrutura:
            print(f"  - {campo['Field']}: {campo['Type']} {'(NOT NULL)' if campo['Null'] == 'NO' else '(NULL)'}")
        
        # Verificar produtos da venda 23190
        produtos_venda = await mysql.query("SELECT * FROM VENDA_PRODUTO WHERE VENDA_ID = '23190'")
        print(f"\nProdutos da venda 23190:")
        if produtos_venda:
            for produto in produtos_venda:
                print(f"  - ID: {produto.get('ID')}")
                print(f"    VENDA_ID: {produto.get('VENDA_ID')}")
                print(f"    PRODUTO_ID: {produto.get('PRODUTO_ID')}")
                print(f"    PRECO: {produto.get('PRECO')}")
                print(f"    QTDE: {produto.get('QTDE')}")
                print(f"    TOTAL: {produto.get('TOTAL')}")
                print(f"    NEGOCIO_ID: {produto.get('NEGOCIO_ID')}")
                print(f"    EXCLUIDO: {produto.get('EXCLUIDO')}")
                print()
        else:
            print("  ❌ Nenhum produto encontrado para a venda 23190!")
        
        # Verificar últimos produtos inseridos
        print("\nÚltimos 5 produtos inseridos:")
        ultimos = await mysql.query("SELECT * FROM VENDA_PRODUTO ORDER BY ID DESC LIMIT 5")
        for produto in ultimos:
            print(f"  ID: {produto.get('ID')}, VENDA_ID: {produto.get('VENDA_ID')}, PRODUTO_ID: {produto.get('PRODUTO_ID')}, TOTAL: {produto.get('TOTAL')}")
        
        # Verificar total de registros
        total = await mysql.query("SELECT COUNT(*) as total FROM VENDA_PRODUTO")
        print(f"\nTotal de produtos na tabela: {total[0]['total'] if total else 0}")
        
        # Verificar produtos com VENDA_ID = 0 (problemáticos)
        problemas = await mysql.query("SELECT COUNT(*) as total FROM VENDA_PRODUTO WHERE VENDA_ID = 0")
        if problemas and problemas[0]['total'] > 0:
            print(f"⚠️  Produtos com VENDA_ID = 0 (problemáticos): {problemas[0]['total']}")
    
    else:
        print("❌ Tabela VENDA_PRODUTO não existe!")

if __name__ == "__main__":
    asyncio.run(main()) 