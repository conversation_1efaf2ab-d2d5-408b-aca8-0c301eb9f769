#!/usr/bin/env python3
"""
Script para testar o endpoint business/get da forma correta
"""

import requests
import json

def testar_endpoint():
    """Testa o endpoint business/get com a forma correta"""
    
    # URL correta (POST com path parameter)
    url = "http://127.0.0.1:8000/api/agent/business/get/4015743441"
    
    # Body com as colunas desejadas
    data = {
        "columns": ["nome", "razao_social", "cnpj_cpf"]
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("🔍 Testando endpoint business/get...")
        print(f"📍 URL: {url}")
        print(f"📄 Method: POST")
        print(f"📦 Data: {json.dumps(data, indent=2)}")
        
        # Fazer a requisição POST
        response = requests.post(url, json=data, headers=headers)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("✅ Sucesso! Endpoint funcionando corretamente.")
        else:
            print(f"❌ Erro: Status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Erro: Não foi possível conectar ao servidor. Verifique se o servidor está rodando.")
    except Exception as e:
        print(f"❌ Erro: {str(e)}")

if __name__ == "__main__":
    testar_endpoint() 