from .agent_agent import Agent
from .agent_user import User
from .agent_business import Business
from .agent_converter import Converter
from .agent_session import Session
from .agent_llm import LLM
from .agent_whatsappwhapi import WhatsAppWhapi
from .agent_functioncall import FunctionCall
from .agent_message import Message
from .agent_time import Time
from .agent_product import Product
from .agent_mysql import Mysql
from .agent_user import User
from .restaurant.functions_maps import get_meal_day_map

class Restaurant:

    def __init__(self,agente=None,usuario=None,negocio:Business=Business()):
        #print("========== Restaurant__Init__() ==========")
        
        self.agente = agente if agente else Agent()
        self.usuario  = usuario if usuario else User()
        self.negocio = negocio 
        self.converter = Converter()
        self.time = Time()
        self.produto = Product()
        self.mysql = Mysql()
        self.function_map = {
        "get_meal_day": get_meal_day_map,
        }
        


    
    async def get_system(self):
      #print("========== get_system() ==========")
      dia_da_semana = self.time.get_day_of_week(self.time.today())
      data_hora = self.time.get_date_hour()
      hora_atual = self.time.get_hour()
      categorias = await self.mysql.fetch("ID,NOME","PRODUTO_CATEGORIA",[f"NEGOCIO_ID={self.negocio.ID}"])
      #print("categorias",categorias)
      return f"""
    Você é  {self.agente.NOME},  uma atendente criada pela IAPN para ser a atendente do restaurante {self.negocio.NOME}.
    Vocé é cordial, atenciosa e bem educada. Sua função principal é atender os clientes do restaurante, anotar os pedidos e dar informações sobre o restaurante e sobre os pratos do cardápio.
    Segue abaixo informações que você pode passar para os clientes, e o processo de atendimento e registro de pedido:

    [INFORMAÇÕES]

    Localização: 
      O Restaurante fica na rua Lidia, 720, no BAirro Pirajá, em Belo Horizonte, MG

    Horário de Funcionamento:
     {self.negocio.HORARIO}
        IMPORTANTE:
    Agora  são {hora_atual} e hoje é {dia_da_semana}. Só aceite pedidos se hoje for um dia constante na tabela de hoários e se a hora atual esta dentro do horário e funcionamento.

    Categorias de produtos do nosso cardápio:
    {categorias}



    [PROCESSO DE ATENDIMENTO]
    0 - Agora são {hora_atual} e hoje é {dia_da_semana}. Verificar se o pedido esta sendo feito dentro do horario de funcionamento da empresa para a dia da semana de hoje, que é {dia_da_semana}. O pedido só pode ser feito se o horario atual for depois do horário de inicio e antes do horário final do horário de funcionamento.
    1 - Se o cliente perguntar qual o prato do dia, verifique usando sua função get_meal_day() qual é o prato do dia dia e informe ao cliente. Caso ele pergunte sobre o prato de outro dia, gentilmente peça o para entrar em contato no dia da semana que o prato é servido. Ele não perguntando, você porntamente informe qual o prato principal e a opção do dia.
    2 - Registre em sua memória todos os pedidos dos clientes, e ao final do atendimento, informe ao cliente o total do pedido e pergunte se ele deseja algo mais.
    3 - Peça  ao cliente o endereço para entrega e informe o prazo de entrega, que é de 40 minutos.
    4 - Pergunte ao cliente como ele deseja pagar, se é por pix, cartão de crédito ou dinheiro. Infomre que o pagamento deverá ser feito ao entregador no ato da entrega.
    5 - Ao final do atendimento, agradeça ao cliente pela preferência e informe que agora é só agaurdar a entrega.  Deseje um bom apetite.

    Resumindo o processo que dever ser executado:
    0 - Caso o pedido seja fora do horário de atendimento, informe ao cliente que infelizmente o pedido não poderá ser atendido mais hoje, e o convite a voltar e fazer outro dia.
    1 - informar o prato do dia
    2 - Ver quais e quantos pratos do dia ou opção que o cliente deseja
    3 - Perguntar o endereço para entrega
    4 - Informar o total do pedido e Perguntar a forma de pagamento
    5 - Agradecer pela preferência e informar o prazo de entrega.

    O pedido só será finalizado com todos estes passos seguidos. Mesmo que o cleinte force a execução de algum passo fora da ordem, por exemplo, perguntar qual a taxa de entrega antes de fazer o pedido.
    Mas logo em seguida retorne ao passo correto e ainda pendente, buscando completar as informações que faltam para finalizar o pedido.

    Caso o cleinte peça informações sobre qualquer outro assunto que não seja sobre o restaurante, informe que você é a atendente do restaurante Tropeiro da Dany e que não pode ajudar com outras informações, nem realizar outros serviços que não seja o atendimento do restaurante.
    Quando alguma de suas funções requererem o id do restaurante, utilize este {self.negocio.ID}.
      """
   
    async def get_meal_day(self,negocio_id):
       print("@@@@@@@@@@ prato do dia @@@@@@@@@@")
       dia = self.time.get_number_day_of_week()
       print("dia_da_semana",dia)
       print("negocio_id",negocio_id)
       pratos = await self.mysql.fetch("ID,NOME,DESCR,PRECO,STATUS,VARIAVEL,VARIACAO,WHATSAPP_MIDIA","VISAO_PRODUTO_RESTAURANTE",[f"NEGOCIO_ID={negocio_id}",f"DIA_SEMANA_ID={dia}"],order = "STATUS DESC")
       print("pratos",pratos)
       return pratos
    
  
    async def direct_response_whatsappwhapi_get_meal_day(self,resposta_funcao):
       print("\n========== direct_response_whatsappwhapi_get_meal_day() ==========","\n")
       print("resposta_funcao",resposta_funcao)

       if not resposta_funcao:
            return "Não temos prato do dia hoje."
       else:
            status = ""
            nome = ""
            descricao = ""

            resposta = []
            for prato in resposta_funcao:
                print("\n")
                status = prato["STATUS"]
                nome = prato["NOME"]
                descricao = prato["DESCR"]
            
                caption = status + ": " + nome + "\n\n ## " + descricao +"\n"
                variacoes = prato["VARIACAO"].split("|")
                #print("variacoes",variacoes)
                for variacao in variacoes:
                    caption += "\n" + Restaurant.formata_valor_atributos(variacao.strip( ))
                print(caption, "\n")
                pt = {}
                if prato["WHATSAPP_MIDIA"]==1:
                    pt["type"] = "video"
                    pt["url"] = f"https://gptalk.com.br/site/sites/{self.negocio.NOME_ID}/produtos/imagens/{prato['ID']}-0.png?id=3"

                if prato["WHATSAPP_MIDIA"]==2:
                    pt["type"] = "video"
                    pt["url"] = f"https://gptalk.com.br/site/sites/{self.negocio.NOME_ID}/produtos/videos/{prato['ID']}-0.mp4?id=5"
                
                pt["caption"] = caption
                
                resposta.append(pt)
       
       return resposta

    @staticmethod
    def formata_valor_atributos (input_str):
        import re
        
        # Extrair o ID, PREÇO e TAMANHO da string
        match = re.search(r'ID: (\d+), PRECO: ([\d.]+) \(TAMANHO: (\w+)\)', input_str)
        if not match:
            raise ValueError("A string de entrada não está no formato esperado")
    
        preco = float(match.group(2))
        tamanho = match.group(3)
    
        # Formatar o preço
        preco_formatado = f'R$ {preco:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.')
    
        # Criar a nova string
        nova_string = f'{preco_formatado} - {tamanho}'
    
        return nova_string

  

    async def get_menu(self,categoria_id,limit=10,offset=0):
       print("========== get_menu() ==========")
       print("categoria_id", categoria_id)
       produtos = await self.mysql.fetch("NOME,DETALHES,PRECO,VARIAVEL,STATUS","VISAO_PRODUTO_RESTAURANTE",[f"NEGOCIO_ID={self.negocio.ID}",f"CATEGORIA_ID={categoria_id}"],limit=limit,offset=offset)
       return produtos


if __name__ == "__main__":
    import asyncio
    async def main():
       negocio = Business()
       await negocio.fetch("*",[f"ID=3"])
       restaurant = Restaurant(negocio=negocio)
       
       
       resposta_funcao =  [{'ID': 25, 'NOME': 'Tropeiro', 'DESCR': 'Arroz ,tropeiro, bife acebolado, couve e salada', 'PRECO': None, 'STATUS': 'Principal', 'VARIAVEL': 0, 'VARIACAO': 'ID: 53, PRECO: 15.00 (TAMANHO: Grande) | ID: 81, PRECO: 10.00 (TAMANHO: Pequeno)', 'WHATSAPP_MIDIA': 2}, {'ID': 26, 'NOME': 'Lombo de panela', 'DESCR': 'Arroz, feijão,lombo de panela e saladas', 'PRECO': None, 'STATUS': 'Opção', 'VARIAVEL': 0, 'VARIACAO': 'ID: 54, PRECO: 15.00 (TAMANHO: Grande) | ID: 82, PRECO: 10.00 (TAMANHO: Pequeno)', 'WHATSAPP_MIDIA': 2}]
       
       
       resposta = await restaurant.direct_response_whatsappwhapi_get_meal_day(resposta_funcao)
       print("\n\nresposta direta",resposta,"\n\n")
    async def main_prato_do_dia():
      llm = LLM()
      usuario = User()
      functionCall = FunctionCall()
      llm_modelo = llm.model_openai.gpt_4_o
      historico = []
      recebido ="quais são os pratos do restaurante?"
      mensagem = {"role":"user","content":recebido} 
      #chama o modelo de linguagem para processar a mensagem e agurda a resposta
      agente = Agent()
      await agente.fetch_agent_id("*",10)
      canal_id = agente.CANAL_ID
      conversa_id = 45
      resposta = await functionCall.chat(
      historico=historico,
      mensagemRecebida=mensagem,
      llm_modelo=llm_modelo,
      llm=llm,
      agente=agente,
      usuario=usuario,
      conversa_id = conversa_id, 
      canal_id=canal_id)
        #print("##### recebido ##### \n", recebid
      print("##### recebido ##### \n", recebido)
      print("##### resposta##### \n",resposta)


    asyncio.run(main())