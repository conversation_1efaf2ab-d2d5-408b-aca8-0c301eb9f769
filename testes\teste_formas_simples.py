import asyncio
import sys
import os

sys.path.append('.')

async def main():
    try:
        # Importar diretamente da conexão MySQL
        from api.agent.agent_mysql import Mysql
        mysql = Mysql()
        
        print("=== TESTE SIMPLES - FORMAS PAGAMENTO ===")
        
        # Verificar tabelas relacionadas a venda
        tabelas = await mysql.query("SHOW TABLES LIKE '%VENDA%'")
        print("Tabelas com VENDA:")
        for tabela in tabelas:
            print(f"  - {tabela}")
        
        # Verificar tabelas relacionadas a pagamento/forma
        tabelas_pag = await mysql.query("SHOW TABLES LIKE '%PAGAMENTO%'")
        print("\nTabelas com PAGAMENTO:")
        for tabela in tabelas_pag:
            print(f"  - {tabela}")
        
        # Verificar se a venda 23190 existe
        venda = await mysql.query("SELECT ID, TOTAL_RS FROM VENDA WHERE ID = 23190")
        print(f"\nVenda 23190: {venda}")
        
        # Verificar se existe tabela VENDA_FORMA_PAGAMENTO
        try:
            estrutura = await mysql.query("DESCRIBE VENDA_FORMA_PAGAMENTO")
            print(f"\nEstrutura VENDA_FORMA_PAGAMENTO: {estrutura}")
            
            formas = await mysql.query("SELECT * FROM VENDA_FORMA_PAGAMENTO WHERE VENDA_ID = 23190")
            print(f"Formas de pagamento da venda 23190: {formas}")
            
        except Exception as e:
            print(f"\n❌ Erro ao acessar VENDA_FORMA_PAGAMENTO: {e}")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 