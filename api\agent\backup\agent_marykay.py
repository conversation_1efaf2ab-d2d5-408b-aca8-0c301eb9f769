# -*- coding: utf-8 -*- # Boa prática declarar encoding

# 1. Importar bibliotecas necessárias
from playwright.async_api import async_playwright
from .agent_email import Email
from functions.util.generate_unique_id import generate_unique_id
from bs4 import BeautifulSoup
from .agent_mysql import Mysql
import asyncio
import time
from urllib.parse import urljoin
import re
import json


# Opcional: Para evitar detecção anti-bot (descomente se necessário após instalar)
# pip install playwright-stealth
# from playwright_stealth import stealth_async

# 2. Definir constantes e variáveis iniciais
BASE_URL = "https://loja.marykay.com.br/todos-os-produtos?page=1" # URL única a ser processada
# TOTAL_PAGINAS = 1  # Removido - não há mais loop de páginas
SITE_BASE = "https://loja.marykay.com.br"
lista_global_produtos = []

negocio_idx = "5544332211"
mysql = Mysql()

# 3. Funções auxiliares para limpeza de dados
def limpar_preco(texto_preco):
    """Remove 'R$', espaços, converte vírgula para ponto e retorna float."""
    if texto_preco is None:
        return None
    try:
        numeros = re.sub(r'[^\d,]', '', texto_preco)
        numeros = numeros.replace(',', '.')
        return float(numeros)
    except (ValueError, TypeError):
        print(f"  Aviso: Não foi possível converter preço: '{texto_preco}'")
        return None

def limpar_percentual(texto_percentual):
    """Remove '%' e retorna int."""
    if texto_percentual is None:
        return None
    try:
        numeros = re.sub(r'[^\d]', '', texto_percentual)
        return int(numeros)
    except (ValueError, TypeError):
        print(f"  Aviso: Não foi possível converter percentual: '{texto_percentual}'")
        return None

# 4. Função para rolar a página e carregar todos os produtos (agora async)
async def scroll_to_load_all_products(page, max_scrolls=20, scroll_delay=2.5):
    """Rola a página para carregar todos os produtos dinamicamente (versão async)."""
    print("  Rolando a página para carregar todos os produtos...")
    last_height = await page.evaluate("document.body.scrollHeight")
    for i in range(max_scrolls):
        # Rolar incrementalmente por uma janela
        await page.evaluate("window.scrollBy(0, window.innerHeight);")
        await asyncio.sleep(scroll_delay / 2)
        # Rolar até o final da página
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
        await asyncio.sleep(scroll_delay)
        new_height = await page.evaluate("document.body.scrollHeight")
        # Verificar quantos produtos estão visíveis no DOM
        num_produtos = await page.evaluate("document.querySelectorAll('section.vtex-product-summary-2-x-container').length")
        print(f"  Rolagem {i+1}/{max_scrolls} - Produtos visíveis: {num_produtos}")
        if new_height == last_height and num_produtos >= 15:
            print("  Não há mais conteúdo para carregar e número esperado de produtos atingido.")
            break
        elif new_height == last_height:
            print("  Não há mais conteúdo para carregar, mas menos produtos do que esperado.")
            break
        last_height = new_height
    # Pausa extra para garantir carregamento
    print("  Pausa extra para carregamento final...")
    await asyncio.sleep(5)

async def gera_mysql_insert(produto_atual):
    print("gera_mysql_insert()",produto_atual)
    """
    Prepara os dados do produto em um dicionário e chama mysql.add para inserir na tabela PRODUTO.
    """
    idx = generate_unique_id()
    print("idx",idx)

    # Cria o dicionário com os dados para a tabela PRODUTO
    # As chaves do dicionário devem corresponder exatamente aos nomes das colunas da tabela
    data_to_insert = {
        "IDX": idx,
        "NOME": produto_atual.get("nome"),
        "URL": produto_atual.get("link_produto"),
        "URL_IMAGEM": produto_atual.get("url_imagem"),
        "PRECO_MAIOR": produto_atual.get("preco_original"), # mysql.add deve tratar None como NULL
        "PRECO": produto_atual.get("preco_venda"),       # mysql.add deve tratar None como NULL
        "INFO_PARCELAMENTO": produto_atual.get("info_parcelamento"),
        "NEGOCIO_IDX": negocio_idx
    }
    
    print("Dados para inserir:", data_to_insert)

    try:
        # Chama o método add da classe Mysql, passando o nome da tabela e o dicionário de dados
        inserted_id = await mysql.add('PRODUTO', data_to_insert)
        if inserted_id:
             print(f"  Produto '{data_to_insert.get('NOME')}' inserido via mysql.add com ID: {inserted_id} (IDX: {idx}).")
        else:
             print(f"  Falha ao inserir produto '{data_to_insert.get('NOME')}' via mysql.add (IDX: {idx}). Verificar logs de mysql.add.")

    except Exception as e:
        print(f"ERRO ao chamar mysql.add para produto '{data_to_insert.get('NOME')}' (IDX: {idx}): {e}")
        # Você pode querer relançar o erro ou tratá-lo de outra forma
        # raise e

async def scrapear_produtos():

    # 5. Loop principal para iterar pelas páginas de listagem -> Removido loop, processa apenas BASE_URL
    print("Iniciando o processo de Web Scraping com Playwright...")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        print("  Navegador Playwright (Chromium) iniciado.")

        try:
            # Processa apenas a URL definida em BASE_URL
            url_pagina_atual = BASE_URL
            print(f"\n[Página Única] Acessando: {url_pagina_atual}")

            # 6. Usar Playwright para carregar a página e obter o HTML dinâmico
            pagina_navegador = None
            html_content = None
            try:
                pagina_navegador = await browser.new_page()
                await pagina_navegador.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36     (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7'
                })
                # await stealth_async(pagina_navegador)
                await pagina_navegador.goto(url_pagina_atual, timeout=60000) # Usa diretamente a BASE_URL
                print(f"  Página acessada com Playwright.")

                await scroll_to_load_all_products(pagina_navegador)

                num_produtos = await pagina_navegador.evaluate("document.querySelectorAll('section.vtex-product-summary-2-x-container').length")
                print(f"  Produtos encontrados no DOM antes do parsing: {num_produtos}")

                if num_produtos > 0:
                    print("  Aguardando produtos serem carregados...")
                    await pagina_navegador.wait_for_selector('section.vtex-product-summary-2-x-container',    state='visible', timeout=30000)
                    print(f"  Produtos carregados. Total visível: {num_produtos}")
                else:
                    print("  Nenhum produto visível encontrado, pulando extração de conteúdo.")
                    # Se não houver produtos na única página, podemos sair ou apenas registrar
                    # Neste caso, o código continuará e tentará o parse do HTML (que provavelmente não terá seções de produto)

                html_content = await pagina_navegador.content()

            except Exception as e:
                print(f"  ERRO ao carregar ou processar a página {url_pagina_atual} com Playwright: {e}") # Mensagem atualizada
                if pagina_navegador:
                    await pagina_navegador.close()
                # Como é uma única página, podemos sair do try/finally do browser aqui se desejado
                # raise e # Ou apenas registrar e deixar o finally fechar o browser
            finally:
                # Fecha a página específica se ainda estiver aberta
                if pagina_navegador and not pagina_navegador.is_closed():
                    await pagina_navegador.close()

            # Se não conseguiu obter o conteúdo HTML ou não encontrou produtos visíveis, o processo termina aqui para esta página.
            if html_content is None:
                print(f"  AVISO: Não foi possível obter o conteúdo HTML da página {url_pagina_atual}. Processo encerrado.")
            elif num_produtos == 0:
                 print(f"  AVISO: Nenhum produto encontrado no DOM da página {url_pagina_atual}. Processo encerrado.")
            else:
                # 7. Parsear o HTML obtido pelo Playwright
                try:
                    soup = BeautifulSoup(html_content, 'lxml')
                except Exception as e:
                    print(f"  ERRO ao parsear o HTML da página {url_pagina_atual}: {e}") # Mensagem atualizada
                    # Considerar encerrar se o parse falhar

                if 'soup' in locals(): # Verifica se o soup foi criado com sucesso
                    # 8. Encontrar todas as seções de produto na página
                    secoes_produtos = soup.select('section.vtex-product-summary-2-x-container')
                    print(f"  Encontrados {len(secoes_produtos)} produtos nesta página.")

                    if not secoes_produtos:
                        print(f"  AVISO: Nenhuma seção de produto encontrada no HTML parseado da página {url_pagina_atual}.") # Mensagem atualizada
                    else:
                        # 9. Loop para extrair dados de cada seção de produto
                        produtos_extraidos_count = 0
                        for secao in secoes_produtos:
                            produto_atual = {}
                            try:
                                # Extrair Nome
                                tag_nome = secao.select_one('span.vtex-product-summary-2-x-productBrand')
                                produto_atual['nome'] = tag_nome.get_text(strip=True) if tag_nome else None

                                # Extrair Link Relativo e criar Absoluto
                                tag_link = secao.select_one('a.vtex-product-summary-2-x-clearLink')
                                link_relativo = tag_link['href'] if tag_link and tag_link.has_attr('href') else     None
                                produto_atual['link_produto'] = urljoin(SITE_BASE, link_relativo) if    link_relativo else None

                                # Extrair URL da Imagem
                                tag_img = secao.select_one('img.vtex-product-summary-2-x-imageNormal')
                                produto_atual['url_imagem'] = tag_img['src'] if tag_img and tag_img.has_attr    ('src') else None

                                # Extrair Preço Original (limpando)
                                tag_preco_orig = secao.select_one('div.promotion-price')
                                preco_orig_str = tag_preco_orig.get_text(strip=True) if tag_preco_orig else None
                                produto_atual['preco_original'] = limpar_preco(preco_orig_str)

                                # Extrair Preço de Venda (limpando)
                                tag_preco_venda_container = secao.select_one('span.vtex-store-components-3-x-sellingPriceValue')
                                if tag_preco_venda_container:
                                    partes_preco = tag_preco_venda_container.find_all('span', recursive=False)
                                    preco_venda_str = "".join(p.get_text(strip=True) for p in partes_preco)
                                else:
                                    preco_venda_str = None
                                produto_atual['preco_venda'] = limpar_preco(preco_venda_str)

                                # Extrair Percentual de Desconto (limpando)
                                tag_desconto = secao.select_one('div.promotion-percentage')
                                desconto_str = tag_desconto.get_text(strip=True) if tag_desconto else None
                                produto_atual['percentual_desconto'] = limpar_percentual(desconto_str)

                                # Extrair Info Parcelamento
                                tag_parcel = secao.select_one('span.vtex-product-price-1-x-installments')
                                produto_atual['info_parcelamento'] = tag_parcel.get_text(strip=True) if     tag_parcel else None

                                # Extrair Flag Promocional
                                tag_flag = secao.select_one('span.vtex-product-highlights-2-x-productHighlightText')
                                produto_atual['flag_promocional'] = tag_flag.get_text(strip=True) if tag_flag   else None

                                # Adicionar placeholder para categoria
                                produto_atual['categoria'] = None

                                # Adicionar o produto extraído à lista global e inserir no DB
                                if produto_atual.get('nome'):
                                    lista_global_produtos.append(produto_atual)
                                    await gera_mysql_insert(produto_atual)
                                    produtos_extraidos_count += 1
                                else:
                                    print("  Aviso: Produto pulado por não ter nome W .")

                            except Exception as e:
                                print(f"  ERRO ao processar um produto específico. Detalhes: {e}") # Mensagem atualizada
                                continue # Pula para o próximo produto
                        print(f"\n  Extração finalizada. Total de produtos processados nesta página: {produtos_extraidos_count}")

        finally:
            # Garante que o navegador seja fechado ao final
            if browser and browser.is_connected():
                await browser.close()
                print("\n  Navegador Playwright fechado.")

    # 11. Finalização e Exibição dos Resultados
    print("\n--------------------------------------------------")
    print(f"Processo de scraping finalizado!")
    print(f"Total de produtos extraídos: {len(lista_global_produtos)}")
    print("--------------------------------------------------")

    if lista_global_produtos:
        print(f"\nExibindo todos os {len(lista_global_produtos)} produtos encontrados:")
        for i, prod in enumerate(lista_global_produtos):
            print(f"\n--- Produto {i+1} ---")
            print(json.dumps(prod, indent=2, ensure_ascii=False))

    # Opcional: Salvar os resultados em um arquivo JSON
    # nome_arquivo = 'produtos_marykay.json'
    # try:
    #     with open(nome_arquivo, 'w', encoding='utf-8') as f:
    #         json.dump(lista_global_produtos, f, ensure_ascii=False, indent=4)
    #     print(f"\nDados salvos com sucesso no arquivo: {nome_arquivo}")
    # except Exception as e:
    #     print(f"\nErro ao salvar os dados em JSON: {e}")

    # Opcional: Para scrapear múltiplas páginas dinamicamente
    # Para detectar o número total de páginas, inspecione o elemento de paginação
    # Exemplo:
    # total_paginas = soup.select_one('seletor-da-ultima-pagina').get_text(strip=True)
    # Ou simule cliques no botão "Carregar Mais":
    # while True:
    #     botao_carregar_mais = pagina_navegador.query_selector('seletor-do-botao-carregar-mais')
    #     if not botao_carregar_mais:
    #         break
    #     botao_carregar_mais.click()
    #     time.sleep(5)


if __name__ == "__main__":
    # O import do asyncio foi movido para o topo

    async def main():
       
        await scrapear_produtos()


        # Opcional: Fechar conexão se a classe Mysql tiver um método close
        # Verifique se o método close é async ou sync
        # if hasattr(mysql, 'close') and callable(mysql.close):
        #     try:
        #         if asyncio.iscoroutinefunction(mysql.close):
        #             await mysql.close()
        #         else:
        #             mysql.close()
        #         print("Conexão MySQL fechada.")
        #     except Exception as e:
        #         print(f"Erro ao fechar conexão MySQL: {e}")


    #asyncio.run(main())