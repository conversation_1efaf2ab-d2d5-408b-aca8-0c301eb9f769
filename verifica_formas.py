import asyncio
from api.agent.agent_mysql import Mysql

async def main():
    mysql = Mysql()
    
    print("=== VERIFICAÇÃO FORMAS PAGAMENTO ===")
    
    # Verificar últimas formas de pagamento
    result = await mysql.query("SELECT * FROM VENDA_FORMA_PAGAMENTO ORDER BY ID DESC LIMIT 5")
    print(f"Últimas 5 formas de pagamento:")
    for r in result:
        print(f"  ID: {r.get('ID')}, VENDA_ID: {r.get('VENDA_ID')}, FORMA_ID: {r.get('FORMA_ID')}, VALOR: {r.get('VALOR')}")
    
    # Verificar total de registros
    total = await mysql.query("SELECT COUNT(*) as total FROM VENDA_FORMA_PAGAMENTO")
    print(f"\nTotal de registros na tabela: {total[0]['total'] if total else 0}")

if __name__ == "__main__":
    asyncio.run(main()) 