# core/extrator.py
import re
from typing import Dict, Any

class ExtratorParametros:
    def __init__(self):
        self.padroes = {
            'codigo': r'código?\s*(\d+)',
            'categoria': r'categoria\s+([^\s,?.!]+)',
            'preco_min': r'(acima|maior|mínimo|acima\s+de)\s+R?\$?(\d+)',
            'preco_max': r'(abaixo|menor|máximo|até)\s+R?\$?(\d+)',
            'preco_entre': r'entre\s+R?\$?(\d+)\s*e\s*R?\$?(\d+)',
            'limite': r'(primeiros|últimos|top)\s+(\d+)',
            'periodo': r'(hoje|ontem|semana|mês|ano|últimos?\s+(\d+)\s+(dias?|meses?|anos?))',
            'status': r'(ativo|inativo|todos|novo|disponível)'
        }
    
    def extrair(self, query: str) -> Dict[str, Any]:
        """Extrai todos os parâmetros possíveis incluindo contexto"""
        params = {}
        query_lower = query.lower()
        
        # 1. Detecta contexto principal
        contextos = {
            'produto': r'(produto|produtos|item|itens|mercadoria)',
            'cor': r'(cor|cores)',
            'cliente': r'(cliente|clientes|usuário|usuários)'
        }
        
        contexto_detectado = None
        for ctx, padrao in contextos.items():
            if re.search(padrao, query_lower):
                contexto_detectado = ctx
                break
        
        # 2. Default inteligente
        if not contexto_detectado:
            # Se tem limite ou filtros de produto, assume produto
            if re.search(r'(limite|preço|código|categoria)', query_lower):
                contexto_detectado = 'produto'
            else:
                contexto_detectado = 'produto'  # Default seguro
        
        params['contexto'] = contexto_detectado
        
        # 3. Extrai outros parâmetros
        for chave, padrao in self.padroes.items():
            matches = re.findall(padrao, query_lower, re.IGNORECASE)
            if matches:
                match = matches[0]
                if isinstance(match, tuple):
                    if chave == 'preco_entre':
                        params['preco_min'] = int(match[0])
                        params['preco_max'] = int(match[1])
                    elif chave == 'periodo' and len(match) > 1:
                        params['periodo'] = f"{match[1]} {match[2]}" if len(match) > 2 else match[0]
                    else:
                        params[chave] = int(match[-1]) if match[-1].isdigit() else match[-1]
                else:
                    params[chave] = int(match) if match.isdigit() else match
        
        return params
    
    def _limpar_parametros(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Converte strings numéricas para int"""
        limpos = {}
        
        for chave, valor in params.items():
            if isinstance(valor, str) and valor.isdigit():
                limpos[chave] = int(valor)
            else:
                limpos[chave] = valor
        
        return limpos

# Teste rápido
if __name__ == "__main__":
    extrator = ExtratorParametros()
    
    testes = [
        "Mostre produto código 123456",
        "Listar produtos categoria Lanches preço entre 10 e 50",
        "Top 10 produtos mais caros hoje",
        "Cores disponíveis",
        "Lista de clientes ativos"
    ]
    
    for teste in testes:
        print(f"Query: {teste}")
        print(f"Params: {extrator.extrair(teste)}")
        print("-" * 40)