import json
import os
import asyncio
import re
from typing import Dict


from api.agent.agent import salva_mensagem
from ...agent_neo4j import AgentNeo4j
from ...agent_evolutionzap import AgentEvolutionZap
from agents.tool import function_tool
import platform
from ...agent_secret import Secret




# ---------- CACHE GLOBAL ----------
cache_var = {}   # int -> variação original
var_cnt   = 1    # contador global

secret = Secret()
is_local = platform.system() == "Windows"
intancia_id = ""
if is_local:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID_LOCAL")
else:
    instancia_id = secret.get_secret("EVOLUTION_API_INSTANCE_ID")

evolutionApi = AgentEvolutionZap()


# Inicializar cliente Neo4j
neo4j = AgentNeo4j()

# Query padrão fixa - mantém o formato original para gerar cartões corretamente
QUERY_PADRAO = """
MATCH (n:Negocio {idx: $negocio_idx})
CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termos) YIELD node AS p
MATCH (n)-[:POSSUI_PRODUTO]->(p)
WHERE p.excluido = 0
OPTIONAL MATCH (p)-[:TEM_COR]->(c:Cor)<-[:DISPONIBILIZA_COR]-(n)
WITH p.nome AS nome,
     head(collect(p.preco)) AS preco,
     head(collect(p.preco_maior)) AS preco_maior,
     head(collect(p.url_imagem)) AS url_imagem,
     collect(DISTINCT {codigo: p.codigo,
                       cor: COALESCE(c.nome, 'Cor única')}) AS variacoes
RETURN nome, preco, preco_maior, url_imagem, variacoes
ORDER BY nome
"""

def processar_termos_compostos(termos):
    import re

    if not termos or not isinstance(termos, str):
        return termos

    termos = termos.strip()

    if ' AND ' in termos.upper() or ' OR ' in termos.upper():
        return termos

    if termos.isdigit():
        return termos

    palavras_ignoradas = {
        'de', 'da', 'do', 'das', 'dos', 'a', 'o', 'as', 'os', 'um', 'uma', 'uns', 'umas',
        'para', 'por', 'per', 'com', 'sem', 'sob', 'sobre', 'em', 'no', 'na', 'nos', 'nas',
        'a', 'à', 'às', 'ao', 'aos', 'pelo', 'pela', 'pelos', 'pelas', 'até', 'entre',
        'durante', 'contra', 'desde', 'após', 'antes', 'depois', 'como', 'que',
        'se', 'e', 'ou', 'mas', 'mais', 'muito', 'pouco', 'muitos', 'poucos', 'este',
        'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas', 'aquele', 'aquela',
        'aqueles', 'aquelas', 'meu', 'minha', 'meus', 'minhas', 'seu', 'sua', 'seus', 'suas'
    }

    palavras_invariaveis = {'jeans'}
    preservar_plural_diante_de = {'sapatos'}
    preposicoes_de = {'de', 'do', 'da', 'dos', 'das'}

    # ✅ REGRAS CORRIGIDAS: ordem importa
    regras_pt = [
        (r'(ões)$', 'ão'),
        (r'(ães)$', 'ão'),
        (r'(is)$', 'l'),
        (r'(éis)$', 'el'),
        (r'(eis)$', 'el'),
        (r'(óis)$', 'ol'),
        (r'(uis)$', 'ul'),
        (r'(ns)$', 'm'),
        (r'(rs)$', 'r'),
        (r'(is)$', 'il'),
        (r'(?<!e)s$', ''),  # ✅ remove 's' final, mas NÃO remove 'es'
    ]

    # ✅ REGRAS EM INGLÊS CORRIGIDAS: ordem importa
    regras_en = [
        (r'(ies)$', 'y'),   # babies → baby
        (r'(ves)$', 'f'),   # knives → knife
        (r'(ses)$', 's'),   # ✅ shoes → shoe (corrigido)
        (r'(s)$', ''),      # cats → cat
    ]

    def singularizar_palavra(palavra):
        palavra_lower = palavra.lower()

        if palavra_lower in palavras_invariaveis:
            return palavra_lower

        for plural, singular in regras_pt:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)

        for plural, singular in regras_en:
            if re.search(plural, palavra_lower):
                return re.sub(plural, singular, palavra_lower)

        return palavra_lower

    palavras = [p.strip() for p in termos.split()]
    palavras_processadas = []

    for i, palavra in enumerate(palavras):
        p_lower = palavra.lower()
        prox = palavras[i + 1].strip().lower() if i + 1 < len(palavras) else ''

        # ✅ Preserva plural só se vier antes de "de/do/da..."
        if p_lower in preservar_plural_diante_de and prox in preposicoes_de:
            palavra_final = p_lower
        else:
            palavra_final = singularizar_palavra(palavra)

        if palavra_final not in palavras_ignoradas:
            palavras_processadas.append(palavra_final)

    # ✅ Se não sobrou nada útil, retorna vazio
    if not palavras_processadas:
        return ""

    if len(palavras_processadas) == 1:
        return palavras_processadas[0]
    return ' AND '.join(palavras_processadas)

def aplicar_busca_fonetica(termos, distancia=None):
    """
    Adiciona sufixo ~ (ou ~n) a cada termo pesquisável para ativar busca fuzzy (Lucene),
    sem alterar operadores booleanos nem a estrutura da query.
    
    Regras:
    - Mantém AND/OR/NOT e parênteses como estão
    - Não altera frases entre aspas (para não virar consulta de proximidade)
    - Não altera números (ex.: códigos)
    - Não altera tokens com ':' (ex.: campo:valor)
    - Não duplica se já houver ~ ou ~n no final do token
    - distancia=None -> usa apenas "~"
      distancia=1/2 -> usa "~1" ou "~2"
    """
    import re

    if not termos or not isinstance(termos, str):
        return termos

    # Tokeniza preservando operadores, parênteses e frases entre aspas
    tokens = re.findall(r'"[^"]*"|\(|\)|\bAND\b|\bOR\b|\bNOT\b|[^\s()]+', termos)
    sufixo = '~' if distancia is None else f'~{distancia}'

    out = []
    for t in tokens:
        if t in ('AND', 'OR', 'NOT', '(', ')'):
            out.append(t)
            continue

        # Frases entre aspas: preserva
        if t.startswith('"') and t.endswith('"'):
            out.append(t)
            continue

        # Field query (campo:valor): preserva
        if ':' in t:
            out.append(t)
            continue

        # Números: preserva
        if re.fullmatch(r'\d+', t):
            out.append(t)
            continue

        # Já possui fuzzy explicitado
        if re.search(r'~\d*$', t):
            out.append(t)
            continue

        # Aplica fuzzy
        out.append(f'{t}{sufixo}')

    result = ' '.join(out)
    # Ajuste cosmético de espaços com parênteses
    result = re.sub(r'\(\s+', '(', result)
    result = re.sub(r'\s+\)', ')', result)
    return result
@function_tool
async def gera_cartao_produto(
                             usuario_whatsapp: str,
                             tipo_resposta: str, 
                             mensagem_inicial: str,
                             mensagem_final: str,
                             em_resposta_idx: str,
                             agente_idx: str,
                             conversa_idx: str,
                             usuario_idx: str,
                             canal_idx: str,
                             parametros: str,
                             ) -> Dict:
    """
    VISÃO GERAL
    
    Realiza consultas diversas dos produtos do negócio atual, negócio este determinado pelo parâmetro negocio_idx, que corresponde ao idx do negócio.

    A função executa a consulta no Neo4j usando uma QUERY PADRÃO FIXA e ela mesma envia para o usuário, não sendo necessário ao agente informar nada mais ao usuário.

    ===============================================================================
    🚨🚨🚨 MUDANÇA IMPORTANTE - LEIA COM ATENÇÃO 🚨🚨🚨
    ===============================================================================
    A partir de agora, a QUERY É FIXA NA FUNÇÃO. O agente NÃO CRIA mais queries Cypher.
    O agente apenas fornece os PARÂMETROS necessários para a query predefinida.
    ===============================================================================

    ===============================================================================
    🚨🚨🚨 REGRA CRÍTICA - LEIA COM ATENÇÃO 🚨🚨🚨
    ===============================================================================
    TODAS as consultas DEVEM retornar as variações de cor, MESMO QUE HAJA APENAS UM PRODUTO.
    Isso é OBRIGATÓRIO para o correto funcionamento do sistema.
    NUNCA use uma consulta que não retorne as variações, mesmo para um único produto.
    ===============================================================================

    PARÂMETROS
    ----------
    1. canal - str. obrigatório   
    identificador do canal (whatsapp, web_app, etc.)
    
    2. tipo_resposta - str. obrigatório   
    opções: 'cartao' | 'livre' -  
    Informa se haverá dados cadastrais de um ou mais produtos (cartao) ou um texto apenas com alguma informação  
    
    3. mensagem_inicial: str - opcional 
    mensagem que antecede aos dados. Visa apresentar ou dizer o que são os dados.
    Exemplo: "Encontrei estes 4 produtos que são da cor que solicitou:"
    
    4. mensagem_final: str - opcional
    mensagem que procede os dados. Normalmente um comentário ou explicação dos dados.
    Exemplo: "Para compartilhar com o cliente, siga este procedimento..."
    
    5. parametros: str - obrigatório 
    String JSON com os parâmetros para a query fixa.
    
    ===============================================================================
    QUERY FIXA UTILIZADA:
    ===============================================================================
    MATCH (n:Negocio {idx: $negocio_idx})
    CALL db.index.fulltext.queryNodes("produtoNomeCodigoFT", $termos) YIELD node AS p
    MATCH (n)-[:POSSUI_PRODUTO]->(p)
    WHERE p.excluido = 0
    OPTIONAL MATCH (p)-[:TEM_COR]->(c:Cor)<-[:DISPONIBILIZA_COR]-(n)
    WITH p.nome AS nome,
         head(collect(p.preco)) AS preco,
         head(collect(p.url_imagem)) AS url_imagem,
         collect(DISTINCT {codigo: p.codigo,
                           cor: COALESCE(c.nome, 'Cor única')}) AS variacoes
    RETURN nome, preco, url_imagem, variacoes
    ORDER BY nome
    ===============================================================================
    
    ===============================================================================
    PARÂMETROS OBRIGATÓRIOS PARA A QUERY:
    ===============================================================================
    
    1. **negocio_idx** (obrigatório): ID do negócio
    2. **termos** (obrigatório): Termos de busca 
    
    EXEMPLO DE PARÂMETROS:
    ```json
    {
        "negocio_idx": "5544332211",
        "termos": "pneu AND goodyear"
    }
    ```
    
    ===============================================================================
    PROCESSO PARA O AGENTE:
    ===============================================================================
    
    1. **Identificar os termos** da solicitação do usuário
       Exemplo: "liste os pneus da marca mate" → termos: "pneus" e "mate"
    
    2. **Corrigir termos** que estão claramente escritos de forma errada
       Exemplo: "batttons" → "pneus"
    
    3. **Passar os termos para o singular**
       Exemplo: "pneus" → "pneu"
    
    4. **Criar o termo de pesquisa**
       
       🚨🚨🚨 REGRA IMPORTANTE PARA CÓDIGOS E TERMOS 🚨🚨🚨
       
       **PARA CÓDIGOS DE PRODUTOS:**
       - Códigos devem ser NÚMEROS EXATOS, SEM busca fonética
       - NÃO adicione o símbolo ~ (til) após códigos
       - Use o código exatamente como fornecido
       
       ❌ ERRO: {"negocio_idx": "5544332211", "termos": "10102674~"}
       ✅ CORRETO: {"negocio_idx": "5544332211", "termos": "10102674"}
       
       **PARA NOMES/TERMOS DE PRODUTOS:**
       - Use busca PARCIAL sem busca fonética
       - NÃO use o símbolo ~ (til) - isso ativa busca fonética
       - Use apenas o termo sem modificadores para busca parcial
       - Exemplo: "pneu" (não "pneu~")
       
       **COMO IDENTIFICAR:**
       - Se for apenas números = é código (sem modificadores)
       - Se contém letras = é nome/termo (sem modificadores para busca parcial)
       
       **REGRAS PARA TERMOS MÚLTIPLOS:**
       - Use AND para buscar produtos que contenham TODOS os termos
       - Use OR para buscar produtos que contenham PELO MENOS um dos termos
       - Separe os termos com espaços após AND/OR
       - Máximo de 3-4 termos por busca para evitar resultados muito restritos
       - Exemplos:
         * "pneu AND goodyear AND vermelho" (busca produtos com todos os termos)
         * "pneu OR gloss OR lip" (busca produtos com pelo menos um termo)
         * "10102674 OR pneu" (busca mista: código OU termo)
    
    5. **Montar os parâmetros JSON**
       
       **Exemplos corretos:**
       
       Para busca por código:
       ```json
       {
           "negocio_idx": "123456",
           "termos": "10102674"
       }
       ```
       
       Para busca por nome/termo (busca parcial):
       ```json
       {
           "negocio_idx": "123456",
           "termos": "pneu AND goodyear"
       }
       ```
       
       Para busca mista (código E termo):
       ```json
       {
           "negocio_idx": "123456",
           "termos": "10102674 OR pneu"
       }
       ```
       
       **Exemplos com termos múltiplos:**
       ```json
       {
           "negocio_idx": "123456",
           "termos": "pneu AND goodyear AND vermelho"
       }
       ```
       
       ```json
       {
           "negocio_idx": "123456",
           "termos": "gloss OR pneu OR lip"
       }
       ```
    
    6. **Chamar a função** com os parâmetros
    
    ===============================================================================
    ESTRUTURA DO BANCO DE DADOS (NEO4J):
    ===============================================================================
    - Um NEGÓCIO (Negocio) possui vários PRODUTOS (relacionamento POSSUI_PRODUTO)
    - Um PRODUTO pode ter várias CORES (relacionamento TEM_COR)
    - Um NEGÓCIO disponibiliza CORES específicas (relacionamento DISPONIBILIZA_COR)

    ===============================================================================
    EXEMPLO DE RESULTADO CORRETO:
    ===============================================================================
    {
      'nome': 'pneu X',
      'preco': 50.0,
      'variacoes': [
        {'codigo': '123', 'cor': 'Vermelho'},
        {'codigo': '124', 'cor': 'Azul'}
      ]
    }

    ===============================================================================
    EXEMPLO DE RESULTADO INCORRETO (produto duplicado):
    ===============================================================================
    {
      'nome': 'pneu X',
      'preco': 50.0,
      'variacoes': [{'codigo': '123', 'cor': 'Vermelho'}]
    }
    {
      'nome': 'pneu X',
      'preco': 50.0,
      'variacoes': [{'codigo': '124', 'cor': 'Azul'}]
    }

    Esquema de dados:
    Esta função irá utilizar o seguinte esquema extraído do banco de dados neo4j da plataforma:
    {gera_cartao_produto_neo4j}  

    Retorno
    -------
    json com as chaves:
        status: success ou error
        message: string com dados do resultado ou mensagem de erro
       
    🚨🚨🚨 A RESPOSTA DEVE SER ENVIADA EXATAMENTE ASSIM, SEM ALTERAÇÕES. O USUÁRIO ESPERA RECEBER UM JSON COM RESPOSTA. ENTREGUE, PORTANTO, A STRING DO JSON RECEBIDO COMO RESPOSTA, SEM AJUSTES, SEM CONVERSÕES, SEM COMENTÁRIOS.
    """
    
    print("==============================")  
    print("#### @@@@@ ####### @@@@@@@@ @@@@@@ gera_cartao_produto()")
    print("===============================")
    print("instancia_id", instancia_id)
    print("tipo_resposta", tipo_resposta)
    print("mensagem_inicial", mensagem_inicial) 
    print("mensagem_final", mensagem_final)
    print("em_resposta_idx", em_resposta_idx)
    print("@@@@@@@@   parametros", parametros)    
    print("canal_idx", canal_idx)
    
    # Processar parâmetros
    try:
        if parametros:
            parametros_dict = json.loads(parametros)
        else:
            return {
                "status": "error", 
                "message": "Parâmetros são obrigatórios."
            }
    except json.JSONDecodeError:
        return {
            "status": "error", 
            "message": "Parâmetros inválidos. Deve ser um JSON válido."
        }
    
    # Validar parâmetros obrigatórios
    if "negocio_idx" not in parametros_dict:
        return {
            "status": "error", 
            "message": "Parâmetro 'negocio_idx' é obrigatório."
        }
    
    if "termos" not in parametros_dict:
        return {
            "status": "error", 
            "message": "Parâmetro 'termos' é obrigatório."
        }
    
    # Processar termos compostos
    termos_originais = parametros_dict["termos"]
    parametros_dict["termos"] = processar_termos_compostos(termos_originais)
    
    # Verificar se é para listar todos os produtos (termo vazio ou palavras-chave como 'todos', 'tudo', etc.)
    termo_tudo = (not parametros_dict["termos"].strip() or 
                 parametros_dict["termos"].lower() in ["todos", "todo", "tudo", "all", "*"])
    
    # Query alternativa para listar todos os produtos (sem fulltext)
    if termo_tudo:
        query_alternativa = """
        MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
        WHERE p.excluido = 0
        OPTIONAL MATCH (p)-[:TEM_COR]->(c:Cor)<-[:DISPONIBILIZA_COR]-(n)
        WITH p.nome AS nome,
             head(collect(p.preco)) AS preco,
             head(collect(p.preco_maior)) AS preco_maior,
             head(collect(p.url_imagem)) AS url_imagem,
             collect(DISTINCT {codigo: p.codigo,
                             cor: COALESCE(c.nome, 'Cor única')}) AS variacoes
        RETURN nome, preco, preco_maior, url_imagem, variacoes
        ORDER BY nome
        """
        # Remover o parâmetro termos pois não é usado na query alternativa
        parametros_dict.pop("termos", None)
        query_a_usar = query_alternativa
    else:
        # Aplicar busca fonética (fuzzy) apenas para buscas específicas
        parametros_dict["termos"] = aplicar_busca_fonetica(parametros_dict["termos"], distancia=2)
        query_a_usar = QUERY_PADRAO
    
    # Executar a query apropriada
    try:
        print("@@@@@ query:", query_a_usar)
        print("@@@@@ parametros_dict:", parametros_dict)

        resultado = await neo4j.execute_read_query(query_a_usar, parametros_dict)
        print("@@@@@ resultado da gera_cartao_produto:", resultado)
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Erro ao executar consulta: {str(e)}"
        }
    
    if not resultado:
        mensagem = "produto não encontrado"
        await evolutionApi.send_evolution_message(usuario_whatsapp, mensagem, instancia_id)
        return {"status": "success", 
        "message": "Nenhum produto encontrado.",
        "function": "gera_cartao_produto"
        }  
    
    
    # Enviar mensagens via WhatsApp
    await enviar_mensagens_zap(
        whatsapp=usuario_whatsapp,
        mensagem_inicial=mensagem_inicial,
        mensagem_final=mensagem_final,
        dados=resultado,
        instancia_id=instancia_id,
        conversa_idx=conversa_idx,
        usuario_idx=usuario_idx,
        agente_idx=agente_idx,
        em_resposta_idx=em_resposta_idx,
        canal_idx=canal_idx
    )

    # Monta a resposta final com todos os campos necessários
    resposta = {
        "status": "success",
        "message": resultado,  # ✅ CORREÇÃO: string vazia pois já enviou as mensagens
        "function": "gera_cartao_produto"
    }   
    print("@@@@@ resposta:", resposta)
    return resposta

def sanitizar_mensagem_para_banco(mensagem: str) -> str:
    """
    Sanitiza mensagem formatada para evitar problemas no banco de dados Neo4j.
    
    Args:
        mensagem (str): Mensagem original com formatação Markdown
        
    Returns:
        str: Mensagem sanitizada para armazenamento seguro
    """
    if not mensagem:
        return ""
    
    # Escapar aspas simples e duplas que podem quebrar queries Cypher
    mensagem_sanitizada = mensagem.replace("'", "\\'").replace('"', '\\"')
    
    # Escapar barras invertidas
    mensagem_sanitizada = mensagem_sanitizada.replace("\\", "\\\\")
    
    # Normalizar quebras de linha para evitar problemas de parsing
    mensagem_sanitizada = mensagem_sanitizada.replace("\r\n", "\n").replace("\r", "\n")
    
    # Remover caracteres de controle problemáticos (mantendo quebras de linha)
    mensagem_sanitizada = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', mensagem_sanitizada)
    
    # Limitar tamanho da mensagem para evitar problemas de memória
    if len(mensagem_sanitizada) > 10000:
        mensagem_sanitizada = mensagem_sanitizada[:10000] + "... [mensagem truncada]"
    
    return mensagem_sanitizada 


    
async def enviar_mensagens_zap(whatsapp,mensagem_inicial, mensagem_final, dados,instancia_id,conversa_idx=None,usuario_idx=None,agente_idx=None,em_resposta_idx=None, canal_idx=None):
            #print (f"===== enviar_mensagens()=====")
            #print("mensagem_inicial:" , mensagem_inicial)
            #print("mensagem_final:" , mensagem_final)
            #print("dados" , dados)   

            if mensagem_inicial:
                result = await evolutionApi.send_evolution_message(whatsapp,mensagem_inicial,instancia_id)

                #print("📩 mensagem inicial enviada", result)
            

            # ---------- DENTRO DA FUNÇÃO ----------
                # Após o for que envia mensagens, **salva histórico leve**
            for item in dados:
                    #print("item",item)
                    #print("url_imagem",item['url_imagem'])
                    # 1. Formatação rica para WhatsApp (seu código original)
                    mensagem_whatsapp = formata_whatsapp(item)   # Markdown + emojis
                    #print("")
                    #print("mensagem_whatsapp", mensagem_whatsapp)
                    #print("type(mensagem_whatsapp)",type(mensagem_whatsapp))
                    #print("")
                    # 2. Versão LIGHT para histórico (vai pro LLM)
                    mensagem_historico = compactar_produto_historico(item, max_var_visiveis=3)
                    #print("")
                    #print("mensagem_historico", mensagem_historico)
                    #print("")

                    # 4. Salva **histórico leve** (é esse que o LLM lê depois)
                    await salva_mensagem(
                        mensagem=mensagem_whatsapp,
                        conversa_idx=conversa_idx,
                        usuario_idx=usuario_idx,
                        agente_idx=agente_idx,
                        em_resposta_idx=em_resposta_idx,
                        remetente="agente",
                        destinatario="usuario",
                        canal_idx=canal_idx
                    )


                
                    
                    # Adicionar URL da mídia se disponível
                    
                    if 'url_imagem' in item and item['url_imagem']:
                        #print("tem media_url" , item['url_imagem'])
                    
                        result = await evolutionApi.send_evolution_media(
                                whatsapp,
                                item["url_imagem"],
                                "image",
                                mensagem_whatsapp,
                                instancia_id
                                )
                        #print(f"Resultado: {result}")
                    else:
                        #print("nao tem imagem")
                        result = await evolutionApi.send_evolution_message(
                                whatsapp,
                                mensagem_whatsapp,
                                instancia_id
                        )
                                    



            await asyncio.sleep(2)

                #if mensagem_final:
                    #message = {}
                    #message["status"] = "success"
                    #message["message"] = mensagem_final
                    #result = await evolutionApi.send_evolution_message(whatsapp,mensagem_final,#instancia_id)
                    #print("📩 mensagem final enviada", result)


            #print(" 📩📩📩📩📩 menagens enviadas")        

            return 

def formata_whatsapp(item: dict) -> str:
    """Formatação rica com Markdown e emojis - só para WhatsApp."""
    #print("===== formatata_whatsapp() =====")
    #print("type(item)",type(item))
    #print("item",item)
    msg = f"*{item.get('nome', 'Produto')}*\n"
    if item.get('preco') == item.get('preco_maior') or item.get('preco_maior', 0) == 0:
        msg += f"💵 *Preço:* R$ {item['preco']:.2f}\n"
    elif item.get('preco') < item.get("preco_maior"):
        msg += f"🚨🥳💵  *Preço:*  De ~*R$ {item['preco_maior']:.2f}*~  por apenas  *R$ {item['preco']:.2f}*\n"
    if item.get('variacoes'):
        msg += "\n*Cores disponíveis:*\n"
        for v in item['variacoes']:
            cor   = v.get('cor', 'Cor única')
            cod   = v.get('codigo', '')
            msg  += f"• {cor}"
            if cod:
                msg += f" (Código: {cod})"
            msg += "\n"
    return msg.strip()

# ---------- COMPACTADOR DE PRODUTO MODIFICADO ----------
def compactar_produto_historico(prod: dict, max_var_visiveis: int = 3) -> str:
    """
    Devolve string com TODAS as variações de cor e códigos no formato /codigo.
    Remove limite de cores visíveis.
    """
    global var_cnt

    # 1. nome abreviado
    nome = prod.get("nome", "Produto")
    if len(nome) > 40:
        nome = nome[:37] + "..."

    # 2. preço sem centavos
    preco = prod.get("preco")
    preco_str = f"R${int(preco)}" if preco else ""

    # 3. incluir TODAS as variações com códigos
    var_visiveis = []
    for v in prod.get("variacoes", []):
        idx = var_cnt
        var_cnt += 1
        cache_var[idx] = v
        cor = v.get("cor", "Cor única")
        cod = v.get("codigo", "")
        
        # Formato: [idx] Cor (codigo)
        if cod:
            var_visiveis.append(f"[{idx}] {cor}({cod})")
        else:
            var_visiveis.append(f"[{idx}] {cor}")

    # 4. monta linha única com todas as cores e códigos
    cores_texto = ', '.join(var_visiveis)
    return f"{nome} {preco_str} Cor/codigo: {cores_texto}"

#criar uma sessao de execucao de testes 
if __name__ == "__main__":
    import asyncio
    
    async def testa_consultar_produtos():
    
        canal_idx = "3245124231"
        whatsapp = "553184198720"
        tipo_resposta = "cartao"
        mensagem_inicial = "Aqui estão os produtos que você solicitou:"
        mensagem_final = "Caso precise de mais informações, só chamar."
        em_resposta_idx = "0129001290"
        agente_idx = "1508250458"
        conversa_idx = "8355885236"
        
        # Removida a variável query - agora é fixa na função
        
        texto ="liste o produto 10253731"
        #parametros = """{"negocio_idx":"5544332211","termos":"pneu~ AND goodyear~"}"""
        parametros = """{"negocio_idx":"5544332211","termos":"10102674"}"""
        usuario_idx = "1234567890"  # ID do usuário para teste
        
        # Chamada atualizada sem o parâmetro query
        resultado = await gera_cartao_produto(whatsapp, canal_idx, tipo_resposta, mensagem_inicial, mensagem_final, em_resposta_idx, agente_idx, conversa_idx, usuario_idx, parametros)
        print("resultado final @@@@@@@", resultado)    
    
    asyncio.run(testa_consultar_produtos())
    #execucao
    #py -m api.agent.business.functions.gera_cartao_produto

# ---------- TESTES PARA A FUNÇÃO processar_termos_compostos ----------
def testar_processar_termos_compostos():
    """
    Testes automatizados para a função processar_termos_compostos.
    Verifica singularização, remoção de preposições e artigos, 
    e preservação de operadores lógicos.
    """
    
    print("=" * 60)
    print("TESTES DA FUNÇÃO processar_termos_compostos")
    print("=" * 60)
    
    # Casos de teste: (entrada, saída_esperada)
    casos_teste = [
        # Testes de singularização em português
        ("kits de presente", "kit AND presente"),
        ("pneus goodyear", "pneu AND goodyear"),
        ("calças femininas", "calça AND feminina"),
        ("sapatos masculinos", "sapato AND masculino"),
        ("blusas de algodão", "blusa AND algodão"),
        
        # Testes de singularização em inglês
        ("shoes red", "shoe AND red"),
        ("dresses blue", "dress AND blue"),
        
        # Testes com preposições e artigos
        ("o pneu vermelho", "pneu AND vermelho"),
        ("a calça jeans", "calça AND jeans"),
        ("os sapatos de couro", "sapatos AND couro"),
        ("as blusas de seda", "blusa AND seda"),
        
        # Testes com códigos numéricos
        ("produto 10253731", "produto AND 10253731"),
        ("10102674", "10102674"),
        
        # Testes com operadores lógicos
        ("pneu AND vermelho", "pneu AND vermelho"),
        ("calça OR jeans", "calça OR jeans"),
        ("sapatos AND couro OR tênis", "sapatos AND couro OR tênis"),
        
        # Testes mistos
        ("os kits de presente", "kit AND presente"),
        ("blusas femininas de algodão", "blusa AND feminina AND algodão"),
        ("vestido 12345 de festa", "vestido AND 12345 AND festa"),
        
        # Casos especiais
        ("", ""),
        ("o", ""),
        ("de", ""),
        ("a e o", ""),
    ]
    
    # Executar testes
    total_testes = len(casos_teste)
    testes_passados = 0
    
    for entrada, esperado in casos_teste:
        resultado = processar_termos_compostos(entrada)
        status = "✅ PASSOU" if resultado == esperado else "❌ FALHOU"
        
        print(f"\nEntrada:    '{entrada}'")
        print(f"Esperado:   '{esperado}'")
        print(f"Resultado:  '{resultado}'")
        print(f"Status:     {status}")
        
        if resultado != esperado:
            print(f"           ❗ Diferença detectada!")
        
        if resultado == esperado:
            testes_passados += 1
    
    # Resumo dos testes
    print("\n" + "=" * 60)
    print(f"RESUMO DOS TESTES:")
    print(f"Total de testes: {total_testes}")
    print(f"Testes passados: {testes_passados}")
    print(f"Testes falhados: {total_testes - testes_passados}")
    print(f"Taxa de sucesso: {(testes_passados/total_testes)*100:.1f}%")
    
    if testes_passados == total_testes:
        print("🎉 Todos os testes passaram!")
    else:
        print("⚠️  Alguns testes falharam. Verifique os resultados acima.")
    
    return testes_passados == total_testes

# Teste interativo
def teste_interativo_processar_termos_compostos():
    """
    Permite testar a função interativamente com entrada do usuário.
    """
    print("\n" + "=" * 60)
    print("TESTE INTERATIVO")
    print("=" * 60)
    print("Digite uma frase para testar a função processar_termos_compostos")
    print("ou digite 'sair' para encerrar.")
    print("=" * 60)
    
    while True:
        entrada = input("\nFrase: ").strip()
        if entrada.lower() == 'sair':
            break
        
        if entrada:
            resultado = processar_termos_compostos(entrada)
            print(f"Resultado: '{resultado}'")
        else:
            print("Por favor, digite uma frase.")

# Executar testes quando o arquivo for executado diretamente
if __name__ == "__main__":
    import asyncio
    
    # Verificar se deve executar testes específicos
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "testar_termos":
            # Executar apenas os testes da função processar_termos_compostos
            sucesso = testar_processar_termos_compostos()
            
            # Perguntar se quer teste interativo
            resposta = input("\nDeseja fazer testes interativos? (s/n): ").strip().lower()
            if resposta == 's':
                teste_interativo_processar_termos_compostos()
            
            sys.exit(0)
    
    # Caso contrário, executar o teste original da função gera_cartao_produto
    async def testa_consultar_produtos():
    
        canal_idx = "3245124231"
        whatsapp = "553184198720"
        tipo_resposta = "cartao"
        mensagem_inicial = "Aqui estão os produtos que você solicitou:"
        mensagem_final = "Caso precise de mais informações, só chamar."
        em_resposta_idx = "0129001290"
        agente_idx = "1508250458"
        conversa_idx = "8355885236"
        
        # Removida a variável query - agora é fixa na função
        
        texto ="liste o produto 10253731"
        #parametros = """{"negocio_idx":"5544332211","termos":"pneu~ AND goodyear~"}"""
        parametros = """{"negocio_idx":"5544332211","termos":"10224533"}"""
        usuario_idx = "1234567890"  # ID do usuário para teste
        
        # Chamada atualizada sem o parâmetro query
        resultado = await gera_cartao_produto(whatsapp, canal_idx, tipo_resposta, mensagem_inicial, mensagem_final, em_resposta_idx, agente_idx, conversa_idx, usuario_idx, parametros)
        print("resultado final @@@@@@@", resultado)    
    
    asyncio.run(testa_consultar_produtos())
    #execucao
    
    
    # Para executar apenas os testes da função auxiliar:
    # py -m api.agent.business.functions.gera_cartao_produto testar_termos
    # py -m api.agent.business.functions.gera_cartao_produto testar_termos
