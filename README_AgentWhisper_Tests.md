# Testes do AgentWhisper 🎙️

Este diretório contém testes para o sistema de transcrição de áudio usando OpenAI Whisper.

## Arquivos de Teste

### 1. `test_whisper_standalone.py` - Teste Direto
**Uso:** Testa a classe `AgentWhisper` diretamente, sem servidor FastAPI.
```bash
python test_whisper_standalone.py
```

**Características:**
- ✅ Execução rápida
- ✅ Sem dependências de servidor
- ✅ Teste direto da classe

### 2. `test_fastapi_server.py` - Servidor de Teste
**Uso:** Inicia um servidor FastAPI para testar o endpoint.
```bash
python test_fastapi_server.py
```

**Características:**
- 🚀 Servidor em http://localhost:8000
- 📖 Documentação em http://localhost:8000/docs
- 🔗 Endpoint: POST /api/agent/transcribe

### 3. `test_whisper_main.py` - Teste de Requisição HTTP
**Uso:** Faz requisições HTTP para o endpoint (servidor deve estar rodando).
```bash
python test_whisper_main.py
```

**Características:**
- 📤 Envia arquivo via HTTP POST
- 🌐 Testa integração completa
- ⚠️ Requer servidor rodando

## Como Executar os Testes Completos

### Opção 1: Teste Rápido (Standalone)
```bash
# Teste direto da classe (mais rápido)
python test_whisper_standalone.py
```

### Opção 2: Teste Completo com API
```bash
# Terminal 1: Iniciar servidor
python test_fastapi_server.py

# Terminal 2: Executar teste de requisição
python test_whisper_main.py
```

## Configurações

### Arquivo de Áudio
Por padrão, os testes usam:
```
C:\Users\<USER>\Audio\_temp\olabdia.mp3
```

**Para alterar:**
1. Edite a variável `AUDIO_FILE_PATH` em cada arquivo de teste
2. Ou substitua o arquivo no caminho atual

### Formatos Suportados
- `.mp3` (recomendado)
- `.wav`
- `.m4a`
- `.flac`
- `.ogg`

## Resultados Esperados

### Sucesso ✅
```
🎙️  TESTE STANDALONE - AGENTWHISPER 🎙️
==================================================
🔄 Inicializando AgentWhisper...
📤 Transcrevendo arquivo: olabdia.mp3
✅ Transcrição realizada com sucesso!
📝 Texto: Olá bom dia!
🌍 Idioma: pt
==================================================
🎉 Teste passou com sucesso!
```

### Possíveis Problemas

#### ❌ Arquivo não encontrado
```
❌ Arquivo não encontrado: C:\Users\<USER>\Audio\_temp\olabdia.mp3
💡 Dica: Ajuste o caminho do arquivo no código
```
**Solução:** Verifique o caminho do arquivo de áudio.

#### ❌ Módulo não encontrado
```
❌ Erro ao importar: No module named 'agent_logger'
```
**Solução:** Verifique se existe o arquivo `agent_logger.py`.

#### ❌ Conexão recusada (teste HTTP)
```
❌ Erro: Não foi possível conectar ao servidor
💡 Dica: Certifique-se de que o servidor FastAPI está rodando
```
**Solução:** Inicie o servidor com `python test_fastapi_server.py`.

## Dependências Necessárias

```bash
pip install openai-whisper uvicorn python-multipart requests fastapi
```

## Logs e Debug

Os logs são exibidos automaticamente durante a execução. Para mais detalhes, verifique a implementação em `api/agent/agent_whisper.py`. 