#!/usr/bin/env python3
"""
Teste do novo método test_console_output
"""

import sys
import os

# Adiciona o diretório api ao path para importar o módulo
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from api.agent.agent_logger import AgentLogger

def test_new_method():
    print("=== TESTE DO NOVO MÉTODO test_console_output ===")
    
    # Cria instância do logger
    logger = AgentLogger()
    
    print("Chamando test_console_output()...")
    logger.test_console_output()
    
    print("=== FIM DO TESTE ===")

if __name__ == "__main__":
    test_new_method()