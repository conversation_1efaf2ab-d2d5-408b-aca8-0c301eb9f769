from .agent_mysql import Mysql
from .agent_converter import Converter
from fastapi import APIRouter
from .agent_logger import AgentLogger
logger = AgentLogger()


router = APIRouter()

class Business:
    def __init__(self):
        self.ID = 0
        self.mysql = Mysql()
        self.converter = Converter()
        
    async def get(self, id):
        columns = "*"
        filters = [f"IDX = '{id}'"]
        response = await self.mysql.fetch(columns, "VISAO_NEGOCIO", filters)
        if 'ERRO' in response:
            return response
        if response:
            response = response[0]
            for column in response.keys():
                setattr(self, column, response[column])
        return response


    async def get_token(self):
        return self.converter.generate_token(self.ID + 7)
    

    async def fetch(self,columns:str,filters:list):
       negocio = await self.mysql.fetch(columns,"VISAO_NEGOCIO_PAGAMENTO",filters,order="ID DESC")
       if negocio:
            negocio = negocio[0]
            for column in negocio.keys():
                setattr(self, column, negocio[column])
  
       return negocio

    async def area_fetch(self):
        try:
            result = await self.mysql.query("SELECT ID, NOME FROM NEGOCIO_AREA WHERE ID > 0 ORDER BY NOME")
            # Aqui você pode fazer algo com o resultado, como exibir na tela ou processá-lo de outra forma.
            return result
        except Exception as e:
            return {"ERRO":str(e)}
        
    async def type_fetch(self,data):    
        try:
            if data:
                query = f"SELECT ID,AREA_ID,NOME FROM NEGOCIO_TIPO WHERE AREA_ID = {data['AREA_ID'] } ORDER BY NOME"
            else:
                query = "SELECT ID,AREA_ID,NOME FROM NEGOCIO_TIPO ORDER BY NOME"

            result = await self.mysql.query(query)
            # Aqui você pode fazer algo com o resultado, como exibir na tela ou processá-lo de outra forma.
            return result
        except Exception as e:
            return {"ERRO":str(e)}

    async  def save(self,data):
        try:
            if "ID" not in data or data["ID"] == 0 or data["ID"] is None:
                data.pop("ID")
          
                id = await self.mysql.add("NEGOCIO",data)
                #obtenha o IDX do negocio adicionado pelo ID
                return id
            else:

                result = await self.mysql.update("NEGOCIO",data)
          
                return 0
        except Exception as e:
            return {"ERRO":str(e)}

    async def get_idx(self, idx, columns="*"):
        """
        Busca dados do Negócio no Neo4j pelo IDX
        
        Args:
            idx (str): IDX do negócio a ser buscado
            columns (str): Colunas específicas separadas por vírgula ou "*" para todas
        
        Returns:
            dict: Dados do negócio ou None se não encontrado
        """
        logger.info(f"get_idx() - Buscando negócio IDX: {idx}")
        logger.info(f"get_idx() - Colunas solicitadas: {columns}")
        
        try:
            # Mapeamento de propriedades MySQL -> Neo4j
            property_mapping = {
                "razao_social": "razaoSocial",
                "cnpj_cpf": "cnpjCpf",
                "nome": "nome",
                "telefone": "telefone",
                "email": "email",
                "cidade": "cidade",
                "uf": "uf",
                "bairro": "bairro",
                "logradouro": "logradouro",
                "numero": "numero",
                "complemento": "complemento",
                "responsavel": "responsavel",
                "usuario_idx": "usuarioIdx",
                "area_id": "areaId",
                "tipo_id": "tipoId",
                "idx": "idx"
            }
            
            # Construir a query Cypher baseada nas colunas solicitadas
            if columns == "*":
                # Retornar todas as propriedades do nó com aliases em maiúsculo
                cypher_query = """
                MATCH (n:Negocio {idx: $idx}) 
                RETURN 
                    n.idx AS IDX,
                    n.nome AS NOME,
                    n.razaoSocial AS RAZAO_SOCIAL,
                    n.cnpjCpf AS CPF_CNPJ,
                    n.telefone AS TELEFONE,
                    n.email AS EMAIL,
                    n.cidade AS CIDADE,
                    n.uf AS UF,
                    n.bairro AS BAIRRO,
                    n.logradouro AS LOGRADOURO,
                    n.numero AS NUMERO,
                    n.complemento AS COMPLEMENTO,
                    n.responsavel AS RESPONSAVEL,
                    n.usuarioIdx AS USUARIO_IDX,
                    n.areaId AS AREA_ID,
                    n.tipoId AS TIPO_ID
                """
                logger.info("get_idx() - Consultando TODAS as propriedades do negócio com aliases em maiúsculo")
            else:
                # Retornar apenas as propriedades específicas
                # Converter string de colunas para lista de propriedades
                colunas_lista = [col.strip() for col in columns.split(",")]
                
                # Mapear propriedades para nomenclatura do Neo4j com aliases em maiúsculo
                propriedades_neo4j = []
                for col in colunas_lista:
                    col_lower = col.lower()
                    neo4j_prop = property_mapping.get(col_lower, col_lower)
                    
                    # Criar alias em maiúsculo com mapeamentos específicos
                    if col_lower == "razao_social":
                        alias_maiusculo = "RAZAO_SOCIAL"
                    elif col_lower == "cnpj_cpf":
                        alias_maiusculo = "CPF_CNPJ"
                    else:
                        alias_maiusculo = col.upper()
                    
                    propriedades_neo4j.append(f"n.{neo4j_prop} AS {alias_maiusculo}")
                
                propriedades_return = ", ".join(propriedades_neo4j)
                
                cypher_query = f"""
                MATCH (n:Negocio {{idx: $idx}}) 
                RETURN {propriedades_return}
                """
                logger.info(f"get_idx() - Consultando propriedades específicas: {propriedades_return}")
            
            # Parâmetros da query
            params = {"idx": idx}
            
            # Executar a query usando o agent_neo4j (importação local para evitar circular import)
            #logger.info("get_idx() - Executando query no Neo4j...")
            from .agent_neo4j import AgentNeo4j
            neo4j = AgentNeo4j()
            resultado = await neo4j.execute_read_query(cypher_query, params)
            
            #logger.info(f"get_idx() - Resultado bruto do Neo4j: {resultado}")
            
            # Processar o resultado
            if resultado and len(resultado) > 0:
                # Tanto "*" quanto específicas agora retornam com aliases em maiúsculo
                negocio_data = resultado[0]
                logger.info(f"get_idx() - Propriedades encontradas: {negocio_data}")
                
                # Definir atributos da classe baseado nas propriedades retornadas
                # As propriedades já vêm com os aliases corretos em maiúsculo
                for propriedade, valor in negocio_data.items():
                    setattr(self, propriedade, valor)
                
                return negocio_data
            else:
                logger.warning(f"get_idx() - Negócio não encontrado para IDX: {idx}")
                return None
                
        except Exception as e:
            logger.error(f"get_idx() - Erro ao buscar negócio: {str(e)}")
            return {"ERRO": f"Erro ao buscar negócio no Neo4j: {str(e)}"}

        
@router.get("/area/fetch")
async def  area_fetch():
    business = Business()
    result = await business.area_fetch()
    return result


@router.post("/save")
async def save(data:dict):
    business = Business()
    result = await business.save(data)
    return {
        "ID": result,
        "success": True,
        "message": "Negócio salvo com sucesso"
    }




@router.post("/get/{idx}")
async def  get_idx(idx:str,columns:dict):
    logger.info(f"get_idx() - IDX: {idx}")
    colunas = ",".join(columns["columns"])
    
    business = Business()
    result = await business.get_idx(idx,colunas)
    return result


@router.get("/get/{idx}")
async def get_idx_simple(idx: str, columns: str = "*"):
    """
    Endpoint GET simples para buscar dados do negócio
    
    Args:
        idx (str): IDX do negócio
        columns (str): Colunas separadas por vírgula ou "*" para todas
    
    Returns:
        dict: Dados do negócio
    """
    logger.info(f"get_idx_simple() - IDX: {idx}, Colunas: {columns}")
    
    business = Business()
    result = await business.get_idx(idx, columns)
    return result


@router.post("/type/fetch")
async def  type_fetch(data:dict):
    business = Business()
    result = await business.type_fetch(data)
    return result




if __name__ == "__main__":
    import asyncio
    from fastapi.testclient import TestClient
    from api.main import app

    async def test_get_idx():
        #print("=== TESTANDO FUNÇÃO get_idx() ===")
        
        # Teste 1: Testar a função get_idx diretamente
        business = Business()
        
        # Testar com IDX válido
        idx_teste = "4015743441"
        
        result = await business.get_idx(idx_teste, "idx,nome,razao_social,cnpj_cpf")
        print(f"   Resultado1: {result}")
        
        print("==========================================================")
        # Teste 2: Testar com todas as colunas
        print("\n2. Testando get_idx() com todas as colunas:")
        result_all = await business.get_idx(idx_teste, "*")
        print(f"   Resultado completo: {result_all}")
        return;
        
        # Teste 3: Testar endpoint via HTTP
        print("\n3. Testando endpoint HTTP:")
        client = TestClient(app)
        
        # Teste endpoint POST /get/{idx}
        url = f"/api/agent/business/get/{idx_teste}"
        data = {"columns": ["nome", "razao_social", "cnpj_cpf"]}
        response = client.post(url, json=data)
        print(f"   Status: {response.status_code}")
        print(f"   Resposta: {response.json()}")
        
        print("\n=== FIM DOS TESTES ===")
        
        # Teste 4: Testar IDX que não existe
        print("\n4. Testando IDX inexistente:")
        result_inexistente = await business.get_idx("999999999", "nome")
        print(f"   Resultado (deve ser None): {result_inexistente}")
        


    # Execução do teste
    asyncio.run(test_get_idx())