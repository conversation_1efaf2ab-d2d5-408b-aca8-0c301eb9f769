"""
Exemplo de como usar os decoradores personalizados junto com @function_tool
"""

from agents.tool import function_tool
from api.agent.decorators import usuario, admin, gerente, vendedor, publico
from typing import Dict

# Exemplo 1: Função apenas para usuários com função "Usuario"
@function_tool
@usuario
async def funcao_apenas_usuario(parametro: str) -> Dict:
    """
    Esta função só será carregada se usuario_funcao for "Usuario"
    """
    return {"resultado": f"Função executada por usuário: {parametro}"}

# Exemplo 2: Função apenas para administradores
@function_tool
@admin
async def funcao_apenas_admin(parametro: str) -> Dict:
    """
    Esta função só será carregada se usuario_funcao for "Admin"
    """
    return {"resultado": f"Função executada por admin: {parametro}"}

# Exemplo 3: Função apenas para gerentes
@function_tool
@gerente
async def funcao_apenas_gerente(parametro: str) -> Dict:
    """
    Esta função só será carregada se usuario_funcao for "Gerente"
    """
    return {"resultado": f"Função executada por gerente: {parametro}"}

# Exemplo 4: Função apenas para vendedores
@function_tool
@vendedor
async def funcao_apenas_vendedor(parametro: str) -> Dict:
    """
    Esta função só será carregada se usuario_funcao for "Vendedor"
    """
    return {"resultado": f"Função executada por vendedor: {parametro}"}

# Exemplo 5: Função pública (disponível para qualquer usuário)
@function_tool
@publico
async def funcao_publica(parametro: str) -> Dict:
    """
    Esta função será carregada para qualquer usuario_funcao
    """
    return {"resultado": f"Função pública executada: {parametro}"}

# Exemplo 6: Função sem decorator personalizado (NÃO será carregada)
@function_tool
async def funcao_nao_sera_carregada(parametro: str) -> Dict:
    """
    Esta função NÃO será carregada porque tem apenas @function_tool
    sem decorator personalizado (@usuario, @admin, etc.)
    """
    return {"resultado": f"Esta função nunca será executada: {parametro}"}
