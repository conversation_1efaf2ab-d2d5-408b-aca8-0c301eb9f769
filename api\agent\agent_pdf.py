import fitz  # PyMuPDF
import os
import tempfile
from PIL import Image
import io
import base64
import requests


class Pdf:
    def __init__(self):
        pass


    async def urlPdf_to_image_base64(self, url):
        print("urlPdf_to_image_base64()", url)
        """
        A função recebe a url de um arquivo pdf. Em seguida , converte este pdf em uma lista de imagens.
        E em seguida, concatena todas as imagens em uma única imagem. e retorna esta imagem em base64.
        """
        # Download do PDF da URL
        response = requests.get(url)
        pdf_data = response.content

        # Criar um arquivo temporário para o PDF
        temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        temp_pdf.write(pdf_data)
        temp_pdf.close()
        temp_pdf_path = temp_pdf.name

        try:
            # Abrir o PDF com PyMuPDF
            pdf_document = fitz.open(temp_pdf_path)
            
            # Lista para armazenar as imagens
            images = []
            
            # Converter cada página em imagem
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))  # 300 DPI
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))
                images.append(img)

            # Calcular dimensões totais para a imagem concatenada
            total_height = sum(img.height for img in images)
            max_width = max(img.width for img in images)

            # Criar uma nova imagem com o tamanho total
            concatenated_image = Image.new('RGB', (max_width, total_height), 'white')

            # Colar cada imagem na posição correta
            current_height = 0
            for img in images:
                concatenated_image.paste(img, (0, current_height))
                current_height += img.height

            # Converter a imagem concatenada para base64
            buffered = io.BytesIO()
            concatenated_image.save(buffered, format="PNG")
            img_str = "data:image/png;base64," + base64.b64encode(buffered.getvalue()).decode()

            return img_str

        finally:
            # Fechar o documento PDF
            pdf_document.close()
            # Limpar o arquivo temporário
            try:
                os.unlink(temp_pdf_path)
            except Exception as e:
                print(f"Aviso: Não foi possível deletar o arquivo temporário: {str(e)}")


if __name__ == "__main__":
    import asyncio

    async def test_pdf_to_image():
        # URL de exemplo de um PDF
        pdf_url = "http://localhost/gptalk/negocios/0068108573/documentos/contabilidade/2305562857.pdf"
        
        # Criar instância da classe PDF
        pdf = Pdf()
        
        try:
            # Converter PDF para imagem
            imagem_base64 = await pdf.urlPdf_to_image_base64(pdf_url)
            
            # Verificar se a string base64 foi gerada
            if imagem_base64:
                print("✅ Conversão do PDF para imagem realizada com sucesso!")
                print(f"Tamanho da string base64: {len(imagem_base64)} caracteres")
                
                # Opcional: Salvar a imagem para verificação visual
                imagem_bytes = base64.b64decode(imagem_base64)
                with open("teste_pdf_convertido.png", "wb") as f:
                    f.write(imagem_bytes)
                print("✅ Imagem salva como 'teste_pdf_convertido.png'")
            else:
                print("❌ Falha na conversão: string base64 vazia")
                
        except Exception as e:
            print(f"❌ Erro durante a conversão: {str(e)}")

    # Executar o teste
    asyncio.run(test_pdf_to_image())




