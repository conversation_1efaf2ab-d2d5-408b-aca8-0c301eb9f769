import json,ast
from textwrap import dedent

async def extract_object(data):
    # Encontra a primeira ocorrência de '{'
    start = data.find('{')
    if start == -1:
        return None  # Retorna None se não encontrar '{'

    # Contador para acompanhar os pares de chaves
    brace_count = 0
    for i in range(start, len(data)):
        if data[i] == '{':
            brace_count += 1
        elif data[i] == '}':
            brace_count -= 1

        # Quando brace_count volta a zero, encontramos o par correspondente
        if brace_count == 0:

             return  ast.literal_eval(dedent(data[start:i+1]))

    return None  # Retorna None se não encontrar um par de chaves correspondente



# Exemplo de uso da função:
data = """Seu exemplo de string com array aqui"""
extracted_array = await extract_array(data)