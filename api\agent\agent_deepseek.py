from .agent_secret import Secret

import asyncio


class DeepSeek:
    def __init__(self):
        self.secret = Secret()
        self._base_url = "https://api.deepseek.com"
        self._api_key = self.secret.DEEPSEEK_API_KEY
        self._model_chat_name = "deepseek-chat"
    
    @property
    def base_url(self):
        return self._base_url
    
    @property
    def api_key(self):
        return self._api_key
        
    @property
    def model_chat_name(self):
        return self._model_chat_name




