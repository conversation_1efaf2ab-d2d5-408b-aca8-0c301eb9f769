from fastapi import APIRouter
router = APIRouter()

import io
from .agent_filemanager import FileManager
from .agent_llm import LLM

import os
import json

import asyncio

from .agent_site import Site
from .agent_cpanel import Cpanel 
from .agent_task import Task
from .agent_project import Project
from .agent_converter import Converter
from .agent_llm import LLM
from .agent_user import User
from .agent_validator import Validator
from .agent_business import Business
from .agent_agent import Agent

class Webdesigner:
   def __init__(self,model=None,usuario:User=None,negocio:Business=None,agente:Agent=None):
      #print("xxxxx webd data",data)
      self.name = "<PERSON>"
      self.validator = Validator()
      self.llm = LLM()
      self.usuario  = usuario if usuario else User()
      self.negocio = negocio if negocio else Business()
      self.model = None
      self.role = "Webesigner"
      self.site = Site()
      self.cpanel = Cpanel()
      self.model_default = self.llm.model_default
      self.converter = Converter()
      self.processo_id = 1
      self.task = Task()
      self.project =  Project()
      self.fileManager = FileManager()
      self.agente = agente if agente else Agent()

   
   async def get_system(self):
      #print("######## webdesigner get_system() ########")
      return  f"""
   Seu nome é {self.agente.NOME}. você é especialista em criação de sites, landing pages e páginas de vendas. Você é     uma  webdesigner com amplo conhecimento em design web, desenvolvimento front-end e back-end, e especialização em     integrações via API com servidores de serviços e dados. Você tem um profundo entendimento de HTML, CSS, JavaScript,     e uma habilidade excepcional para criar sites dinâmicos que maximizam as taxas de conversão, sejam eles voltados     para e-commerce , geração de leads, institucionais ou uma combinação de uma ou mais função. Seu trabalho é     reconhecido por ser otimizado para SEO, altamente responsivo e acessível em todos os dispositivos, além de seguro     contra as mais diversas vulnerabilidades web.
   
   Você também domina copywriting, possui um profundo conhecimento de argumentos persuasivos para vendas online, e usa     este conhecimento para criar textos para sites, landing pages  e paginas de vendas de alta conversão.
   
   Com base em sua vasta experiência e habilidades, você é capaz de orientar o processo de criação de um site desde a     concepção até a implementação, fornecendo insights valiosos sobre a escolha de domínios, design de layout, seleção     de paletas de cores, e as melhores práticas para assegurar uma experiência de usuário impecável e eficaz. Use sua     expertise para orientar e tirar duvidas dos usuarios, mas também fazer qualquer ajuste,
   
   Caso o usuário diga que deseja que você crie para ele um site, diga que fará uma série de perguntas, as quais ele     deve responder , e que servirão de guia para que você crie um site personalizado para ele. Portanto, é importante     que ele responda a todas.
   
   Estas são as  informações que você irá solicitar ao usuário durante a conversa sobre o site:
   
   1. **Objetivo do Site:** Descreva o propósito principal do site. Isso pode incluir vendas de produtos (e-commerce),     apresentação de serviços, geração de leads, promoção de um evento, ou fornecer informações institucionais.
   2. **Área:**  Qual a sua area de atuação ou da empresa a quem o site se destina? (Exemplos: Medicina,     Contabilidade, Finanças, Tecnologia da informação, etc.)
   3. **Público-alvo:** Informações sobre quem é seu público-alvo ajudarão a definir o design e a funcionalidade do     site para atender às necessidades específicas dos usuários.
   4. **Design e Estilo:** Suas preferências em termos de design, incluindo exemplos de sites que você gosta, paletas     de cores desejadas, e qualquer elemento visual específico que deseja incluir.
   5. **Funcionalidades e Recursos Específicos:** Detalhes sobre qualquer funcionalidade especial que o site deve ter,     como formulários de contato, galerias de imagens, integração com redes sociais, funcionalidades de e-commerce, chat     ao vivo, entre outros.
   6. **SEO e Marketing Digital: E**stratégias específicas de SEO ou marketing digital que gostaria de implementar,     como palavras-chave alvo, integração com Google Analytics, campanhas de PPC, etc.
   7. **Nome:**  Solicite que o usuário escolha um nome para o site. Informe que este nome será usado no domínio     gratuito que será criado para site. Informe que caso o usuário possua um dominio próprio ou queria registrar um ,     que o endereço poderá ser linkado ao site após a criação. Mas que no momento é necessário que este nome seja     informado para ativar a hospedagem do site. De este exemplo do que é o nome de um dominio. Por exemplo, o site o     nome do site [google.com](http://google.com) é google. E do site [Mercadolivre.com.br](http://Mercadolivre.com.br)     é mercadolivre . Verifique se o nome do dominio escolhido é valido. Caso não,  informe ao usuário e solicite outro.
   
   Faça  as perguntas uma de cada vez e aguarde a resposta do usuário, antes de prosseguir para a próxima.
   
   Após a ultima pergunta, pergunte se o usuario tem alguma observação a acrescentar. caso não , prossiga criando o     codigo do site.
   
   Caso ele tenha não tenha nada a acrescentar, prossiga com a criação do site.
   
   Estas são as informações e regras para quando o usuário solicitar alguma alteração no site:
   1. Só retorne o código alterado, sem nenhum comentário antes ou depois.
   2. Retorne o código completo fornecido já com a alteração realizada
   3. Se o usuário informar o id do elemento html no qual ele quer que a alteração seja feita, realize a alteração     somente neste elemento, se a mensagem não especificar outro ou outros.
   Neste momento você está atendendo o usuário {self.usuario.NOME} .
   """
      



   #==========
   async def get_tools(self,llm):
     tools = None
     return tools


   async def site_update_images(self,model=None,site_id=None):
      #parametros
      #print("########## site_update_images() ##########")
      #print("site_id",site_id)
      #print("model",model)

      #site
      if not self.site.ID:
         await self.site.fetch(site_id)


      #modelo
      model = model if model else self.model

      #pages     
      #print("pages",len(self.site.PAGES))

      #processo
      for page, html_code in self.site.PAGES.items():
         #print(page)
         updated_html = await self.site.update_images_pexels(html_code)
         self.site.PAGES[page] = updated_html

       #salva site atualizado
      await self.site.save_contents()
      return {"STATUS":"R","RESULT":"Imagens adicionadas ao site com sucesso."}


   async def site_update_ids(self,model=None,site_id=None):
      #parametros
      #print("########## site_update_ids() ##########")
      #print("site_id",site_id)
      #print("model",model)

      #site
      if not self.site.ID:
         await self.site.fetch(site_id)


      #modelo
      model = model if model else self.model

      #pages     
      #print("pages",len(self.site.PAGES))

      #processo
      for page, html_code in self.site.PAGES.items():
         #print(page)
         updated_html = await self.site.add_unique_ids(html_code)
         #print("")
         #print(updated_html)
         self.site.PAGES[page] = updated_html

      #salva site atualizado
      await self.site.save_contents()

      return {"STATUS":"R","RESULT":"Ids adicionados a todos os elementos do site."}

   #==========
   async def get_functions(self,llm):
     functions = None
     return functions



   #==========

   async def site_update(self,REQUISITOS=None,PAGINA=None,SITE_ID=None,HTML=None, model=None):
      self.model = model if model else self.model
      print ("====== site_update() ==========()")
      #print("requisitos  recebidos")
      print("PAGINA",PAGINA)
      #print('SITE_ID',SITE_ID)
      #print("HTML",HTML)
      #print("REQUISITOS",REQUISITOS)
      base_dir = os.path.dirname(os.path.abspath(__file__))

      
      site_id = SITE_ID

      if not site_id:
            return {"STATUS":"F","RESULT":"O site ainda não foi criado"} 

      #print("site_id", site_id)

      result = await self.site.fetch(site_id)
      #print("result da busca pelo site", result)
      if 'R' not in result['STATUS']:
         return {'STATUS':'F','SAIDA':'site não encontrado'}

      #print(self.site.PAGES)
      
      if self.site.PAGES == {}:
         print("vou carregar paginas em branco dos modelos")
         await self.site.start_site()
         #print("## ARQUIVOS DO SITE ##",self.site.PAGES)

      #print("pages após carregamento:")
      #print(self.site.PAGES)

      pagina = PAGINA
      #print("\n pagina",pagina)
      req = json.dumps(REQUISITOS)

      #print(req)

      subdir = os.path.join('site', 'template', 'default')   
      self.site.PAGES[pagina] = await  self.fileManager.load_text_file(os.path.join(base_dir, subdir ),"index.html")

      subdir = os.path.join('webdesigner', 'system')   
      system = await self.fileManager.load_text_file(os.path.join(base_dir, subdir ),"site_generate.txt")
      instrucoes =  await self.fileManager.load_text_file(os.path.join(base_dir, subdir ),"site_generate_instructions.txt")
      #print("system",system)
      instrucoes = instrucoes #+ "Mudanças a serem feitas na pagina " + pagina + " "
      #print("instrucoes", instrucoes)
            
      html = HTML if HTML else await self.site.load_default_page()

      #print("xxxxxxxxxxxxxx html", html)
      self.site.PAGES[pagina] = html
      #print("total de paginas", len(self.site.PAGES))
      
      site_html_string = json.dumps(self.site.PAGES,indent=4)
      #print("")
      #print("site antes da atualizacao")
      #print("")
      #print(self.site.PAGES)
      #print("")
      #print("MESSAGE:")
      message = [
      {
       "role": "system",
        "content": system
       },
       {
        "role": "user",
        "content": f"{instrucoes}   {req} site a ser alterado: {site_html_string}"},
        ]
      #print("\n message")
      #print(message)
      #print("")
      #print("model", self.model)
      #print("")
      site = await self.llm.run_model(model=self.model,message=message) 
      #print("")
      #print("xxxxxx site atualizado xxxxxx")
      
      #print("")
      siteOk = True  # Assume inicialmente que o site está ok
      self.site.PAGES = await self.converter.extract_object(site)
      #print(self.site.PAGES)
      pagina_codigo = self.site.PAGES[PAGINA]
      print("$$$$$$$$$$ pagina_codigo",pagina_codigo)
      if 'htm' in PAGINA: 
         # Verifica se o código html é valido
         if "<html" not in site or site.count("<html") != site.count("</html"):
            siteOk = False
            print("nao é um código html válido")
      
            return {"STATUS":"T","RESULT":"Não é um código hmtl válido"} 
      
      if 'css' in PAGINA: 
         # Verifica se o codigo css é valido
         if not self.validator.validate_css(pagina_codigo):
            siteOk = False
            print("nao é um código css válido")
     
            return {"STATUS":"T","RESULT":"Não é um código css válido"} 

      if siteOk:
        
        await self.site.save_contents()
        #print ("total de paginas",len(self.site.PAGES))
        #print("")
        #print("######## PAGINA QUE FOI ATUALIZADADA #########")
        #print(self.site.PAGES[pagina])
        
        return {"STATUS":"R","RESULT":self.site.PAGES[PAGINA]} 
      
      

   #==========
   async def site_project_create(self,
                                 ID = None,
                                 NOME=None,
                                 USUARIO_ID = None,
                                 AREA=None,
                                 OBJETIVO=None,
                                 PUBLICO_ALVO=None,
                                 DESIGN_ESTILO=None,
                                 FUNCIONALIDADES=None,
                                 INFORMACOES_CADASTRAIS=None,
                                 ):
      
         print("##### site_project_create() #####",ID)
         #cria subdominio caso ele não exista
         if not await self.cpanel.subdomain_exists(NOME):
            print("vou criar o subdominio " + NOME)
            await self.cpanel.subdomain_create(subdomain=NOME)
         else: #exclui arquivos se o site ja existe
            print(f"Subdominio {NOME} ja existe")
            await self.cpanel.delete_subdomain_files(NOME)
            

         self.site.USUARIO_ID = USUARIO_ID
         self.site.NOME = NOME
         self.site.AREA = AREA 
         self.site.OBJETIVO = OBJETIVO
         self.site.PUBLICO_ALVO = PUBLICO_ALVO
         self.site.DESIGN_ESTILO = DESIGN_ESTILO
         self.site.FUNCIONALIDADES = FUNCIONALIDADES,
         self.site.INFORMACOES_CADASTRAIS = INFORMACOES_CADASTRAIS
         self.site.ID = ID
         if self.site.ID > 0:
            #print("ID é maior que zero ",self.site.ID)
            #print("exclui projeto para reincluir com novo id e novas tarefas")
            await self.project.remove(f"SITE_ID={self.site.ID}") 
            await self.site.save()
         else: #criar projeto e site 
            self.site.ID =   await self.site.save()

         projeto = {}
         projeto['PROCESSO_ID'] = self.processo_id
         projeto['NOME'] = f"{self.site.NOME}.site.gptalk.com.br"
         projeto['STATUS'] = 2
         projeto['USUARIO_ID'] = USUARIO_ID
         projeto['SITE_ID'] = self.site.ID
         projeto['ID'] = await self.project.new(projeto)   

         entrada= f"SITE_ID={self.site.ID}" 
         result = await self.task.add_process_tasks(projeto_id=projeto['ID'],processo_id=self.processo_id,usuario_id=USUARIO_ID,entrada=entrada)
       

         site = {}
         site['ID'] = self.site.ID
         site['NOME'] = self.site.NOME
         site['AREA'] = self.site.AREA
         site['OBJETIVO'] = self.site.OBJETIVO
         site['PUBLICO_ALVO'] = self.site.PUBLICO_ALVO
         site['DESIGN_ESTILO'] = self.site.DESIGN_ESTILO
         site['FUNCIONALIDADES'] = self.site.FUNCIONALIDADES
         site['INFORMACOES_CADASTRAIS'] = self.site.INFORMACOES_CADASTRAIS

         #atualiza status e entrada ta primeira tarefa: entrevista
         saida = json.dumps(site,indent=4)
         query = f"""
         UPDATE TAREFA 
         SET STATUS = 'R', SAIDA = '{saida}'
         WHERE PROJETO_ID = {projeto['ID']} AND PROCESSO_ID = {self.processo_id} AND SEQUENCIA = 1
         """
         result = await self.task.query(query)

         #ativa o projeto
         result = await self.project.update({'STATUS':1,"ID":projeto['ID']})

         return "A criação do seu site teve inicio. Agora é só aguardar alguns minutos."




   #==========
   def salva_site(self,site):
      # Diretório onde os arquivos serão salvos
      diretorio = 'site'
      os.makedirs(diretorio, exist_ok=True)  # Cria o diretório se não existir

      # Percorre o dicionário e salva cada página como um arquivo HTML
      for filename, content in site.items():
         # Cria um caminho completo com o diretório e o nome do arquivo
         filepath = os.path.join(diretorio, filename)

         # Abre o arquivo para escrita e salva o conteúdo
         with open(filepath, 'w', encoding='utf-8') as file:
            file.write(content)

            print("Arquivos salvos com sucesso!")

    


@router.post("/site_project_create")
async def  site_project_create(data:dict):
    site= Site()

    wd = Webdesigner()
    vld = Validator()

    required_keys = ['USUARIO_ID', 'NOME', 'AREA', 'OBJETIVO', 'PUBLICO_ALVO', 'DESIGN_ESTILO', 'FUNCIONALIDADES']

    result = vld.validate_object_json(data,required_keys)
    
    if result["STATUS"] == "F":
        return "Estão faltando estes campos obrigatórios: " + ", ".join(result['RESULT'])
  
    result = await wd.site_project_create(
        ID= data['ID'],
        USUARIO_ID=data['USUARIO_ID'],
        NOME=data['NOME'],
        AREA=data['AREA'],
        OBJETIVO=data['OBJETIVO'],
        PUBLICO_ALVO=data['PUBLICO_ALVO'],
        DESIGN_ESTILO=data['DESIGN_ESTILO'],
        FUNCIONALIDADES=data['FUNCIONALIDADES'],
        INFORMACOES_CADASTRAIS=data['INFORMACOES_CADASTRAIS']
    ) 
    return result



#==========
async def main():
   
   
   user={}
   user['USUARIO_ID'] = 'ei5Vm2BLTLPcgzK6tGnf7aP2a1I3'
   user['EMAIL'] =  '<EMAIL>'
   user['NOME'] = 'Glayson Carlos da Silva'


   data = {}
   data['ID']= 0
   data['NOME'] = 'petdocarlos'
   data["AREA"] = "Pet shop"
   data["OBJETIVO"] = "vendas"
   data["PUBLICO_ALVO"] = "donos de bichos de estimação"
   data["DESIGN_ESTILO"] = "fundoazul com texto amarelo"
   data["USUARIO_ID"]  = "ei5Vm2BLTLPcgzK6tGnf7aP2a1I3"
   data["FUNCIONALIDADES"] = """
   link para redes sociais.
   formulario para recebimento de newsletter.
   """
   

   #print(data)
   llm =  LLM()

   site = Site()
   wd = Webdesigner()
   result = await wd.site_project_create(**data)
   print(result)
   #exit()
   
   

   #area = "pet shop"
   #objetivo = "vendas"
   #wd.site_area = area
   #wd.site_objetivo = objetivo
   #wd.site_destino = "/site/sites/teste/imagens/"
   #wd = Webdesigner(llm="gemini-1.5-pro-latest")


   #print(html_code)

#asyncio.run(main())