﻿from pydantic import BaseModel, Field 
from typing import Optional, List, Literal 
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from .agent_llm import LLM
from .agent_customer import AgentCustomer
from ..cache import cache
from ..functions.util import generate_unique_id
from ..functions.util import cep_data
from .agent_openai import OpenAi
from agents import function_tool
from .agent_mysql import Mysql
from ..functions.validation.cpf_cnpj import cpf_cnpj_valido
import requests
import json
import re
from datetime import datetime
import pytz
from .agent_logger import AgentLogger
from .agent_message import Message
from .agent_ttsEdge import AgentTTSEdge  # Adicionando import para TTS
from .agent_whisper import AgentWhisper  # Adicionando import para transcrição de áudio
from threading import Lock
import tempfile
import os
import base64
from .agent_product import Product
from .agent_workOrder import AgentWorkOrder

logger = AgentLogger()
modo_resposta_ia = "texto"
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()  # ✅ PROTEÇÃO CONTRA CONCORRÊNCIA
agentMessage = Message()
agentProduct = Product()
agentCustomer = AgentCustomer()

router = APIRouter()
oai = OpenAi()  # Using your custom OpenAi class
mysql = Mysql()


def limpar_tags_agente(texto: str) -> str:
    """
    Remove tags incompletas ou malformadas que podem aparecer nas respostas do agente
    especialmente quando funções/tools são executadas.
    
    Args:
        texto (str): Texto com possíveis tags malformadas
        
    Returns:
        str: Texto limpo sem tags malformadas
    """
    if not texto:
        return texto
    
    # Remover tags incompletas /toolcall> e /tool_call> no início da resposta
    texto = re.sub(r'^/toolcall>', '', texto.strip())
    texto = re.sub(r'^/tool_call>', '', texto.strip())
    
    # Remover outras possíveis tags malformadas relacionadas a tool calls
    texto = re.sub(r'^<toolcall[^>]*>', '', texto.strip())
    texto = re.sub(r'^</toolcall>', '', texto.strip())
    texto = re.sub(r'^<tool_call[^>]*>', '', texto.strip())
    texto = re.sub(r'^</tool_call>', '', texto.strip())
    texto = re.sub(r'^</?tool[^>]*>', '', texto.strip())
    
    # Remover tags de função incompletas no início
    texto = re.sub(r'^/function>', '', texto.strip())
    texto = re.sub(r'^<function[^>]*>', '', texto.strip())
    
    # Remover espaços extras que podem ter sobrado
    texto = texto.strip()
    
    return texto


def extrair_texto_puro(conteudo: str) -> str:
    """
    Extrai o texto puro de um conteúdo, removendo formatação HTML e Markdown,
    tags malformadas do agente e preservando quebras de linha naturais.

    Args:
        conteudo (str): O texto com formatação a ser limpo.

    Returns:
        str: O texto puro sem formatação, com quebras de linha preservadas.
    """
    # 0. Primeiro, limpar tags malformadas do agente
    texto_sem_tags = limpar_tags_agente(conteudo)
    
    # 1. Remover tags HTML (ex: <span>, <img>, <p>, <div>, etc.)
    texto_sem_html = re.sub(r'<[^>]+>', '', texto_sem_tags)

    # 2. Remover formatação Markdown (negrito, itálico, etc.)
    texto_sem_markdown = re.sub(r'(\*\*|__|~~|`|\*|_)+', '', texto_sem_html)

    # 3. Processar linhas:
    linhas_limpas = [linha.strip() for linha in texto_sem_markdown.splitlines() if linha.strip()]

    # 4. Unir as linhas limpas preservando quebras de linha naturais.
    texto_puro_final = "\n".join(linhas_limpas)

    return texto_puro_final


def limpar_emojis_para_tts(texto: str) -> str:
    """
    Remove emojis do texto para evitar problemas na síntese de voz
    """
    # Pattern para detectar emojis
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002500-\U00002BEF"  # chinese char
        u"\U00002702-\U000027B0"
        u"\U00002702-\U000027B0"
        u"\U000024C2-\U0001F251"
        u"\U0001f926-\U0001f937"
        u"\U00010000-\U0010ffff"
        u"\u2640-\u2642" 
        u"\u2600-\u2B55"
        u"\u200d"
        u"\u23cf"
        u"\u23e9"
        u"\u231a"
        u"\ufe0f"  # dingbats
        u"\u3030"
                      "]+", flags=re.UNICODE)
    return emoji_pattern.sub(r'', texto)


async def text_to_speech(text: str, voice: str = "pt-BR-FranciscaNeural") -> str:
    """
    Converte texto para áudio usando AgentTTSEdge (Edge-TTS local)
    Remove emojis automaticamente para evitar pronúncia incorreta
    Retorna áudio em base64
    """
    try:
        import base64
        
        # Limpar emojis do texto antes de enviar para TTS
        texto_limpo = limpar_emojis_para_tts(text)
        logger.info(f"🧹 TTS: Texto original ({len(text)} chars) → limpo ({len(texto_limpo)} chars)")
        
        # Usar AgentTTSEdge local
        tts_agent = AgentTTSEdge()
        result = await tts_agent.synthesize_to_bytes(texto_limpo, voice)
        
        if result and result.get("success") and result.get("audio_data"):
            audio_bytes = result["audio_data"]
            # Converter para base64
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            logger.info(f"🎙️ TTS: Texto convertido para áudio usando voz {voice} - {len(audio_bytes)} bytes")
            return audio_base64
        
        logger.warning("🎙️ TTS: Nenhum áudio gerado pelo AgentTTSEdge")
        return ""
        
    except Exception as e:
        logger.error(f"🎙️ TTS: Erro na síntese de voz com AgentTTSEdge: {str(e)}")
        return ""


async def transcribe_audio(audio_base64: str, audio_format: str = "mp3") -> str:
    """
    Converte áudio em base64 para texto usando AgentWhisper
    Suporta formatos: mp3, wav, webm, m4a, flac, ogg
    """
    try:
        # Mapear formatos para extensões corretas
        format_mapping = {
            "mp3": "mp3",
            "wav": "wav", 
            "webm": "webm",
            "m4a": "m4a",
            "flac": "flac",
            "ogg": "ogg",
            "mpeg": "mp3",
            "mpga": "mp3"
        }
        
        # Usar formato mapeado ou padrão
        file_extension = format_mapping.get(audio_format.lower(), "mp3")
        
        # Decodificar base64 para bytes
        audio_bytes = base64.b64decode(audio_base64)
        logger.info(f"Áudio decodificado: {len(audio_bytes)} bytes")
        
        # Criar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Usar AgentWhisper para transcrição
            whisper_agent = AgentWhisper()
            result = whisper_agent.transcribe(temp_file_path)
            
            # Extrair texto da resposta
            result_text = result.get("transcription", "") if isinstance(result, dict) else str(result)
            return result_text
            
        finally:
            # Limpar arquivo temporário
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"Erro na transcrição com AgentWhisper: {str(e)}")
        return ""


# Modelos para cliente
class ClienteData(BaseModel):
    # CAMPOS OBRIGATÓRIOS
    IDX: str  # Obrigatório - ID único do cliente
    NOME: str  # Obrigatório - Nome completo do cliente
    EMAIL: str  # Obrigatório - Email do cliente
    TELEFONE: str  # Obrigatório - Telefone do cliente
    
    # CAMPOS OPCIONAIS
    CPF_CNPJ: Optional[str] = None
    CEP: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None
    # Adicione todos os campos relevantes do seu dicionário 'cliente'



# Modelo para um item de serviço individual
class ProdutoItem(BaseModel):
    id: int
    nome: str
    valor: float
    quantidade: int


# Modelo para um item de serviço individual
class ServicoItem(BaseModel):
    id: int
    nome: str
    valor: float
    quantidade: int

    

# Modelo para um item de serviço individual
class Item(BaseModel):
    id: int
    nome: str
    valor: float
    quantidade: int    


# Modelo para um item de forma de pagamento individual
class FormaPagamentoItem(BaseModel):
    nr: int = Field(alias="nr") # Use Field(alias) se o nome do campo Python for diferente do JSON
    id: int
    valor: float
    vencimento: str # Ou datetime.date, se preferir lidar com objetos de data
    pago: Literal[0, 1] # 0 ou 1   

# Modelo para negocio (se for um dicionário simples, pode ser mais simples)
class NegocioData(BaseModel):
    IDX: Optional[str] = None
    NOME: Optional[str] = None
    LOGRADOURO: Optional[str] = None
    NUMERO: Optional[str] = None
    COMPLEMENTO: Optional[str] = None
    BAIRRO: Optional[str] = None
    CIDADE: Optional[str] = None
    UF: Optional[str] = None
    RESPONSAVEL: Optional[str] = None
    TELEFONE: Optional[str] = None
    EMAIL: Optional[str] = None
    # ... outros campos relevantes do seu dicionário 'negocio'     


class AgentOficinatech:
    def __init__(
        self,
        name="oficinatech",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        negocio= None,
        plataforma_url=None):
        logger.info("===== AgentOficinatech() =====")
        # Faixa: 22-27
        logger.info(f"name: {name}")
        logger.info(f"usuario_nome: {usuario_nome}")
        logger.info(f"usuario_idx: {usuario_idx}")
        logger.info(f"negocio_idx: {negocio_idx}")
        logger.info(f"negocio_nome: {negocio_nome}")
        logger.info(f"negocio_area: {negocio_area}")
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.instructions = f"""
        Seu nome é Tech. Você é uma atendente do aplicativo oficinatech, aplicativo de gerenciamento de oficina de veiculos. Estas são as suas atribuições:
        -responder as perguntas do usuário sobre o uso do aplicativo de forma clara e objetiva.
        -responder perguntas sobre os dados da oficina, como faturamento, agentamentos feitos, etc. 
        -Realizar operações cadastrais quando for sollicitada, como cadastrar bicicletas, clientes, etc. Para isto, você deverá usar as funções especiais disponiveis (tools).
        -Sempre que receber alguma imagem, sem que esteja claro o contexto ou o que fazer com ela, ou o usuário não tenha ainda informado, pergunte a ele o que  deseja fazer com a imagem. 
        Alguns dados adicionais que você precisará para algumas tarefas e funções:
        -Nome do usuário: {usuario_nome}
        -Nome da oficina: {negocio_nome}
        -URL da plataforma: {plataforma_url}  
        -IDX do usuário: {usuario_idx}
        -IDX do negócio: {negocio_idx}
        -Data e hora atual: {data_hora}
        -Negocio: {negocio}

        Você executa tarefas especiais de atualização de dados da oficina, usando suas funções especiais.
        -Atualizar logo: logo_atualizar()
        -Adicionar modelo de veiculo: produto_modelo_adicionar()
        -Iniciar ordem de serviço: ordem_servico_abrir() - IMPORTANTE: sempre passe o parâmetro plataforma_url = {plataforma_url}
        

     

        Não use emojis em todas as frases. Use apenas quando for necessário ou o contexto exigir.
        Consulta de dados deverão ser feitas usando a função consulta_atualiza_dados(). Nunca use em 2 frases seguidas


        Ao dar informações ao usuário, seja listas, resumos, tabelsa etc, leve em consideração que a maioria dos usuários acessa o app por celular. Então que o sdados sejam exibidos de forma responsiva e mobile-first (layou mobile como prioridade), evitando assim o uso de multiplas colunas e muitos dados na horizontal.


        Quando o usuario solicitar informaões ou você precisar de buscar dados de cliente, serviços, produtos, etc, sempre use a função consulta_atualiza_dados() ou outra função que seja adequada para o caso. Nunca responda baseado em seu proprio conhecimento ou de fontes externas. Só confie em dados obtidos através das suas funções de consulta.

        Qualquer referencia a código do cliente, na verdade se refere ao IDX no cliente no banco de dados. Então se houver uma busca ou solicitação de dados e for mencionado 'código do cliente' , o que se ser na verdade buscado, adicionado ou referenciado é o IDX do cliente.


        ORDEM DE SERVIÇO:

        STATUS (status_id):
        0 - 🟡 A Iniciar (padrão)
        1 - 🔵 Em Andamento
        2 - 🟠 Aguardando Peças
        3 - 🟣 Aguardando Cliente
        4 - 🟢 Concluída
        5 - 🔴 Cancelada
        6 - ✅ Entregue
        
        PRIORIDADE (prioridade_id):
        0 - 📄 Normal (padrão)
        1 - ⬇️ Baixa
        2 - ➡️ Média
        3 - ⬆️ Alta
        4 - 🚨 Urgente
        5 - ⚡ Crítica

        IMPORTANTE:
        Não informe ao clente que irá fazer uma consulta, nem informe como fará. Jamais mostre as querys de consulta. Apenas realize as consultas necessarias e no final informe o resultado com os dados encontrados.

        """

    def get_router(self):
        return self.router

    def get_instructions(self):
        return self.instructions

    def get_agent(self):
        return self.agent


class AgentOficinatechRealTime:
    def __init__(
        self,
        name="oficinatech_realtime",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None,
        negocio_nome=None,
        negocio_area=None,
        negocio=None,
        plataforma_url=None):
        logger.info("===== AgentOficinatechRealTime() =====")
        # Faixa: Linhas da nova classe AgentOficinatechRealTime
        logger.info(f"name: {name}")
        logger.info(f"usuario_nome: {usuario_nome}")
        logger.info(f"usuario_idx: {usuario_idx}")
        logger.info(f"negocio_idx: {negocio_idx}")
        logger.info(f"negocio_nome: {negocio_nome}")
        logger.info(f"negocio_area: {negocio_area}")
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx
        self.negocio_nome = negocio_nome
        self.negocio_area = negocio_area
        self.negocio = negocio
        self.plataforma_url = plataforma_url
        
        # Configurações específicas para tempo real
        self.connection = None  # Conexão WebSocket com OpenAI
        self.session = None     # Sessão da Realtime API
        self.is_connected = False
        
        self.instructions = f"""
        Seu nome é Tech. Você é uma atendente do aplicativo oficinatech, aplicativo de gerenciamento de oficina de veiculos.
        
        ⚡ MODO TEMPO REAL ATIVADO ⚡
        Você está operando em modo de voz tempo real, respondendo diretamente em áudio sem conversões de texto.
        
        Suas atribuições são:
        - Responder as perguntas do usuário sobre o uso do aplicativo de forma clara e objetiva
        - Responder perguntas sobre os dados da oficina, como faturamento, agendamentos feitos, etc
        - Realizar operações cadastrais quando solicitadas, usando as funções especiais disponíveis (tools)
        - Sempre que receber alguma imagem, pergunte ao usuário o que deseja fazer com ela
        
        Dados importantes para suas tarefas:
        - Nome do usuário: {usuario_nome}
        - Nome da oficina: {negocio_nome}
        - URL da plataforma: {plataforma_url}  
        - IDX do usuário: {usuario_idx}
        - IDX do negócio: {negocio_idx}
        - Data e hora atual: {data_hora}
        - Negocio: {negocio}

        INSTRUÇÕES ESPECÍFICAS PARA TEMPO REAL:
        - Seja conversacional e natural, como se fosse uma conversa ao vivo
        - Evite respostas muito longas - mantenha-se direto e objetivo
        - Use linguagem falada, não escrita (ex: "vou verificar isso pra você" ao invés de "verificarei")
        - Não cite códigos ou números técnicos em excesso
        - Seja empático e prestativo como um atendente real
        
        STATUS DA ORDEM DE SERVIÇO:
        0 - A Iniciar (padrão)
        1 - Em Andamento
        2 - Aguardando Peças
        3 - Aguardando Cliente
        4 - Concluída
        5 - Cancelada
        6 - Entregue
        
        PRIORIDADE:
        0 - Normal (padrão)
        1 - Baixa
        2 - Média
        3 - Alta
        4 - Urgente
        5 - Crítica

        IMPORTANTE:
        Não informe que irá fazer consultas - apenas faça e informe o resultado naturalmente.
        """

    def get_instructions(self):
        """Retorna as instruções do agente para tempo real"""
        return self.instructions

    def get_tools_list(self):
        """Retorna lista de ferramentas compartilhadas com o agente principal"""
        # As mesmas tools do agente principal, compartilhadas
        return [
            consulta_atualiza_dados,
            cliente_adicionar,
            produto_adicionar,
            ordem_servico_abrir,
        ]

    async def initialize_connection(self):
        """Inicializa conexão WebSocket com OpenAI Realtime API"""
        # Implementação da conexão será feita na Etapa 2
        logger.info("🔄 Inicializando conexão WebSocket para tempo real...")
        pass

    async def close_connection(self):
        """Fecha conexão WebSocket"""
        # Implementação será feita na Etapa 2
        logger.info("🔌 Fechando conexão WebSocket...")
        pass


def get_conversa_key(usuario_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{usuario_idx}_{agente_nome}_{ conversa_idx}"
    

def find_active_conversation(usuario_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{usuario_idx}_{agente_nome}_"):
            
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    #print("===== add_message_to_history() =====")
    #print("history", history)
    #print("message", message)
    #print("is_user", is_user)   
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


def limitar_tamanho_mensagem(mensagem: str, imagem: str = None, max_tokens: int = 60000) -> str:
    """
    Limita o tamanho total da mensagem para evitar exceder o limite de tokens.
    
    Args:
        mensagem (str): Mensagem original do usuário
        imagem (str): Imagem em base64 (opcional)
        max_tokens (int): Número máximo de tokens permitidos
        
    Returns:
        str: Mensagem formatada dentro do limite de tokens
    """
    # Estima-se que 1 token ~ 4 caracteres em média
    max_chars = max_tokens * 4
    
    if not imagem:
        return mensagem[:max_chars] if len(mensagem) > max_chars else mensagem
    
    # Reserva 20% do espaço para a mensagem do usuário
    max_msg_chars = int(max_chars * 0.2)
    max_img_chars = int(max_chars * 0.8)
    
    # Trunca a mensagem se necessário
    if len(mensagem) > max_msg_chars:
        mensagem = mensagem[:max_msg_chars] + "..."
    
    # Trunca a imagem se necessário
    if len(imagem) > max_img_chars:
        imagem = imagem[:max_img_chars] + "..."
    
    return f"Imagem em base64 (truncada se necessário):\n{imagem}\n\nMensagem do usuário:\n{mensagem}"



async def detectar_intencao_usuario(mensagem: str):
    return ""



async def process_with_agent(
    mensagem: str,
    negocio_idx: str,
    modelo: str,
    usuario_funcao:int,
    usuario_nome: str = "Carlos",
    usuario_idx: str = "",
    plataforma_url: str = "",
    imagem: str = "",
    is_audio_response: bool = False,
    negocio: dict = None,
    
):
    """
    Função comum para processar mensagens com o agente OficinatTech
    Usada tanto pelo endpoint de texto quanto pelo de áudio
    
    Args:
        mensagem (str): Texto a ser processado pelo agente
        negocio_idx (str): ID do negócio
        modelo (str): Modelo de IA a ser usado
        usuario_nome (str): Nome do usuário
        plataforma_url (str): URL da plataforma
        imagem (str): Imagem em base64 (opcional)
        usuario_funcao (int): Função do usuário (0=administrador, 1=funcionário)
        is_audio_response (bool): Se a resposta será convertida para áudio
        
    Returns:
        tuple: (agente_obj, historico_mensagens, conversa_idx, intencao)
    """
    logger.info("===== process_with_agent() =====")
    logger.info(f"mensagem: {mensagem}")
    logger.info(f"modelo: {modelo}")
    logger.info(f"is_audio_response: {is_audio_response}")
    
    # Verificar se existe conversa ativa
    conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "oficinatech")
    
    if not conversa_idx:
        conversa_idx = generate_unique_id()
        historico_mensagens = []
    
    # Processar imagem se fornecida
    if imagem:
        mensagem = "imagem_link :" + imagem + ";mensagem:" + mensagem
    
    # Adicionar mensagem do usuário ao histórico
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)


    if usuario_funcao == 0:
        usuario_funcao = "administrador"
    else:
        usuario_funcao = "funcionario"
    
    # Criar agente
    agentOfTech = AgentOficinatech(
        negocio_idx=negocio_idx,
        usuario_nome=usuario_nome,
        plataforma_url=plataforma_url,
        usuario_idx=usuario_idx,
        negocio=negocio
    )
    
    llm = LLM()
    model = llm.get_model_idx(modelo)
    logger.info("model carregado %s", model)
    
    instructions = agentOfTech.get_instructions()
    
    # Detectar se é uma solicitação de listagem de produtos
    intencao = await detectar_intencao_usuario(mensagem)
    logger.info(f"Intenção detectada: {intencao}")
    
    # Para respostas de áudio, sempre usar texto simples ao invés de JSON estruturado
    if is_audio_response:
        logger.info("Resposta para áudio detectada - forçando formato de texto simples")
      
        instructions += """
        
        INSTRUÇÃO ESPECIAL PARA RESPOSTA DE ÁUDIO:
        Esta resposta será convertida para áudio (text-to-speech), portanto:
        - NUNCA use JSON estruturado ou HTML
        - SEMPRE responda em texto simples e natural
        - Converta valores em texto por extenso e informe no final a moeda utilizda. Exemplo: R$ 100,00 = cem reais. 
        - Para produtos, descreva-os de forma conversacional
        - Use frases completas e bem estruturadas
        - Evite citar códigos, símbolos especiais ou formatação complexa
        - Seja claro e direto na comunicação
        """
    
    output_type = None


    # Criar lista base de tools (comum para todos os usuários)
    tools_list = [
        consulta_atualiza_dados,
        cliente_adicionar,
        produto_adicionar,
        ordem_servico_abrir,
    ]
    

    
    agenteTech = {
        "name": "MCP+",
        "instructions": instructions,
        "model": model,
        "tools": tools_list,
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,  # Temporariamente desabilitado devido ao errocom     json_schema
        "input_guardrails": [],
        "output_guardrails": [],
    }
    
    # Criar o agente
    agenteTech_obj = await oai.agent_create(**agenteTech)
    logger.info("agente objeto criado")
    
    return agenteTech_obj, historico_mensagens, conversa_idx,intencao


async def process_agent_stream(
    agenteTech_obj,
    historico_mensagens: list,
    negocio_idx: str,
    conversa_idx: str
):
    """
    Função comum para processar o streaming do agente OficinatTech
    Versão simplificada sem produtos estruturados
    
    Args:
        agenteTech_obj: Objeto do agente criado
        historico_mensagens: Lista do histórico de mensagens
        negocio_idx: ID do negócio
        conversa_idx: ID da conversa
        
    Yields:
        str: Chunks da resposta do agente
    """
    global messageChat
    
    logger.info("=== INICIANDO PROCESS_AGENT_STREAM OFICINATECH ===")
    resposta_completa = ""
    chunk_count = 0
    
    try:
        async for response in oai.agent_run(agenteTech_obj, historico_mensagens):
            chunk_count += 1
            
            # Tratamento robusto do chunk
            if isinstance(response, bytes):
                chunk_str = response.decode('utf-8')
            else:
                chunk_str = str(response)
            
            # Acumula a resposta completa
            resposta_completa += chunk_str
            
            # ✅ LIMPAR TAGS MALFORMADAS APENAS DO PRIMEIRO CHUNK SE NECESSÁRIO
            if chunk_count == 1 and ('/tool_call>' in chunk_str or '/toolcall>' in chunk_str or '<tool' in chunk_str):
                chunk_limpo = limpar_tags_agente(chunk_str)
                yield chunk_limpo
            else:
                # Para outros chunks, envia normalmente
                yield chunk_str
            
        #logger.info(f"=== STREAM FINALIZADO === (Total chunks: {chunk_count})")
        
        # Processa a resposta completa para salvar no histórico
        # Primeiro limpar tags malformadas, depois extrair texto puro
        resposta_limpa = limpar_tags_agente(resposta_completa)
        texto_para_historico = extrair_texto_puro(resposta_limpa)
        historico_mensagens = add_message_to_history(historico_mensagens, texto_para_historico, False)

        logger.info(f"@@@@@ @@@@@  historico_mensagens: {len(historico_mensagens)} mensagens")
        logger.info(historico_mensagens)
        
        # Salvar histórico no cache
        cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
        cache[cache_key] = historico_mensagens
        #logger.info(f"Histórico salvo no cache com chave: {cache_key}")
        
        # ✅ USAR A VARIÁVEL GLOBAL COM PROTEÇÃO
        with messageChat_lock:
            # ✅ VERIFICAR SE O CONVERSA_IDX ESTÁ CORRETO ANTES DE GRAVAR
            logger.info(f"🔍 VERIFICAÇÃO: CONVERSA_IDX no messageChat antes da gravação: {messageChat.get('CONVERSA_IDX', 'NÃO DEFINIDO')}")
            
            # ✅ ATUALIZAR APENAS OS CAMPOS QUE EXISTEM NA TABELA MENSAGEM
            messageChat["SAIDA"] = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")
            messageChat["RECEBIDO"] = resposta_limpa  # Usar resposta limpa (sem tags malformadas) ao invés do texto puro
            
            # ✅ CALCULAR TOKENS SE NECESSÁRIO
            messageChat["RECEBIDO_TKS"] = len(texto_para_historico.split())  # Estimativa simples
            messageChat["TOTAL_TKS"] = messageChat.get("ENVIADO_TKS", 0) + messageChat["RECEBIDO_TKS"]
            
            logger.info(f"📋 messageChat final com CONVERSA_IDX: {messageChat.get('CONVERSA_IDX')}")
            
            messageId = await agentMessage.add(messageChat.copy())
            logger.info(f"💾 messageId gravado: {messageId}")
            
            if messageId:
                logger.info("✅ Mensagem salva com sucesso no banco de dados!")
            else:
                logger.error("❌ ERRO: Mensagem não foi salva no banco de dados!")
                logger.error(f"Dados enviados: {messageChat}")

    except Exception as stream_error:
        logger.error(f"Erro durante o streaming: {str(stream_error)}")
        # Em caso de erro, ainda tenta salvar o que foi processado até agora
        if resposta_completa:
            texto_puro = extrair_texto_puro(resposta_completa)
            historico_mensagens = add_message_to_history(historico_mensagens, texto_puro, False)
            cache_key = get_conversa_key(negocio_idx, "oficinatech", conversa_idx)
            cache[cache_key] = historico_mensagens
        raise stream_error


@router.post("/send/text")
async def send_text(data: dict):
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL (conforme sugerido)
    
    logger.info("===== send_text() =====")
    logger.info(f"data: {data}")
    # Extrair dados necessários
    mensagem = data.get("mensagem", "")
    negocio_idx = data.get("negocio_idx", "")
    modelo = data.get("modelo", "")
    imagem = data.get("imagem", "")
    plataforma_url = data.get("plataforma_url", "")
    usuario_nome = data.get("usuario_nome", "Carlos")
    usuario_funcao = data.get("usuario_funcao", 1)
    usuario_idx = data.get("usuario_idx", "")
    modo_resposta_ia = data.get("modo_resposta_ia", "texto")
    logger.info(f"modo_resposta_ia: {modo_resposta_ia}")
    negocio = data.get("negocio", {})
    logger.info(f"negocio: {negocio}")




    if not modelo:
        return {"success": False, "message": "modelo é obrigatório"}

    # ✅ MAPEAR CORRETAMENTE PARA OS CAMPOS DA TABELA MENSAGEM
    with messageChat_lock:  # 🔒 PROTEÇÃO CONTRA CONCORRÊNCIA
        messageChat.clear()  # ✅ LIMPAR DADOS ANTERIORES
        messageChat.update({
            # ✅ CAMPOS QUE EXISTEM NA TABELA MENSAGEM:
            "ENVIADO": mensagem,                                              # text
            "LLM_IDX": modelo,                                               # varchar(10)
            "CANAL_ID": "WEB_APP",                                           # varchar(50)
            "ENTRADA": datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S"),  # datetime
            "AGENTE_ID": 13,                                                  # int(10) - ID do agente OficinatTech
            "CONVERSA_IDX": 0,                                               # varchar(50) - será atualizado se necessário
            "ENVIADO_TKS": 0,                                                # smallint(5)
            "RECEBIDO_TKS": 0,                                               # smallint(5)
            "TOTAL_TKS": 0,                                                  # smallint(4)
            "FUNCAO_CHAMADA": 0,                                             # smallint(1)
            # ✅ NOVOS CAMPOS ADICIONADOS NA TABELA:
            "NEGOCIO_IDX": negocio_idx,                                      # varchar(10)
            "USUARIO_IDX": negocio_idx,                                      # varchar(10) - usando negocio_idx como usuario_idx
            "IMAGEM": imagem if imagem else None,                            # text - aceita imagem completa em base64
        })
        
        logger.info(f"messageChat (todos os campos da tabela): {messageChat}")


    try:
        # Usar a função comum para processar com o agente
        agentOficinatech_obj, historico_mensagens, conversa_idx, intencao = await process_with_agent(
            mensagem=mensagem,
            negocio_idx=negocio_idx,
            modelo=modelo,
            usuario_nome=usuario_nome,
            plataforma_url=plataforma_url,
            imagem=imagem,
            usuario_idx=usuario_idx,
            usuario_funcao=usuario_funcao,
            negocio=negocio
        )

        # ✅ ATUALIZAR O CONVERSA_IDX COM O VALOR REAL OBTIDO **ANTES** DO STREAMING
        with messageChat_lock:
            messageChat["CONVERSA_IDX"] = conversa_idx
            logger.info(f"🔧 CORREÇÃO: CONVERSA_IDX atualizado para {conversa_idx} ANTES do streaming")

        # Verificar modo de resposta para processar adequadamente
        logger.info(f"🎯 MODO_RESPOSTA_IA recebido: '{modo_resposta_ia}' (tipo: {type(modo_resposta_ia)})")
        
        if modo_resposta_ia == "audio":
            # Modo áudio: coletar resposta completa e retornar áudio em JSON
            logger.info("Processando no modo áudio...")
            resposta_completa = ""
            async for chunk in process_agent_stream(
                agenteTech_obj=agentOficinatech_obj,
                historico_mensagens=historico_mensagens,
                negocio_idx=negocio_idx,
                conversa_idx=conversa_idx
            ):
                # Extrair texto do chunk de streaming
                if isinstance(chunk, bytes):
                    chunk_str = chunk.decode('utf-8')
                else:
                    chunk_str = str(chunk)
                
                # Remover prefixos de SSE se existirem
                if chunk_str.startswith('data: '):
                    chunk_str = chunk_str[6:]
                
                resposta_completa += chunk_str
            
            # Limpar tags malformadas e extrair texto puro da resposta
            resposta_limpa = limpar_tags_agente(resposta_completa)
            texto_resposta = extrair_texto_puro(resposta_limpa)
            logger.info(f"Resposta para TTS: {texto_resposta}")
            
            # Converter resposta para áudio
            audio_resposta = await text_to_speech(texto_resposta)
            
            if audio_resposta:
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "audio",
                    "resposta_texto": resposta_limpa,  # Retornar resposta limpa
                    "resposta_audio": audio_resposta,
                    "audio_format": "mp3"
                }, headers={
                    "Content-Type": "application/json"
                })
            else:
                # Se falhar na conversão para áudio, retorna só texto
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "audio",
                    "resposta_texto": resposta_limpa,  # Retornar resposta limpa
                    "message": "Resposta gerada, mas falha na conversão para áudio"
                }, headers={
                    "Content-Type": "application/json"
                })
                
        elif modo_resposta_ia == "voz_ao_vivo":
            # Modo voz ao vivo: coletar resposta completa, retornar somente áudio para reprodução automática
            logger.info("Processando no modo voz ao vivo...")
            resposta_completa = ""
            async for chunk in process_agent_stream(
                agenteTech_obj=agentOficinatech_obj,
                historico_mensagens=historico_mensagens,
                negocio_idx=negocio_idx,
                conversa_idx=conversa_idx
            ):
                # Extrair texto do chunk de streaming
                if isinstance(chunk, bytes):
                    chunk_str = chunk.decode('utf-8')
                else:
                    chunk_str = str(chunk)
                
                # Remover prefixos de SSE se existirem
                if chunk_str.startswith('data: '):
                    chunk_str = chunk_str[6:]
                
                resposta_completa += chunk_str
            
            # Limpar tags malformadas e extrair texto puro para TTS
            resposta_limpa_voz = limpar_tags_agente(resposta_completa)
            texto_para_tts = extrair_texto_puro(resposta_limpa_voz)
            logger.info(f"Texto para TTS: {texto_para_tts}")
            
            # Converter para áudio
            audio_resposta = await text_to_speech(texto_para_tts)
            
            if audio_resposta:
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "voz_ao_vivo",
                    "resposta_audio": audio_resposta,      # Áudio para reproduzir automaticamente
                    "audio_format": "mp3"
                }, headers={
                    "Content-Type": "application/json"
                })
            else:
                # Se falhar na conversão para áudio, retorna só uma resposta básica
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "voz_ao_vivo",
                    "message": "Resposta gerada, mas falha na conversão para áudio"
                }, headers={
                    "Content-Type": "application/json"
                })
                
        elif modo_resposta_ia == "texto_voz_ao_vivo":
            # Modo texto + voz ao vivo: coletar resposta completa, retornar texto estruturado + áudio
            logger.info("Processando no modo texto + voz ao vivo...")
            resposta_completa = ""
            async for chunk in process_agent_stream(
                agenteTech_obj=agentOficinatech_obj,
                historico_mensagens=historico_mensagens,
                negocio_idx=negocio_idx,
                conversa_idx=conversa_idx
            ):
                # Extrair texto do chunk de streaming
                if isinstance(chunk, bytes):
                    chunk_str = chunk.decode('utf-8')
                else:
                    chunk_str = str(chunk)
                
                # Remover prefixos de SSE se existirem
                if chunk_str.startswith('data: '):
                    chunk_str = chunk_str[6:]
                
                resposta_completa += chunk_str
            
            # Limpar tags malformadas e extrair texto puro para TTS
            resposta_limpa_texto_voz = limpar_tags_agente(resposta_completa)
            texto_para_tts = extrair_texto_puro(resposta_limpa_texto_voz)
            logger.info(f"Texto para TTS: {texto_para_tts}")
            
            # Converter para áudio
            audio_resposta = await text_to_speech(texto_para_tts)
            
            if audio_resposta:
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "texto_voz_ao_vivo",
                    "resposta_texto": resposta_limpa_texto_voz,  # Resposta limpa para exibir
                    "resposta_audio": audio_resposta,      # Áudio para reproduzir automaticamente
                    "audio_format": "mp3"
                }, headers={
                    "Content-Type": "application/json"
                })
            else:
                # Se falhar na conversão para áudio, retorna só texto estruturado
                return JSONResponse(content={
                    "success": True,
                    "modo_resposta_ia": "texto_voz_ao_vivo",
                    "resposta_texto": resposta_limpa_texto_voz,  # Resposta limpa mesmo quando áudio falha
                    "message": "Resposta gerada, mas falha na conversão para áudio"
                }, headers={
                    "Content-Type": "application/json"
                })
        else:
            # Modo texto normal: streaming response
            async def event_stream():
                async for chunk in process_agent_stream(
                    agenteTech_obj=agentOficinatech_obj,
                    historico_mensagens=historico_mensagens,
                    negocio_idx=negocio_idx,
                    conversa_idx=conversa_idx
                ):
                    yield chunk
                
            return StreamingResponse(event_stream(), media_type="text/plain")

    except Exception as e:
        logger.error(f"Erro durante execução do agente: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise


@router.post("/send/audio")
async def send_audio(data: dict):
    """
    Endpoint específico para processamento de áudio do OficinatTech
    Recebe áudio, converte para texto, processa com o agente e pode retornar nos 4 formatos
    """
    global messageChat  # ✅ USAR A VARIÁVEL GLOBAL
    
    logger.info("===== send/audio() =====")
    logger.info(f"data: {data}")
    
    try:
        # Extrair dados necessários
        audio_data = data.get("audio", "")  # Base64 do áudio
        audio_format = data.get("audio_format", "mp3")  # Formato do áudio (mp3, wav, etc.)
        negocio_idx = data.get("negocio_idx", "")
        modelo = data.get("modelo", "")
        usuario_nome = data.get("usuario_nome", "Carlos")
        usuario_funcao = data.get("usuario_funcao", 1)
        usuario_idx = data.get("usuario_idx", "")
        plataforma_url = data.get("plataforma_url", "")
        modo_resposta_ia = data.get("modo_resposta_ia", "texto")
        
        logger.info(f"modo_resposta_ia: {modo_resposta_ia}")
        logger.info(f"audio_format: {audio_format}")
        logger.info(f"Tamanho do áudio: {len(audio_data)} chars")
        
        if not audio_data:
            return {"success": False, "message": "Áudio é obrigatório"}
        
        if not modelo:
            return {"success": False, "message": "Modelo é obrigatório"}
        
        # Passo 1: Converter áudio para texto usando AgentWhisper
        logger.info("Iniciando transcrição do áudio...")
        texto_transcrito = await transcribe_audio(audio_data, audio_format)
        
        if not texto_transcrito:
            return {"success": False, "message": "Erro na transcrição do áudio"}
        
        logger.info(f"Texto transcrito: {texto_transcrito}")
        
        # ✅ MAPEAR CORRETAMENTE PARA OS CAMPOS DA TABELA MENSAGEM
        with messageChat_lock:  # 🔒 PROTEÇÃO CONTRA CONCORRÊNCIA
            messageChat.clear()  # ✅ LIMPAR DADOS ANTERIORES
            messageChat.update({
                # ✅ CAMPOS QUE EXISTEM NA TABELA MENSAGEM:
                "ENVIADO": texto_transcrito,                                  # text - o texto transcrito
                "LLM_IDX": modelo,                                           # varchar(10)
                "CANAL_ID": "WEB_AUDIO",                                     # varchar(50) - diferente para áudio
                "ENTRADA": datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S"),  # datetime
                "AGENTE_ID": 13,                                             # int(10) - ID do agente OficinatTech
                "CONVERSA_IDX": 0,                                           # varchar(50) - será atualizado
                "ENVIADO_TKS": 0,                                            # smallint(5)
                "RECEBIDO_TKS": 0,                                           # smallint(5)
                "TOTAL_TKS": 0,                                              # smallint(4)
                "FUNCAO_CHAMADA": 0,                                         # smallint(1)
                # ✅ NOVOS CAMPOS ADICIONADOS NA TABELA:
                "NEGOCIO_IDX": negocio_idx,                                  # varchar(10)
                "USUARIO_IDX": negocio_idx,                                  # varchar(10)
                "IMAGEM": None,                                              # text - áudio não tem imagem
            })
            
            logger.info(f"messageChat configurado para áudio: {messageChat}")
        
        # Passo 2: Processar o texto transcrito com o agente usando a função comum
        logger.info("Processando texto transcrito com o agente...")
        
        # Determinar se é resposta para áudio simplificado
        is_audio_response = (modo_resposta_ia == "audio")
        
        agentOficinatech_obj, historico_mensagens, conversa_idx, intencao = await process_with_agent(
            mensagem=texto_transcrito,
            negocio_idx=negocio_idx,
            modelo=modelo,
            usuario_funcao=usuario_funcao,
            usuario_nome=usuario_nome,
            plataforma_url=plataforma_url,
            imagem="",  # Áudio não tem imagem
            usuario_idx=usuario_idx,
            is_audio_response=is_audio_response
        )
        
        # ✅ ATUALIZAR O CONVERSA_IDX COM O VALOR REAL OBTIDO **ANTES** DO STREAMING
        with messageChat_lock:
            messageChat["CONVERSA_IDX"] = conversa_idx
            logger.info(f"🔧 CORREÇÃO: CONVERSA_IDX atualizado para {conversa_idx} ANTES do streaming")
        
        # Passo 3: Executar o agente e coletar resposta completa (necessário para todos os modos de áudio)
        logger.info("Coletando resposta completa do agente...")
        resposta_completa = ""
        async for chunk in process_agent_stream(
            agenteTech_obj=agentOficinatech_obj,
            historico_mensagens=historico_mensagens,
            negocio_idx=negocio_idx,
            conversa_idx=conversa_idx
        ):
            # Extrair texto do chunk de streaming
            if isinstance(chunk, bytes):
                chunk_str = chunk.decode('utf-8')
            else:
                chunk_str = str(chunk)
            
            # Remover prefixos de SSE se existirem
            if chunk_str.startswith('data: '):
                chunk_str = chunk_str[6:]
            
            resposta_completa += chunk_str
        
        # Passo 4: Preparar resposta baseada no modo solicitado (igual ao send/text)
        if modo_resposta_ia == "audio":
            # MODO ÁUDIO: Player de áudio na mensagem
            resposta_limpa_audio = limpar_tags_agente(resposta_completa)
            texto_resposta = extrair_texto_puro(resposta_limpa_audio)
            logger.info(f"Texto para TTS: {texto_resposta}")
            
            # Converter resposta para áudio
            logger.info("Convertendo resposta para áudio...")
            audio_resposta = await text_to_speech(texto_resposta)
            
            if audio_resposta:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "resposta_texto": resposta_limpa_audio,  # Retornar resposta limpa
                    "resposta_audio": audio_resposta,
                    "audio_format": "mp3",
                    "modo_resposta_ia": "audio"
                }
            else:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "resposta_texto": resposta_limpa_audio,  # Retornar resposta limpa
                    "message": "Resposta gerada, mas falha na conversão para áudio"
                }
                
        elif modo_resposta_ia == "voz_ao_vivo":
            # MODO VOZ AO VIVO: Só áudio para reprodução automática
            resposta_limpa_voz_vivo = limpar_tags_agente(resposta_completa)
            texto_para_tts = extrair_texto_puro(resposta_limpa_voz_vivo)
            logger.info(f"Texto para TTS: {texto_para_tts}")
            
            # Converter para áudio
            logger.info("Convertendo resposta para áudio (voz ao vivo)...")
            audio_resposta = await text_to_speech(texto_para_tts)
            
            if audio_resposta:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "resposta_audio": audio_resposta,      # Áudio para reproduzir automaticamente
                    "audio_format": "mp3",
                    "modo_resposta_ia": "voz_ao_vivo"
                }
            else:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "message": "Resposta gerada, mas falha na conversão para áudio",
                    "modo_resposta_ia": "voz_ao_vivo"
                }
                
        elif modo_resposta_ia == "texto_voz_ao_vivo":
            # MODO TEXTO + VOZ AO VIVO: Texto na tela + áudio reproduzido automaticamente
            resposta_limpa_texto_voz_audio = limpar_tags_agente(resposta_completa)
            texto_para_tts = extrair_texto_puro(resposta_limpa_texto_voz_audio)
            logger.info(f"Texto para TTS: {texto_para_tts}")
            
            # Converter para áudio
            logger.info("Convertendo resposta para áudio (texto + voz ao vivo)...")
            audio_resposta = await text_to_speech(texto_para_tts)
            
            if audio_resposta:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "resposta_texto": resposta_limpa_texto_voz_audio,  # Resposta limpa para exibir
                    "resposta_audio": audio_resposta,      # Áudio para reproduzir automaticamente
                    "audio_format": "mp3",
                    "modo_resposta_ia": "texto_voz_ao_vivo"
                }
            else:
                return {
                    "success": True,
                    "transcricao": texto_transcrito,
                    "resposta_texto": resposta_limpa_texto_voz_audio,  # Resposta limpa mesmo quando áudio falha
                    "message": "Resposta gerada, mas falha na conversão para áudio",
                    "modo_resposta_ia": "texto_voz_ao_vivo"
                }
        else:
            # MODO TEXTO: Apenas resposta em texto
            logger.info("Retornando resposta em texto para modo texto")
            resposta_limpa_texto = limpar_tags_agente(resposta_completa)
            return {
                "success": True,
                "transcricao": texto_transcrito,
                "resposta_texto": resposta_limpa_texto,  # Resposta limpa para texto
                "modo_resposta_ia": "texto"
            }
            
    except Exception as e:
        logger.error(f"Erro no processamento de áudio: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


@router.post("/agent/run")
async def agent_run(data: dict):
    
    logger.info(f"===== agent_oficinatech_run() =====")
    #print("data", data)
    logger.info("data")
    # Linha 74-75
    logger.info(f"data: {str(data)}")
    # Extrair dados necessários
    usuario_idx = data.get("usuario_idx")
    mensagem = data.get("mensagem", "")
    negocio_idx = data.get("negocio_idx", "")
    modelo = data.get("modelo", "")
    modelo = "1234567891"
    imagem = data.get("imagem", "")
    plataforma_url = data.get("plataforma_url", ""),
    usuario_nome = data.get("usuario_nome", "Carlos"),
    negocio_nome = data.get("negocio_nome", "Lucas Manutenções Bikes"),
    
    # Se negocio_idx estiver vazio, usar o valor padrão hardcoded
    if not negocio_idx:
        negocio_idx = "4015743441"
    
    logger.info(f"usuario_idx: {usuario_idx}")
    logger.info(f"mensagem: {mensagem}")
    logger.info(f"negocio_idx: {negocio_idx}")
    logger.info(f"modelo: {modelo}")
    logger.info(f"imagem: {imagem}")
    logger.info(f"plataforma_url: {plataforma_url}")
    logger.info(f"usuario_nome: {usuario_nome}")
    logger.info(f"negocio_nome: {negocio_nome}")

    #if imagem:
    #    mensagem = limitar_tamanho_mensagem(mensagem, imagem)



    
    agentOficinatech = AgentOficinatech(
        usuario_idx=usuario_idx,
        negocio_idx=negocio_idx,
        usuario_nome=usuario_nome,
        negocio_nome=negocio_nome,
        negocio=negocio,
        plataforma_url=plataforma_url,
        
    )

    if imagem:
        mensagem = "imagem_link :" + imagem + ";mensagem:" + mensagem

    if not usuario_idx:
        return {"success": False, "message": "usuario_idx é obrigatório"}

    if not modelo:
        return {"success": False, "message": "modelo é obrigatório"}


    # Verificar se existe conversa ativa
    conversa_idx, historico_mensagens = find_active_conversation(usuario_idx,agentOficinatech.name)


    # Se não existir conversa ativa, criar nova
    if not conversa_idx:
        #print("Não existe conversa ativa, criando nova conversa")
        conversa_idx = generate_unique_id()
        #print("conversa_idx", conversa_idx)
        historico_mensagens = []

    # Adicionar mensagem do usuário ao histórico
    #if imagem:
    #    mensagem_sistema = {
    #        "role": "system",
    #        "content": f"Uma nova imagem foi fornecida em base64: {imagem[:50]}..."
    #    }
    #historico_mensagens.append(mensagem_sistema)
    
    historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)
    #print("historico_mensagens", historico_mensagens)   


    llm = LLM()
    logger.info("*****modelo %s", modelo)
    model = llm.get_model_idx(modelo)
    logger.info("*****modelo 2 %s", model)

    instructions = agentOficinatech.get_instructions()  
    #print("instructions", instructions)
    
    agenteTech = {
        "name": "MCP+",
        "instructions": instructions,
        "model": model,
        "tools": [logo_atualizar, 
                  veiculo_categoria_adicionar,
                  veiculo_tipo_adicionar,   
                  veiculo_cor_adicionar,
                  consulta_atualiza_dados,
                  cliente_adicionar,
                  servico_adicionar,
                  produto_adicionar,
                  ordem_servico_abrir,
                  conta_email_crud
          
                  ],
        "handoff_description": None,
        "handoffs": [],
        "output_type": None,
        "input_guardrails": [],
        "output_guardrails": [],
    }

    #print("agenteTech", agenteTech)
     # Criar o agente DRE360
    agenteTech_obj = await oai.agent_create(**agenteTech)
    #print("agenteTech_obj", agenteTech_obj)


     # Executar o agente com o histórico de mensagens
    #print("")
    #print("")
    #print("")
    #print("")
    
    # Corrigir: agent_run retorna async generator, não pode usar await
    resposta_completa = ""
    async for response in oai.agent_run(agenteTech_obj, historico_mensagens):
        if isinstance(response, bytes):
            chunk_str = response.decode('utf-8')
        else:
            chunk_str = str(response)
        resposta_completa += chunk_str
    
    # Criar objeto response simples para manter compatibilidade
    class ResponseObj:
        def __init__(self, final_output):
            self.final_output = final_output
    
    response = ResponseObj(resposta_completa)
    
    #print("### Resposta comopleta do agent e###", response)
    #print("")
    #print("")
    #print("")
    #print("\n=== TRACES DO AGENTE ===")
    #print("Resposta final:", response.final_output)
    #print("Traces completos:", response.trace)
    #if hasattr(response, 'tool_outputs'):
    #print("Outputs das ferramentas:", response.tool_outputs)

    # Linhas 217-221 (aproximadamente)
    if hasattr(response, 'tool_outputs') and response.tool_outputs:
        #print("\n=== FERRAMENTAS UTILIZADAS ===")
        for tool_output in response.tool_outputs:
            # A estrutura exata pode variar um pouco, inspecione    'tool_output'
            print(f"- Ferramenta: {tool_output.tool_name}") 
            print(f"  Output: {tool_output.output}")
        print("=======================\n")
    else:
        pass
        #print("Nenhuma ferramenta utilizada")

     # Adicionar resposta ao histórico
    historico_mensagens = add_message_to_history(historico_mensagens, response.final_output, False)

    # Salvar histórico no cache
    cache_key = get_conversa_key(usuario_idx, agentOficinatech.name, conversa_idx)
    cache[cache_key] = historico_mensagens
    print("resposta do agente", response.final_output)
    return {"success": True, "message": response.final_output}

@function_tool
async def logo_atualizar(imagem_link: str, plataforma_url: str, negocio_idx: str):
    """
    Atualiza a logo da oficina a partir de um link de imagem.
    """
    logger.info(f"===== logo_atualiza() =====")
    logger.info(f"imagem_link: {imagem_link}")   
    logger.info(f"plataforma_url: {plataforma_url}")
    logger.info(f"negocio_idx: {negocio_idx}")
    
    # Remover possíveis aspas da string da URL da plataforma
    if isinstance(plataforma_url, tuple):
        plataforma_url = plataforma_url[0]
    
    # Remover possíveis vírgulas ou caracteres indesejados
    plataforma_url = plataforma_url.strip(",' ")

    # Construir a URL para o endpoint de salvar logo
    url = f"{plataforma_url}/app_phj/geral/imagens/logo/salvar.php"
    params = {
        "negocio_idx": negocio_idx,
        "imagem_link": imagem_link
    }
    logger.info(f"url: {url}")
    logger.info(f"params: {params}")
    
    try:
        # Fazer requisição para o endpoint usando GET com params (não data)
        response = requests.get(url, params=params)
        
        # Imprimir a resposta completa para depuração
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Resposta completa: {response.text}")
        
        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            try:
                if response.text.strip():  # Verificar se a resposta não está vazia
                    result = response.json()
                    if result.get('status') == 'ok':
                        return {"success": True, "message": "Logo atualizada com sucesso"}
                    else:
                        return {"success": False, "message": f"Erro ao atualizar logo: {result.get('mensagem', 'Erro desconhecido')}"}
                else:
                    return {"success": False, "message": "Resposta vazia do servidor"}
            except ValueError as e:
                # Se não conseguir decodificar o JSON, mostrar o conteúdo da resposta
                logger.error(f"Erro ao decodificar JSON: {str(e)}")
                logger.error(f"Conteúdo da resposta: {response.text}")
                return {"success": False, "message": f"Erro ao processar resposta do servidor: {str(e)}"}
        else:
            return {"success": False, "message": f"Erro na requisição: {response.status_code} - {response.text}"}
    except Exception as e:
        logger.error(f"Erro ao atualizar logo: {str(e)}")
        return {"success": False, "message": f"Erro ao atualizar logo: {str(e)}"}

@router.post("/drop/messages")
async def drop_messages(data: dict):
    """
    Endpoint para limpar cache de conversas e zerar variáveis de histórico
    
    Parâmetros:
    - negocio_idx (str): ID do negócio (obrigatório)
    - usuario_idx (str): ID específico do usuário (opcional - se não informado, limpa todas do negócio)
    """
    logger.info("=====🗑️🚮 drop_messages()🗑️🚮 =====")
    logger.info(f"data: {data}")
    
    negocio_idx = data.get("negocio_idx", "")
    usuario_idx = data.get("usuario_idx", "")
    
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    try:
        chaves_removidas = 0
        chaves_para_remover = []
        
                # Buscar chaves do cache que correspondem aos critérios
        for key in list(cache.keys()):
            # Filtrar por conversas do agente oficinatech
            logger.info(f"key: {key}")
            if key.startswith("conversa_") and "_oficinatech_" in key:
                logger.info(f"key é de conversa: {key}")
                # Se negocio_idx foi fornecido, usar como filtro (prioridade)
                if negocio_idx:
                    # Padrão: conversa_{negocio_idx}_oficinatech_{conversa_idx}
                    if f"conversa_{negocio_idx}_oficinatech_" in key:
                        logger.info(f"key é de conversa do negocio: {key}")
                        chaves_para_remover.append(key)
                # Se apenas usuario_idx específico foi fornecido, filtrar por ele
                elif usuario_idx:
                    if f"conversa_{usuario_idx}_oficinatech_" in key:
                        logger.info(f"key é de conversa do usuario: {key}")
                        chaves_para_remover.append(key)
        
        # Remover as chaves identificadas do cache
        for key in chaves_para_remover:
            try:
                del cache[key]
                chaves_removidas += 1
                logger.info(f"Chave removida do cache: {key}")
            except KeyError:
                logger.warning(f"Chave já não existe no cache: {key}")
        
        logger.info(f"Cache limpo: {chaves_removidas} conversas removidas")
        
        return {
            "success": True,
            "message": f"Cache de conversas limpo com sucesso! {chaves_removidas} conversas removidas.",
            "detalhes": {
                "conversas_removidas": chaves_removidas,
                "filtro_usado": f"negocio_idx: {negocio_idx}" + (f", usuario_idx: {usuario_idx}" if usuario_idx else " (todos os usuários)")
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {str(e)}")
        return {
            "success": False,
            "message": f"Erro ao limpar cache: {str(e)}"
        }


@function_tool
async def produto_categoria_adicionar(
    nome: str, 
    codigo: str,
    negocio_idx: str
    ):


    """
    Adiciona uma nova categoria de produto no banco de dados na tabela PRODUTO_CATEGORIA. Exemplos de categorias: Bicicleta, Acessórios da bicicleta, Acessórios do ciclista, lubrificantes, etc.

    NOME: 
    -Nome completo da categoria.
    -Exemplos: Acessórios da bicicleta, Acessórios do ciclista, lubrificantes, etc.
    -Obrigatório

    CODIGO: 
    -Código da categoria.
    -Exemplos: adb(acessorios da bicicleta), 015 (Lubrificantes).
    -opcional
  
    """

    if not nome:
        return {"success": False, "message": "Nome é obrigatório."};



    produto_categoria = {
        "IDX": generate_unique_id(),
        "NEGOCIO_IDX": negocio_idx,
        "CODIGO": codigo,
        "NOME": nome
    }

    result = await mysql.add("PRODUTO_CATEGORIA", produto_categoria)

    
    return {"success": True, "message": "Categoria de produto adicionada com sucesso."}

@function_tool
async def cliente_adicionar(
    nome: str, 
    telefone: str, 
    email: str, 
    cpf_cnpj: str, 
    cep: str,
    numero: str,
    complemento: str,
    negocio_idx: str
    ):
    """
    Adiciona um novo cliente no banco de dados.

    NOME: 
    -Nome completo do cliente.
    -Exemplo: Carlos Silva
    -Obrigatório

    TELEFONE: 
    -Telefone do cliente com ddd.
    -Exemplo: 31984784825
    -Obrigatório

    EMAIL: 
    -Email do cliente.
    -Exemplo: <EMAIL>
    -Obrigatório

    CPF_CNPJ: 
    -CPF ou CNPJ do cliente.
    -Exemplos: 
        cpf: 50518151594 
        cnpj: 37753978000134
    -Obrigatório

    CEP: 
    -CEP do endereço do cliente.
    -Exemplo: 31910720
    -Obrigatório
    
    """

    if not nome:
        return {"success": False, "message": "Nome é obrigatório."};
    

    if not telefone:
        return {"success": False, "message": "Telefone   é obrigatório."};

    if not email:
        return {"success": False, "message": "Email é obrigatório."};

    if not cpf_cnpj:
        return {"success": False, "message": "CPF ou CNPJ é obrigatório."};

    if not cep:
        return {"success": False, "message": "CEP é obrigatório."};

    if not numero:
        return {"success": False, "message": "Número é obrigatório."};


    telefone_id = await mysql.get_id(telefone, "CLIENTE", "TELEFONE", negocio_idx)
    logger.info(f"telefone_id: {telefone_id}")
    if telefone_id > 0:
        return {"success": False, "message": "Telefone já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    email_id = await mysql.get_id(email, "CLIENTE", "EMAIL", negocio_idx)
    logger.info(f"email_id: {email_id}")
    if email_id > 0:
        return {"success": False, "message": "Email já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    cpf_cnpj_ok = await cpf_cnpj_valido(cpf_cnpj)
    logger.info(f"cpf_cnpj_ok: {cpf_cnpj_ok}")
    if not cpf_cnpj_ok:
        return {"success": False, "message": "CPF ou CNPJ inválido."}
    cpf_cnpj_id = await mysql.get_id(cpf_cnpj, "CLIENTE", "CPF_CNPJ", negocio_idx)
    logger.info(f"cpf_cnpj_id: {cpf_cnpj_id}")
    if cpf_cnpj_id > 0:
        return {"success": False, "message": "CPF ou CNPJ já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}

    


    cpf_cnpj_id = await mysql.get_id(cpf_cnpj, "CLIENTE", "CPF_CNPJ", negocio_idx)
    logger.info(f"cpf_cnpj_id: {cpf_cnpj_id}")
    if cpf_cnpj_id > 0:
        return {"success": False, "message": "CPF ou CNPJ já cadastrado. Possivelmente o cliente já existe ou houve um erro de cadastro que deve ser corrigido."}



    #print("vou carregar o enereço do cep " + cep)
    #logradouro,bairro,cidade,uf = ""
    endereco = await cep_data(cep)
    #print("endereco", endereco)

    if not endereco:
        return {"success": False, "message": "CEP inválido."}

    logradouro = endereco.get("logradouro", "")
    bairro = endereco.get("bairro", "")
    cidade = endereco.get("localidade", "")
    uf = endereco.get("uf", "")
    #print("logradouro", logradouro)
    #print("bairro", bairro)
    #print("cidade", cidade)
    #print("uf", uf)


    cliente = {
        "IDX": generate_unique_id(),
        "NEGOCIO_IDX": negocio_idx,
        "NOME": nome,
        "TELEFONE": telefone,
        "EMAIL": email,
        "NEGOCIO_IDX": negocio_idx,
        "CPF_CNPJ": cpf_cnpj,
        "CEP": cep,
        "LOGRADOURO": logradouro,
        "BAIRRO": bairro,
        "CIDADE": cidade,
        "UF": uf,
        "NUMERO": numero,
        "COMPLEMENTO": complemento
    }
    logger.info(f"cliente: {cliente}")

    result = await mysql.add("CLIENTE", cliente)

    
    return {"success": True,  "message": "Cliente adicionado com sucesso."}

@function_tool
async def ordem_servico_abrir(cliente: ClienteData, # Alterado de dict
                             veiculo_categoria: str, # Se for um tipo específico, defina-o
                             veiculo_nome: str, 
                             veiculo_marca: str, 
                             veiculo_ano: str, 
                             veiculo_serie: str,
                             veiculo_cor: str,
                             veiculo_placa: str,
                             veiculo_km: str,
                             qtde_parcelas_pagamento: int,
                             servicos: List[ServicoItem], # Alterado de list[dict]
                             produtos: List[ProdutoItem], # Alterado de list[dict]
                             formas_pagamento: List[FormaPagamentoItem], # Alterado de list[dict]
                             total: float,
                             observacao: str,
                             status_id: int,
                             prioridade_id: int,
                             negocio_idx: str,
                             negocio: NegocioData, # Alterado de dict
                             plataforma_url: str):

    """
    Não solicite todas as informações de uma vez. Solicite uma por vez.
    Abre uma nova ordem de serviço;
    
    CLIENTE:
    -Objeto ClienteData com os dados completos do cliente
    -CAMPOS OBRIGATÓRIOS DO CLIENTE:
        • NOME: string - Nome completo do cliente (obrigatório)
        • EMAIL: string - Email do cliente (obrigatório)
        • TELEFONE: string - Telefone do cliente (obrigatório)
    -CAMPOS OPCIONAIS DO CLIENTE:
        • CPF_CNPJ, CEP, LOGRADOURO, BAIRRO, CIDADE, UF, NUMERO, COMPLEMENTO
    -Obrigatório
    -Solicite que o usuário informe um destes dados: nome do cliente, cpf, cnpj, email ou telefone. Use a informação fornecida para localizar o cliente, e em seguida passe-o para a função com TODOS os campos obrigatórios preenchidos. Caso o cliente não seja encontrado, informe ao usuario que o cliente não foi encontrado e solicite que informe outra informação do cliente para uma nova busca.
    
    VEICULO_CATEGORIA:
    -categoria do veiculo. 
    -Exemplos: Bicicleta, Moto, Carro, Caminhão, etc.
    -default = "Bicicleta".
    -Opcional
    VEICULO_NOME:
    -nome ou modelo do  veiculo.
    -Obrigatório
    -Exemplo: Sense21, Gol, etc.
    VEICULO_MARCA:
    -marca do veiculo.
    -default = "".
    -Opcional
    -Exemplo: Sense, Honda, Yamaha, Shimano, etc.
    VEICULO_ANO:
    -ano do veiculo.
    -Opcional
    VEICULO_SERIE:
    -serie do veiculo.
    -default = "".
    -Opcional
    VEICULO_COR:
    -cor do veiculo.
    -default = "".
    -Opcional
    -Exemplo: Vermelho, Preto, etc.
    VEICULO_PLACA:
    -placa do veiculo. Solicitar somente se o veiculo for um veiculo.
    -default = "".
    -Opcional
    -Exemplo: ABC1234
    VEICULO_KM:
    -quilometragem do veiculo.
    -default = "".
    -Opcional

    
    SERVICOS:
    -Array de dicionarios com os serviços a serem feitos. cada serviço tera os seguintes dados:
    -id
    -nome
    -valor
    -quantidade
    -Exemplo:
    [
        {"id": 1, nome: "revisão geral","valor": 100, "quantidade": 1},
    ]
    -Opcional (pode não ter serviços)
    -Solicite o nome ou código do serviço e faça uma busca na tabela SERVICO para encontrar o ID do serviço. Caso não encontre, informe ao usuario que o serviço não foi encontrado e solicite que informe o nome ou código do serviço ou pergunte se ele deseja ver os serviços disponíveis. Caso ele queira, carregue e liste os serviços do negócio dele.
    Após enconrar o serviço, solicite ao usuário a quantidade, caso ele ainda não tenha informado. 
    Encontrado o serviço, adicine a lista de serviços com o id, valor e quantidade.
    Pergunte se ele deseja adicionar mais serviços.
    IMPORTANTE: Ao fazer buscas por serviços, verifque  se o texto informado é encontrado total ou parcialmente na coluna NOME ou CODIGO do serviço. Em geral os serviços também possuem códigos, e o usuario pode optar em informar o codigo em vez do nome.

    PRODUTOS:
    -Array de dicionarios com os produtos a serem utilizados. cada produto tera os seguintes dados:
    -id
    -nome
    -valor
    -quantidade
    -Exemplo:
    [
        {"id": 2, nome: "Pneu aro 26","valor": 80, "quantidade": 2},
    ]
    -Opcional (pode não ter produtos)
    -Solicite o nome ou código do produto e faça uma busca na tabela PRODUTO para encontrar o ID do produto. Caso não encontre, informe ao usuario que o produto não foi encontrado e solicite que informe o nome ou código do produto ou pergunte se ele deseja ver os produtos disponíveis. Caso ele queira, carregue e liste os produtos do negócio dele.
    Após enconrar o produto, solicite ao usuário a quantidade, caso ele ainda não tenha informado. 
    Encontrado o produto, adicine a lista de produtos com o id, valor e quantidade.
    Pergunte se ele deseja adicionar mais produtos.
    IMPORTANTE: Ao fazer buscas por produtos, verifque  se o texto informado é encontrado total ou parcialmente na coluna NOME ou CODIGO do produto. Em geral os produtos também possuem códigos, e o usuario pode optar em informar o codigo em vez do nome.

    TOTAL:
    -Total da ordem de serviço.
    -Obrigatório
    -Exemplo: 100, 200, 300
    -Neste momento calcule o total a ser pago pelos serviços (soma de (valor * quantidade) de todos os serviços) + produtos (soma de (valor * quantidade) de todos os produtos) e informe ao usuario.  


    QTDE_PARCELAS_PAGAMENTO:
    -Quantidade de parcelas de pagamento.
    -inteiro(default 1)
    -Obrigatório
    -Exemplo: 1, 2, 3, etc.
    -Total a ser pago pelos serviços + produtos (soma de ((valor * quantidade) de todos os serviços) + ((valor * quantidade) de todos os produtos)).  
    

    FORMAS_PAGAMENTO:
    -Array de dicionarios com as formas de pagamento. Cada forma de pagamento tera os seguintes dados:
    -nr .  Número sequencial da parcela. O número máximo sera o total de parcelas.
    -id
    -valor 
    -vencimento . Data no formato YYYY-MM-DD . Mas caso for necessário exibir ao usuario, exiba no formato DD/MM/YYYY.
    pago: e se esta pago ou não. Caso esteja pago, o valor será 1, caso não esteja pago, o valor será 0.
    -Exemplo: 
    {[
        {{"nr": 1, "id": 1, "valor": 100, "vencimento": "2024-01-01", "pago": 1}},
        {{"nr": 2, "id": 2, "valor": 100, "vencimento": "2024-01-01", "pago": 0}}
    ]}

            Estes são os ids das formas de pagamento:
            1 - Pix
            2 - Cartão de crédito
            3 - Boleto
            4 - Cheque
            5 - Transferência

    OBSERVACAO:
    -Observação da ordem de serviço.
    -default = ""
    -Opcional

    STATUS_ID:
    -ID do status da ordem de serviço.
    -Inteiro (default = 0)
    -Opcional
    -Deve ser solicitado APÓS as formas de pagamento
    -Apresentar as opções ao usuário:
        0 - 🟡 A Iniciar (padrão)
        1 - 🔵 Em Andamento  
        2 - 🟠 Aguardando Peças
        3 - 🟣 Aguardando Cliente
        4 - 🟢 Concluída
        5 - 🔴 Cancelada
        6 - ✅ Entregue
    -Usuário pode informar o número (ID) ou a descrição
    -Exemplo: "1" ou "Em Andamento"

    PRIORIDADE_ID:
    -ID da prioridade da ordem de serviço.
    -Inteiro (default = 0)
    -Opcional
    -Deve ser solicitado APÓS as formas de pagamento
    -Apresentar as opções ao usuário:
        0 - 📄 Normal (padrão)
        1 - ⬇️ Baixa
        2 - ➡️ Média
        3 - ⬆️ Alta
        4 - 🚨 Urgente
        5 - ⚡ Crítica
    -Usuário pode informar o número (ID) ou a descrição
    -Exemplo: "3" ou "Alta"

    PLATAFORMA_URL:
    -URL da plataforma para geração do link do PDF
    -Obrigatório
    -Exemplo: http://localhost/gptalk ou https://app.oficinatech.gptalk.com.br
            

    OBSERVAÇÕES IMPORTANTES:
    1-Após o usuário identificar a categoria do veiculo, passe a se referir ao veiculo pela sua categoria.
    Por exemplo, caso ele informe que a categoria é bicicleta, passe a se referir ao veiculo como bicicleta. Exemplos: qual a marca da bicileta? qual a cor da bicileta?
    2-Se a categoria for bicicleta, não solicitar placa nem kilometragem
    3-A ordem de serviço pode ter apenas serviços, apenas produtos, ou ambos. Sempre pergunte se o usuário deseja adicionar serviços e produtos.
    4-APÓS confirmar as formas de pagamento, solicite o STATUS e a PRIORIDADE da ordem de serviço, apresentando as opções numeradas para o usuário escolher.

    TEMPLATE DE RESPOSTA FINAL:
    Após gerar a ordem de serviço com sucesso, você DEVE usar EXATAMENTE este template de resposta:

    "✅ **Ordem de serviço gerada com sucesso!**

    📋 **Dados da OS:**
    - Cliente: [NOME_DO_CLIENTE]
    - Veículo: [VEICULO_CATEGORIA] [VEICULO_MARCA] [VEICULO_NOME]
    - Total: R$ [VALOR_TOTAL]

    📄 **Acesse o PDF da ordem de serviço:**

    <div style='margin: 15px 0; text-align: center;'>
        <button onclick='window.open(\"{PDF_LINK}\", \"_blank\");' 
                style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer; font-size: 14px;'>
            👁️ VER PDF
        </button>
        <button onclick='navigator.clipboard.writeText(\"{PDF_LINK}\"); alert(\"Link copiado para a área de transferência!\");' 
                style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;'>
            📋 COPIAR LINK
        </button>
    </div>"

    IMPORTANTE: Substitua {PDF_LINK} pelo link real gerado pela função.


    """
    logger.info(f"===== ordem_servico_abrir() =====")
    logger.info(f"cliente: {cliente}")
    logger.info(f"veiculo_categoria: {veiculo_categoria}")
    logger.info(f"veiculo_nome: {veiculo_nome}")
    logger.info(f"veiculo_marca: {veiculo_marca}")
    logger.info(f"veiculo_ano: {veiculo_ano}")
    logger.info(f"veiculo_serie: {veiculo_serie}")
    logger.info(f"veiculo_cor: {veiculo_cor}")
    logger.info(f"veiculo_placa: {veiculo_placa}")
    logger.info(f"veiculo_km: {veiculo_km}")
    logger.info(f"qtde_parcelas_pagamento: {qtde_parcelas_pagamento}")
    logger.info(f"servicos: {servicos}")
    logger.info(f"produtos: {produtos}")
    logger.info(f"formas_pagamento: {formas_pagamento}")
    logger.info(f"total: {total}")
    logger.info(f"observacao: {observacao}")
    logger.info(f"status_id: {status_id}")
    logger.info(f"prioridade_id: {prioridade_id}")
    logger.info(f"negocio: {negocio}")

    fuso_brasilia = pytz.timezone('America/Sao_Paulo')
    entrada = datetime.now(fuso_brasilia).strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"entrada: {entrada}")

    # Calcular totais de serviços e produtos
    total_servicos = sum(servico.valor * servico.quantidade for servico in servicos) if servicos else 0
    total_produtos = sum(produto.valor * produto.quantidade for produto in produtos) if produtos else 0
    total_geral = total_servicos + total_produtos

    # Instanciar o AgentWorkOrder para obter o próximo número
    work_order_agent = AgentWorkOrder()
    try:
        numero_ordem = await work_order_agent.get_proximo_numero_ordem(negocio_idx)
        logger.info(f"🔢 Número da ordem gerado: {numero_ordem}")
    except Exception as e:
        logger.error(f"❌ Erro ao gerar número da ordem: {e}")
        raise Exception(f"Erro ao gerar numeração da ordem de serviço: {str(e)}")



    ordem_servico = {
        "NUMERO": numero_ordem,  # ✅ CORREÇÃO: Adicionar o número gerado automaticamente
        "IDX": generate_unique_id(),
        "CLIENTE_IDX": cliente.IDX,
        "ITEM_NOME": veiculo_nome,
        "ITEM_CATEGORIA": veiculo_categoria,
        "ITEM_MARCA": veiculo_marca,
        "ITEM_ANO": veiculo_ano,
        "ITEM_COR": veiculo_cor,
        "ITEM_PLACA": veiculo_placa,
        "ITEM_KM": veiculo_km,
        "ITEM_SERIE": veiculo_serie,
        "NEGOCIO_IDX": negocio_idx ,
        "QTDE_PARCELAS_PAGAMENTO": qtde_parcelas_pagamento,
        "TOTAL_SERVICOS": total_servicos,
        "TOTAL_PRODUTOS": total_produtos,
        "TOTAL": total_geral,
        "ENTRADA": entrada,
        "SAIDA": None,  # Mudar de "" para None para evitar warning MySQL
        "OBSERVACAO": observacao,
        "STATUS": status_id,  # ✅ NOVO: Status da ordem de serviço
        "PRIORIDADE": prioridade_id,  # ✅ NOVO: Prioridade da ordem de serviço

    }

    logger.info(f"ordem_servico: {ordem_servico}")

    # ========== INÍCIO DO TRATAMENTO DE EXCEÇÕES PARA GRAVAÇÃO NO BD ==========
    try:
        logger.info("🔄 Iniciando gravação da ordem de serviço no banco de dados...")
        
        # Inserir ordem de serviço principal
        ordem_servico_id = await mysql.add("NEGOCIO_ORDEM_SERVICO", ordem_servico)
        ordem_servico["ID"]= ordem_servico_id
        logger.info(f"✅ Ordem de serviço principal gravada com sucesso - ID: {ordem_servico_id}")
        logger.info(f"formas_pagamento: {formas_pagamento}")

        # Processar formas de pagamento
        formasPag = []        
        for i, forma_pagamento in enumerate(formas_pagamento):
            try:
                logger.info(f"🔄 Processando forma de pagamento {i+1} de {len(formas_pagamento)}...")
                
                # Mapear ID da forma de pagamento para nome
                formas_pagamento_nomes = {
                    1: "Pix",
                    2: "Cartão de crédito", 
                    3: "Boleto",
                    4: "Cheque",
                    5: "Transferência"
                }
                
                fp = {
                    "ORDEM_SERVICO_ID": ordem_servico_id,
                    "VALOR": forma_pagamento.valor,          # Manter VALOR em maiúsculo (correto para BD)
                    "vencimento": forma_pagamento.vencimento,  # PHP espera minúsculo
                    "pago": forma_pagamento.pago,              # PHP espera minúsculo
                    "PARCELA_NR": forma_pagamento.nr,
                    "FORMA_ID": forma_pagamento.id,
                    "nome": formas_pagamento_nomes.get(forma_pagamento.id, "Não especificado"),  # PHP espera minúsculo
                    "NEGOCIO_IDX": negocio_idx
                }
                
                logger.info(f"fp: {fp}")
                result = await mysql.add("VENDA_FORMA_PAGAMENTO", fp)
                logger.info(f"✅ Forma de pagamento gravada - ID: {result}")
                formasPag.append(fp)
                
            except Exception as e:
                logger.error(f"❌ ERRO ao gravar forma de pagamento {i+1}: {str(e)}")
                logger.error(f"❌ Dados da forma de pagamento que causou erro: {forma_pagamento}")
                raise Exception(f"Erro ao gravar forma de pagamento {i+1}: {str(e)}")
        
        # Processar serviços se houver
        if servicos:
            logger.info(f"🔄 Processando {len(servicos)} serviço(s)...")
            for i, servico in enumerate(servicos):
                try:
                    logger.info(f"🔄 Processando serviço {i+1} de {len(servicos)}...")
                    
                    svc = {
                        "ORDEM_SERVICO_ID": ordem_servico_id,
                        "SERVICO_ID": servico.id,
                        "PRECO": servico.valor,
                        "QTDE": servico.quantidade,
                        "TOTAL": servico.valor * servico.quantidade,
                        "NEGOCIO_IDX": negocio_idx
                    }
                    logger.info(f"svc: {svc}")
                    venda_servico_id = await mysql.add("VENDA_SERVICO", svc)
                    logger.info(f"✅ Serviço gravado - ID: {venda_servico_id}")
                    
                except Exception as e:
                    logger.error(f"❌ ERRO ao gravar serviço {i+1}: {str(e)}")
                    logger.error(f"❌ Dados do serviço que causou erro: {servico}")
                    raise Exception(f"Erro ao gravar serviço '{servico.nome}': {str(e)}")

        # Processar produtos se houver
        if produtos:
            logger.info(f"🔄 Processando {len(produtos)} produto(s)...")
            for i, produto in enumerate(produtos):
                try:
                    logger.info(f"🔄 Processando produto {i+1} de {len(produtos)}...")
                    
                    prd = {
                        "ORDEM_SERVICO_ID": ordem_servico_id,
                        "PRODUTO_ID": produto.id,
                        "PRECO": produto.valor,
                        "QTDE": produto.quantidade,
                        "TOTAL": produto.valor * produto.quantidade,
                        "NEGOCIO_IDX": negocio_idx
                    }
                    logger.info(f"prd: {prd}")
                    venda_produto_id = await mysql.add("VENDA_PRODUTO", prd)
                    logger.info(f"✅ Produto gravado - ID: {venda_produto_id}")
                    
                except Exception as e:
                    logger.error(f"❌ ERRO ao gravar produto {i+1}: {str(e)}")
                    logger.error(f"❌ Dados do produto que causou erro: {produto}")
                    raise Exception(f"Erro ao gravar produto '{produto.nome}': {str(e)}")

        logger.info("✅ Todas as gravações no banco de dados foram concluídas com sucesso!")

    except Exception as e:
        logger.error("❌ ERRO CRÍTICO durante a gravação da ordem de serviço!")
        logger.error(f"❌ Tipo do erro: {type(e).__name__}")
        logger.error(f"❌ Mensagem do erro: {str(e)}")
        logger.error(f"❌ Dados da ordem de serviço: {ordem_servico}")
        logger.error(f"❌ Cliente: {cliente}")
        logger.error(f"❌ Serviços: {servicos}")
        logger.error(f"❌ Produtos: {produtos}")
        logger.error(f"❌ Formas de pagamento: {formas_pagamento}")
        
        # Tentar capturar informações adicionais do erro
        import traceback
        logger.error(f"❌ Stack trace completo: {traceback.format_exc()}")
        
        raise Exception(f"Falha ao gravar ordem de serviço no banco de dados: {str(e)}")
    
    # ========== FIM DO TRATAMENTO DE EXCEÇÕES PARA GRAVAÇÃO NO BD ==========

    # Linha 61-63
    logger.info("vou gerar o pdf da ordem de serviço #####")
    
    # Usar a plataforma_url para gerar a URL correta do PHP
    if isinstance(plataforma_url, tuple):
        plataforma_url_clean = plataforma_url[0].strip(",' ")
    else:
        plataforma_url_clean = str(plataforma_url).strip(",' ")
    
    # Gerar URL do PHP baseada na plataforma atual
    url = f"{plataforma_url_clean}/app_phj/app/oficinatech/modulo/ordem_servico/ordem_servico_pdf.php"
    
    logger.info(f"servicos: {servicos}")
    logger.info(f"produtos: {produtos}")
    logger.info(f"formas_pagamento: {formas_pagamento}")
    logger.info(f"ordem_servico: {ordem_servico}")

    # Preparar dados do negócio para o PHP
    negocio_para_pdf = negocio.dict() if hasattr(negocio, 'dict') else negocio
    logger.info(f"negocio_para_pdf: {negocio_para_pdf}")
    # Adicionar LOGRAUDOURO para compatibilidade com PHP que ainda espera este campo
    if 'LOGRADOURO' in negocio_para_pdf:
        negocio_para_pdf['LOGRAUDOURO'] = negocio_para_pdf['LOGRADOURO']

    # Preparar dados dos serviços com campo 'valor' para o PHP
    servicos_para_pdf = []
    for servico in servicos:
        servico_dict = servico.dict() if hasattr(servico, 'dict') else servico
        servicos_para_pdf.append({
            "id": servico_dict.get("id", servico.id),
            "nome": servico_dict.get("nome", servico.nome),
            "valor": servico_dict.get("valor", servico.valor),  # PHP espera 'valor'
            "quantidade": servico_dict.get("quantidade", servico.quantidade),
            "total": servico.valor * servico.quantidade
        })

    # Preparar dados dos produtos com campo 'valor' para o PHP
    produtos_para_pdf = []
    for produto in produtos:
        produto_dict = produto.dict() if hasattr(produto, 'dict') else produto
        produtos_para_pdf.append({
            "id": produto_dict.get("id", produto.id),
            "nome": produto_dict.get("nome", produto.nome),
            "valor": produto_dict.get("valor", produto.valor),  # PHP espera 'valor'
            "quantidade": produto_dict.get("quantidade", produto.quantidade),
            "total": produto.valor * produto.quantidade
        })

    payload = {
        "ordem_servico": ordem_servico,
        "cliente": cliente.dict() if hasattr(cliente, 'dict') else cliente,
        "servicos": servicos_para_pdf,
        "produtos": produtos_para_pdf,
        "formas_pagamento": [
            {
                "nr": fp.get("PARCELA_NR"),
                "id": fp.get("FORMA_ID"), 
                "valor": fp.get("VALOR"),  # Converter para minúsculo para o PHP
                "vencimento": fp.get("vencimento"),
                "pago": fp.get("pago"),
                "nome": fp.get("nome")
            }
            for fp in formasPag
        ],
        "negocio": negocio_para_pdf
    }

    headers = {'Content-Type': 'application/json'}

    try:
        logger.info(f"Tentando gerar PDF com URL: {url}")
        response = requests.post(url, data=json.dumps(payload), headers=headers, timeout=30)
        
        logger.info(f"Status da resposta HTTP: {response.status_code}")
        logger.info(f"Conteúdo da resposta: {response.text[:500]}...")  # Log primeiros 500 chars
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"PDF gerado com sucesso: {data}")
                if not data.get("success", False):
                    logger.error(f"PHP retornou sucesso=false: {data}")
                    raise Exception(f"PHP retornou erro: {data.get('message', 'Erro desconhecido')}")
            except ValueError as e:
                logger.error(f"Erro ao decodificar JSON da resposta: {e}")
                logger.error(f"Resposta completa: {response.text}")
                raise Exception(f"Erro ao processar resposta do servidor PDF: {e}")
        else:
            logger.error(f"Erro HTTP {response.status_code}: {response.text}")
            raise Exception(f"Erro HTTP {response.status_code} ao gerar PDF")
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Erro na requisição HTTP para gerar PDF: {e}")
        raise Exception(f"Erro de conexão com servidor PDF: {e}")
    except Exception as e:
        logger.error(f"Erro inesperado ao gerar PDF: {e}")
        raise Exception(f"Erro ao gerar PDF: {str(e)}")




    #retornar o link do pdf :
    #plataforma_url/negocios/negocio.IDX/pdf/{pdf_nome}
    
    # Gerar o link completo do PDF
    pdf_nome = data.get("pdf_nome", "")
    
    if not pdf_nome:
        raise Exception("Nome do PDF não retornado pelo servidor")
    
    pdf_link = f"{plataforma_url_clean}/negocios/{negocio_idx}/pdf/{pdf_nome}"
    
    # Mapear IDs para descrições para exibição
    status_descricoes = {
        0: "🟡 A Iniciar",
        1: "🔵 Em Andamento",
        2: "🟠 Aguardando Peças", 
        3: "🟣 Aguardando Cliente",
        4: "🟢 Concluída",
        5: "🔴 Cancelada",
        6: "✅ Entregue"
    }
    
    prioridade_descricoes = {
        0: "📄 Normal",
        1: "⬇️ Baixa",
        2: "➡️ Média",
        3: "⬆️ Alta",
        4: "🚨 Urgente",
        5: "⚡ Crítica"
    }
    
    status_texto = status_descricoes.get(status_id, f"Status {status_id}")
    prioridade_texto = prioridade_descricoes.get(prioridade_id, f"Prioridade {prioridade_id}")

    # Criar mensagem com botões
    mensagem_sucesso = f"""
✅ **Ordem de serviço gerada com sucesso!**

📋 **Dados da OS:**
- Cliente: {cliente.NOME}
- Veículo: {veiculo_categoria} {veiculo_marca} {veiculo_nome}
- Status: {status_texto}
- Prioridade: {prioridade_texto}
- Serviços: R$ {total_servicos:.2f}
- Produtos: R$ {total_produtos:.2f}
- **Total: R$ {total_geral:.2f}**

📄 **Acesse o PDF da ordem de serviço:**

<div style='margin: 15px 0; text-align: center;'>
    <button onclick='window.open(\"{pdf_link}\", \"_blank\");' 
            style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer; font-size: 14px;'>
            👁️ VER PDF
    </button>
    <button onclick='navigator.clipboard.writeText(\"{pdf_link}\"); alert(\"Link copiado para a área de transferência!\");' 
            style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;'>
            📋 COPIAR LINK
        </button>
    </div>"
"""
    
    return {
        "success": True,
        "message": mensagem_sucesso,
        "pdf_nome": pdf_nome,
        "pdf_link": pdf_link
    }

@function_tool
async def veiculo_categoria_adicionar(categoria: str, negocio_idx: str):
    logger.info(f"===== veiculo_categoria_adicionar() =====")
    """
    Adiciona uma nova categoria de veiculo no banco de dados na tabela VEICULO_CATEGORIA. Exemplos de categorias: Bicicleta, Moto, Carro, Caminhão, etc.
    """
    categoria = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": categoria
    }
    logger.info(f"categoria: {categoria}")
    result = await mysql.add("VEICULO_CATEGORIA",categoria)
    logger.info(f"result: {result}")


@function_tool
async def veiculo_tipo_adicionar(tipo: str, negocio_idx: str):
    logger.info(f"===== veiculo_tipo_adicionar() =====")
    """
    Adiciona um novo tipo de veiculo no banco de dados na tabela VEICULO_TIPO.
    """
    tipo = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": tipo
    }
    logger.info(f"tipo: {tipo}")
    result = await mysql.add("VEICULO_TIPO",tipo)
    logger.info(f"result: {result}")




@function_tool
async def produto_adicionar(nome: str, codigo: str, preco: float, estoque: float, negocio_idx: str):
    logger.info(f"===== produto_adicionar() =====")
    """
    Adiciona um novo produto no banco de dados na tabela PRODUTO. 

    NOME:
    -Nome do produtos. Exemplos: pneu, freio, manopla,etc.
    -Obrigatório

    CODIGO:
    -código do produto. Exemplos: pn (pneu), cr(corrente), gd (guidom), etc.
    -Obrigatório
    

    PRECO:
    -Preço do produto.
    -Obrigatório
    -  decimal com 2 casas decimais. Exemplos: 100.00, 200.00, 300.00, etc.

    ESTOQUE:
    -estoque inicial do produto.
    -Opcional. Se não for informado, será 0.
    -default: 0

    NEGOCIO:
    -Dicionario com os dados do negocio.
    -Obrigatório
    
    """

    produto = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": nome,
        "PRECO": preco,
        "CODIGO": codigo,
        "ESTOQUE": estoque
    }
    logger.info(f"produto: {produto}")
    result = await mysql.add("PRODUTO", produto)
    logger.info(f"result: {result}")




@function_tool
async def servico_adicionar(nome: str, codigo: str, preco: str, negocio_idx: str):
    logger.info(f"===== produto_marca_adicionar() =====")
    """
    Adiciona um novo servico no banco de dados na tabela SERVICO. 

    NOME:
    -Nome do serviço. Exemplos: Manutenção, Revisão, Troca de óleo, etc.
    -Obrigatório

    CODIGO:
    -código do serviço. Exemplos: tdp (troca de pneu), lvc(lavagem completa), rvg (revisão geral), etc.
    -Obrigatório
    

    PRECO:
    -Preço do serviço.
    -Obrigatório
    -  decimal com 2 casas decimais. Exemplos: 100.00, 200.00, 300.00, etc.

    NEGOCIO:
    -Dicionario com os dados do negocio.
    -Obrigatório
    
    """

    if not nome:
        return {"success": False, "message": "Nome é obrigatório."};

    if not preco:
        return {"success": False, "message": "Preço é obrigatório."};

    #se tiver "," no preço, substituir por "."  
    if "," in preco:
        preco = preco.replace(",", ".")

    #se preço nao for um numero float valido
    try:
        preco = float(preco)
    except ValueError:
        return {"success": False, "message": "Preço inválido."}

    
    servico = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": nome,
        "PRECO": preco,
        "CODIGO": codigo
    }
    logger.info(f"servico: {servico}")
    result = await mysql.add("SERVICO", servico)
    logger.info(f"result: {result}")


@function_tool
async def produto_marca_adicionar(marca: str, negocio_idx: str):
    logger.info(f"===== produto_marca_adicionar() =====")
    """
    Adiciona uma nova marca de veiculo no banco de dados na tabela PRODUTO_MARCA.
    """
    marca = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": marca
    }
    logger.info(f"marca: {marca}")
    result = await mysql.add("PRODUTO_MARCA", marca)
    logger.info(f"result: {result}")

@function_tool
async def veiculo_cor_adicionar(cor: str, negocio_idx: str):
    logger.info(f"===== veiculo_cor_adicionar() =====")
    """
    Adiciona uma nova cor de veiculo no banco de dados na tabela VEICULO_COR.
    """
    cor = {
        "NEGOCIO_IDX": negocio_idx,
        "NOME": cor
    }
    logger.info(f"cor: {cor}")
    result = await mysql.add("VEICULO_COR", cor)
    logger.info(f"result: {result}")






    return {"success": True, "message": "Marca adicionada com sucesso."}

@function_tool
async def produto_modelo_adicionar(
    
    negocio_idx: str, 
    categoria: str,
    tipo: str,
    modelo: str,
    codigo: str,
    marca: str,
    cor: str,
    ano: int,
    foto: str
):

    logger.info(f"===== produto_modelo_adicionar() =====")

    
    f"""
        Adiciona um novo modelo de produto no banco de dados na tabela PRODUTO_MODELO.
        parametros:
        NEGOCIO_IDX: 
            -string com 10 digitos que idnetificam a empresa
            -Obrigatório

        CATEGORIA: 
            -categoria de veiculo. Exemplos : Bicicleta, Moto, Carro, Caminhão, etc.Categoria é algo mais genérico. São as 'espécies' de veiculos.
            -Obrigatório
        TIPO: 
            -tipo do veiculo. Exemplos   : Passeio, Esporte, Mountain Bike, Urbana, etc.
            -Obrigatório

        MODELO: 
            - Modelo ou nome do veiculo.Exemplos: biciletas (Sense, Caloi Cross, BMX, etc), motos (Honda Hornet 2.0, Yamaha Fazer 250, etc), carros (Gol, Palio, etc).
            -string com até 50 caracteres que identifica o modelo ou  nome do veiculo.
            -Obrigatório
        CODIGO: 
            -string com até 15 caracteres que identifica o modelo ou  nome do veiculo.
            -obrigatório
            -Exemplos: Sense21, Gol, etc.


        MARCA: 
            -Nome da marca do veiculo.  Exemplos: Caloi, Honda, Yamaha, Shimano, etc.
            -Obrigatório

        COR: 
            -Cor do veiculo. Exemplos: Branco, Preto, Vermelho, Azul, etc.
            -Opcional

        ANO: 
            -int com o ano do veiculo .Exemplos: 2020, 2021, 2022, etc.
            -opcional. se não for informado, ficará em branco ('')

        FOTO:
            -link da foto do veiculo. 
            -opcional
            
        TODAS as informações acima devem ser solicitadas ao usuário.

        Não solicitar nenhuma informação adicional além dos parametros acima.
        Não solicite placa, chassi ou número de série.


      EXEMPLOS:
       Exemplo 1: 
       categoria: Bicicleta
       tipo: Urbana
       modelo: Sense21
       marca: Sense
       cor: Azul
       ano: 2025
       foto: https://www.caloi.com.br/wp-content/uploads/2020/05/13142325.jpg

        Exemplo 2: 
        categoria: Carro
        tipo: Passeio
        modelo: Gol
        marca: VW
        cor: Vermelho
        ano: 2020
        foto: https://www.vw.com.br/content/dam/vw/br/brand/veiculos/gol/gol-2020/2020/01/10/1000x500/gol-2020-1000x500-01.jpg

        VALIDAÇÃO DE CAMPOS OBRIGATÓRIOS:
            - Se algum ds campos obrigatorios não forem informados retornar a seginte resposta:

            {{
        "status_code": 422,
        "message": "price: Campos obrigatórios não informados",
        "missing_fields": [] #listar os campos obrigatórios não informados
        }}


        Se o usuário explicitamente informar que não deseja enviar a foto , o valor de foto sera a string 'False'

       """
    ###VERIFICA A EXISTENCIA DE ITENS EM SUAS RESPECTIVAS TABELAS
    # Verificar se a marca já existe

    categoria_id = await mysql.get_first_id_by_search(categoria, "VEICULO_CATEGORIA", "NOME", negocio_idx)
    logger.info(f"categoria_id: {categoria_id}")
    if categoria_id == 0:
        return {"success": False, "message": "Categoria não encontrada. Deseja cadastrar a categoria?"}

    tipo_id = await mysql.get_first_id_by_search(tipo, "VEICULO_TIPO", "NOME", negocio_idx)
    logger.info(f"tipo_id: {tipo_id}")
    if tipo_id == 0:
        return {"success": False, "message": "Tipo não encontrado. Deseja cadastrar o tipo?"}

    marca_id = await mysql.get_first_id_by_search(marca, "PRODUTO_MARCA", "NOME", negocio_idx)
    logger.info(f"marca_id: {marca_id}")
    if marca_id == 0:
        return {"success": False, "message": "Marca não encontrada. Deseja cadastrar a marca?"}

    cor_id = await mysql.get_first_id_by_search(cor, "VEICULO_COR", "NOME", negocio_idx)
    logger.info(f"cor_id: {cor_id}")
    if cor_id == 0:
        return {"success": False, "message": "Cor não encontrada. Deseja cadastrar a cor?"}

    foto_link = foto
    logger.info(f"foto_link: {foto_link}")
    if not foto_link:
        return {"success": False, "message": "Foto não informada. Deseja adicionar   a foto agora? Caso sim, envie o link ou faça o carregamento da foto."}
    if foto_link == "False":
        foto_link = ""



    produto_modelo = {
        "IDX": generate_unique_id(),
        "NEGOCIO_IDX": negocio_idx,
        "NOME": modelo,
        "COR_ID": cor_id,
        "CATEGORIA_ID": categoria_id,
        "TIPO_ID": tipo_id,
        "MARCA_ID": marca_id,
        "ANO": ano,
        "FOTO_LINK": foto_link
    }

    result = await mysql.add("PRODUTO_MODELO", produto_modelo)

    
    return {"success": True, "message": "Veiculo adicionado com sucesso."}

@function_tool
async def consulta_atualiza_dados(query: str,negocio_idx: str):

  """
    Executa consultas e atualizações no banco de dados.
    
    🚨 REGRA CRÍTICA DE SEGURANÇA - NUNCA VIOLE: 🚨
    TODA QUERY DEVE TER O FILTRO "AND NEGOCIO_IDX = '{negocio_idx}'" NO FINAL!
    
    FORMATO OBRIGATÓRIO DE FILTROS:
    - Se apenas um filtro: WHERE condicao AND NEGOCIO_IDX = '{negocio_idx}'  
    - Se múltiplos filtros: WHERE (filtro1 OR filtro2 OR filtro3) AND NEGOCIO_IDX = '{negocio_idx}'
    
    EXEMPLOS CORRETOS:
    ✅ SELECT * FROM CLIENTE WHERE NOME LIKE '%João%' AND NEGOCIO_IDX = '4015743441'
    ✅ SELECT * FROM CLIENTE WHERE (NOME LIKE '%João%' OR TELEFONE LIKE '%123%') AND NEGOCIO_IDX = '4015743441'
    ✅ UPDATE CLIENTE SET NOME = 'João Silva' WHERE ID = 1 AND NEGOCIO_IDX = '4015743441'
    
    EXEMPLOS INCORRETOS (NUNCA FAÇA):
    ❌ SELECT * FROM CLIENTE WHERE NOME LIKE '%João%'
    ❌ SELECT * FROM CLIENTE WHERE (NOME LIKE '%João%' OR TELEFONE LIKE '%123%')
    ❌ UPDATE CLIENTE SET NOME = 'João Silva' WHERE ID = 1
    
    Esquema do banco de dados:
    - CLIENTE
      - ID: int(10) [PK, AUTO_INCREMENT]
      - IDX: varchar(10) Este é o código do cliente no banco de dados.
      - NOME: varchar(100) [FULLTEXT]
      - CPF_CNPJ: varchar(14) default null [FULLTEXT]
      - TELEFONE: varchar(11) default null [FULLTEXT]
      - EMAIL: varchar(40) default null [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
      - CEP: varchar(8) default null 
      - LOGRADOURO: varchar(40) default null 
      - BAIRRO: varchar(40) default null [FULLTEXT
      - CIDADE: varchar(40) default null [FULLTEXT]
      - UF: varchar(2) default null [FULLTEXT]
      - NUMERO: varchar(10) default null 
      - COMPLEMENTO: varchar(20) default null
    - PRODUTO
        ID: int(10) [PK, AUTO_INCREMENT]
        IDX: varchar(10)
        NOME: varchar(100) [FULLTEXT] 
        CODIGO: varchar(10)   
        PRECO: decimal(8,2) default 0
        NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX]
        ESTOQUE: decimal(8,2) default 0
        CATEGORIA_ID: int(10) [FK: PRODUTO_CATEGORIA.ID]
        DESCR: varchar(4000) default null
    - PRODUTO_CATEGORIA
      - ID: int(10) [PK, AUTO_INCREMENT]
      - IDX: varchar(10)
      - NOME: varchar(40) [FULLTEXT]    
      - NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX] 
      - CODIGO: varchar(10)
    - SERVICO
      - ID: int(10) [PK, AUTO_INCREMENT]
      - IDX: varchar(10)
      - NOME: varchar(100) [FULLTEXT] 
      - CODIGO: varchar(10) [FULLTEXT]   
      - PRECO: decimal(8,2) default 0
      - NEGOCIO_IDX: varchar(10) [FK: NEGOCIO.IDX]
    - VEICULO_CATEGORIA 
      - ID: int(10) [PK, AUTO_INCREMENT]
      - NOME: varchar(40) [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
    - VEICULO_COR
      - ID: int(10) [PK, AUTO_INCREMENT]
      - NOME: varchar(50) [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
    - PRODUTO_MARCA
      - ID: int(10) [PK, AUTO_INCREMENT]
      - NOME: varchar(50) [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
    - VEICULO_TIPO
      - ID: int(10) [PK, AUTO_INCREMENT]
      - NOME: varchar(50) [FULLTEXT]
      - NEGOCIO_IDX: varchar(10)
    - VISAO_ORDEM_SERVICO (VIEW - Visão unificada de NEGOCIO_ORDEM_SERVICO com dados do CLIENTE  e NEGOCIO. Use esta tabela para obter e exibir informações de ordem de serviço.)
      - OS_ID: int [NOT NULL, DEFAULT 0] - ID da ordem de serviço
      - OS_IDX: varchar [NULL] - IDX da ordem de serviço
      - OS_NUMERO: smallint [DEFAULT 0] - Número sequencial da ordem de serviço (1-9999)
      - OS_NEGOCIO_IDX: varchar [NULL] - IDX do negócio da ordem de serviço
      - OS_CLIENTE_IDX: varchar [NOT NULL, DEFAULT '0'] - IDX do cliente
      - ITEM_NOME: varchar [NULL] - Nome do item/veículo
      - ITEM_CATEGORIA: varchar [NULL] - Categoria do item/veículo
      - ITEM_MARCA: varchar [NULL] - Marca do item/veículo
      - ITEM_PLACA: varchar [NULL] - Placa do veículo
      - ITEM_ANO: varchar [NULL] - Ano do veículo
      - ITEM_COR: varchar [NULL] - Cor do veículo
      - ITEM_SERIE: varchar [NULL] - Série do veículo
      - ITEM_KM: varchar [NULL] - Quilometragem do veículo
      - QTDE_PARCELAS_PAGAMENTO: smallint [DEFAULT 1] - Quantidade de parcelas para pagamento
      - TOTAL_SERVICOS: decimal [NOT NULL, DEFAULT 0.00] - Total dos serviços
      - TOTAL_PRODUTOS: decimal [NOT NULL, DEFAULT 0.00] - Total dos produtos
      - TOTAL: decimal [NOT NULL, DEFAULT 0.00] - Total geral da ordem de serviço
      - ENTRADA: datetime [NULL] - Data/hora de entrada da ordem de serviço
      - SAIDA: datetime [NULL] - Data/hora de saída da ordem de serviço
      - OBSERVACAO: varchar [NULL] - Observações da ordem de serviço
      - OS_URL_IMAGEM: varchar [NULL] - URL da imagem da ordem de serviço
      - CLIENTE_ID: int [DEFAULT 0] - ID do cliente
      - CLIENTE_IDX: varchar [NULL] - IDX do cliente
      - CLIENTE_NOME: varchar [NULL] - Nome do cliente
      - CLIENTE_TELEFONE: varchar [NULL] - Telefone do cliente
      - CLIENTE_EMAIL: varchar [NULL] - Email do cliente
      - CLIENTE_CPF_CNPJ: varchar [NULL] - CPF/CNPJ do cliente
      - CLIENTE_CEP: varchar [NULL] - CEP do cliente
      - CLIENTE_NUMERO: varchar [NULL] - Número do endereço do cliente
      - CLIENTE_COMPLEMENTO: varchar [NULL] - Complemento do endereço do cliente
      - CLIENTE_LOGRADOURO: varchar [NULL] - Logradouro do cliente
      - CLIENTE_BAIRRO: varchar [NULL] - Bairro do cliente
      - CLIENTE_CIDADE: varchar [NULL] - Cidade do cliente
      - CLIENTE_UF: varchar [NULL] - UF do cliente
      - CLIENTE_URL_IMAGEM: varchar [NULL] - URL da imagem do cliente
      - CLIENTE_EXCLUIDO: smallint [DEFAULT 0] - Se cliente está excluído (0=ativo, 1=excluído)
      - NEGOCIO_ID: int [DEFAULT 0] - ID do negócio
      - NEGOCIO_IDX: varchar [NULL] - IDX do negócio
      - PROPRIETARIO_IDX: varchar [DEFAULT 'NULL'] - IDX do proprietário
      - AREA_ID: smallint [DEFAULT 0] - ID da área do negócio
      - TIPO_ID: smallint [DEFAULT 0] - ID do tipo do negócio
      - RAZAO_SOCIAL: varchar [NULL] - Razão social do negócio
      - NEGOCIO_CNPJ_CPF: varchar [NULL] - CNPJ/CPF do negócio
      - NEGOCIO_NOME: varchar [NULL] - Nome do negócio
      - NOME_ID: varchar [NULL] - ID do nome
      - NEGOCIO_TELEFONE: varchar [NULL] - Telefone do negócio
      - NEGOCIO_EMAIL: varchar [NULL] - Email do negócio
      - SITE: varchar [NULL] - Site do negócio
      - TOKEN: varchar [NULL] - Token do negócio
      - HORARIO: varchar [NULL] - Horário de funcionamento
      - NEGOCIO_CEP: varchar [NULL] - CEP do negócio
      - NEGOCIO_LOGRADOURO: varchar [NULL] - Logradouro do negócio
      - NEGOCIO_NUMERO: varchar [NULL] - Número do endereço do negócio
      - NEGOCIO_COMPLEMENTO: varchar [NULL] - Complemento do endereço do negócio
      - NEGOCIO_BAIRRO: varchar [NULL] - Bairro do negócio
      - NEGOCIO_CIDADE: varchar [NULL] - Cidade do negócio
      - NEGOCIO_UF: varchar [NULL] - UF do negócio
      - STATUS: smallint(1) [DEFAULT 0] - ID do status da ordem de serviço
      - PRIORIDADE: smallint(1) [DEFAULT 0] - ID da prioridade da ordem de serviço
      - EXCLUIDO: smallint(1) [DEFAULT 0] - Se ordem de serviço está excluída (0=ativo, 1=excluído)

    - NEGOCIO_ORDEM_SERVICO (tabela de ordem de serviço)
      - ID: int(10) [PK, AUTO_INCREMENT] - ID único da ordem de serviço
      - IDX: varchar(10) [NULL] - IDX único da ordem de serviço
      - NEGOCIO_IDX: varchar(10) [NULL] - IDX do negócio (FK)
      - CLIENTE_IDX: varchar(10) [NOT NULL, DEFAULT '0'] - IDX do cliente (FK)
      - ITEM_NOME: varchar(100) [NULL] - Nome do item/veículo a ser reparado
      - ITEM_CATEGORIA: varchar(100) [NULL] - Categoria do item (ex: Bicicleta, Moto, Carro)
      - ITEM_MARCA: varchar(100) [NULL] - Marca do item/veículo
      - ITEM_PLACA: varchar(20) [NULL] - Placa do veículo (se aplicável)
      - ITEM_ANO: varchar(10) [NULL] - Ano do veículo
      - ITEM_COR: varchar(50) [NULL] - Cor do item/veículo
      - ITEM_SERIE: varchar(30) [NULL] - Número de série do item
      - ITEM_KM: varchar(20) [NULL] - Quilometragem do veículo
      - QTDE_PARCELAS_PAGAMENTO: smallint(2) [DEFAULT 1] - Quantidade de parcelas para pagamento
      - TOTAL_SERVICOS: decimal(10,2) [NOT NULL, DEFAULT 0.00] - Total dos serviços
      - TOTAL_PRODUTOS: decimal(10,2) [NOT NULL, DEFAULT 0.00] - Total dos produtos utilizados
      - TOTAL: decimal(10,2) [NOT NULL, DEFAULT 0.00] - Total geral da ordem de serviço
      - ENTRADA: datetime [NULL] - Data e hora de entrada da ordem de serviço
      - SAIDA: datetime [NULL] - Data e hora de saída/conclusão da ordem de serviço
      - OBSERVACAO: varchar(1000) [NULL] - Observações sobre a ordem de serviço
      - URL_IMAGEM: varchar(100) [NULL] - URL de imagem relacionada à ordem de serviço
      - NUMERO: smallint(4) [DEFAULT 0] - Número sequencial da ordem de serviço (1-9999)
      - STATUS: smallint(1) [DEFAULT 0] - ID do status da ordem de serviço
      - PRIORIDADE: smallint(1) [DEFAULT 0] - ID da prioridade da ordem de serviço
      - EXCLUIDO: smallint(1) [DEFAULT 0] - Se ordem de serviço está excluída (0=ativo, 1=excluído)
    
    A query deve respeitar as relações de chaves estrangeiras e incluir o filtro NEGOCIO_IDX e EXCLUIDO = 0. Ambos devem constar em todas as querys e sao filtros obrigatórios. 
    IMPORTANTE: Toda query deve ter o filtro do NEGOCIO_IDX , sem exceção, por que só devem ser mostrados s dados da oficina que esta usando o sistema no momento.

    Quando solicitado a exclusão de um registro, deve ser feito um soft delete, marcando o campo EXCLUIDO como 1.   Não deve ser feito um hard delete.

    """
  logger.info(f"===== consulta_atualiza_dados() =====")
  logger.info(f"query: {query}")
  logger.info(f"negocio_idx: {negocio_idx}") 
  result = await mysql.query(query)
  logger.info(f"result: {result}")
  return {"success": True, "result": result}
    

@function_tool
async def conta_email_crud(query: str, negocio_idx: str):
    """
    Executa operações CRUD (CREATE, READ, UPDATE, DELETE) na tabela NEGOCIO_EMAIL
    para gerenciamento de contas de email transacionais e de contato.
    
    Esquema da tabela NEGOCIO_EMAIL:
    - ID: int(10) [PK, AUTO_INCREMENT]
    - IDX: varchar(10) [UNIQUE]
    - EMAIL: varchar(50) [NOT NULL] - Email da conta
    - SMTP: varchar(100) [NOT NULL] - Servidor SMTP
    - SMTP_PORTA: smallint(3) [NOT NULL] - Porta do SMTP
    - TIPO_ID: smallint(1) [NOT NULL] - Tipo: 1=transacional, 2=contato
    - NEGOCIO_IDX: varchar(10) [NOT NULL] - ID do negócio
    - EXCLUIDO: smallint(1) [DEFAULT 0] - 0=ativo, 1=excluído
    
    TIPOS DE EMAIL:
    1 - Transacional (para envio de emails automáticos)
    2 - Contato (para comunicação geral)
    
    CAMPOS OBRIGATÓRIOS PARA CRIAÇÃO:
    - EMAIL: endereço de email
    - SMTP: servidor SMTP
    - SMTP_PORTA: porta do servidor
    - TIPO_ID: tipo do email (1 ou 2)
    - NEGOCIO_IDX: identificador do negócio
    
    RESTRIÇÕES DE SEGURANÇA:
    - A query deve conter NEGOCIO_IDX
    - A tabela deve ser NEGOCIO_EMAIL
    - Exclusão não é permitida. A exclusão é feita através do campo EXCLUIDO, marcando ele com o valor 1.
    """
    logger.info(f"===== conta_email_crud() =====")
    logger.info(f"query: {query}")
    logger.info(f"negocio_idx: {negocio_idx}")
    
    # Validação de segurança 1: negocio_idx deve estar presente
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    # Validação de segurança 2: query deve conter NEGOCIO_EMAIL
    if "NEGOCIO_EMAIL" not in query.upper():
        return {"success": False, "message": "Query deve operar apenas na tabela NEGOCIO_EMAIL"}
    
    # Validação de segurança 3: query deve conter referência ao negocio_idx
    if negocio_idx not in query:
        return {"success": False, "message": "Query deve conter o NEGOCIO_IDX para segurança"}
    
    try:
        result = await mysql.query(query)
        logger.info(f"result: {result}")
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Erro ao executar query: {e}")
        return {"success": False, "message": f"Erro ao executar operação: {str(e)}"}


