import ast,json, re
from textwrap import dedent
import pytz
from datetime import datetime, date


class Converter:
    def __init__(self):
        pass
    
    @staticmethod
    async def stringArrayObject_to_ArrayObject(string):
        pass

    @staticmethod
    async def extract_array(data):
        # Substitui crases por aspas triplas
        data = data.replace('`', "'''")
        
        # Encontra a primeira ocorrência de '['
        start = data.find('[')
        if start == -1:
            return None  # Retorna None se não encontrar '['
        
        # Contador para acompanhar os pares de colchetes
        bracket_count = 0
        for i in range(start, len(data)):
            if data[i] == '[':
                bracket_count += 1
            elif data[i] == ']':
                bracket_count -= 1
            
            # Quando bracket_count volta a zero, encontramos o par completo
            if bracket_count == 0:
                # Avalia o texto como uma expressão literal de Python e retorna
                return ast.literal_eval(dedent(data[start:i + 1]))

    @staticmethod
    async def extract_object(data):
        #print('xxxxxxxxxxextract_objectxxxxxxxxxx')
        #print('Substitui crases por aspas triplas')
        data = data.replace('`', "'''")
        
        #print("Encontra a primeira ocorrência de '{'")
        start = data.find('{')
        if start == -1:
            return None  # Retorna None se não encontrar '{'
        
        #print("Contador para acompanhar os pares de chaves")
        brace_count = 0
        for i in range(start, len(data)):
            if data[i] == '{':
                brace_count += 1
            elif data[i] == '}':
                brace_count -= 1
            
            #print("Quando brace_count volta a zero, encontramos o par correspondente")
            if brace_count == 0:
                return json.loads(dedent(data[start:i + 1]))
                #return ast.literal_eval(dedent(data[start:i + 1]))
     
        print("Retorna None se não encontrar um par de chaves correspondente")
        return None  
    

    def country_unmasked_telephone(self,telephone,country_code="55"):
        # Remove caracteres não numéricos
        telephone = re.sub(r'\D', '', telephone)
        # Adiciona o código do país
        telephone = country_code + telephone
        return telephone
    
    def remove_markdown(self,text):
        # Remove links no formato Markdown
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        # Remove imagens no formato Markdown
        text = re.sub(r'\!\[([^\]]*)\]\([^\)]+\)', '', text)
        
        # Remove cabeçalhos no estilo Markdown
        text = re.sub(r'#+ ', '', text)
        # Remove formatações de negrito e itálico (* ou _)
        text = re.sub(r'\*{1,2}(\S[^\*]*?)\*{1,2}', r'\1', text)
        text = re.sub(r'_{1,2}(\S[^_]*?)_{1,2}', r'\1', text)
        
        


        # Remove citações em bloco
        text = re.sub(r'>\s?', '', text)
        
        

        # Remove listas não ordenadas e ordenadas
        text = re.sub(r'^\s*[\-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)


        


        
        
        # Remoção de espaços extras
        text = re.sub(r'\n\s+\n', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        
        return text.strip()
    @staticmethod
    def remove_empty_values(obj):
        
        if not isinstance(obj, dict):
            raise ValueError("Input must be a dictionary")

        return {k: v for k, v in obj.items() if v not in ('', "", None)}

    @staticmethod
    def time_now_to_timestamp(only_int=False):
        # Obtém o timestamp atual
        timestamp = datetime.now().timestamp()
        # Converte o timestamp para inteiro
        if only_int:
            timestamp = int(timestamp)
        return timestamp 
    
    def time_now_to_date_hour(self,timeZone="America/Bahia"):
        timezone = pytz.timezone(timeZone)
        tnow = datetime.now(timezone)
        date_hour = tnow.strftime("%Y-%m-%d %H:%M:%S")
        return date_hour

    @staticmethod
    async def message_chat(messages):
        #print("message_chat", messages)
        formatted_messages = []
        for message in messages:
            #print("message", message)
            if 'ENVIADO' in message and message['ENVIADO'] is not None:
                formatted_messages.append({"role": "user", "content": message['RECEBIDO'].strip()})
            if 'RECEBIDO' in message and message['RECEBIDO'] is not None:
                formatted_messages.append({"role": "assistant", "content": message['ENVIADO'].strip()})
        return formatted_messages
    
    @staticmethod
    def default_serializable_string_converter(o):
        if isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable')

    @staticmethod
    async def convert_date_format_comma_hyphen(query_result: list[dict[str, any]]) -> list[dict[str, any]]:
        date_pattern = re.compile(r'^\d{4},\d{2},\d{2}$')
        
        for record in query_result:
            for key in record:
                if isinstance(record[key], str) and date_pattern.match(record[key]):
                    try:
                        corrected_date = record[key].replace(',', '-')
                        record[key] = corrected_date
                    except Exception as e:
                        print(f"Error processing record {record}: {e}")
                elif isinstance(record[key], date):
                    record[key] = record[key].isoformat()  
  
        return query_result
    
    @staticmethod
    def generate_token(number):
        import hashlib
        # Convert the number to a string
        number_str = str(number)

        # Create a SHA-256 hash object
        sha256 = hashlib.sha256()

        # Update the hash object with the bytes of the number string
        sha256.update(number_str.encode('utf-8'))

        # Get the hexadecimal representation of the hash
        token = sha256.hexdigest()

        return token

    @staticmethod
    def decimal_to_integer(value: str) -> str:
        return value.replace(',', '')

    
        
if __name__ == "__main__":
    import asyncio
    from .task.functions_maps import task_get_map, task_add_map, task_exclude_map

    

    async def main():
        pass
    asyncio.run(main())