from agents import function_tool
from ....agent.agent_neo4j import AgentNeo4j

@function_tool
async def produto_excluir(
    codigo: str
):
    """
    Use esta função para excluir produtos solicitados pelo usuário.
    Marca um produto como excluido no banco de dados. Isto é feito setando a propriedade excluido com o valor 1. 
    Retorna um erro se o produto com o codigo especificado não for encontrado.
    """
    agent_neo4j = AgentNeo4j()
    # Modificamos a query para retornar o produto. Se nenhum produto for encontrado, o resultado estará vazio.
    query = "MATCH (p:Produto {codigo: $codigo}) SET p.excluido = 1 RETURN p"
    params = {"codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_write_query(query=query, params=params)
        
        # Se o resultado (uma lista) contiver dados, o produto foi encontrado e atualizado.
        if resultado and len(resultado) > 0:
            return {"success": True, "message": f"Produto excluido com sucesso!"}
        else:
            # Se o resultado estiver vazio, nenhum produto foi encontrado com o codigo especificado.
            return {"success": False, "message": f"Nenhum produto encontrado com o codigo {codigo} foi encontrado."}
            
    except Exception as e:
        return {"success": False, "message": f"Ocorreu uma exceção ao tentar excluir o produto: {str(e)}"}


async def verificar_produto_existe(codigo: str):
    """
    Verifica se um produto existe no banco de dados antes de tentar excluí-lo.
    """
    agent_neo4j = AgentNeo4j()
    query = "MATCH (p:Produto {codigo: $codigo}) RETURN p.codigo, p.nome, p.excluido"
    params = {"codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_read_query(query=query, params=params)
        return resultado
    except Exception as e:
        print(f"Erro ao verificar produto: {str(e)}")
        return None
    
@function_tool
async def cor_excluir(
    negocio_idx: str,
    codigo: str
):
    """
    Use esta função para excluir cores solicitadas pelo usuário.
    Marca uma cor como excluida no banco de dados. Isto é feito setando a propriedade excluido com o valor 1.
    Retorna um erro se a cor com o codigo especificado não for encontrada.
    """
    agent_neo4j = AgentNeo4j()
    # Modificamos a query para incluir o negocio_idx e retornar a cor
    query = """
    MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {codigo: $codigo})
    SET c.excluido = 1 
    RETURN c
    """
    params = {"negocio_idx": negocio_idx, "codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_write_query(query=query, params=params)
        
        # Se o resultado (uma lista) contiver dados, a cor foi encontrada e atualizada.
        if resultado and len(resultado) > 0:
            return {"success": True, "message": f"Cor excluida com sucesso!"}
        else:
            # Se o resultado estiver vazio, nenhuma cor foi encontrada com o codigo especificado.
            return {"success": False, "message": f"Nenhuma cor encontrada com o codigo {codigo} no negócio {negocio_idx}."}
            
    except Exception as e:
        return {"success": False, "message": f"Ocorreu uma exceção ao tentar excluir a cor: {str(e)}"}


async def verificar_cor_existe(negocio_idx: str, codigo: str):
    """
    Verifica se uma cor existe no banco de dados antes de tentar excluí-la.
    """
    agent_neo4j = AgentNeo4j()
    query = """
    MATCH (n:Negocio {idx: $negocio_idx})-[:DISPONIBILIZA_COR]->(c:Cor {codigo: $codigo})
    RETURN c.codigo, c.nome, c.hexadecimal, c.excluido
    """
    params = {"negocio_idx": negocio_idx, "codigo": codigo}
    
    try:
        resultado = await agent_neo4j.execute_read_query(query=query, params=params)
        return resultado
    except Exception as e:
        print(f"Erro ao verificar cor: {str(e)}")
        return None
    
if __name__ == "__main__":
    import asyncio
    
    async def teste_cor_excluir():
        negocio_idx = "5544332211"
        codigo = "COR001"
        
        # Primeiro, verificar se a cor existe
        print(f"🔍 Verificando se a cor {codigo} existe no negócio {negocio_idx}...")
        cor_existe = await verificar_cor_existe(negocio_idx, codigo)
        
        if cor_existe:
            print(f"✅ Cor encontrada: {cor_existe}")
        else:
            print(f"❌ Cor {codigo} NÃO foi encontrada no banco de dados!")
            return
        
        # Se existe, tentar excluir
        print(f"\n🗑️ Tentando excluir a cor {codigo}...")
        resultado = await cor_excluir(negocio_idx, codigo)
        print(resultado)
        
        if resultado["success"]:
            print("Cor excluída com sucesso!")
        else:
            print(f"Erro ao excluir cor: {resultado['message']}")
            
        # Verificar novamente após a exclusão
        print(f"\n🔍 Verificando estado após exclusão...")
        cor_apos = await verificar_cor_existe(negocio_idx, codigo)
        if cor_apos:
            print(f"📊 Estado atual da cor: {cor_apos}")
    
    asyncio.run(teste_cor_excluir())
    #Execução:
    #python -m api.agent.business.functions.cor_excluir
    
    async def teste_produto_excluir():
        codigo = "10253731"
        
        # Primeiro, verificar se o produto existe
        print(f"🔍 Verificando se o produto {codigo} existe...")
        produto_existe = await verificar_produto_existe(codigo)
        
        if produto_existe:
            print(f"✅ Produto encontrado: {produto_existe}")
        else:
            print(f"❌ Produto {codigo} NÃO foi encontrado no banco de dados!")
            return
        
        # Se existe, tentar excluir
        print(f"\n🗑️ Tentando excluir o produto {codigo}...")
        resultado = await produto_excluir(codigo)
        print(resultado)
        
        if resultado["success"]:
            print("Produto excluído com sucesso!")
        else:
            print(f"Erro ao excluir produto: {resultado['message']}")
            
        # Verificar novamente após a exclusão
        print(f"\n🔍 Verificando estado após exclusão...")
        produto_apos = await verificar_produto_existe(codigo)
        if produto_apos:
            print(f"📊 Estado atual do produto: {produto_apos}")
    
    asyncio.run(teste_produto_excluir())
    #Execução:
    #python -m api.agent.business.functions.produto_excluir