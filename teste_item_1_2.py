#!/usr/bin/env python3
"""
TESTE 1.2: Verificar QR Code da instância (endpoint público)
"""
import requests

# Configurações
url = 'https://apinocode01.megaapi.com.br/rest/instance/qrcode/megacode-MqgHxQV7790'
headers = {'Authorization': 'Bearer 4649a86d-1e20-4dd1-a6ad-e41f1b92c495'}

print('🔍 TESTE 1.2: QR Code da instância (endpoint público)')
print(f'URL: {url}')
print(f'Token: 4649a86d-1e20-4dd1-a6ad-e41f1b92c495')
print('-' * 50)

try:
    response = requests.get(url, headers=headers)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response: {data}')
        
        if 'qrcode' in data or 'qr' in data:
            print('✅ SUCESSO: QR Code obtido!')
            print('🎯 A autenticação está funcionando!')
        else:
            print('❓ Resposta inesperada')
    elif response.status_code == 401:
        print('❌ FALHA: Ainda não autorizado')
        print(f'Response: {response.text}')
    else:
        print(f'❌ FALHA: HTTP {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ ERRO DE CONEXÃO: {e}')

print('-' * 50)
print('RESULTADO DO TESTE 1.2:') 