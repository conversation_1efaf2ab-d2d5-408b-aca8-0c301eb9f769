from fastapi import APIRouter
from .agent_neo4j import AgentNeo4j
from .agent_logger import AgentLogger

router = APIRouter()
logger = AgentLogger()

class Service:
    def __init__(self):
        self.neo4j = AgentNeo4j()

    async def create_services(self, data: dict):
        """Cria novos serviços no Neo4j"""
        logger.info("===== agent_service.create_services() (Neo4j) =====")
        results = []
        for record in data["adicionados"]:
            idx_result = await self.neo4j.execute_read_query("CALL apoc.create.uuid() YIELD uuid RETURN uuid AS idx")
            idx = idx_result[0]["idx"] if idx_result and "idx" in idx_result[0] else None
            props = {k.lower(): v for k, v in record.items()}
            props["idx"] = idx
            cypher = """
            CREATE (s:Servico $props)
            RETURN s
            """
            params = {"props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "servicos": results}

    async def update_services(self, data: dict):
        """Atualiza serviços existentes no Neo4j"""
        logger.info("===== agent_service.update_services() (Neo4j) =====")
        results = []
        for record in data["atualizados"]:
            props = {k.lower(): v for k, v in record.items()}
            idx = props.get("idx") or record.get("IDX")
            if not idx:
                continue
            cypher = """
            MATCH (s:Servico {idx: $idx})
            SET s += $props
            RETURN s
            """
            params = {"idx": idx, "props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "servicos": results}
    
    async def categories_update(self, data: dict):
        """Atualiza categorias de serviços no Neo4j"""
        logger.info("===== categories_update() (Neo4j) =====")
        results = []
        for record in data["atualizados"]:
            props = {k.lower(): v for k, v in record.items()}
            idx = props.get("idx") or record.get("IDX")
            if not idx:
                continue
            cypher = """
            MATCH (c:ServicoCategoria {idx: $idx})
            SET c += $props
            RETURN c
            """
            params = {"idx": idx, "props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "categorias": results}

    async def services_fetch(self, business_idx: str):
        """Busca todos os serviços de um negócio no Neo4j"""
        logger.info(f"===== services_fetch() (Neo4j) ===== {business_idx}")
        cypher = """
        MATCH (n:Negocio {idx: $business_idx})-[:POSSUI_SERVICO]->(s:Servico)
        WHERE s.excluido = 0 OR s.excluido IS NULL
        RETURN s
        ORDER BY s.nome
        """
        params = {"business_idx": business_idx}
        result = await self.neo4j.execute_read_query(cypher, params)
        servicos = [{k.upper(): v for k, v in r["s"].items()} for r in result if "s" in r]
        logger.info(f"Result: {servicos}")
        return servicos

    async def categories_fetch(self, business_id: str, colunas_nome: dict):
        """Busca categorias de serviços de um negócio no Neo4j"""
        logger.info(f"===== categories_fetch() (Neo4j) ===== {business_id}, {colunas_nome}")
        cypher = """
        MATCH (c:ServicoCategoria)-[:PERTENCE_AO_NEGOCIO]->(n:Negocio {idx: $business_id})
        WHERE c.excluido = 0 OR c.excluido IS NULL
        RETURN c
        ORDER BY c.nome
        """
        params = {"business_id": business_id}
        result = await self.neo4j.execute_read_query(cypher, params)
        categorias = [{k.upper(): v for k, v in r["c"].items()} for r in result if "c" in r]
        logger.info(f"Result: {categorias}")
        return categorias

    async def categories_add(self, data: dict):
        """Adiciona novas categorias de serviços no Neo4j"""
        logger.info(f"===== categories_add() (Neo4j) ===== {data}")
        results = []
        for record in data['adicionados']:
            idx_result = await self.neo4j.execute_read_query("CALL apoc.create.uuid() YIELD uuid RETURN uuid AS idx")
            idx = idx_result[0]["idx"] if idx_result and "idx" in idx_result[0] else None
            props = {k.lower(): v for k, v in record.items()}
            props["idx"] = idx
            cypher = """
            CREATE (c:ServicoCategoria $props)
            RETURN c
            """
            params = {"props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "categorias": results}

    async def categories_children_fetch(self, business_id: str):
        """Busca categorias filhas (sem subcategorias) no Neo4j"""
        logger.info(f"===== categories_children_fetch() (Neo4j) ===== {business_id}")
        cypher = """
        MATCH (c:ServicoCategoria)-[:PERTENCE_AO_NEGOCIO]->(n:Negocio {idx: $business_id})
        WHERE (c.excluido = 0 OR c.excluido IS NULL)
        AND NOT (c)<-[:E_SUBCATEGORIA_DE]-(:ServicoCategoria)
        RETURN c
        ORDER BY c.nome
        """
        params = {"business_id": business_id}
        result = await self.neo4j.execute_read_query(cypher, params)
        categorias = [{k.upper(): v for k, v in r["c"].items()} for r in result if "c" in r]
        logger.info(f"Result: {categorias}")
        return categorias

            # Substituir %s pelos valores reais
    async def services_search_all(self, filters: dict):
        """Busca serviços de forma inteligente e flexível por NOME, CODIGO, DESCR e CATEGORIA_NOME no Neo4j"""
        logger.info(f"===== services_search_all() (Neo4j) ===== {filters}")
        try:
            texto_busca = filters.get("text", "")
            negocio_idx = filters.get('negocio_idx', '')
            limit = filters.get('limit', 10)
            offset = filters.get('offset', 0)
            cypher = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:PRESTA_SERVICO]->(s:Servico)
            WHERE (s.excluido = 0 OR s.excluido IS NULL)
            """
            params = {"negocio_idx": negocio_idx}
            if texto_busca and texto_busca.strip():
                cypher += " AND (toLower(s.nome) CONTAINS toLower($texto_busca) OR toLower(s.codigo) CONTAINS toLower($texto_busca) OR toLower(s.descr) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
            cypher += " RETURN s ORDER BY s.nome"
            if limit > 0:
                cypher += " SKIP $offset LIMIT $limit"
                params["offset"] = offset
                params["limit"] = limit
            result = await self.neo4j.execute_read_query(cypher, params)
            servicos = [{k.upper(): v for k, v in r["s"].items()} for r in result if "s" in r]
            resposta = {
                "success": True,
                "data": servicos,
                "total": len(servicos),
                "message": f"Encontrados {len(servicos)} serviços para '{texto_busca}'"
            }
            return resposta
        except Exception as e:
            logger.error(f"❌ ERRO em services_search_all: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }
            logger.info(f"🔍 SQL FINAL (com valores substituídos):\n{query_formatada}")
            

    async def services_search(self, business_id: str, keywords: str, columns: str):
        """Busca serviços por palavras-chave no Neo4j"""
        logger.info(f"===== services_search() (Neo4j) ===== {business_id}, {keywords}, {columns}")
        cypher = """
        MATCH (n:Negocio {idx: $business_id})-[:POSSUI_SERVICO]->(s:Servico)
        WHERE (s.nome CONTAINS $keywords OR s.codigo CONTAINS $keywords) AND (s.excluido = 0 OR s.excluido IS NULL)
        RETURN s
        ORDER BY s.nome
        """
        params = {"business_id": business_id, "keywords": keywords}
        result = await self.neo4j.execute_read_query(cypher, params)
        servicos = [{k.upper(): v for k, v in r["s"].items()} for r in result if "s" in r]
        logger.info(f'RESULTADO FINAL: {servicos}')
        return servicos


# ENDPOINTS DO ROUTER

@router.post("/create/services")
async def create_services(data: dict):
    """Endpoint para criar novos serviços"""
    logger.info(f"Endpoint create_services chamado: {data}")
    service = Service()
    result = await service.create_services(data)
    return result

@router.post("/update/services")
async def update_services(data: dict):
    """Endpoint para atualizar serviços existentes"""
    logger.info(f"##### /agent/service/update/services ##### {data}")
    service = Service()
    result = await service.update_services(data)
    return result

@router.get("/categories/children/fetch")
async def categories_children_fetch(business_id: str):
    """Endpoint para buscar categorias filhas"""
    service = Service()
    result = await service.categories_children_fetch(business_id)
    return result

@router.post("/services/fetch")
async def services_fetch(business_idx: str):
    """Endpoint para buscar serviços de um negócio"""
    service = Service()
    result = await service.services_fetch(business_idx)
    return result

@router.post("/search/all")
async def services_search_all(data: dict):
    """Endpoint principal para busca inteligente de serviços"""
    logger.info("===== services_search_all() =====")
    service = Service()
    result = await service.services_search_all(data)
    return result

@router.post("/services/search")
async def services_search(business_id: str, keywords: str, columns: dict):
    """Endpoint para busca simples de serviços"""
    logger.info("services_search()")
    logger.info(f"business_id: {business_id}")
    logger.info(f"keywords: {keywords}")
    logger.info(f"columns: {columns}")
    service = Service()
    columns_str = ",".join(columns["colunas_nome"])
    result = await service.services_search(business_id, keywords, columns_str)
    return result

@router.post("/categories/fetch/{business_id}")
async def categories_fetch(business_id: str, colunas_nome: dict):
    """Endpoint para buscar categorias de serviços"""
    colunas = ",".join(colunas_nome["colunas_nome"])
    logger.info(f"colunas: {colunas}")
    logger.info(f"business_id: {business_id}")
    service = Service()
    result = await service.categories_fetch(business_id, colunas)
    logger.info(f"result: {result}")
    return result

@router.post("/categories/add")
async def categories_add(data: dict):
    """Endpoint para adicionar novas categorias"""
    logger.info(f"categories_add(): {data}")
    service = Service()
    result = await service.categories_add(data)
    logger.info(f"result: {result}")
    return result

@router.post("/categories/update")
async def categories_update(data: dict):
    """Endpoint para atualizar categorias existentes"""
    service = Service()
    result = await service.categories_update(data)
    return result


if __name__ == "__main__":
    import asyncio

    async def test_services_search_all():
        logger.info("===== test_products_search_all() - INÍCIO =====")
        service = Service()
        negocio_idx = "4015743441"
        data = {
            "negocio_idx": negocio_idx,
            "text": "",
            "limit": 0,
            "offset": 0
        }
        logger.info(f"data: {data}")
        result = await service.services_search_all(data)
        logger.info(f"result: {result}")

    asyncio.run(test_services_search_all())
