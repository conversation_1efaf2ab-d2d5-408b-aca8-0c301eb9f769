from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()

@function_tool
async def cliente_consultar(query: str, params: str):
    """
    Consulta GENÉRICA na base de CLIENTES (Neo4j).

    NÓS DISPONÍVEIS:
    - Pessoa  {idx, nome, telefone, email, cpf_cnpj,logradouro, numero, complemento, bairro, cidade, uf,excluido}
    RELAÇÕES:
    - (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa)
      (ambos são nós Pessoa, onde 'cliente' é o cliente e 'consultora' é a consultora)

    REGRA DE ESCOPO OBRIGATÓRIA:
    - A consulta DEVE retornar APENAS clientes do consultor atual
    - O parâmetro $usuario_idx é OBRIGATÓRIO e será fornecido automaticamente
    - A query DEVE conter o padrão exato:
        MATCH (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa {idx: $usuario_idx})
    
    REGRAS ADICIONAIS:
    - WHERE coalesce(cliente.excluido, 0) <> 1  (ignora clientes excluídos)
    - RETURN ... (obrigatório)

    Exemplos prontos:
    1. Buscar cliente por nome ou telefone (case-insensitive e parcial):
       query: "MATCH (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa {idx: $usuario_idx}) WHERE (toLower(cliente.nome) CONTAINS toLower($busca) OR cliente.telefone CONTAINS $busca) AND coalesce(cliente.excluido, 0) <> 1 RETURN cliente LIMIT 10"
       params: '{"usuario_idx": "112233", "busca": "João"}'

    2. Listar clientes recentes (últimos 30 dias):
       query: "MATCH (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa {idx: $usuario_idx}) WHERE coalesce(cliente.excluido, 0) <> 1 AND datetime(cliente.criado_em) > datetime().minus(duration('P30D')) RETURN cliente.nome AS nome, cliente.telefone AS telefone, date(cliente.criado_em) AS data_cadastro ORDER BY cliente.criado_em DESC"
       params: '{"usuario_idx": "112233"}'
    """
    print("===== cliente_consultar() =====")
    print("Query recebida:", query)
    print("Params recebidos:", params)
    try:
        p = json.loads(params)
        if p.get("usuario_idx") is None:
            return {"status": "error", "message": "usuario_idx obrigatório"}

        q = query.upper()
        if "MATCH" not in q or "RETURN" not in q:
            return {"status": "error", "message": "Query precisa de MATCH e RETURN"}
        # Exigir presença do parâmetro do escopo correto
        if "$USUARIO_IDX" not in q:
            return {"status": "error", "message": "Falta o filtro obrigatório do consultor ($usuario_idx)"}

        # Aceitar diferentes formas de tratar exclusão lógica:
        # - EXCLUIDO diretamente
        # - COALESCE(cliente.excluido, 0) <> 1
        # - NOT EXISTS/EXISTS(cliente.excluido) (quando aplicável)
        has_exclusion_filter = (
            "EXCLUIDO" in q or "COALESCE" in q or ("EXISTS(" in q and "EXCLUIDO" in q)
        )
        if not has_exclusion_filter:
            return {"status": "error", "message": "Falta filtro de exclusão lógica: use coalesce(cliente.excluido,0) <> 1, ou cláusula equivalente."}

        result = await neo4j.execute_read_query(query, p)
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    import asyncio
    
    async def testa_consulta_cliente():
        # Teste 1: Buscar cliente por nome
        print("\n=== Teste 1: Buscar cliente por nome ===")
        query1 = """
        MATCH (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa {idx: $usuario_idx})
        WHERE toLower(cliente.nome) CONTAINS toLower($busca)
          AND coalesce(cliente.excluido, 0) <> 1
        RETURN cliente.nome AS nome, cliente.telefone
        LIMIT 5
        """
        params1 = '{"usuario_idx": "112233", "busca": "João"}'
        
        result1 = await cliente_consultar(query1, params1)
        print("Resultado 1:", result1)
        
        # Teste 2: Listar clientes recentes
        print("\n=== Teste 2: Clientes recentes ===")
        query2 = """
        MATCH (cliente:Pessoa)-[:ATENDIDO_POR]->(consultora:Pessoa {idx: $usuario_idx})
        WHERE coalesce(cliente.excluido, 0) <> 1
        RETURN 
          cliente.nome AS nome,
          cliente.telefone,
          date(cliente.criado_em) AS data_cadastro
        ORDER BY cliente.criado_em DESC
        LIMIT 3
        """
        params2 = '{"usuario_idx": "1122334455"}'
        
        result2 = await cliente_consultar(query2, params2)
        print("Resultado 2:", result2)

    asyncio.run(testa_consulta_cliente())
    # Execução:
    # python -m api.agent.assistenciamk.functions.cliente_consultar