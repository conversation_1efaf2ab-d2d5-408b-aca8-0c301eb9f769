#==========================================
import requests, json
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import PlainTextResponse
from .agent_logger import <PERSON><PERSON><PERSON><PERSON>
from .agent_secret import Secret
from .agent_gptalkzap import AgentG<PERSON>lkZap
import asyncio

logger = AgentLogger()
router = APIRouter()
secret = Secret()

# Configurações da Mega API
MEGA_API_BASE_URL = secret.get_secret("MEGA_API_BASE_URL")
MEGA_API_TOKEN = secret.get_secret("MEGA_API_TOKEN")
MEGA_API_INSTANCE_KEY = secret.get_secret("MEGA_API_INSTANCE_KEY")

#logger.info(f"MEGA_API_BASE_URL: {MEGA_API_BASE_URL}")
#logger.info(f"MEGA_API_TOKEN: {MEGA_API_TOKEN}")
#logger.info(f"MEGA_API_INSTANCE_KEY: {MEGA_API_INSTANCE_KEY}")

# Debug adicional para verificar variáveis de ambiente
import os
#logger.info(f"=== DEBUG MEGA API VARIABLES ===")
#logger.info(f"Direct os.getenv('MEGA_API_TOKEN'): {os.getenv('MEGA_API_TOKEN')}")
#logger.info(f"Direct os.getenv('MEGA_API_BASE_URL'): {os.getenv('MEGA_API_BASE_URL')}")
#logger.info(f"Direct os.getenv('MEGA_API_INSTANCE_KEY'): {os.getenv('MEGA_API_INSTANCE_KEY')}")
# logger.info(f"Secret object instance - MEGA_API_TOKEN: {secret.MEGA_API_TOKEN}")
#logger.info(f"Secret object get_secret - MEGA_API_TOKEN: {secret.get_secret('MEGA_API_TOKEN')}")
#logger.info(f"=================================")


class AgentMegaApi:  #PascalCase
    
    def __init__(self):
        self.gptalkzap = AgentGptalkZap()
    
    def _get_mime_type(self, media_type: str) -> str:
        """
        Retorna o MIME type baseado no tipo de mídia
        """
        mime_types = {
            "image": "image/jpeg",
            "video": "video/mp4",
            "audio": "audio/ogg; codecs=opus",
            "ptt": "audio/ogg; codecs=opus",
            "document": "application/pdf"
        }
        return mime_types.get(media_type, "application/octet-stream")

    async def process_message(self, body: dict):
        """
        Processa mensagens recebidas via webhook da Mega API
        """
        try:
            # Verifica se o webhook contém dados de mensagem válidos
            if "messageType" in body and "key" in body:

                # Verifica se é uma mensagem própria (enviada pelo bot)
                if body["key"].get("fromMe", False):
                    logger.info("Webhook da própria mensagem enviada - ignorando")
                    return None

                # Verifica se é um webhook de status/confirmação
                message_type = body.get("messageType", "")
                if message_type in ["message.ack", "message.reaction", "message.delete"]:
                    logger.info(f"Webhook de status/confirmação ({message_type}) - ignorando")
                    return None

                # Extrai informações do remetente
                sender_name = body.get("pushName", "Nome não disponível")
                sender_phone = body["key"]["remoteJid"].replace("@s.whatsapp.net", "")
                #logger.info(f"message_type: {message_type}")

                # Extrai o conteúdo da mensagem baseado no tipo
                message_content = ""
                if message_type == "conversation":
                    message_content = body.get("message", {}).get("conversation", "")
                elif message_type == "extendedTextMessage":
                    message_content = body.get("message", {}).get("extendedTextMessage", {}).get("text", "")
                elif message_type == "imageMessage":
                    caption = body.get("message", {}).get("imageMessage", {}).get("caption", "")
                    message_content = f"[IMAGEM] {caption}" if caption else "[IMAGEM]"
                elif message_type == "audioMessage":
                    message_content = "[ÁUDIO]"
                elif message_type == "videoMessage":
                    caption = body.get("message", {}).get("videoMessage", {}).get("caption", "")
                    message_content = f"[VÍDEO] {caption}" if caption else "[VÍDEO]"
                elif message_type == "documentMessage":
                    filename = body.get("message", {}).get("documentMessage", {}).get("fileName", "documento")
                    message_content = f"[DOCUMENTO] {filename}"
                else:
                    message_content = f"[{message_type.upper()}]"

                # Gera o log formatado com as informações solicitadas
                self._log_message_info(sender_name, sender_phone, message_content)

                # Processa a mensagem apenas se for texto
                if message_type in ["conversation", "extendedTextMessage"] and message_content.strip():
                    #logger.info(f"Processando mensagem de texto de {sender_name} ({sender_phone}): {message_content}")
                    data = {
                        "from": sender_phone,
                        "to": sender_phone,
                        "message": message_content,
                        "type": message_type,
                        "sender_name": sender_name,
                        "sender_phone": sender_phone,
                    }
                    result = await self.gptalkzap.send(data)
                    return result

                else:
                    logger.info(f"Mensagem de mídia recebida de {sender_name} ({sender_phone}): {message_content}")
                    return None

            else:
                logger.info("Webhook recebido mas sem estrutura de mensagem válida")
                return None

        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {str(e)}")

    def _log_message_info(self, sender_name: str, sender_phone: str, message_content: str):
        """
        Gera log formatado com as informações da mensagem recebida
        """
        # Log em uma única linha para evitar problemas de formatação
        #log_message = f"📱 MEGA API | 👤 {sender_name} | 📞 {sender_phone} | 💬 {message_content}"
        #logger.info(log_message)
        
        # Log detalhado adicional
        #logger.info(f"--- DETALHES DA MENSAGEM ---")
        #logger.info(f"Nome do remetente: {sender_name}")
        #logger.info(f"Telefone: {sender_phone}")
        #logger.info(f"Mensagem: {message_content}")
        #logger.info(f"--- FIM DOS DETALHES ---")

    async def send_mega_api_message(self, to_number: str, message_text: str):
        #logger.info("--- send_mega_api_message() ---")
        #logger.info(f"Enviando mensagem para {to_number}: {message_text}")
        #logger.info(f"MEGA_API_BASE_URL: {MEGA_API_BASE_URL}")
        #logger.info(f"MEGA_API_TOKEN: {MEGA_API_TOKEN}")
        #logger.info(f"MEGA_API_INSTANCE_KEY: {MEGA_API_INSTANCE_KEY}")

        """
        Envia mensagem de texto usando a Mega API
        """
        try:
            # URL do endpoint da Mega API para envio de mensagens
            url = f"{MEGA_API_BASE_URL}/rest/sendMessage/{MEGA_API_INSTANCE_KEY}/text"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {MEGA_API_TOKEN}"
            }
            
            payload = {
                "messageData": {
                    "to": f"{to_number}@s.whatsapp.net",
                    "text": message_text
                }
            }
            
            # Faz a requisição POST para a Mega API
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            logger.info(f"Mensagem enviada com sucesso para {to_number}: {response.json()}")
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Erro HTTP ao enviar mensagem via Mega API: {e.response.status_code} - {e.response.text}")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao enviar mensagem via Mega API: {e}")
            raise

    async def send_mega_api_media(self, to_number: str, media_url: str, media_type: str, caption: str = ""):
        """
        Envia mídia (imagem, vídeo, áudio, documento) usando a Mega API
        """
        try:
            # Usa o endpoint genérico para mídia por URL
            url = f"{MEGA_API_BASE_URL}/rest/sendMessage/{MEGA_API_INSTANCE_KEY}/mediaUrl"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {MEGA_API_TOKEN}"
            }
            
            payload = {
                "messageData": {
                    "to": f"{to_number}@s.whatsapp.net",
                    "url": media_url,
                    "type": media_type,
                    "fileName": f"media.{media_type}",
                    "mimeType": self._get_mime_type(media_type)
                }
            }
            
            # Adiciona caption se fornecido
            if caption:
                payload["messageData"]["caption"] = caption
            
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            logger.info(f"Mídia {media_type} enviada com sucesso para {to_number}")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao enviar mídia via Mega API: {e}")
            raise

    async def get_instance_status(self):
        """
        Verifica o status da instância na Mega API
        """
        try:
            url = f"{MEGA_API_BASE_URL}/rest/instance/{MEGA_API_INSTANCE_KEY}"
            
            headers = {
                "Authorization": f"Bearer {MEGA_API_TOKEN}"
            }
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            status_data = response.json()
            logger.info(f"Status da instância: {status_data}")
            return status_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao verificar status da instância: {e}")
            raise



            
            


@router.get("/webhook")
async def webhook_verification(request: Request):
    """
    Endpoint para verificação do webhook (se necessário pela Mega API)
    """
    logger.info("Verificação de webhook - agent_megaapi")
    
    # A Mega API pode ter um sistema de verificação específico
    # Aqui você pode implementar a lógica de verificação necessária
    
    return {"status": "webhook verified"}


@router.post("/webhook")
async def handle_mega_webhook(request: Request):
    """
    Endpoint para receber webhooks da Mega API
    """
    try:
        body = await request.json()
        
        #logger.info("--- Webhook Mega API Recebido ---")
        #logger.info(json.dumps(body, indent=2))
        #logger.info("--------------------------------")
        
        mega_api = AgentMegaApi()
        result = await mega_api.process_message(body)
        #logger.info(f"*********result mega_api_process_message: {result}")
        
        # Só enviar resposta se houve resultado válido e não é mensagem própria
        if result and not body["key"].get("fromMe", False):
            # Extrair apenas o número do telefone (remover @s.whatsapp.net)
            phone_number = body["key"]["remoteJid"].replace("@s.whatsapp.net", "")
            
            # Verificar se o resultado contém um array de mensagens (novo formato)
            if result.get("messages") and isinstance(result["messages"], list):
                logger.info(f"Processando array de {len(result['messages'])} mensagens para {phone_number}")
                
                # Percorrer o array e enviar cada mensagem sequencialmente
                for i, message_data in enumerate(result["messages"]):
                    try:
                        logger.info(f"Enviando mensagem {i+1}/{len(result['messages'])}: {message_data}")
                        
                        # Verificar se é uma resposta com mídia ou texto
                        if message_data.get("media_url"):
                            # Enviar mídia
                            await mega_api.send_mega_api_media(
                                phone_number, 
                                message_data["media_url"], 
                                message_data.get("media_type", "image"), 
                                message_data.get("message", "")
                            )
                        elif message_data.get("message"):
                            # Enviar texto
                            await mega_api.send_mega_api_message(phone_number, message_data["message"])
                        
                        # Pequeno delay entre mensagens para evitar spam/rate limiting
                        if i < len(result["messages"]) - 1:  # Não aguardar após a última mensagem
                            await asyncio.sleep(0.5)  # 500ms de delay
                            
                    except Exception as e:
                        logger.error(f"Erro ao enviar mensagem {i+1}: {str(e)}")
                        continue  # Continuar com as próximas mensagens mesmo se uma falhar
                        
            # Manter compatibilidade com formato antigo (mensagem única)
            elif result.get("media_url"):
                # Enviar mídia
                await mega_api.send_mega_api_media(
                    phone_number, 
                    result["media_url"], 
                    result.get("media_type", "image"), 
                    result.get("message", "")
                )
            elif result.get("message"):
                # Enviar texto
                await mega_api.send_mega_api_message(phone_number, result["message"])
        
        
        
    except Exception as e:
        logger.error(f"Erro ao processar webhook da Mega API: {str(e)}")
        return {"status": "error", "message": str(e)}


@router.get("/status")
async def get_status():
    """
    Endpoint para verificar status da instância
    """
    try:
        mega_api = AgentMegaApi()
        status = await mega_api.get_instance_status()
        return status
    except Exception as e:
        logger.error(f"Erro ao obter status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/send-message")
async def send_message_endpoint(request: Request):
    """
    Endpoint para enviar mensagens via API
    """
    try:
        body = await request.json()
        to_number = body.get("to")
        message = body.get("message")
        
        if not to_number or not message:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'message' são obrigatórios")
        
        mega_api = AgentMegaApi()
        result = await mega_api.send_mega_api_message(to_number, message)
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/send-media")
async def send_media_endpoint(request: Request):
    """
    Endpoint para enviar mídia via API
    """
    try:
        body = await request.json()
        to_number = body.get("to")
        media_url = body.get("media_url")
        media_type = body.get("media_type", "document")
        caption = body.get("caption", "")
        
        if not to_number or not media_url:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'media_url' são obrigatórios")
        
        mega_api = AgentMegaApi()
        result = await mega_api.send_mega_api_media(to_number, media_url, media_type, caption)
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"Erro ao enviar mídia: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Exemplo de uso (para testes)
if __name__ == "__main__":
    import asyncio

    async def main():
        # Exemplo de envio de mensagem
        mega_api = AgentMegaApi()
        await mega_api.send_mega_api_message("553191149571", "Enviando mensagem para o numero 553191149571")
        await mega_api.send_mega_api_message("318419-8720", "Enviando mensagem para o numero 318419-8720")
        
        # Exemplo de verificação de status
        status = await mega_api.get_instance_status()
        print(f"Status: {status}")

    asyncio.run(main())

#========================================== 