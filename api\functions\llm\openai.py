from ..secrets import fetch_api_key_env
from openai import OpenAI
import asyncio



client = OpenAI(
    api_key= secret.
)


async def openai_chat(messages, config=None):
    
    
  if config is None:
      config = {
        "model" : "gpt-4-0125-preview"
      }
    
  chat_completion = client.chat.completions.create(
  model=config["model"],
  messages=messages
)
  response = chat_completion.choices[0].message.content
  return response



async def main():
    
    messages = [
    {"role": "user", "content": "<PERSON><PERSON>, qual o seu nome"}

    ]
    
    system = ""
    result = await openai_chat(messages)
    #print("result", result)
    
#asyncio.run(main())
#execução a partir do raiz: python -m api.functions.llm.openai