from ..secrets import fetch_api_key_env
import google.generativeai as genai
import asyncio

genai.configure(api_key =fetch_api_key_env("GEMINI_API_KEY")
)
model = genai.GenerativeModel('gemini-pro')

async  def gemini_generate(text, config=None):

  if isinstance(text, list):
    # Chama a função auxiliar para concatenar o conteúdo
    text = await concatenate_text_contents(text)


  response =   model.generate_content(text)
  #print("translated:",response.text)
  return response.text

#==============================
async def concatenate_text_contents(text_list):
    """
    Processa uma lista de dicionários, concatenando o valor de 'content' de cada dicionário,
    separado por quebras de linha.
    """
    concatenated_text = ""
    for item in text_list:
        concatenated_text += item['content'] + "\n"
    return concatenated_text

async def main():

 
    response = await gemini_generate(
 
        text = [{
            "role":"user",
            "content": "oi, bom dia"
        }]
    )
    
    print("response",response)
#asyncio.run(main())