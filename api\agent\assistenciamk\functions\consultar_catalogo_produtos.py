from agents.tool import function_tool 
from ...agent_neo4j import AgentNeo4j
import json

neo4j = AgentNeo4j()
function_tool
async def consultar_catalogo_produtos(query: str, params: str):
    """
    CONSULTA O CATÁLOGO GERAL DE PRODUTOS (Neo4j).
    
    ⚠️⚠️⚠️ ATENÇÃO: LEIA COM ATENÇÃO ANTES DE USAR ESTA FUNÇÃO ⚠️⚠️⚠️
    
    USE ESTA FUNÇÃO PARA:
    - Buscar produtos no catálogo geral
    - Filtrar produtos por características (cor, preço, etc)
    - Obter informações técnicas sobre os produtos
    - Consultar disponibilidade no catálogo (não confundir com estoque pessoal)

    🚫🚫🚫 REGRAS ABSOLUTAS DURANTE O FLUXO DE COMPRA 🚫🚫🚫
    - NUNCA verifique o estoque durante o fluxo de compra
    - Ignore completamente a informação de estoque ao adicionar itens a uma compra
    - A informação de estoque zero NÃO deve impedir a adição de itens a uma compra
    - NUNCA mostre mensagens sobre estoque zero durante o fluxo de compra
    - NÃO pergunte sobre estoque ou saldo ao usuário durante a compra
    - Lembre-se: compra = entrada de itens no estoque, não venda

    ✅ QUANDO USAR ESTA FUNÇÃO:
    - Para buscar produtos no catálogo geral
    - Para obter informações técnicas sobre produtos
    - Para listar produtos disponíveis para compra

    ❌ NÃO USE ESTA FUNÇÃO para verificar estoque pessoal. Para isso, use verificar_estoque_revenda().

    NÓS DISPONÍVEIS:
    - Produto  {idx, codigo, nome, descricao, preco, preco_maior, excluido}
    - Cor      {idx, nome, codigo, hexadecimal}

    RELAÇÕES:
    - (p:Produto)-[:TEM_COR]->(c:Cor)

    REGRAS OBRIGATÓRIAS na query:
    - MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
    - WHERE p.excluido <> 1
    - RETURN ...

    EXEMPLOS DE USO:
    
    1. Buscar produtos por nome ou código:
       ```
       query: MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
              WHERE p.excluido <> 1 
              AND (toLower(p.nome) CONTAINS toLower($termo) OR p.codigo CONTAINS $termo)
              RETURN p
       params: {"negocio_idx": "5544332211", "termo": "batom"}
       ```

    2. Produtos de uma cor específica:
       ```
       query: MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)-[:TEM_COR]->(c:Cor)
              WHERE p.excluido <> 1 AND toLower(c.nome) = toLower($cor_nome)
              RETURN p
       params: {"negocio_idx": "5544332211", "cor_nome": "Vermelho"}
       ```

    3. Produtos por faixa de preço:
       ```
       query: MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
              WHERE p.excluido <> 1 
              AND p.preco >= $preco_min AND p.preco <= $preco_max
              RETURN p.nome, p.preco 
              ORDER BY p.preco
       params: {"negocio_idx": "5544332211", "preco_min": 50, "preco_max": 100}
       ```
    
    4. Produtos mais caros (top 5):
       ```
       query: MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
              WHERE p.excluido <> 1
              RETURN p.codigo, p.nome, p.preco 
              ORDER BY p.preco DESC 
              LIMIT 5
       params: {"negocio_idx": "5544332211"}
       ```
    """
    print("===== CONSULTAR CATÁLOGO DE PRODUTOS =====") 
    print(f"Query: {query}")
    print(f"Params: {params}")

    try:
        p = json.loads(params)
        if p.get("negocio_idx") is None:
            return {"status": "error", "message": "negocio_idx obrigatório"}

        q = query.upper()
        if "MATCH" not in q or "RETURN" not in q:
            return {"status": "error", "message": "Query precisa de MATCH e RETURN"}
        if "$NEGOCIO_IDX" not in q or "EXCLUIDO" not in q:
            return {"status": "error", "message": "Faltam filtros obrigatórios ($negocio_idx ou excluido)"}

        result = await neo4j.execute_read_query(query, p)
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
   import asyncio
   
   async def testa_consultar_catalogo():
      # Exemplo: Buscar os 5 produtos mais caros
      query = """
      MATCH (n:Negocio {idx: $negocio_idx})-[:POSSUI_PRODUTO]->(p)
      WHERE p.excluido <> 1
      RETURN p.codigo, p.nome, p.preco 
      ORDER BY p.preco DESC 
      LIMIT 5"""
      params = '{"negocio_idx": "5544332211"}'
      result = await consultar_catalogo_produtos(query, params)
      print("=== RESULTADO DA CONSULTA AO CATÁLOGO ===")
      print(result)

   asyncio.run(testa_consultar_catalogo())
# Execução:
# python -m api.agent.assistenciamk.functions.consultar_catalogo_produtos