-- =============================================
-- SCRIPT PRECISO PARA CRIAÇÃO DE ÍNDICES FULLTEXT
-- Baseado nas tabelas e colunas EXISTENTES no banco gptalkc1_01
-- =============================================

-- Verificar índices FULLTEXT existentes antes de criar novos
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND INDEX_TYPE = 'FULLTEXT'
ORDER BY TABLE_NAME, INDEX_NAME;

-- =============================================
-- 1. TABELA PRODUTO - Colunas para busca textual
-- Usado em: agent_marykay.py, agent_product.py, agent_assistenciamk.py
-- =============================================

-- Índices individuais
ALTER TABLE PRODUTO ADD FULLTEXT idx_produto_nome (NOME);
ALTER TABLE PRODUTO ADD FULLTEXT idx_produto_codigo (CODIGO);
ALTER TABLE PRODUTO ADD FULLTEXT idx_produto_descr (DESCR);
ALTER TABLE PRODUTO ADD FULLTEXT idx_produto_marca (MARCA);

-- Índice composto para busca em múltiplas colunas
ALTER TABLE PRODUTO ADD FULLTEXT idx_produto_busca_completa (NOME, CODIGO, DESCR);

-- =============================================
-- 2. TABELA COR - Coluna NOME
-- Usado em: agent_marykay.py, agent_assistenciamk.py  
-- =============================================

ALTER TABLE COR ADD FULLTEXT idx_cor_nome (NOME);

-- =============================================
-- 3. TABELA SERVICO - Colunas para busca textual
-- Usado em: agent_service.py
-- =============================================

-- Índices individuais
ALTER TABLE SERVICO ADD FULLTEXT idx_servico_nome (NOME);
ALTER TABLE SERVICO ADD FULLTEXT idx_servico_codigo (CODIGO);

-- Índice composto (SERVICO não tem coluna DESCR)
ALTER TABLE SERVICO ADD FULLTEXT idx_servico_busca_completa (NOME, CODIGO);

-- =============================================
-- 4. TABELA CATEGORIA_PRODUTO - Coluna NOME
-- Usado em: agent_product.py (CATEGORIA_NOME em VIEWs)
-- =============================================

ALTER TABLE PRODUTO_CATEGORIA ADD FULLTEXT idx_produto_categoria_nome (NOME);

-- =============================================
-- 5. TABELA CATEGORIA_SERVICO - Coluna NOME  
-- Usado em: agent_service.py (CATEGORIA_NOME em VIEWs)
-- =============================================

ALTER TABLE SERVICO_CATEGORIA ADD FULLTEXT idx_servico_categoria_nome (NOME);

-- =============================================
-- 6. TABELA CLIENTE - Colunas para busca textual
-- Usado em: agent_customer.py, agent_oficinatech.py, agent_mktcontact.py
-- =============================================

-- Índices individuais para colunas principais
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_nome (NOME);
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_email (EMAIL);
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_telefone (TELEFONE);
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_cpf_cnpj (CPF_CNPJ);

-- Índices para endereço
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_bairro (BAIRRO);
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_cidade (CIDADE);
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_uf (UF);

-- Índice composto para busca principal
ALTER TABLE CLIENTE ADD FULLTEXT idx_cliente_busca_principal (NOME, EMAIL, TELEFONE);

-- =============================================
-- 7. TABELA NEGOCIO - Coluna NOME
-- Usado em: agent_oficinatech.py
-- =============================================

ALTER TABLE NEGOCIO ADD FULLTEXT idx_negocio_nome (NOME);
ALTER TABLE NEGOCIO ADD FULLTEXT idx_negocio_razao_social (RAZAO_SOCIAL);

-- =============================================
-- 8. TABELA VEICULO_CATEGORIA - Coluna NOME
-- Usado em: agent_oficinatech.py
-- =============================================

ALTER TABLE VEICULO_CATEGORIA ADD FULLTEXT idx_veiculo_categoria_nome (NOME);

-- =============================================
-- 9. TABELA PRODUTO_TIPO - Coluna NOME
-- Usado em: agent_oficinatech.py
-- =============================================

ALTER TABLE PRODUTO_TIPO ADD FULLTEXT idx_produto_tipo_nome (NOME);

-- =============================================
-- 10. TABELA CONTABIL_CONTA - Colunas NOME e CODIGO
-- Usado em: agent_accountant.py
-- =============================================

ALTER TABLE CONTABIL_CONTA ADD FULLTEXT idx_contabil_conta_nome (NOME);
ALTER TABLE CONTABIL_CONTA ADD FULLTEXT idx_contabil_conta_codigo (CODIGO);
ALTER TABLE CONTABIL_CONTA ADD FULLTEXT idx_contabil_conta_busca (NOME, CODIGO);

-- =============================================
-- 11. TABELA AGENTE - Colunas NOME e IDX
-- Usado em: agent_atm.py, agent_mysql.py
-- =============================================

ALTER TABLE AGENTE ADD FULLTEXT idx_agente_nome (NOME);
ALTER TABLE AGENTE ADD FULLTEXT idx_agente_idx (IDX);

-- =============================================
-- 12. VERIFICAÇÃO DOS ÍNDICES CRIADOS
-- =============================================

-- Verificar todos os índices FULLTEXT criados
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND INDEX_TYPE = 'FULLTEXT'
ORDER BY TABLE_NAME, INDEX_NAME;

-- Verificar tamanho dos índices
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'INDEX_SIZE_MB'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND INDEX_LENGTH > 0
ORDER BY INDEX_LENGTH DESC;

-- =============================================
-- NOTAS IMPORTANTES:
-- =============================================

/*
✅ TABELAS VERIFICADAS E CONFIRMADAS:
- PRODUTO: NOME, CODIGO, DESCR, MARCA (todas varchar)
- COR: NOME (varchar)
- SERVICO: NOME, CODIGO (varchar) - não tem DESCR
- PRODUTO_CATEGORIA: NOME (varchar)
- SERVICO_CATEGORIA: NOME (varchar)
- CLIENTE: NOME, EMAIL, TELEFONE, CPF_CNPJ, BAIRRO, CIDADE, UF (todas varchar)
- NEGOCIO: NOME, RAZAO_SOCIAL (varchar)
- VEICULO_CATEGORIA: NOME (varchar)
- PRODUTO_TIPO: NOME (varchar)
- CONTABIL_CONTA: NOME, CODIGO (varchar)
- AGENTE: NOME, IDX (varchar)

⚠️ TABELAS NÃO ENCONTRADAS OU SEM COLUNAS NECESSÁRIAS:
- FINANCEIRO_CONTAS_LANCAMENTO: não possui DOCUMENTO, HISTORICO como varchar
- PRODUTO_MARCA: não existe como tabela separada
- PRODUTO_COR: existe mas só tem COR_CODIGO, NEGOCIO_IDX

💡 OTIMIZAÇÕES RECOMENDADAS:
1. Use MATCH() AGAINST() em vez de LIKE '%termo%' após criar os índices
2. Para buscas em VIEWs, os índices das tabelas base serão automaticamente utilizados
3. Monitore o desempenho das consultas com EXPLAIN
4. Considere usar IN BOOLEAN MODE para buscas mais complexas

🚀 EXEMPLO DE USO APÓS CRIAÇÃO DOS ÍNDICES:
-- Em vez de: WHERE NOME LIKE '%termo%'
-- Use: WHERE MATCH(NOME) AGAINST('termo' IN BOOLEAN MODE)

-- Para múltiplas palavras:
-- WHERE MATCH(NOME, CODIGO, DESCR) AGAINST('+palavra1 +palavra2' IN BOOLEAN MODE)
*/ 