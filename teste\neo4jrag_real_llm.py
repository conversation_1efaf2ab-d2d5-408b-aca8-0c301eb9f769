import asyncio
import os
import neo4j
from neo4j_graphrag.embeddings import OpenAIEmbeddings
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import OpenAILLM

# --- Configurações da Aplicação ---
# Este script usa os serviços reais da OpenAI (LLM e Embeddings)
NEO4J_URI = "neo4j://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "sua_senha_super_secreta"
NEO4J_DATABASE = "neo4j"

# --- Texto de Exemplo para Ingestão ---
# Usaremos um texto simples para focar no funcionamento da biblioteca.
# A lógica para extrair texto de um vídeo do YouTube (Passo 1) permanece a mesma.
TEXTO_PARA_PROCESSAR = """
A Acme Corporation, fundada em 1985 por <PERSON> em Nova York, é líder em widgets.
<PERSON>, que entrou em 2010, agora é a Diretora de Engenharia.
O produto principal, SuperWidget X1, foi desenvolvido pela equipe de <PERSON>.
"""

# --- De<PERSON><PERSON><PERSON> do Nosso Esquema (Nossa "Planta da Casa") ---
# Este é o esquema que desenhamos juntos, agora formatado para a biblioteca.
# A biblioteca usará isso para guiar o LLM na extração.
NOSSO_ESQUEMA = {
    "node_types": [
        {"label": "Person", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Organization", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Product", "properties": [{"name": "name", "type": "STRING"}]},
        {"label": "Location", "properties": [{"name": "name", "type": "STRING"}]}
    ],
    "relationship_types": [
        "WORKS_FOR",
        "FOUNDED_BY",
        "LOCATED_IN",
        "DEVELOPED"
    ],
    "patterns": [
        ("Person", "WORKS_FOR", "Organization"),
        ("Organization", "FOUNDED_BY", "Person"),
        ("Organization", "LOCATED_IN", "Location"),
        ("Product", "DEVELOPED", "Person")
    ]
}


async def main():
    """
    Função principal que configura e executa o pipeline de ingestão
    usando a biblioteca neo4j-graphrag com LLM real da OpenAI.
    """
    print("--- Iniciando Pipeline de Ingestão com LLM Real da OpenAI ---")

    # Verifica se a chave da API foi configurada
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        print("❌ ERRO: A variável de ambiente OPENAI_API_KEY não foi definida.")
        print("   Configure sua chave da API OpenAI antes de executar o script.")
        return

    # --- Configuração dos Componentes ---
    # Usando componentes reais da OpenAI
    print("-> Configurando LLM e Embeddings da OpenAI...")
    llm = OpenAILLM(model_name="gpt-4o", api_key=openai_api_key)
    embedder = OpenAIEmbeddings(api_key=openai_api_key)

    # Conexão real com o banco de dados Neo4j
    print("-> Conectando ao Neo4j...")
    try:
        driver = neo4j.GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        
        # Testa a conexão
        with driver.session(database=NEO4J_DATABASE) as session:
            session.run("RETURN 1")
        print("-> Conexão com Neo4j estabelecida com sucesso.")
        
    except Exception as e:
        print(f"❌ ERRO: Não foi possível conectar ao Neo4j: {e}")
        print("   Verifique se o Neo4j está rodando e as credenciais estão corretas.")
        return

    # Limpa o banco de dados para um novo teste
    with driver.session(database=NEO4J_DATABASE) as session:
        session.run("MATCH (n) DETACH DELETE n")
    print("-> Banco de dados limpo para o teste.")

    # --- Criação do Pipeline ---
    # A classe `SimpleKGPipeline` é o nosso "mestre de obras".
    # Ela já contém toda a lógica para orquestrar os passos que discutimos.
    print("-> Criando pipeline de extração de conhecimento...")
    kg_builder = SimpleKGPipeline(
        llm=llm,
        driver=driver,
        embedder=embedder,
        schema=NOSSO_ESQUEMA,
        from_pdf=False,  # Estamos usando texto, não um arquivo PDF
        on_error="RAISE", # Se o LLM falhar, o processo para.
        neo4j_database=NEO4J_DATABASE
    )
    
    print("\n-> Pipeline configurado. Executando a ingestão do texto...")

    try:
        # --- Execução do Pipeline ---
        # Com uma única chamada, a biblioteca executa todo o fluxo:
        # 1. Quebra o texto em pedaços (chunks).
        # 2. Gera embeddings para os chunks.
        # 3. Chama o LLM para extrair entidades e relações com base no nosso esquema.
        # 4. Escreve o grafo léxico (Documento, Chunks) e o grafo de conhecimento no Neo4j.
        # 5. Executa a resolução de entidades (MERGE) para evitar duplicatas.
        result = await kg_builder.run_async(text=TEXTO_PARA_PROCESSAR)

        print("\n--- ✅ Pipeline Concluído com Sucesso ---")
        print(f"ID da Execução: {result.run_id}")
        print(f"Resultado Final: {result.result}")

        # --- Verificação dos Resultados no Banco de Dados ---
        print("\n--- Verificando o Grafo Criado no Neo4j ---")
        with driver.session(database=NEO4J_DATABASE) as session:
            # Consulta nós de entidade
            nodes_result = session.run("MATCH (n:__Entity__) RETURN n.label AS label, n.properties.name AS name")
            print("Nós de Entidade Criados:")
            node_count = 0
            for record in nodes_result:
                print(f"- Nó: {record['label']}, Nome: {record['name']}")
                node_count += 1
            
            if node_count == 0:
                print("  Nenhum nó de entidade encontrado.")
            
            # Consulta relações
            rels_result = session.run(
                "MATCH (n)-[r]->(m) WHERE NOT r:FROM_CHUNK AND NOT r:NEXT_CHUNK "
                "RETURN n.properties.name AS start, type(r) AS type, m.properties.name AS end"
            )
            print("\nRelações Criadas:")
            rel_count = 0
            for record in rels_result:
                print(f"- Relação: ({record['start']})-[:{record['type']}]->({record['end']})")
                rel_count += 1
            
            if rel_count == 0:
                print("  Nenhuma relação encontrada.")

            # Estatísticas gerais
            stats_result = session.run(
                "MATCH (n) RETURN labels(n) AS labels, count(n) AS count"
            )
            print("\nEstatísticas do Grafo:")
            for record in stats_result:
                labels = ", ".join(record['labels'])
                print(f"- {labels}: {record['count']} nós")

    except Exception as e:
        print(f"\n❌ ERRO durante a execução do pipeline: {e}")
        print("   Verifique sua conexão com a OpenAI e configurações do Neo4j.")
    
    finally:
        driver.close()
        print("\n-> Conexão com Neo4j fechada.")


if __name__ == "__main__":
    """
    Para rodar este script, você precisa:
    1. Neo4j rodando localmente (porta 7687)
    2. Variável de ambiente OPENAI_API_KEY configurada
    3. Bibliotecas instaladas: pip install "neo4j-graphrag[openai]"
    
    Exemplo de uso:
    
    # No Windows (PowerShell):
    $env:OPENAI_API_KEY = "sk-sua-chave-aqui"
    python teste/neo4jrag_real_llm.py
    
    # No Linux/Mac:
    export OPENAI_API_KEY="sk-sua-chave-aqui"
    python teste/neo4jrag_real_llm.py
    """
    asyncio.run(main())
