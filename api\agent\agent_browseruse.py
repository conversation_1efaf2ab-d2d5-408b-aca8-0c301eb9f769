from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI
from browser_use import Agent, BrowserConfig
import asyncio
from dotenv import load_dotenv
import os
from .agent_secret import Secret

# Carrega as variáveis do arquivo .env
load_dotenv()

# Define o modelo a ser usado
nomeLLM = "openai"  # Mude para "gemini" se preferir

# Define se quer rodar em modo headless (False para ver o navegador durante testes)
HEADLESS_MODE = False  # Mude para True para rodar sem abrir o navegador


async def salva_produtos(Categoria: str, Subcategoria: str, productos: list):
    print("***** salvando produtos() *****")
    print(f"Salvando produtos da categoria {Categoria} e subcategoria {Subcategoria}")

async def main():

    secret = Secret()
    # Pega as chaves de API e credenciais do arquivo .env
    google_api_key = os.getenv("GEMINI_API_KEY")
    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
    #openai_api_key = os.getenv("OPENAI_API_KEY")
    openai_api_key = secret.get_secret("OPENAI_API")
    consultant_code = os.getenv("CONSULTANT_CODE", "LN9427")  # Valor padrão para testes
    consultant_password = os.getenv("CONSULTANT_PASSWORD", "@Gigi1810")  # Valor padrão para testes
    print("openai_api_key: ", openai_api_key)
    
    # Verifica as chaves de API com base no modelo selecionado
    if nomeLLM == "gemini" and not google_api_key:
        print("Erro: A GOOGLE_API_KEY não foi encontrada no arquivo .env")
        print("Adicione-a ao arquivo .env ou verifique se está correta.")

    elif nomeLLM == "deepseek" and not deepseek_api_key:
        print("Erro: A DEEPSEEK_API_KEY não foi encontrada no arquivo .env")
        print("Adicione-a ao arquivo .env ou verifique se está correta.")
        return
    elif nomeLLM == "openai" and not openai_api_key:
        print("Erro: A OPENAI_API_KEY não foi encontrada no arquivo .env")
        print("Adicione-a ao arquivo .env ou verifique se está correta.")
        return

    # Define o modelo de linguagem com base no nomeLLM
    if nomeLLM == "gemini":
        llm = ChatGoogleGenerativeAI(
            model="models/gemini-2.0-flash",
            google_api_key=google_api_key
        )
    elif nomeLLM == "deepseek":
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=deepseek_api_key
        )
    elif nomeLLM == "openai":
        llm = ChatOpenAI(
            model="gpt-4o",
            openai_api_key=openai_api_key
        )




    # Define a tarefa de login e extração de produtos
    login_task = f"""
    1. Acessar o site https://order.marykayintouch.com.br/opos?lang=pt_BR .
    2. Localizar e clicar no campo de entrada para o código de consultora.
    3. Digitar o código de consultora '{consultant_code}' no campo apropriado.
    4. Localizar e clicar no campo de entrada para a senha.
    5. Digitar a senha '{consultant_password}' no campo apropriado.
    6. Clicar no botão 'Iniciar' ou botão de login para submeter o formulário.
    7. Aguardar a página carregar completamente.
    8. Clicar na caixa 'Ir até uma Categoria'.
    9. Localizar a Categoria 'Fragrâncias' e clicar na subcategoria 'At Play'. Aguardar os produtos serem exibidos.
    10. Analisar a lista de produtos exibida na tela. Para cada produto da subcategoria 'At Play' e somente desta subcategoria e da Categoria 'Fragrâncias', extrair as seguintes informações: Código, Nome do Produto, Pontos e Preço. Estruturar esses dados como um array de objetos, onde cada objeto representa um produto e tem as chaves "Codigo", "Nome", "Pontos" e "Preco" com seus respectivos valores.
    11. Chamar a função 'salva_produtos' com os seguintes argumentos: Categoria="Fragrâncias", Subcategoria="At Play", e o array de produtos extraído no passo anterior.
    """

    print(f"--- Executando tarefa: Login Mary Kay com {nomeLLM} ---")
    print(f"Instruções: {login_task}")
    print(f"Modo headless: {HEADLESS_MODE}")

    # Configura o navegador
    browser_config = BrowserConfig(
        headless=HEADLESS_MODE,  # Define se o navegador será visível ou não
        browser_type="chromium"  # Usa Chromium
    )

    # Cria o agente
    agent = Agent(
        task=login_task,
        llm=llm,
        use_vision=True,  # Habilita visão (se suportado pelo modelo)
        #browser_config=browser_config
    )

    # Executa o agente com tratamento de erros
    try:
        await agent.run()
        print(f"--- Tarefa de login concluída com sucesso ---")
    except Exception as e:
        print(f"Erro durante a execução do agente: {str(e)}")
        print("Verifique os logs ou ative BROWSER_USE_LOGGING_LEVEL=debug no .env para mais detalhes.")

# Executa a função principal assíncrona
if __name__ == '__main__':
    asyncio.run(main())