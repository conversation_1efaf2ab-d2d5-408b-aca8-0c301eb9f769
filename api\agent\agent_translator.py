import deepl
from .agent_secret import Secret

class Translator:
    def __init__(self):
        secret = Secret()
        self.deepLtranslator = deepl.Translator(secret.DEEPL_API_KEY)

    async def deepl_translate_text(self,text:str, target_lang:str):
        result = self.deepLtranslator.translate_text(text=text, target_lang=target_lang)
        return result


import asyncio
async def main():
    tsl = Translator()
    text = "bom dia"
    target = "EN-US"
    result  = await tsl.deepl_translate_text(text,target)
    print("result",result)
#asyncio.run(main())