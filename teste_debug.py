import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.agent.agent import carrega_ferramentas

print("=== TESTE DE DEBUG ===")
print("Chamando carrega_ferramentas...")

# Teste com agent_test
resultado = carrega_ferramentas('agent_test')
print(f"Resultado: {resultado}")
print(f"Quantidade: {len(resultado)}")

# Teste com agent_atmzap
resultado2 = carrega_ferramentas('agent_atmzap')
print(f"Resultado atmzap: {resultado2}")
print(f"Quantidade atmzap: {len(resultado2)}")