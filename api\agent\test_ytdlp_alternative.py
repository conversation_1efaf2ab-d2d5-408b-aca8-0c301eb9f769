import yt_dlp
import os

# URL do vídeo
url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"

# Caminho absoluto para salvar o arquivo
output_dir = r"C:\Users\<USER>\Documents\GitHub\gptalk\temp\audio"

# Criar o diretório se não existir
os.makedirs(output_dir, exist_ok=True)

# Configurações do yt-dlp com cookies do navegador
ydl_opts = {
    'format': 'bestaudio/best',
    'outtmpl': os.path.join(output_dir, '%(title)s.%(ext)s'),
    'extractaudio': True,
    'audioformat': 'mp3',
    'noplaylist': True,
    # Usar cookies do Edge para autenticação
    'cookiesfrombrowser': ('edge',),
    # Opções para tentar contornar detecção de bot
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'extractor_args': {
        'youtube': {
            'player_client': ['android', 'web']
        }
    },
    'sleep_interval': 1,
    'max_sleep_interval': 5,
}

try:
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        print("Iniciando download...")
        ydl.download([url])
        print("Download concluído!")
        
    # Listar arquivos no diretório
    print(f"\nArquivos na pasta {output_dir}:")
    for file in os.listdir(output_dir):
        file_path = os.path.join(output_dir, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"  - {file} ({size} bytes)")
            
except Exception as e:
    print(f"Erro durante o download: {e}")
