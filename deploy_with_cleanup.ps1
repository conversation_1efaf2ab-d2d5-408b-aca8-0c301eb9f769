# Fazer o deploy da nova versão
Write-Output "Fazendo o deploy da nova versão..."
$deployOutput = gcloud app deploy --quiet
Write-Output $deployOutput

# Extrair o ID da nova versão do output do deploy
$targetVersionLine = $deployOutput | Select-String -Pattern "target version:"

if ($targetVersionLine) {
    $newVersionID = $targetVersionLine -replace ".*target version:\s+(\S+).*", '$1'
    Write-Output "Nova versão implantada: $newVersionID"

    # Mover todo o tráfego para a nova versão
    Write-Output "Movendo todo o tráfego para a nova versão: $newVersionID"
    gcloud app services set-traffic default --splits "$newVersionID=1"

    # Pausar um pouco para garantir que a mudança de tráfego foi aplicada
    Start-Sleep -Seconds 10

    # Listar todas as versões em ordem de implantação
    $versions = gcloud app versions list --sort-by "~LAST_DEPLOYED" --format="value(VERSION.ID)"
    Write-Output "Versões listadas: $versions"

    # Verificar se há mais de duas versões para deletar
    if ($versions.Count -gt 2) {
        $latestVersions = $versions[0..1]
        $versionsToDelete = $versions[2..$versions.Count]

        Write-Output "Deletando as versões antigas exceto as duas últimas: $versionsToDelete"
        foreach ($version in $versionsToDelete) {
            gcloud app versions delete $version -q
        }
    } else {
        Write-Output "Apenas duas ou menos versões encontradas, nenhuma versão antiga para deletar."
    }
} else {
    Write-Output "Falha ao capturar o ID da nova versão."
}
